/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { Component } from '@tarojs/taro';
import { Image, ScrollView, View } from '@tarojs/components';
import KbPage from '~base/components/page';
import KbLoader from '@base/components/loader';
import KbEmpty from '@base/components/empty';
import { debounce } from '~base/utils/utils';
import { getEditPageLunchParams } from '~/components/_pages/order/_utils/order.edit';
import { addReceiveCoupon, getActivityCouponConfig } from '~/components/_pages/order/kxj/_utils';
import dayjs from 'dayjs';
import classNames from 'classnames';
import { apply_brand_type_map } from '../card/utils';
import './index.scss';

class Index extends Component {
  constructor(props) {
    super(props);
    const { activity, coupon_id } = this.$router.params;
    this.state = {
      activity,
      coupon_id,
      apply_brand_type_map,
      expiration_time_map: {
        hour: '小时',
        minute: '分钟',
        days: '天',
      },
      loading: true,
    };
    this.handleAdd = debounce(this.handleAdd, 1000);
  }

  onUpdate = async (data) => {
    const { logined } = data;
    if (logined) {
      const { activity, coupon_id } = await getEditPageLunchParams(this);
      this.setState(
        {
          activity,
          coupon_id,
        },
        () => {
          this.getCouponConfig();
        },
      );
    }
  };

  getCouponConfig = () => {
    const { activity, coupon_id, apply_brand_type_map } = this.state;
    if (!coupon_id || !activity) return;
    this.setState({
      loading: true,
    });
    getActivityCouponConfig({ activity, coupon_id }).then((res) => {
      this.setState({
        loading: false,
      });
      if (res.data && res.data.length > 0) {
        const couponDetail = res.data[0] || {};
        const { coupon_type, rate_type, min_freight } = couponDetail;
        const apply_brand_type_name = apply_brand_type_map[coupon_type] || '';
        this.setState({
          couponDetail: {
            ...couponDetail,
            rate_type:
              rate_type === 'discount' ? 'discount' : min_freight > 0 ? 'subtract' : 'zhijian',
            apply_brand_type_name,
          },
        });
      } else if (res.code != 0) {
        this.setState({
          msg: res.msg,
        });
      }
    });
  };

  handleAdd = () => {
    const { activity, coupon_id } = this.state;
    if (!coupon_id || !activity) return;
    addReceiveCoupon({ activity, coupon_id }).then((res) => {
      if (res.code == 0) {
        Taro.kbToast({
          text: '领取成功',
          onClose: () => {
            Taro.navigator({
              url: 'order/card',
            });
          },
        });
      }
    });
  };

  render() {
    const { couponDetail = {}, expiration_time_map, loading, msg, ...rest } = this.state;
    const {
      apply_brand_type_name,
      rate,
      rate_type,
      expiration_type,
      expiration_time,
      coupon_img,
      min_freight,
    } = couponDetail || {};
    console.log('couponDetail', couponDetail);
    const isManJian = !!(rate_type === 'subtract');
    const isZhiJian = !!(rate_type === 'zhijian');
    const isZheKou = !!(rate_type === 'discount');
    const rootCls = classNames('kb-customCoupon', {
      'kb-customCoupon-bgColor': !!coupon_img,
    });
    return (
      <KbPage className={rootCls} onUpdate={this.onUpdate} {...rest}>
        <ScrollView className='kb-customCoupon-scrollview' scrollY>
          {msg ? (
            <KbEmpty centered description={msg} />
          ) : loading ? (
            <KbLoader centered />
          ) : (
            <View className='kb-customCoupon-container'>
              <View className='kb-customCoupon-bg'>
                <Image className='img' mode='widthFix' src={coupon_img} />
              </View>
              <View className='kb-customCoupon-coupon'>
                <View className='kb-customCoupon-coupon__tag'>{apply_brand_type_name}</View>
                <View className='kb-customCoupon-coupon__reward'>
                  {!isZheKou ? <View className='unit'>￥</View> : null}
                  <View className='value'>
                    {rate}
                    {isManJian ? <View className='tag'>满{min_freight}可用</View> : null}
                  </View>
                  {isZheKou ? <View className='unit'>折</View> : null}
                </View>
                <View className='kb-customCoupon-coupon__type'>
                  {isManJian ? '满减券' : isZhiJian ? '直减券' : isZheKou ? '折扣券' : ''}
                </View>
                <View
                  className='kb-customCoupon-coupon__btn'
                  onClick={this.handleAdd}
                  hoverClass='kb-hover-opacity'
                >
                  <Image
                    className='img'
                    mode='widthFix'
                    src='https://cdn-img.kuaidihelp.com/qj/miniapp/custom-coupon/btn.png?v=01'
                  />
                </View>
                <View className='kb-customCoupon-coupon__time'>
                  有效期截止：
                  {expiration_type === 'time'
                    ? `${dayjs(expiration_time).format('YYYY-MM-DD')}有效`
                    : `领取后${expiration_time}${expiration_time_map[expiration_type]}内有效`}
                </View>
              </View>
            </View>
          )}
        </ScrollView>
      </KbPage>
    );
  }
}

export default Index;
