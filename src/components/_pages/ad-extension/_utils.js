/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro from '@tarojs/taro';
import request from '@base/utils/request';
import { reportAnalytics, getPage, getStorageSync, setStorageSync } from '@base/utils/utils';
import isArray from 'lodash/isArray';
import isObject from 'lodash/isObject';
import isUndefined from 'lodash/isUndefined';
import { checkIsShieldAd, checkTodayWhichTimeShow, setShieldAdLimit } from './sdk/shield';
import { createAd } from './sdk';
import { filterAdsOutReward } from './sdk/filterAds/out.reward';
import Storage from '~base/utils/storage';
import { filterAdsBase } from './sdk/filterAds/base';

export const _reportAnalytics = reportAnalytics;
export const _getPage = getPage;

export const createAdPlatformConfig = () => {
  let platform = '';
  if (process.env.MODE_ENV === 'wkd') {
    platform =
      process.env.PLATFORM_ENV === 'weapp'
        ? 'wkdmini'
        : process.env.PLATFORM_ENV === 'alipay'
        ? 'wkdaliapp'
        : 'wkdswan';
  } else {
    platform = process.env.PLATFORM_ENV == 'weapp' ? 'yzmina' : 'yzaliapp';
  }
  return platform;
};

export const getAdConfigApiAndData = (params = {}) => {
  let req = {
    type: 'miniapp',
    platform: createAdPlatformConfig(),
    ...params,
  };

  if (process.env.PROJECT_ENV === 'MULTI') {
    const multiAdPlatformMap = {
      'miniapp-wkdmini': {
        type: 'apply',
        platform: 'wkd',
      },
    };
    const { type, platform } = req;
    req = {
      ...req,
      ...(multiAdPlatformMap[`${type}-${platform}`] || {}),
    };
  }

  return {
    url:
      process.env.MODE_ENV === 'wkd'
        ? '/g_tbk/v2/AdConfig/getAdConfig'
        : '/api/weixin/mini/Ad/getAdConfig',
    data: req,
  };
};

/**
 *
 * @description 拉取广告位统一上报
 * position：广告位置，具体可对应admin后台的配置
 * status：load：初始拉取、load-success：拉取成功、load-fail：拉取失败、load-exact-success：拉取成功 - 精确（会有广告位的title）
 */
const triggerAdConfigEventPush = (
  data,
  list,
  status = !list ? 'load' : list.length > 0 ? 'load-success' : 'load-fail',
) => {
  const opts = {
    key: 'event_push_ad',
    position: (data && data.position) || '0',
  };
  reportAnalytics({
    ...opts,
    status,
  });
  if (isArray(list)) {
    // 精确统计
    list.forEach((item) => {
      reportAnalytics({
        ...opts,
        title: item.title,
        status: 'load-exact-success',
      });
    });
  }
};

/**
 * Tower 任务: 支付宝端-微快递小程序-广告调整 ( https://tower.im/teams/258300/todos/102473 )
 * @description 补充请求数据，支付宝，指定位置传入 isCollect
 */
async function patchReqData(reqData) {
  if (process.env.PLATFORM_ENV === 'alipay') {
    const needIsCollectPosition = ['17', '41'];
    const { position } = reqData || {};
    if (needIsCollectPosition.includes(`${position}`)) {
      const { isCollected } = await Taro.isCollected();
      if (isCollected) {
        // 暂时调整为仅当用户收藏再传参数
        reqData.collected = isCollected ? '1' : '0';
      }
    }
  }
  return reqData;
}

/**
 *
 * @description 延迟resolve广告
 */
function adDelayResolve(list) {
  return new Promise((resolve) => {
    const { delayed_time = 0 } = list.find((item) => item.delayed_time) || {};
    if (delayed_time > 0) {
      setTimeout(() => {
        resolve(list);
      }, 1000 * delayed_time);
    } else {
      resolve(list);
    }
  });
}

const ads =
  process.env.PLATFORM_ENV === 'alipay' ? ['17', '6', '50', '5', '19', '47'] : ['46', '4'];
export const adTypeRollingOfficeMap = {
  6: {
    id: ['query.index.full', 'query.index.screen'],
  },
  17: {
    id: ['bill.detail.full', 'bill.detail.screen'],
  },
  // Tower 任务: 支付宝微快递-灯火插屏处理 ( https://tower.im/teams/258300/todos/115149 )
  // 50: {
  //   id: ['out.reward.full', 'out.reward.screen'],
  // },
};
const rollingStorageKey = 'ad_rolling_';
/**
 * @description: 广告缓存轮播加载
 * @param {Array} list
 * @param {Object} data
 * @returns {Promise<Array>}
 */
const adRollingResolve = (list, { position } = {}) => {
  return new Promise((resolve) => {
    if (!ads.includes(position)) {
      return resolve(list);
    }

    const checkIsPriority = (tag) => {
      return tag === 'ad2';
    };

    list.sort((a, b) => a.number - b.number);
    list.sort((a, b) => {
      const priorityA = checkIsPriority(a.tag);
      const priorityB = checkIsPriority(b.tag);
      return priorityA && !priorityB ? -1 : !priorityA && priorityB ? 1 : 0;
    });

    console.log('list===>', list);

    const officeAdKey = `officeAd-${position}`;
    const key = `${rollingStorageKey}${position}`;
    const { data: index } = getStorageSync(key) || {};
    const rollingIndex = !isUndefined(index) && index + 1 < list.length ? index + 1 : 0;
    const rollingList = [...list.slice(rollingIndex), ...list.slice(0, rollingIndex)];

    // 展示自建广告
    const showOwnAd = () => {
      console.log('本轮广告结束===>', rollingIndex >= rollingList.length - 1, rollingIndex);
      if (rollingIndex >= rollingList.length - 1) {
        setStorageSync(officeAdKey, '2');
      }
      setStorageSync(key, rollingIndex);
      const rollingListSlice = rollingList.slice(0, 1);
      resolve(rollingListSlice);
    };

    // 展示官方广告
    // 1、特殊广告展示完
    // 2、存在官方广告配置且本轮循环中没展示官方广告
    const isHasShowPriorityAdDone = !checkIsPriority(rollingList[0] && rollingList[0].tag);
    console.log('特殊自建广告展示完===>', isHasShowPriorityAdDone);
    const { id: officeAdId } = adTypeRollingOfficeMap[position] || {};

    const { data: lastAdType } = getStorageSync(officeAdKey) || {};
    const isHasOfficeAd = officeAdId && officeAdId[0] && lastAdType != '1';
    console.log('本轮循环中没展示过官方广告===>', isHasOfficeAd);
    if (isHasShowPriorityAdDone && isHasOfficeAd) {
      console.log('===>加载官方广告===>', officeAdId);
      createAd(officeAdId[0], {
        onClose: () => {
          if (officeAdId[1]) {
            console.log('二次===>加载官方广告====>');
            createAd(officeAdId[1]);
          }
        },
        onError: (err) => {
          console.log('加载官方广告失败====>', err);
          console.log('===>加载自建广告===>');
          setStorageSync(officeAdKey, '1');
          showOwnAd();
        },
      })
        .then(() => {
          setStorageSync(officeAdKey, '1');
          const emptyList = [];
          // 标记有官方广告
          emptyList.hasOfficeAd = true;
          resolve(emptyList);
        })
        .catch((err) => {
          console.log('catch加载官方广告失败====>', err);
        });
    } else {
      showOwnAd();
    }
  });
};

// const openTypeRollingAds = process.env.PLATFORM_ENV === 'alipay' ? ['6', '17', '50'] : ['4'];
// const adTypeRollingKey = 'ad_type_rolling_';
/**
 * @description: 广告类型轮播加载(自建广告、官方广告)
 * @param {*} list
 * @param {*} data
 * @returns {Promise<Array>}
 */
// eslint-disable-next-line no-unused-vars
// const adTypeRollingResolve = (list, { position } = {}) => {
//   return new Promise((resolve) => {
//     // 广告类型轮播
//     if (openTypeRollingAds.includes(position)) {
//       const [ad = {}] = list;
//       const key = `${adTypeRollingKey}${position}`;
//       const { data: lastAdType = '2' } = getStorageSync(key) || {};
//       console.log(
//         '上次展示广告类型为===>',
//         lastAdType === '1' ? '官方广告' : '自建广告',
//         lastAdType,
//         ad,
//       );
//       // 展示官方广告逻辑
//       // 1、没创建自建广告
//       // 2、上次记录展示的是自建广告
//       // 3、上次记录未知，优先配置的是官方广告或者也没配置优先顺序，均优先展示官方广告
//       const { id: officeAdId } = adTypeRollingOfficeMap[position] || {};
//       if (!ad.id || lastAdType == '2' || (!lastAdType && (ad.tag === 'ad1' || !ad.tag))) {
//         if (officeAdId && officeAdId[0]) {
//           console.log('===>加载官方广告===>', officeAdId);
//           setStorageSync(key, '1');
//           createAd(officeAdId[0], {
//             onClose: () => {
//               if (officeAdId[1]) {
//                 console.log('二次===>加载官方广告====>');
//                 createAd(officeAdId[1]);
//               }
//             },
//             onError: (err) => {
//               console.log('加载官方广告失败====>', err);
//               console.log('===>加载自建广告===>');
//               setStorageSync(key, '2');
//               resolve(list);
//             },
//           })
//             .then(() => {
//               console.log('===>加载官方广告===>then');
//               resolve([]);
//             })
//             .catch((err) => {
//               console.log('catch加载官方广告失败====>', err);
//             });
//           return;
//         }
//       }
//       console.log('===>加载自建广告===>');
//       setStorageSync(key, '2');
//     }
//     resolve(list);
//   });
// };

const retentionLimitAds = ['5', '19', '47'];
const retentionLimitKey = 'retention_limit_ads';
const checkAdRetentionLimit = (position) => {
  const limit = Taro.kbGetGlobalData(retentionLimitKey);
  return limit && retentionLimitAds.includes(`${position}`);
};
const adRetentionLimitResolve = (list, data) => {
  return new Promise((resolve) => {
    if (list.length && retentionLimitAds.includes(`${data.position}`)) {
      Taro.kbSetGlobalData(retentionLimitKey, true);
    }
    resolve(list);
  });
};

const apisMap = {
  a1: '/g_order_core/v2/mina/User/getTablePlaqueBanner',
  a2: '/g_order_core/v2/mina/User/getBannerList',
};

/**
 *
 * @description 注意：统一使用 loadAdminAd 来拉取广告
 * @param {*} params
 * @param {{type:'1'|'2';limit:{key:string;count:number;}}} opts 兼容老逻辑，支持true，微快递不传opts默认为apisMap.a2
 * @param {*} formatResponse
 * @returns
 */
export const getAdConfig = async (params, opts, formatResponse) => {
  let { url, data } = getAdConfigApiAndData(params);
  // 1、微快递中调用getAdConfig ，如果不传opts走type：2；（老逻辑兼容）
  // 2、需要指定type时，应按照新的方式，opts={type:""};
  // 3、注意通过loadAdminAd，调用getAdConfig，opts必须有值；
  const { type = process.env.MODE_ENV === 'wkd' && !opts ? '2' : '', limit } = opts || {};
  const key = `a${type}`;
  const otherUrl = apisMap[key];
  if (otherUrl) {
    url = otherUrl;
    data = {
      ...data,
      ...params,
    };
    const adPositionMap = {
      home: '1',
      place: '2', // 下单结果页广告，会从4号流量主次广告拉取
      record: '3',
      logistics: '18', // 物流详情页流量主次广告
      express_page_icon: '4',
    };
    if (key === 'a1' && !data.position) {
      // a1链接补充position
      data.position = adPositionMap[data.type];
    }
  }
  const { position } = data;
  if (position) {
    // position转化字符串
    data.position = `${position}`;
  }
  data = await patchReqData(data);
  triggerAdConfigEventPush(data); // 开始请求
  return await new Promise((resolve) => {
    const retentionLimit = checkAdRetentionLimit(position);
    if (retentionLimit) {
      resolve(key === 'a1' ? { list: [] } : []);
      return;
    }
    checkIsShieldAd('2', null, limit, data).then((shield) => {
      if (shield) {
        // 拦截自建广告
        resolve(key === 'a1' ? { list: [] } : []);
        return;
      }
      checkTodayWhichTimeShow(data).then((filter) => {
        request({
          url,
          data,
          toastLoading: false,
          formatResponse,
          onThen: async (res) => {
            const { data: resData } = res || {};
            let list = [];
            let resolveData = list;
            if (key === 'a1') {
              // 兼容float组件与insertScreen组件，不同响应；
              const { list: dataList = resData } = resData || {};
              list = filter(
                isArray(dataList) ? dataList : isObject(dataList) && dataList.id ? [dataList] : [],
              ).map((item) => ({
                position,
                ...item,
              }));
              data.position = data.type;
              resolveData = {
                list,
                type: resData.type,
              };
            } else {
              list = filter(
                isArray(resData)
                  ? resData.map((item) => {
                      const [, tag] = `${item.title}`.split('#');
                      return {
                        position,
                        ...item,
                        tag,
                      };
                    })
                  : [],
              );
              list = await filterAdsBase(list, data);
              list = await filterAdsOutReward(list, data);
              list = await adRetentionLimitResolve(list, data)
                .then((_list) => adRollingResolve(_list, data))
                .then(adDelayResolve);
              resolveData = list;
            }
            if (list.length > 0) {
              // 已拉到广告，缓存limit
              setShieldAdLimit(limit);
            }
            triggerAdConfigEventPush(data, list); // 成功
            resolve(resolveData);
          },
        });
      });
    });
  });
};

export const getAdExtensionReq = (key) => {
  const adExtensionReqMap = {
    'query.appointment': 3,
    'order.station': 5,
    'order.result': 4,
    'order.detail': 6,
    query: 1,
    'query.detail': 2,
    'order.edit': 21,
    'welfare.reward': 56,
  };
  return { position: adExtensionReqMap[key] };
};

export const getAdTypeReq = (key) => {
  const adTypeMap = {
    'order.result': 'place',
    'query.detail': 'logistics',
  };
  return { type: adTypeMap[key] };
};

export const getAdStorageKey = (key) => {
  const storageKeyMap = {
    'order.result': 'intersitialAdTimer', // 自建广告
    'order.result.1': 'intersitialAdTimer1', // 流量主广告
    'query.detail': 'logisticsAdTimer',
  };
  return storageKeyMap[key];
};

const subscribeStorageIns = new Storage({ key: 'subscribe-ids' });
export const getAdSubscribeByRemark = (list) => {
  let delay = 0;
  let action;

  const getStorageList = () => {
    const { data: storageData } = subscribeStorageIns.get();
    const { list: storageList = [] } = storageData || {};
    return storageList;
  };

  // 记录已经订阅的ID
  const record = (e) => {
    const { acceptedIds } = e || {};
    if (isArray(acceptedIds)) {
      const storageList = getStorageList();
      const newList = [...new Set(storageList.concat(acceptedIds))];
      subscribeStorageIns.set({ list: newList });
    }
  };

  if (isArray(list)) {
    const storageList = getStorageList();
    for (let i = 0, len = list.length; i < len; i++) {
      const ad = list[i];
      const { remark = '' } = ad || {};

      const match = remark.match(/delay_(\d+)/);
      if (match && match[1]) {
        delay = parseInt(match[1]) * 1000;
        action = remark.split('#')[1].split(',');
      } else {
        action = remark.split(',');
      }
      action = action.filter((item) => !storageList.includes(item));
      if (action.length > 0) {
        break;
      }
    }
  }

  return {
    action,
    delay,
    record,
  };
};
