import apis from '@/utils/apis';
import { relationStorageKey } from '@/utils/config';
import { refreshControl, REFRESH_KEY_ORDER } from '@/utils/refresh-control';
import request from '@base/utils/request';
import { setStorage } from '@base/utils/utils';
import { useSelector } from '@tarojs/redux';
import Taro from '@tarojs/taro';
import { isFresh, transferWkdAddress } from '../../_utils';
import { cancelOrder } from '../../_utils/order.action';
import { openCreditService } from '../../_utils/order.credit-pay';
import { checkActivityWithReturn, getCurQuotationItem } from '../../_utils/order.edit';

export function useCreditFooter(props) {
  const {
    address,
    extraInfo,
    relation,
    freight,
    product_code,
    isOpenCredit,
    isGuideCredit,
    order_id,
    quotationList,
    alipayActivityCoupon,
    activityDetail,
  } = props;
  const { goods_name, goods_remark, goods_weight } = extraInfo || {};
  const { brand } = relation || {};

  const { brands = {} } = useSelector((state) => state.global);

  const onSubmit = async () => {
    if (!isOpenCredit && !isGuideCredit) {
      openCreditService();
      return;
    }
    const result = await submitOrder();
    setStorage({
      key: relationStorageKey,
      data: relation,
    });
    await cancelOrder({ data: props, modal: false, showGuide: false, successToast: false });
    orderDone(result);
  };

  const submitOrder = () => {
    console.info('props=====>submitOrder', props);

    return new Promise((resolve, reject) => {
      const params = {
        ...transferWkdAddress(address),
        is_fresh: isFresh(goods_name) ? '1' : '0',
        package_info: goods_name,
        package_weight: goods_weight,
        package_note: goods_remark,
        product_code: product_code || '',
        shipper_default: 0,
        shipping_default: 0,
        platform: 'yjkd',
        channel: `mina_${Taro.systemInfo.platform}`,
        brand,
        freight,
        // ...(conversionOrderExtraData || {}),
      };
      if (checkActivityWithReturn()) {
        const curQuotationItem = getCurQuotationItem(brand, quotationList);
        if (process.env.PLATFORM_ENV === 'alipay') {
          const { voucher_id, activity_id } = alipayActivityCoupon || {};
          if (voucher_id) {
            params.coupon_id = voucher_id;
            params.activity = 'alipay_marketing_voucher';
            params.alipay_activity_id = activity_id;
          } else if (
            curQuotationItem &&
            curQuotationItem.coupon_id &&
            !curQuotationItem.exists_alipay_coupon
          ) {
            params.coupon_id = curQuotationItem.coupon_id;
            if (curQuotationItem.activity) {
              params.activity = curQuotationItem.activity;
            }
          }
        } else {
          if (activityDetail && activityDetail.is_new == 1) {
            params.activity = 'new_customer';
          }
          if (curQuotationItem && curQuotationItem.coupon_id) {
            params.coupon_id = curQuotationItem.coupon_id;
            if (curQuotationItem.activity) {
              params.activity = curQuotationItem.activity;
            }
          }
        }
      }
      request({
        url: apis[`order.edit.yjkd`],
        data: params,
        toastError: true,
        onThen: ({ code, data }) => {
          if (code == 0) {
            resolve(data);
          } else {
            reject();
          }
        },
      });
    });
  };

  const orderDone = (result) => {
    if (isOpenCredit) {
      const brandObj = brands[brand] || {};
      Taro.kbToast({
        text: `原订单已为您取消，3秒后系统自动跳转至新订单详情页<${brandObj.name || ''}>`,
        duration: 3000,
        onClose: () => {
          Taro.navigator({
            url: `order/detail`,
            options: {
              type: 'brand',
              order_id: result.order_id,
              cancelOrder: '1',
            },
          });
        },
      });
      return;
    }

    Taro.navigator({
      url: 'order/result',
      target: 'self',
      options: {
        source: 'brand',
        status: 'credit',
        brand: brand,
        platform: 'yjkd_brand',
        cancelOrder: '1',
        ...result,
      },
    });
  };

  const onCancel = async () => {
    await cancelOrder({ data: props, modal: false, showGuide: false });
    refreshControl(REFRESH_KEY_ORDER);
    onCancelGuide();
  };

  // 取消订单后引导
  const onCancelGuide = () => {
    Taro.navigator({
      url: 'order/cancel',
      key: 'globalData_orderDetail',
      target: 'self',
      extraData: { order_id },
    });
  };

  return {
    onCancel,
    onSubmit,
  };
}
