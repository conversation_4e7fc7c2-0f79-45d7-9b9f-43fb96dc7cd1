import { useEffect, useMemo, useRef } from '@tarojs/taro';
import isEqual from 'lodash/isEqual';
import { useEstimatedListIndex } from '../../estimated-fee/list/useEstimatedListIndex';
import { getCurQuotationItem } from '../../_utils/order.edit';

export function useCreditList(props) {
  const {
    product_code,
    address,
    isOpenCredit,
    relation,
    extraInfo,
    updateState,
    isGuideCredit,
    cacheRelationInfo,
    alipayActivityCoupon,
    activityCouponInfo,
    handleAlipayCoupon,
    activityDetail,
    conversionOrderExtraData,
    order_id,
  } = props;
  const { goods_weight, volume } = extraInfo || {};
  const { brand } = relation;
  const quotationListRef = useRef();

  const updateRelation = (params) => {
    updateState({
      relation: {
        ...relation,
        ...params,
      },
    });
  };

  const onChange = (key) => {
    const { changeSource, brand, product_code: productCode, quotationList: list } = key;
    switch (changeSource) {
      case 'chooseBrand':
        updateRelation({ brand });
        handleAlipayCoupon(brand, {
          transform_coupon_id:
            conversionOrderExtraData && conversionOrderExtraData.transform_coupon_id,
        });
        break;
      case 'product_code':
        updateState({ product_code: productCode });
        break;
      case 'list':
        updateState({ quotationList: list });
        break;
      default:
        break;
    }
  };

  const fn = useEstimatedListIndex({
    address,
    product_code,
    isOpenCredit,
    relationInfo: relation,
    weight: goods_weight,
    volume,
    moveAreaData: {},
    onChange: onChange,
    conversionOrderExtraData,
    order_id,
  });

  const { quotationList } = fn;

  const isShowInline = isOpenCredit || !isGuideCredit;

  const formatList = useMemo(() => {
    const underlineBrand = ['sto', 'dp', 'jd'];
    const creditKey = isShowInline ? ['2', '3'] : ['1', '3'];

    // 如果当前品牌不是线下列表，切换品牌
    if (!isShowInline && !underlineBrand.includes(brand)) {
      updateRelation({
        brand: underlineBrand[0],
      });
    }
    return quotationList.filter((item) => {
      if (isShowInline) {
        return creditKey.includes(item.pay);
      } else {
        return creditKey.includes(item.pay) && underlineBrand.includes(item.brand);
      }
    });
  }, [isShowInline, brand, quotationList]);

  useEffect(() => {
    let discount_price = '';
    if (formatList.length && brand) {
      formatList.forEach((item) => {
        if (item.brand == brand) {
          discount_price = item.discount_price;
        }
      });
    }
    updateState({
      freight: discount_price,
    });
  }, [formatList, brand]);

  const checkIsReturn = () => {
    const { isReturnModule } = cacheRelationInfo || {};
    return isReturnModule == 1;
  };

  useEffect(() => {
    const list = quotationListRef.current;
    if (quotationList && (!list || !isEqual(list, quotationList))) {
      quotationListRef.current = quotationList;
      if (brand) {
        handleAlipayCoupon(brand, {
          transform_coupon_id:
            conversionOrderExtraData && conversionOrderExtraData.transform_coupon_id,
        });
      }
    }
  }, [quotationList, brand, conversionOrderExtraData]);

  const curQuotationItem = getCurQuotationItem(brand, quotationList);

  return {
    ...fn,
    isShowInline,
    formatList,
    updateRelation,
    checkIsReturn,
    curQuotationItem,
    alipayActivityCoupon,
    activityCouponInfo,
    conversionOrderExtraData,
    activityDetail,
  };
}
