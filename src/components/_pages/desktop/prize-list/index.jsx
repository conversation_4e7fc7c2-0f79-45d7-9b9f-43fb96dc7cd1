/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import KbLongList from '@base/components/long-list';
import Taro, { Component } from '@tarojs/taro';
import { Text, View } from '@tarojs/components';
import { AtButton } from 'taro-ui';
import { connect } from '@tarojs/redux';
import isArray from 'lodash/isArray';
import './index.scss';

@connect(({ global }) => ({
  loginData: global.loginData,
}))
class KbDesktopPrizeList extends Component {
  constructor(props) {
    super(props);
    this.state = {
      list: [],
    };
    this.listData = {
      api: {
        url: '/g_order_core/v2/activity/Lottery/myPrize',
        data: {
          activity_code: 'DP01',
          mode: props.mode,
          pageSize: 20,
        },
        formatResponse: (res, req) => {
          const { data: list } = res;
          if (req.mode == 'all' && isArray(list) && list.length) {
            return {
              code: 0,
              data: {
                list,
              },
            };
          }
          return {
            data: void 0,
          };
        },
        onThen: (list) => {
          // console.log('list', list);
          this.setState({
            list,
          });
        },
      },
    };
  }

  //Ins
  onReady() {}

  handleClickBar = (item) => {
    const { prize_code } = item || {};
    if (prize_code == 'P1') {
      Taro.navigator({
        url: 'user/wallet',
      });
    } else if (prize_code == 'P2') {
      Taro.navigator({
        url: 'user/member',
      });
    }
  };

  render() {
    const { active, mode } = this.props;
    const { list = [] } = this.state;
    return (
      <KbLongList
        data={this.listData}
        onReady={this.onReady.bind(this)}
        active={active}
        enableRefresh
        enableMore
        noDataText={
          mode == 'normal'
            ? '暂无未使用奖励'
            : mode == 'invalid'
            ? '暂无已过期奖励'
            : '暂无任何奖励'
        }
      >
        <View className='kb-card__group'>
          {isArray(list) && list.length > 0
            ? list.map((item) => {
                const { prize_code, prize_price, prize_name } = item || {};
                return (
                  <View className='kb-prizeList-item' key={item}>
                    <View className='kb-prizeList-item--content'>
                      <View className='kb-prizeList-item--amount'>
                        <Text className='kb-size__sm'>￥</Text>
                        <Text className='kb-size__bold'>{prize_price}</Text>
                      </View>
                      <View className='kb-prizeList-item--main'>
                        <View>
                          <View className='title'>{prize_name}</View>
                          <View className='desc'>桌面特权频道-专享福利</View>
                        </View>
                        <AtButton
                          className='kb-prizeList-item--btn'
                          type='secondary'
                          size='small'
                          circle
                          onClick={() => this.handleClickBar(item)}
                        >
                          立即查看
                        </AtButton>
                      </View>
                    </View>
                    <View className='kb-prizeList-item--desc'>
                      中奖奖品已放入小程序{prize_code == 'P1' ? '“我的钱包”' : '“会员中心”'}
                      ，不与其他优惠同享；
                    </View>
                  </View>
                );
              })
            : null}
        </View>
      </KbLongList>
    );
  }
}

KbDesktopPrizeList.options = {
  addGlobalClass: true,
};

export default KbDesktopPrizeList;
