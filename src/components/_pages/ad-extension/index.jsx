/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { getAdConfig } from '@/components/_pages/ad-extension/_utils';
import { useUpdate } from '@base/hooks/page';
import { getStorage, setStorage } from '@base/utils/utils';
import { Image, Swiper, SwiperItem, View } from '@tarojs/components';
import { connect, useSelector } from '@tarojs/redux';
import { Fragment, useRef, useState, useEffect } from '@tarojs/taro';
import classNames from 'classnames';
import dayjs from 'dayjs';
import calendar from 'dayjs/plugin/calendar';
import isArray from 'lodash/isArray';
import { AtCurtain } from 'taro-ui';
import KbExternalAd from '@/components/_pages/ad-extension/ad';
import './index.scss';
import { adNavigator, loadAdminAd } from './sdk';

dayjs.extend(calendar);
const getAdminPopAdsStorageKey = (position) => `adminPopAds_${position}`;

const KbAdExtension = (props) => {
  const actionRef = useRef({});
  const {
    data,
    className,
    reportKey,
    showRemark,
    adUnitIdIndex = '',
    loadAdmin,
    rootClassName,
  } = props;
  const { isVip = false } = useSelector((state) => state.global);
  const [ads, updateAds] = useState(null);
  const [popAds, upPopAds] = useState(null);
  const [popShow, upPopShow] = useState(false);
  const popAdsRef = useRef(null);
  const isOpened = !!(popShow && popAds && !isVip);
  const adConfig =
    process.env.MODE_ENV === 'wkd'
      ? data
      : {
          position: process.env.PLATFORM_ENV == 'weapp' ? 1 : 7,
          ...data,
        };
  // 获取广告
  const getAdList = () => {
    // actionRef.current.loaded = true;
    const getADHandler = loadAdmin ? loadAdminAd : getAdConfig;
    getADHandler(adConfig).then((list) => {
      const isPopAds = (title) => /^(T|t)-/.test(title);
      const isValidList = (list) => isArray(list) && list.length > 0;
      let bannerAds = [];
      let popAds = [];
      isValidList(list) &&
        list.forEach((item) => {
          if (!isPopAds(item.title)) {
            bannerAds.push(item);
          } else {
            popAds.push(item);
          }
        });
      updateAds(isValidList(bannerAds) ? bannerAds : null);
      if (isValidList(popAds)) {
        upPopAds(popAds[0]);
        showPopAds();
      }
    });
  };

  const updatePopInfo = (preInfo) => {
    // 超过2次或者已经弹出2次｜｜距离上次弹出大于30分
    const currentDate = dayjs().format('YYYY-MM-DD HH:mm');
    const { count = 0, date = 0 } = preInfo || {};
    const currentDay = currentDate.split(' ')[0];
    const preDay = date ? date.split(' ')[0] : 0;
    const isDayDifferent = Math.abs(dayjs(currentDay).diff(dayjs(preDay), 'days')) >= 1;
    const isMinuteDifferent = Math.abs(dayjs(currentDate).diff(dayjs(date), 'minute')) >= 30;

    if ((count < 2 && isMinuteDifferent) || isDayDifferent) {
      const nextData = {
        date: currentDate,
        count: isDayDifferent ? 1 : count + 1,
      };
      upPopShow(true);
      popAdsRef.current = nextData;
    }
  };

  const showPopAds = () => {
    const { position = 'query_banner' } = data || {};
    const preData = popAdsRef.current;
    if (!preData) {
      getStorage({
        key: getAdminPopAdsStorageKey(position),
        complete(res) {
          const { data: { date, count } = {} } = res.data || {};
          updatePopInfo({ date, count });
        },
      });
    } else {
      updatePopInfo(preData);
    }
  };
  const handlePopAdsClose = () => {
    upPopShow(false);
  };

  // 点击跳转
  const handleClick = ({ title, id, ...rest }) => {
    handlePopAdsClose();
    const { position } = adConfig || {};
    adNavigator({
      ...rest,
      report: {
        title: title || `banner${id || ''}`,
        key: reportKey,
        platform: `${process.env.PLATFORM_ENV}-${process.env.MODE_ENV}`,
        position,
      },
    });
  };

  useUpdate(
    (loginData) => {
      const { logined } = loginData || {};
      if (!logined || actionRef.current.loaded) return;
      if (process.env.PLATFORM_ENV !== 'swan') {
        getAdList();
      }
    },
    [data],
  );

  useEffect(() => {
    if (isOpened) {
      // 弹出后再设置缓存；
      // todo ...
      // 1、广告组件统一；
      const { position = 'query_banner' } = data || {};
      const popAds = popAdsRef.current;
      setStorage({
        key: getAdminPopAdsStorageKey(position),
        data: popAds,
      });
    }
  }, [isOpened]);

  const wrapCls = classNames('kb-ads__swiper', className);
  const rootCls = classNames('kb-ads', rootClassName);
  return (
    <Fragment>
      {ads ? (
        <View className={rootCls}>
          <Swiper
            className={wrapCls}
            autoplay
            circular
            indicatorDots={ads.length > 1}
            indicatorColor='rgba(255,255,255,0.5)'
            indicatorActiveColor='rgba(255,90,122,0.5)'
          >
            {ads.map((item) => {
              return (
                <SwiperItem key={item.id}>
                  <View
                    className='kb-ads__swiper--item'
                    hoverClass='kb-hover-opacity'
                    onClick={handleClick.bind(null, item)}
                  >
                    <Image className='kb-ads__swiper--image' src={item.imgUrl} />
                  </View>
                </SwiperItem>
              );
            })}
          </Swiper>
        </View>
      ) : adUnitIdIndex ? (
        <KbExternalAd adUnitIdIndex={adUnitIdIndex} />
      ) : null}
      {process.env.PLATFORM_ENV === 'alipay' && popAds ? (
        <AtCurtain onClose={handlePopAdsClose} isOpened={isOpened} closeBtnPosition='top-right'>
          <Image
            style='width:100%;height:auto'
            mode='widthFix'
            src={popAds.imgUrl}
            onClick={handleClick.bind(null, popAds)}
          />
          {showRemark && <View className='kb-color__white kb-size__base2'>{popAds.remark}</View>}
        </AtCurtain>
      ) : null}
    </Fragment>
  );
};

KbAdExtension.options = {
  addGlobalClass: true,
};

KbAdExtension.defaultProps = {
  reportKey: process.env.MODE_ENV === 'wkd' ? 'banner' : 'ad_event',
};

export default connect(({ global }) => ({
  loginData: global.loginData,
}))(KbAdExtension);
