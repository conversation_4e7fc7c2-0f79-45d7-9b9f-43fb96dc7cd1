/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import KbPage from '@base/components/page';
import Taro, { Component } from '@tarojs/taro';
import { View, Text } from '@tarojs/components';
import { AtIcon } from 'taro-ui';
import '../index.scss';

export default class Index extends Component {
  config = {
    navigationBarTitleText: '常见问题',
  };

  constructor() {
    this.state = {};
  }

  componentDidMount() {
    Taro.setNavigationBarTitle({
      title: this.$router.params.label || '常见问题',
    });
  }

  render() {
    const { ...rest } = this.state;
    const { name, value } = this.$router.params;
    return (
      <KbPage {...rest}>
        <View className='kb-spacing-md'>
          <View className='kb-box'>
            <View className='kb-box__title'>
              <AtIcon
                prefixClass='kb-icon'
                value='question2'
                className='kb-icon-size__base kb-color__brand'
              />
              <Text className='kb-icon__text--ml'>{name}</Text>
            </View>
            <View className='kb-box__content kb-spacing-md kb-accordion-item kb-size__base'>
              {value}
            </View>
          </View>
        </View>
      </KbPage>
    );
  }
}
