/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import KbPage from '@base/components/page';
import KbLoader from '@base/components/loader';
import { connect } from '@tarojs/redux';
import Taro, { Component } from '@tarojs/taro';

@connect(({ global }) => ({
  isVip: global.isVip,
  loginData: global.loginData,
}))
class Index extends Component {
  config = {
    navigationBarTitleText: '',
    usingComponents: {
      'ad-xlight': '../../components/_pages/ad-extension/adXlight',
    },
  };

  constructor() {
    this.state = {
      rtaExtMap: null,
    };
  }

  handleUpdate = (data) => {
    const { logined, userInfo } = data;
    if (logined) {
      const { params } = this.$router;
      const { openid: user_id } = userInfo;
      this.setState({
        rtaExtMap: {
          user_id,
          insurance_scene_code: 'ma_shang_xiaoha_exchange',
          ...params,
        },
      });
    }
  };

  // 去首页
  handleToHome = () => {
    Taro.navigator({
      url: 'query',
      target: 'tab',
    });
  };
  handleAdClose = () => this.handleToHome();
  handleAdError = () => this.handleToHome();

  render() {
    const { rtaExtMap, ...rest } = this.state;
    const { params } = this.$router;
    const { adtype, unitId } = params || {};

    return (
      <KbPage {...rest} onUpdate={this.handleUpdate}>
        {rtaExtMap ? (
          <ad-xlight
            onAdClose={this.handleAdClose}
            onAdError={this.handleAdError}
            unitId={unitId || '50_2024040725000089026'}
            type={adtype || '3'}
            rtaExtMap={rtaExtMap}
          />
        ) : (
          <KbLoader centered />
        )}
      </KbPage>
    );
  }
}

export default Index;
