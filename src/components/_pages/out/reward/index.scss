/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb-rewardPage {
  .container {
    min-height: 100vh;
    background-color: #fc2e20;
    background-image: url('https://cdn-img.kuaidihelp.com/wkd/miniApp/prize/bg.png?v=1');
    background-repeat: no-repeat;
    background-position: 0 0;
    background-size: 100% auto;
  }

  .sign-guide {
    &__wrap {
      position: relative;
      height: 330px + $spacing-v-md;
    }

    position: absolute;
    right: 0;
    bottom: $spacing-v-md;
    text-align: right;

    &__image {
      position: relative;
      z-index: 1;
      width: 120px;
      height: 120px;
    }

    &__text {
      position: relative;
      z-index: 2;
      margin-top: -40px;
      padding: $spacing-v-xs $spacing-h-sm;
      padding-left: $spacing-h-md;
      color: $color-red;
      font-size: $font-size-xs;
      background: linear-gradient(to right, #ffffff, #ffd9ad);
      border-radius: $border-radius-arc;
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
    }
  }

  .icon-search {
    width: 300px;
  }

  .box {
    position: relative;
    background: linear-gradient(to bottom, #fef6f0, #ffffff);
    background-color: $color-white;

    &,
    .kb-ad__wrapper,
    .kb-ads {
      overflow: hidden;
      border-radius: $border-radius-xl;
    }

    .kb-ads {
      background-color: transparent !important;

      &__swiper {
        height: 260px !important;
      }
    }

    &-line {
      height: 80px;
      padding-right: $spacing-h-lg;
      border-left: $width-base solid $color-grey-5;
    }

    &-icon {
      position: absolute;
      width: 100px;
      height: 100px;

      &__rt {
        top: 0;
        right: 0;
      }
    }

    &-title {
      display: flex;
      align-items: center;
      margin-bottom: $spacing-v-md;
      padding: $spacing-v-md $spacing-h-md;
      padding-bottom: 0;

      &__first {
        /* Chrome, Safari */
        color: transparent;
        font-weight: bold;
        font-size: $font-size-lg;
        background: -webkit-linear-gradient(45deg, #ff4716, #ff9211);
        /* Chrome, Safari */
        background: linear-gradient(45deg, #ff4716, #ff9211);
        /* 标准语法 */
        -webkit-background-clip: text;
        /* Chrome, Safari */
        background-clip: text;
        -webkit-text-fill-color: transparent;
        /* 其他浏览器 */
      }

      &__second {
        position: relative;
        margin-left: $spacing-h-md;
        padding-left: $spacing-h-md;
        color: $color-grey-2;
        font-size: $font-size-sm;

        &::before {
          position: absolute;
          top: 50%;
          left: 0;
          height: $font-size-sm;
          border-left: $width-base solid $color-grey-5;
          transform: translateY(-50%);
          content: '';
        }
      }
    }

    &-spacing {
      padding: $spacing-v-md $spacing-h-md;
    }

    &-spacing .box-title {
      padding: 0;
    }

    &-group {
      padding: $spacing-v-md $spacing-h-md;
      padding-top: 0;
    }

    &-group .box {
      margin-bottom: $spacing-v-md;

      &:last-child {
        margin-bottom: 0;
      }
    }

    &-empty {
      height: 260px;
      color: $color-grey-2;
      font-size: $font-size-base;
      line-height: 260px;
      text-align: center;
    }

    &-integral-cache {
      .integral-cache {
        &-row {
          display: flex;
          align-items: center;
        }

        &-icon {
          width: 100px;
          height: 100px;
          margin-right: $spacing-h-md;
        }

        &-title {
          margin-bottom: $spacing-v-md;
          font-size: $font-size-lg;
        }

        &-value {
          color: $color-red;
          font-weight: bold;
          font-size: $font-size-xl;

          &__unit {
            font-size: $font-size-base;
          }
        }

        &-btn {
          margin-left: $spacing-h-sm;
          padding: $spacing-v-xs $spacing-h-md;
          color: $color-white;
          font-size: $font-size-base;
          background: linear-gradient(to right, #ff6d25, #ffae2e);
          border-radius: $border-radius-arc;
        }
      }
    }

    &-sign {
      .box-content {
        display: flex;
        padding-right: $spacing-h-md;
      }

      .sign {
        &-item {
          padding: $spacing-v-md 0 $spacing-v-md $spacing-h-md;
          color: $color-grey-2;
          font-size: $font-size-xs;
          text-align: center;

          &__box {
            margin-bottom: $spacing-v-md;
            padding: $spacing-v-md $spacing-h-xs;
            background-color: #f7f7f7;
            border: $width-base solid #f2f2f2;
            border-radius: $border-radius-lg;

            &--value {
              margin-bottom: $spacing-v-md;
            }

            &--image {
              width: 50px;
              height: 50px;
            }
          }

          &__active .sign-item__box {
            color: $color-white;
            background-color: #fa6c23;
            border-color: #ff5700;
          }

          &__today.sign-item {
            color: #ff770f;
          }

          &__today .sign-item__box {
            background-color: #fdf4e0;
            border-color: #ffedc5;
          }
        }
      }
    }

    &-task {
      padding-bottom: 0;

      .box-content {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-around;
      }

      &__item {
        &--done {
          opacity: 0.6;
        }

        position: relative;
        margin-bottom: $spacing-v-md;

        &--label,
        &--money {
          position: absolute;
          left: 0;
          box-sizing: border-box;
          width: 100%;
          padding: 0 $spacing-h-md;
          color: $color-white;
          text-align: center;
        }

        &--label {
          top: 40px;
          font-weight: bold;
        }

        &--image {
          width: 306px;
          height: 320px;
        }

        &--money {
          bottom: 40px;
          font-size: $font-size-base;
        }
      }
    }

    &-redemption {
      padding-bottom: 0;

      .box-content {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-around;
      }

      &__item {
        position: relative;
        margin-bottom: $spacing-v-md;

        &--tips,
        &--label {
          position: absolute;
          left: 0;
          box-sizing: border-box;
          width: 100%;
          padding: 0 $spacing-h-md;
          color: $color-white;
          text-align: center;
        }

        &--desc {
          display: flex;
          align-items: center;
          justify-content: space-between;

          padding: $spacing-v-xs 0;
          padding-bottom: $spacing-v-sm;
          font-size: $font-size-base;

          &-r {
            color: #ff4c4c;
          }
        }

        &--btn {
          width: 100%;
          padding: $spacing-v-sm 0;
          color: $color-white;
          text-align: center;
          background: linear-gradient(to right, #ff6d25, #ffae2e);
          border-radius: $border-radius-arc;
        }

        &--label {
          top: 35px;
          color: #ff552b;
        }

        &--money {
          font-weight: bold;
          font-size: $font-size-xxl * 1.5;
        }

        &--tips {
          top: 195px;
          color: $color-white;
        }

        &--image {
          width: 292px;
          height: 254px;
        }
      }
    }
  }

  .kb-reward {
    &-subscribe {
      position: absolute;
      right: $spacing-h-md;
      bottom: $spacing-v-lg * 2;
      left: $spacing-h-md;
    }

    &-feeds {
      margin-top: -1 * $spacing-v-md;
    }
  }
}
