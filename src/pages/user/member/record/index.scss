/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb-vip-record {
  &__head {
    height: 340px;
    &--bg {
      height: 180px;
      background: $color-brand;
      border-radius: 0 0 80px 80px;
    }
    .kb-recordCard {
      position: relative;
      box-sizing: border-box;
      width: 710px;
      height: 312px;
      margin: -150px auto;
      padding: 30px;
      color: #d7bf9e;
      background: linear-gradient(90deg, #3e4055 0%, #5c616d 100%);
      border-radius: 20px;
      &-cardName {
        color: #fafafa;
        font-weight: 800;
        font-size: 45px;
      }
      &-body {
        display: flex;
        margin-top: 10px;
        font-size: 24px;
      }
      &-footer {
        position: absolute;
        right: 0;
        bottom: 0;
        left: 0;
        display: flex;
        align-items: center;
        height: 88px;
        padding: 0 30px;
        background: #63646d;
        border-radius: 0 0 20px 20px;
      }
      &-cardSimpleName {
        color: #faddb1;
        font-weight: 800;
        font-size: 42px;
        font-style: italic;
      }
      &-cardType {
        height: 32px;
        margin-right: 10px;
        margin-left: 20px;
        padding: 0 10px;
        color: #404256;
        font-weight: 800;
        font-size: 22px;
        line-height: 32px;
        background: #d7bf9e;
        border-radius: 6px;
      }
      &-cardTag {
        position: absolute;
        top: 0;
        right: 0;
        display: flex;
        align-items: center;
        height: 52px;
        padding: 0 $spacing-h-md 0 40px;
        color: $color-black-1;
        font-size: $font-size-base;
        line-height: 52px;
        background: linear-gradient(to right, #cca066, #fee1b5);
        border-radius: 0 $border-radius-lg 0 40px;
        &_arrow {
          position: relative;
          display: flex;
          align-items: center;
          justify-content: center;
          width: 25px;
          height: 25px;
          margin-right: 5px;
          background: #3d444d;
          border-radius: 50%;
          &::after {
            width: 6px;
            height: 10px;
            font-size: 24px;
            border: $width-base solid #d7bf9e;
            border-top-color: transparent;
            border-left-color: transparent;
            transform: rotate(45deg) translate(-2px, -3px);
            content: '';
          }
        }
      }
      &-cardTag-expire {
        background: linear-gradient(90deg, #979797, #d3d3d3);
        .kb-icon {
          line-height: unset;
        }
      }
    }
  }
  &__list {
    &--item {
      margin: $spacing-h-md;
      padding: $spacing-h-md;
      color: #999999;
      font-size: 24px;
      background: #fff;
      border-radius: 10px;
      .kb-flex {
        display: flex;
        align-items: center;
        justify-content: space-between;
      }
      .kb-icon {
        vertical-align: baseline;
      }
      .first-tag {
        width: 208px;
        height: 36px;
        color: $color-brand;
        font-size: 22px;
        line-height: 36px;
        text-align: center;
        border: $width-base solid $color-brand;
        border-radius: 6px;
      }
    }
    &--desc {
      color: #999999;
      font-size: 24px;
      text-align: center;
    }
    &--xc_img {
      width: 710px;
      height: 298px;
      margin: 20px auto;
    }
  }
}
