/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { Fragment, useState } from '@tarojs/taro';
import { View, Image } from '@tarojs/components';
import { adNavigator, loadAdminAd } from '@/components/_pages/ad-extension/sdk';
import { AtCurtain } from 'taro-ui';
import { useUpdate } from '@base/hooks/page';
import './index.scss';

const KbBillCurtain = () => {
  const [isOpened, updateIsOpened] = useState(false);
  const [adConfig, setAdConfig] = useState(null);

  const handleClick = () => {
    adNavigator(adConfig);
    handleClose();
  };
  const handleClose = () => {
    updateIsOpened(false);
  };

  // 补充广告图片宽度
  const pathAdImageSize = (ad) => {
    return new Promise((resolve) => {
      const { imgUrl } = ad;
      if (imgUrl) {
        Taro.getImageInfo({
          src: imgUrl,
          success: (res) => {
            resolve({
              ...res,
              ...ad,
            });
          },
          fail: () => {
            resolve(ad);
          },
        });
      } else {
        resolve(ad);
      }
    });
  };

  // 登录状态变更
  useUpdate((loginData) => {
    if (!loginData.logined) return;
    loadAdminAd({ position: '17' }).then((data) => {
      const [ad] = data;
      if (ad && ad.id) {
        pathAdImageSize(ad).then(setAdConfig);
        updateIsOpened(true);
      }
    });
  }, []);

  return (
    <Fragment>
      <AtCurtain
        isOpened={isOpened}
        onClose={handleClose}
        closeBtnPosition='bottom'
        className='kb-curtain-full'
      >
        <View className='kb-curtain' onClick={handleClick}>
          {adConfig && (
            <Image mode='widthFix' className='kb-curtain__image' src={adConfig.imgUrl} />
          )}
        </View>
      </AtCurtain>
    </Fragment>
  );
};

KbBillCurtain.config = {
  usingComponents: {
    'ad-xlight': '../../ad-extension/adXlight',
  },
};

export default KbBillCurtain;
