/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { addressKeys, chooseAddress } from '@/components/_pages/address/_utils';
import KbArea from '@/components/_pages/order/address-edit/area';
import { getFormItem } from '@/components/_pages/order/_utils';
import KbCheckBox from '@base/components/checkbox';
import KbGps from '@base/components/gps';
import KbLoginAuth from '@base/components/login/auth';
import KbTextarea from '@base/components/textarea';
import { extendMemo } from '@base/components/_utils';
import Form from '@base/utils/form';
import { debounce, extractData } from '@base/utils/utils';
import { View } from '@tarojs/components';
import { connect } from '@tarojs/redux';
import { Component } from '@tarojs/taro';
import classNames from 'classnames';
import isEmpty from 'lodash/isEmpty';
import { AtIcon, AtInput } from 'taro-ui';
import './index.scss';

@connect(({ global }) => ({
  loginData: global.loginData,
}))
class Index extends Component {
  constructor() {
    this.state = {
      form: {
        data: {},
        errKeys: {},
        disabled: true,
      },
      errKeys: {},
      focusMap: {
        name: false,
        mobile: false,
        address: false,
      },
      is_default: 1,
    };
    this.orgMap = {
      send: '寄件',
      receive: '收件',
    };
    this.handleMiniAppAuthPhone = debounce(this.handleMiniAppAuthPhone, 100, {
      leading: false,
      trailing: true,
    });
  }

  componentDidMount() {
    this.createForm();
  }

  componentDidUpdate(preProps) {
    const { data } = preProps;
    let { data: currentData } = this.props;
    if (data !== currentData) {
      currentData = this.dealSendDefault(currentData);
      this.updateFormData(currentData);
    }
  }

  componentWillUnmount() {
    this.clearTimer && clearTimeout(this.clearTimer);
  }

  dealSendDefault = (data) => {
    // 发件地址默认勾选保存
    const { org } = this.props;
    let sendSaveDefault = !!(org == 'send' && data && data.save === '');
    if (sendSaveDefault) {
      data.save = 1;
    }
    return data;
  };

  // 创建表单
  // 表单输入变化
  onChange_form = () => {};
  onSubmit_form = () => {};
  createForm = () => {
    return new Promise((resolve) => {
      const { storageKey, data, isPickupAddress } = this.props;
      let merge = {};
      // 取件地址 姓名手机号非必填
      if (isPickupAddress) {
        merge = {
          name: {
            required: false,
          },
          mobile: {
            required: false,
          },
        };
      }
      const form = getFormItem({
        keys: [...addressKeys, 'province_confidence', 'city_confidence', 'district_confidence'],
        data: { ...data },
        merge,
      });
      this.formIns = new Form(
        {
          form,
          storageKey,
          onUpdate: (data) => {
            const { form = {} } = this.state;
            // 增量更新错误提示
            if (data.eventType == 'outter' || data.eventType == 'change') {
              this.updateData = {
                ...(this.updateData || {}),
                ...(data.data || {}),
              };
              let errKeys = form.errKeys || {};
              if (!isEmpty(errKeys) && !isEmpty(this.updateData)) {
                let keys = Object.keys(this.updateData) || [];
                for (let key in errKeys) {
                  if (!keys.includes(key)) {
                    delete errKeys[key];
                  }
                }
              }
              this.setState({
                errKeys,
              });
            } else if (data.eventType == 'clean') {
              this.updateData = {};
              this.setState({
                errKeys: {},
              });
            }
            const { is_default } = this.state;
            const { showSetDefaultBtn } = this.props;
            return this.props.onChange({
              ...data,
              ...(showSetDefaultBtn ? { is_default } : {}),
            });
          },
          onReady: resolve,
        },
        this,
      );
    });
  };

  // 更新表单
  updateFormData = (data) => {
    if (!this.formIns) return;
    this.formIns.update(data);
  };

  // 清空表单
  handleCleanForm = (key) => {
    this.clearTimer && clearTimeout(this.clearTimer);
    this.clearTimer = setTimeout(() => {
      if (key == 'address') {
        this.formIns.update({
          address: '',
        });
      } else {
        this.formIns.clean();
      }
    }, 100);
  };

  // 选择小程序平台地址
  handleSelector = () => {
    chooseAddress()
      .then((data) => {
        data.name && this.updateFormData(data);
      })
      .catch(() => {});
  };

  handleMiniAppAuthPhone = (mobile) => {
    mobile && this.updateFormData({ mobile });
  };

  // 选择省市区
  onChange_city = (data) => {
    this.updateFormData(data);
  };

  // 定位
  onGps = (data) => {
    this.updateFormData(extractData(data, ['province', 'city', 'district', 'address']));
  };

  // 处理保存地址
  handleSaveAddress(ev) {
    this.updateFormData({
      save: ev ? 1 : 0,
    });
  }

  // 大客户默认地址
  handleSetDefault(ev) {
    const { onChange } = this.props;
    this.setState(
      {
        is_default: ev ? 1 : 0,
      },
      () => {
        const { is_default } = this.state;
        onChange({ is_default });
      },
    );
  }

  // 处理聚焦
  handleFocus(action, key) {
    const { focusMap } = this.state;
    focusMap[key] = action == 'focus' ? true : false;
    this.forceUpdate({
      focusMap,
    });
  }

  handleChange(key, value, ev) {
    // if (ev && ev.type === 'input') return;
    this.onChange_form(key, value, ev);
  }

  render() {
    const { locked, className, org, showSaveBtn, showSetDefaultBtn, loginData } = this.props;
    const { userInfo = {} } = loginData || {};
    const {
      form: { data },
      errKeys = {},
      is_default,
      focusMap,
    } = this.state;
    const wrapCls = classNames('kb-from kb-border-radius-lg', className);
    const nameError = errKeys.name || {};
    const mobileError = errKeys.mobile || {};
    const areaError = errKeys.province || errKeys.city || errKeys.district || {};
    const addressError = errKeys.address || {};
    return (
      <View className={wrapCls}>
        <View className='kb-form__item'>
          <View className='at-col'>
            <View className='at-row'>
              <View className='item-content'>
                <AtInput
                  cursor={-1}
                  value={data.name}
                  disabled={locked.name}
                  placeholder='姓名'
                  focus={focusMap.name}
                  onBlur={this.handleFocus.bind(this, 'blur', 'name')}
                  onChange={this.onChange_form.bind(this, `name`)}
                >
                  {!locked.selector && (
                    <View
                      className='extra-icon'
                      onClick={this.handleSelector}
                      hoverClass='kb-hover-opacity'
                    >
                      <AtIcon
                        className='kb-color__brand'
                        prefixClass='kb-icon'
                        value={process.env.PLATFORM_ENV}
                      />
                    </View>
                  )}
                </AtInput>
              </View>
            </View>
            {nameError.errorMsg && (
              <View
                className='kb-color__red kb-size__base kb-margin-sm-b'
                onClick={this.handleFocus.bind(this, 'focus', 'name')}
              >
                *{nameError.errorMsg}
              </View>
            )}
          </View>
        </View>
        <View className='kb-form__item'>
          <View className='at-col'>
            <View className='at-row'>
              <View className='item-content'>
                <AtInput
                  enableNative={false}
                  cursor={-1}
                  value={data.mobile}
                  type='text'
                  disabled={locked.mobile}
                  placeholder='手机号或固话，如都填写中间用空格分开'
                  focus={focusMap.mobile}
                  onBlur={this.handleFocus.bind(this, 'blur', 'mobile')}
                  onChange={this.onChange_form.bind(this, `mobile`)}
                >
                  {process.env.MODE_ENV !== 'wkd' && process.env.MODE_ENV !== 'yz' ? (
                    <View className='kb-edit-manual__phone'>
                      {userInfo.mobile ? (
                        <AtIcon
                          className='kb-color__brand'
                          prefixClass='kb-icon'
                          value='phone2'
                          size='20'
                          onClick={this.handleMiniAppAuthPhone.bind(this, userInfo.mobile)}
                        />
                      ) : (
                        <KbLoginAuth
                          scope='phoneNumber'
                          useOpenType
                          showIcon='phone2'
                          text=''
                          linked
                          className='kb-button__mini'
                          size='normal'
                          onAuthComplete={this.handleMiniAppAuthPhone.bind(this)}
                        />
                      )}
                    </View>
                  ) : null}
                </AtInput>
              </View>
            </View>
            {mobileError.errorMsg && (
              <View
                className='kb-color__red kb-size__base kb-margin-sm-b'
                onClick={this.handleFocus.bind(this, 'focus', 'mobile')}
              >
                *{mobileError.errorMsg}
              </View>
            )}
          </View>
        </View>
        <View className='kb-form__item kb-clear__form--item'>
          <KbArea locked={locked.area} value={data} onChange={this.onChange_city} />
          {areaError.errorMsg && (
            <View className='kb-color__red kb-size__base kb-spacing-sm-b kb-margin-md-l'>
              *{areaError.errorMsg}
            </View>
          )}
        </View>
        <View className='kb-form__item'>
          <View className='at-col'>
            <View className='at-row'>
              <View className='item-content item-content__edit'>
                <View className='at-row at-row__align--center kb-textarea-wrap'>
                  <View className='at-col'>
                    <KbTextarea
                      placeholder='详细地址(如X栋X单元号)'
                      value={data.address}
                      disabled={locked.address}
                      maxLength={100}
                      count={false}
                      height={process.env.PLATFORM_ENV === 'weapp' ? 0 : 50}
                      focus={focusMap.address}
                      onBlur={this.handleFocus.bind(this, 'blur', 'address')}
                      // onChange={this.onChange_form.bind(this, `address`)}
                      onChange={this.handleChange.bind(this, `address`)}
                    />
                  </View>
                  <KbGps
                    auto={!data.address && org == 'send'}
                    onGps={this.onGps}
                    iconColor='brand'
                  />
                </View>
              </View>
            </View>
            {addressError.errorMsg && (
              <View
                className='kb-color__red kb-size__base kb-margin-sm-b'
                onClick={this.handleFocus.bind(this, 'focus', 'address')}
              >
                *{addressError.errorMsg}
              </View>
            )}
          </View>
        </View>

        <View className='kb-form__item kb-spacing-lg'>
          <View
            className={`at-row ${
              showSaveBtn || showSetDefaultBtn ? 'at-row__justify--between' : 'at-row__justify--end'
            }`}
          >
            {showSaveBtn || showSetDefaultBtn ? (
              <View className='kb-save-address'>
                {showSaveBtn && (
                  <KbCheckBox
                    className='kb-margin-md-r'
                    checked={data.save == 1}
                    label='保存到地址薄'
                    onChange={this.handleSaveAddress.bind(this)}
                  />
                )}
                {showSetDefaultBtn && (
                  <KbCheckBox
                    checked={is_default == 1}
                    label='保存到默认地址'
                    onChange={this.handleSetDefault.bind(this)}
                  />
                )}
              </View>
            ) : null}
            <View>
              {data.name || data.mobile || data.province || data.address ? (
                <View
                  className='at-row at-row__justify--center at-row__align--end kb-size__base kb-color__grey'
                  onClick={this.handleCleanForm}
                  hoverClass='kb-hover-opacity'
                >
                  <AtIcon
                    prefixClass='kb-icon'
                    value='delete'
                    className='kb-color__grey kb-icon-size__base kb-spacing-sm-r kb-margin-xxs-b'
                  />
                  清空
                </View>
              ) : null}
            </View>
          </View>
        </View>
      </View>
    );
  }
}

Index.options = {
  addGlobalClass: true,
};

Index.defaultProps = {
  locked: {}, //需要锁定的信息;例如{name:"张三"}
  data: {}, // 外部表单数据
  storageKey: '', // 表单缓存key
  org: '', // 地址类型；send寄件/receive收件
  action: '', // 处理地址行为 select/fix完善地址
  showSaveBtn: false, //展示保存选项
  className: '',
  onChange: () => {},
};

export default extendMemo(Index);
