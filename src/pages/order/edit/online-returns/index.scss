.online-shop-returns {
  min-height: 100vh;
  padding: 0 16px;
  padding-top: 150rpx;
  padding-bottom: 180rpx;
  background: url('//cdn-img.kuaidihelp.com/miniapp/wkd/banner.png') no-repeat;
  background-color: #f2f2f2;
  background-position: left top;
  background-size: 100% 400rpx;

  .at-textarea {
    border-bottom: 1rpx solid #f0f0f0;
  }
  .at-textarea__textarea {
    width: 560rpx;
    height: 100rpx;
  }
  .send-step {
    display: flex;
    align-items: center;
    justify-content: space-around;
    margin-top: 16px;
    padding: 24px;
    color: #999;
    font-weight: normal;
    font-size: 24px;
    background-color: #fff;
    border-radius: 8px;
    .curStep {
      color: #009fff;
    }
    .kb-color__arrow {
      margin-bottom: 6px;
      color: #8a8a8a;
      transform: scale(0.6);
    }
  }

  .return-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    &-right {
      color: #009fff;
      font-size: 24rpx;
    }
  }

  .return-platform {
    margin-top: 16px;
    padding: 24rpx;
    background-color: #fff;
    border-radius: 8px;
    &-chooice {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      margin-top: 32rpx;
      &-radio {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 210rpx;
        height: 60rpx;
        margin-bottom: 32rpx;
        font-weight: 500;
        font-size: 26rpx;
        background-color: #fff;
        border: $width-base solid #eeeeee;
        border-radius: 30rpx;
      }
      .current {
        color: #099fff;
        background: #f3faff;
        border: $width-base solid #099fff;
      }
    }
    &-tips {
      color: #999;
      font-weight: 500;
      font-size: 24rpx;
      line-height: 36rpx;
    }
  }

  .return-address {
    margin-top: 16px;
    padding: 24rpx;
    background-color: #fff;
    border-radius: 8px;
    &-content {
      display: flex;
      justify-content: space-between;
      width: 610rpx;
      height: 360rpx;
      margin-top: 24rpx;
      padding: 32rpx;
      background: #f7f7f7;
      border-radius: 10rpx;
      &-preview {
        position: relative;
        width: 200rpx;
        height: 356rpx;
        &-mask {
          position: absolute;
          bottom: 0;
          left: 0;
          width: 100%;
          height: 60rpx;
          padding-top: 16rpx;
          color: #fff;
          font-size: 18rpx;
          text-align: center;
          background: #333;
          border-radius: 0rpx 0rpx 6rpx 6rpx;
          opacity: 0.6;
        }
        &-img {
          width: 100%;
          height: 100%;
        }
      }
      &-upload {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 400rpx;
        font-size: 26rpx;
        border-radius: 16rpx;
        &-btn {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 268rpx;
          height: 56rpx;
          margin-top: 32rpx;
          font-size: 26rpx;
          border-radius: 28rpx;
          .screenshot-icon {
            margin-right: 8rpx;
          }
        }
      }
    }
  }

  .return-edit {
    margin-top: 32rpx;
    padding: 32rpx;
    background-color: #fff;
    border-radius: 16rpx;
    .region-picker {
      position: relative;
      display: flex;
      justify-content: space-between;
      width: 85%;
      height: 80rpx;
      padding: 0 32rpx;
      color: #ccc;
      line-height: 80rpx;
      border-bottom: 1rpx solid #f0f0f0;
      &-screenshot {
        width: 32rpx;
        font-size: 12px;
      }
      &-label {
        position: absolute;
        left: 24rpx;
      }
      &-arrow {
        display: flex;
        width: 80vw;

        &-content {
          flex: 1;
        }

        &-icon {
          width: 20rpx;
        }
      }
    }
    &-paste {
      position: relative;
      display: flex;
      align-items: center;
      width: 610rpx;
      margin-top: 32rpx;
      padding: 0 32rpx;
      background: #f5f5f5;
      border-radius: 10rpx;
      .manual-edit {
        position: absolute;
        right: 32rpx;
        bottom: 16rpx;
        z-index: 2;
        display: flex;
        justify-content: space-between;
        width: 240rpx;
        &-clear {
          width: 50rpx;
          height: 23rpx;
          color: #999;
          font-weight: 500;
          font-size: 24rpx;
          line-height: 39rpx;
        }
        &-identify {
          width: 142rpx;
          height: 42rpx;
          color: #fff;
          font-size: 24rpx;
          line-height: 42rpx;
          text-align: center;
          background: #009fff;
          border-radius: 21rpx;
        }
      }
      &-input {
        width: 460rpx;
      }
      &-textarea {
        width: 500rpx;
        height: 200rpx;
        padding-top: 32rpx;
        background: transparent;
        border: none;
      }
      &-icon {
        width: 160rpx;
        color: #009fff;
        font-size: 24rpx;
      }
    }
    .form-item {
      position: relative;
      .required {
        color: red;
        font-size: 24rpx;
      }
      .clear-icon {
        position: absolute;
        top: 34rpx;
        right: 12rpx;
      }
      .region-required {
        position: absolute;
        bottom: -32rpx;
        left: 32rpx;
        color: red;
        font-size: 24rpx;
      }
      .address-required {
        position: absolute;
        bottom: 8rpx;
        left: 32rpx;
        color: red;
        font-size: 24rpx;
      }
      .at-textarea {
        max-width: 600rpx;
      }
      .at-textarea__mask {
        view {
          max-width: 600rpx;
          // padding-left: 12px;
        }
      }
    }
  }

  .return-footer {
    position: fixed;
    bottom: 0;
    left: 0;
    z-index: 99;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 750rpx;
    height: 128rpx;
    background: #fff;
    &-btn {
      width: 710rpx;
      height: 88rpx;
      color: #fff;
      font-weight: bold;
      font-size: 34rpx;
      line-height: 88rpx;
      text-align: center;
      background: #009fff;
      border-radius: 44rpx;
    }
    .btnDisabled {
      background-color: #ccc;
    }
  }
}

$address-border-radius: $border-radius-lg;

.kb-address {
  .kb-form {
    position: relative;

    &__item {
      &--ai {
        background-color: $color-grey-8;
        border-radius: $address-border-radius;
      }

      &--button {
        padding-top: 1.5 * $spacing-v-md;
        padding-bottom: 0.5 * $spacing-v-md;
        text-align: center;
      }

      &--save {
        height: 90px;
        line-height: 90px;
      }
    }

    &::before {
      position: absolute;
      top: 0;
      right: 0;
      left: $spacing-h-md;
      z-index: 1;
      height: $width-base;
      border-top: $border-lightest;
      content: '';
    }
  }
  .item-content__edit {
    border-top: $border-lightest;
  }
  .address-info-active {
    position: relative;
    &-0 {
      animation: down 0.5s linear;
    }
    &-1 {
      animation: up 0.5s linear;
    }
    @keyframes down {
      from {
        top: 0px;
      }
      to {
        top: 120px;
      }
    }
    @keyframes up {
      from {
        top: 0px;
      }
      to {
        top: -120px;
      }
    }
  }
}
