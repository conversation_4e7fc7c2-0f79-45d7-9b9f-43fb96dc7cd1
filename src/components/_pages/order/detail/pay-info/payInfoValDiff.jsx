/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { Text, View } from '@tarojs/components';
import { Fragment } from '@tarojs/taro';
import classNames from 'classnames';
import numeral from 'numeral';
import { AtIcon } from 'taro-ui';
import './payInfoValDiff.scss';

const PayInfoValDiff = (props) => {
  const { data, data_first, showDiffVal = true, unit = '' } = props;

  const countFeeDiff = () => {
    let v1 = 0,
      v2 = 0,
      diff = 0;
    if (data_first) {
      v1 = data * 1 || 0;
      v2 = data_first * 1 || 0;
      diff = v1 * 1 - v2 * 1 || 0;
    }
    return {
      diff,
      v1,
      v2,
    };
  };

  const { diff, v2 } = countFeeDiff();

  const iconCls = classNames('kb-size__sm kb-color__red', {
    'kb-color__green': false,
  });
  return (
    <Fragment>
      {diff != 0 && (
        <View className='kb-payInfo'>
          <Text className='kb-payInfo__originVal'>
            {v2}
            {unit}
          </Text>
          {showDiffVal && (
            <View className='kb-payInfo__originValDiffBox'>
              <Text
                className={classNames('kb-payInfo__originValDiff kb-color__red', {
                  'kb-color__green': false,
                })}
              >
                {numeral(diff).format('0.00')}
                {unit}
              </Text>
              <AtIcon
                className={iconCls}
                prefixClass='kb-icon'
                value={diff < 0 ? 'down2' : 'up2'}
              />
            </View>
          )}
        </View>
      )}
    </Fragment>
  );
};

PayInfoValDiff.options = {
  addGlobalClass: true,
};

export default PayInfoValDiff;
