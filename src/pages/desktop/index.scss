/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb-nav-bar {
  font-weight: 500;
  font-size: 36px;
  background: transparent !important;
  &__wrapper {
    position: fixed !important;
  }
}

page {
  background: #4ea5fb;
}

.kb-desktop {
  position: relative;
  &-bg {
    position: relative;
    width: 750px;
    height: 1694px;
    &--img {
      width: 100%;
      height: 100%;
    }
  }
  &-container {
    position: absolute;
    top: 200px;
    right: 0;
    left: 0;
  }
  &-text {
    position: relative;
    width: 750px;
    height: 236px;
  }
  &-pan {
    position: relative;
    width: 750px;
    height: 916px;
    &-inner {
      position: absolute;
      top: 50px;
      left: 50%;
      width: 582px;
      height: 582px;
      margin-left: -290px;
      transition: all 5s ease-in-out;
      &--active {
        transform-origin: center;
        animation: animate-prize 10s infinite linear;
        @keyframes animate-prize {
          from {
            transform: rotate(0deg);
          }
          to {
            transform: rotate(360deg);
          }
        }
      }
    }
    &-z {
      position: absolute;
      top: 210px;
      left: 50%;
      width: 190px;
      height: 226px;
      transform: translateX(-50%);
    }
  }
  &-img {
    display: block;
    width: 680px;
    margin: 0 auto;
  }
  &-btn {
    padding-bottom: 40px;
    &--img {
      display: block;
      width: 680px;
      margin: 40px auto;
    }
  }
  &-door {
    position: absolute;
    top: 60px;
    right: 0px;
    color: #666666;
    &--box {
      width: 40px;
      margin-bottom: 30px;
      padding: 10px 0;
      font-size: 22px;
      text-align: center;
      background: #ffffff;
      border-radius: 10px 0px 0px 10px;
    }
  }
  &-notice {
    position: absolute;
    top: -55px;
    right: 0;
    left: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 60px;
    padding: 0 20px;
    color: #fff;
    font-size: 24px;
    background: rgba(0, 0, 0, 0.6);
    &--btn {
      display: flex;
      align-items: center;
      justify-content: center;
      box-sizing: border-box;
      width: 144px;
      height: 36px;
      color: $color-brand;
      font-weight: 800;
      font-size: 22px;
      background: #ffffff;
      border-radius: 18px;
      .kb-icon {
        margin-left: 5px;
        font-weight: 800;
        vertical-align: unset;
      }
    }
  }
  &-rule {
    position: absolute;
    top: 50px;
    right: 0;
    height: 54px;
    padding: 0 $spacing-h-md 0 $spacing-h-lg;
    color: #fff;
    font-size: $font-size-base;
    line-height: 54px;
    background: #f4400e;
    border-radius: 50px 0 0 50px;
    &--list {
      &-item {
        display: flex;
        margin-bottom: 8px;
        font-size: $font-size-base;
        text-align: justify;
      }
      &-index {
        width: 40px;
      }
    }
  }
  &-guide {
    &--img {
      width: 640px;
      height: 829px;
    }
    .at-curtain__body {
      position: absolute;
      top: 200px;
    }
    // .at-curtain__btn-close {
    //   display: none;
    // }
  }
  &-failGuide {
    &--head {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 108px;
      color: #333333;
      font-weight: bold;
      font-size: 36px;
      background: #ebf9ff;
      border-radius: 20px 20px 0px 0px;
    }
    &--content {
      padding: 20px 30px;
      .title {
        margin: 20px 0;
        color: #333333;
        font-weight: bold;
        font-size: 32px;
      }
      .desc {
        margin-bottom: 10px;
        color: #666666;
        font-weight: 500;
        font-size: 28px;
      }
      .desc2 {
        display: flex;
        align-items: center;
        padding-left: $spacing-h-md;
      }
      .img_setting {
        width: 88px;
        height: 52px;
        margin-right: $spacing-h-md;
      }
      .img_phone {
        display: block;
        width: 480px;
        height: 344px;
        margin: 20px auto 0;
      }
    }
    &--options {
      display: flex;
      justify-content: center;
      font-size: 30px !important;
      .btn {
        margin: 0 20px;
        .at-button {
          box-sizing: border-box;
          width: 240px;
          height: 66px;
          font-weight: 500;
          line-height: 66px;
          border-radius: 60px;
        }
      }
    }
  }
  &-prizeModal {
    &--content {
      position: relative;
      left: 50%;
      width: 670px;
      height: 731px;
      transform: translateX(-50%);
    }
    &--img {
      display: block;
      width: 100%;
      height: 100%;
    }
    &--prize {
      position: absolute;
      top: 186px;
      right: 85px;
      left: 85px;
      display: flex;
      height: 156px;
      color: #fff;
      .left {
        display: flex;
        align-items: baseline;
        justify-content: center;
        width: 198px;
        margin-top: 30px;
        .money {
          font-size: 72px;
        }
      }
      .right {
        display: flex;
        flex: 1;
        flex-direction: column;
        justify-content: center;
        padding-left: 20px;
        &-title {
          font-weight: bold;
          font-size: 36px;
        }
      }
    }
    &--btn {
      position: absolute;
      bottom: 185px;
      left: 50%;
      color: #921f00;
      font-weight: bold;
      font-size: 34px;
      transform: translateX(-50%);
    }
    &--desc {
      position: absolute;
      right: 0;
      bottom: 100px;
      left: 0;
      color: #fff;
      font-size: 24px;
      text-align: center;
    }
  }
}
