import { Text, View } from '@tarojs/components';
import Taro, { useMemo, useState, Fragment, useEffect } from '@tarojs/taro';
import { getSystemInfoSync, reportAnalyticsUnify } from '@base/utils/utils';
import { useCheckIsCollected } from '@base/hooks/collect';
import './index.scss';
import request from '~base/utils/request';

/**
 *
 * @param {{}} props
 * @returns
 */
const GuideCollect = () => {
  const [isOpened, setIsOpened] = useState(false);

  const styles = useMemo(() => {
    const { statusBarHeight = 24, titleBarHeight = 0 } = getSystemInfoSync();
    return {
      marginTop: titleBarHeight + statusBarHeight,
    };
  }, []);

  const { run } = useCheckIsCollected(({ isCollected, status }) => {
    if (status === 'init' && !isCollected) {
      // 统计：初始未收藏引导展示；
      reportAnalyticsUnify({
        action: 'collect-guide-show',
      });
    }
    if (isCollected && status === 'reGet') {
      // 从未收藏，变为收藏
      request({
        url: '/v1/WeApp/completeCollect',
        toastLoading: false,
      });
      // 统计：为收藏引导点击收藏
      reportAnalyticsUnify({
        action: 'collect-guide-click',
      });
    }
    setIsOpened(!isCollected);
  });

  useEffect(() => {
    run();
  }, []);

  return isOpened ? (
    <View className='kb-guide-favorites' style={styles}>
      <View>
        <Text>点击右上角五角星</Text>
        <Text className='kb-icon kb-icon-star-outlined'></Text>
        <Text>收藏</Text>
      </View>
      <View>
        <Text>即可领取寄件红包~</Text>
      </View>
    </View>
  ) : (
    <Fragment />
  );
};

export default GuideCollect;
