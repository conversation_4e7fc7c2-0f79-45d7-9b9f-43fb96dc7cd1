/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb-order-tabItems {
  display: flex;
  padding: $spacing-v-md $spacing-h-md;
  padding-bottom: 0;

  &__item {
    margin-right: $spacing-h-md;

    &:last-child {
      margin-right: 0;
    }

    &--btn {
      color: #666;
      background-color: $color-white !important;
      border: $width-base solid #e6e6e6;
      border-radius: 30px !important;

      &__active {
        color: $color-white;
        background-color: $color-brand !important;
      }
    }

    &--btn::after {
      border: none;
    }
  }
}
