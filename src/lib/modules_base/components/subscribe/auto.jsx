/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { useState } from '@tarojs/taro';
import { autoSubscribeKey } from '~/components/_pages/ad-extension/sdk';

import KbSubscribe from '~base/components/subscribe';
import { useDidShowCom } from '~base/hooks/page';
import { getPage, getStorageSync, now, removeStorageSync } from '~base/utils/utils';
import { getAllRedirectPath } from '../retention/_utils';

const AutoSubscribe = () => {
  const [action, setAction] = useState();

  const handleGetSubscribe = () => {
    const { ts, data = {} } = getStorageSync(autoSubscribeKey) || {};
    const { path, delay = 0, action } = data;
    const { $router: { path: curPath } = {} } = getPage();
    if (now() - ts < 2 * 60 * 1000 && curPath.indexOf(path) > -1) {
      // 兼容支付宝home页重新加载
      const secondPaths = getAllRedirectPath();
      const canShow =
        curPath.indexOf('target') > -1 || secondPaths.every((item) => curPath.indexOf(item) == -1);
      if (canShow) {
        // 挽留页
        removeStorageSync(autoSubscribeKey);
        setTimeout(() => {
          setAction(action);
        }, delay);
      }
    }
  };

  useDidShowCom(() => {
    handleGetSubscribe();
  });

  return action && <KbSubscribe action={action} auto className='kb-hide' />;
};

AutoSubscribe.options = {
  addGlobalClass: true,
};

export default AutoSubscribe;
