/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { preloadAd } from '@/components/_pages/ad-extension/sdk';
import { receiveStorageKey } from '@/components/_pages/address/_utils';
import KbAgreement from '@/components/_pages/agreement';
import KbAddressEdit from '@/components/_pages/order/address-edit';
import KbDHEstimatedFee from '@/components/_pages/order/estimated-fee/dh';
import KbInputNumber from '@base/components/input-number';
import KbModal from '@base/components/modal';
import { findProductTypeEn } from '@/components/_pages/order/extra-info/_utils';
import KbSubmitBar from '@/components/_pages/order/submit-bar';
import {
  fixDynamicFormsData,
  getForm,
  isFresh,
  transferWkdAddress,
} from '@/components/_pages/order/_utils';
import { confirmCredit } from '@/components/_pages/order/_utils/order.credit-pay';
import {
  cleanOrderEditFormInfo,
  getAuthCode,
  getCurQuotationItem,
  getQuotationDetail,
  getUnPayOrders,
  handleCheckCreditService,
  handleOpenCredit,
  handleSecondConfirmOrder,
  interceptCreditFailOrder,
  updateReceiveList,
  getOrderEditAgreement,
  setOrderEditAgreement,
} from '@/components/_pages/order/_utils/order.edit';
import { checkCustomerAuthStatus } from '@/components/_pages/store-card/_utils';
import { homeActivityReportAnalytics } from '@/components/_pages/user/activity/_utils';
import apis from '@/utils/apis';
import { scanAction } from '@/utils/scan';
import { getShareAppMessage } from '@/utils/share';
import { getShareTimeline } from '@/utils/shareTimeline';
import { sendNoticeWkd } from '@/utils/subscribe';
import KbCheckbox from '@base/components/checkbox';
import KbLoginAuthAndBind from '@base/components/login/authAndBind';
import KbPage from '@base/components/page';
import KbScrollView from '@base/components/scroll-view';
import GoodsInfo from '@/components/_pages/order/edit/goods-info';
import BrandInfo from '@/components/_pages/order/edit/dh/brand-info';
import AddValueService from '@/components/_pages/order/edit/dh/addValueService';
import { handleProPriceClick } from '@/components/_pages/order/edit/dh/_utils';
import Form from '@base/utils/form';
import logger from '@base/utils/logger';
import { getLaunchParams } from '@base/utils/navigator';
import {
  creatSchemeLink,
  debounce,
  getUserStateByComplete,
  noop,
  removeStorage,
  reportAnalytics,
  scanParse,
  setStorage,
} from '@base/utils/utils';
import { Text, View } from '@tarojs/components';
import { connect } from '@tarojs/redux';
import Taro, { Component, createRef, Fragment } from '@tarojs/taro';
import { AtIcon } from 'taro-ui';
import isArray from 'lodash/isArray';
import isObject from 'lodash/isObject';
import { get } from '@/actions/brands';
import { getDefaultGoods } from '@/components/_pages/order/_utils/order.goods';
import {
  formatServiceData,
  getDHRelationConfig,
} from '@/components/_pages/order/_utils/order.edit.dh';
import './index.scss';
import OrderNotice from '~/components/_pages/order/notice';

@connect(
  ({ global }) => ({
    loginData: global.loginData,
    serviceConfig: global.serviceConfig,
  }),
  {
    get,
  },
)
class Index extends Component {
  config = {
    navigationBarTitleText: '寄快递',
    backgroundColorTop: '#f2f2f2',
  };

  constructor() {
    this.defaultExtraInfo = {
      goods_weight: 30,
      package_num: 1,
    };
    this.state = {
      realnamed: false,
      relationInfo: {
        dakId: '',
        courier_id: '',
        relation_id: '',
      },
      cur: 0,
      relationData: { brand: '' },
      form: { data: {}, disabled: true },
      dynamicForms: fixDynamicFormsData(),
      addressData: null,
      total: 1,
      extraInfo: this.defaultExtraInfo,
      extraInfoData: null,
      estimatedFeeActive: false,
      estimatedData: {},
      isOpenCredit: false,
      agree: false,
      scanCoupon: '',
      interceptChannelOpen: false,
      relationInfoLoading: false,
      oAsyncBatchStatus: {},
    };
    this.handleSubmitForm = debounce(this.handleSubmitForm, 500, {
      leading: true,
      trailing: false,
    });
    // refs
    this.realnameRef = createRef();
    this.extraInfoRef = createRef();
    this.addressEditRef = createRef();
    this.agreeActionRef = createRef();
    this.estimatedFeeListRef = createRef();
    this.couponModalRef = createRef();
    this.batchSubmitRef = createRef();
  }

  componentDidMount() {
    this.props.get();
    this.createForm(() => {
      this.fixFormUpdateData(this.formFixData);
      this.dealDefaultValue();
    });
    const { source } = Taro.launchParams || {};
    if (source) {
      // 上报
      reportAnalytics({
        key: 'launch_source',
        source,
      });
    }
    this.setState({
      agree: getOrderEditAgreement(),
    });
  }

  componentDidShow() {
    this.handleCheckCreditService();
    this.handleSecondConfirmOrder();
    preloadAd(['order.result']);
    this.dealDefaultValue('change');
  }

  //分享
  onShareAppMessage = getShareAppMessage;
  // 朋友圈分享
  onShareTimeline = getShareTimeline;

  // 登录状态更新
  onUpdate = (data) => {
    const { logined } = data;
    this.logined = logined;
    if (logined) {
      // 扫码寄
      scanAction(null, this)
        .then((res) => {
          console.log('寄件页.扫码寄.参数', res);
        })
        .catch((err) => console.log(err));
      // 外部进入时带着下单关系的场景
      let params = getLaunchParams(this);
      if (params.scene) {
        let { query = {} } = scanParse({ scene: params.scene });
        params = {
          ...params,
          ...query,
        };
      }

      this.onPostMessage('routerParamsChange', {
        params,
      });
    }
  };

  // 监听
  onPostMessage = (key, data) => {
    console.log('key', key, data);

    switch (key) {
      case 'routerParamsChange':
        // 路由参数变更
        const {
          params: {
            returnModuleInfo,
            address,
            extraInfo,
            extraData,
            type = '',
            sign,
            uid,
            sub_uid,
            ...rest
          } = {},
          source,
        } = data;
        if (source == 'goods') {
          this.onChange('info', data);
          return;
        }
        if (address) {
          this.setState({
            addressData: address,
          });
          if (extraInfo) {
            this.initExtraInfoLock = true;
            this.onChange('info', {
              data: extraInfo,
            });
          }
          if (type === 'clone') {
            // 再来一单，重新下单
            this.cleanFormInfo(['estimatedData']);
          }
        }
        // 进入寄快递携带下单信息
        const { brand } = rest;

        const relationList = [brand];
        const hasRelation = relationList.filter((item) => !!item).length > 0;
        if (hasRelation) {
          this.setState({
            relationData: rest,
          });
        }
        break;
      case 'temporaryBack':
        this.handleSubmitForm('submitTemporary');
        break;
    }
  };

  // 更新下单关系数据
  updateRelationInfo = (oData) => {
    // console.log('updateRelationInfo111==>oData', oData);
    getDHRelationConfig(oData).then((data) => {
      // console.log('updateRelationInfo222==>data', data);
      let { dynamicForms = {} } = data || {};
      const oBrandDynamicsConfig = Taro.kbGetGlobalData('DHBrandDynamicsConfig') || {};
      if (oBrandDynamicsConfig.brand === data.brand) {
        dynamicForms = { ...dynamicForms, ...(oBrandDynamicsConfig.dynamicForms || {}) };
      }
      let state = {
        relationInfo: {
          ...this.relationInfo,
          ...data,
        },
      };
      this.relationInfo = data || {};
      state.dynamicForms = fixDynamicFormsData(dynamicForms ? dynamicForms : {});
      this.setState({ ...state });
      if (oData.brand && this.updateBrand != oData.brand) {
        if (this.updateBrand) {
          this.onChange('info', {
            data: {
              service: {},
            },
          });
        }
        this.updateBrand = oData.brand;
      }
      logger.info('下单关系变化-updateRelationInfo', data);
    });
  };

  // 处理变化事件
  onChange = (key, e) => {
    console.log('处理变化事件', key, e);
    const { data = {}, nextData, receiveList } = e || {};
    const { extraInfo = {} } = this.state;
    switch (key) {
      case 'address':
        const { receive_province, send_province } = nextData;
        receive_province && send_province && (data.disabled = false);
        this.fixFormUpdateData(data);
        this.updateReceiveList(receiveList);
        this.chaoquTipsLock = false;
        break;
      case 'info':
        this.setState({
          extraInfo: {
            ...extraInfo,
            ...data,
          },
        });
        break;
      case 'estimatedFee':
        this.estimatedFee = data.estimatedFee || 0;
        break;
      case 'quotation':
        this.setState({
          quotation: e,
        });
        break;
    }
  };

  dealDefaultValue = (type = 'init') => {
    // 更改默认物品类型
    if (this.formIns) {
      getDefaultGoods().then((dGoods) => {
        if (type == 'change' && this.dGoods == dGoods) return;
        this.dGoods = dGoods;
        this.defaultExtraInfo.goods_name = dGoods;
      });
    }
  };

  // 切换是否同意
  onSwitchAgree = (agree) => {
    setOrderEditAgreement(agree);
    this.setState({ agree });
  };

  // 创建表单&表单变化
  onChange_form = () => {};
  onSubmit_form = () => {};
  createForm = (onReady) => {
    // 所有表单只包含地址信息，其他数据通过formatRequest合并
    // 这种方式可以利用form.disabled 判断是否展开物品信息
    // this.receiveList 批量下单时的收件人列表数据
    this.formIns = new Form(
      {
        form: getForm(),
        enableEmpty: false,
        api: {
          url: apis[`order.edit.yjkd`],
          toastError: true,
          onIntercept: () => {
            const { isOpenCredit, quotation, relationInfo } = this.state;
            const { brand, delivery_type } = relationInfo || {};
            // 拦截批量请求
            if (this.receiveList) {
              Taro.kbToast({
                text: '该下单对象暂不支持批量下单!',
              });
              return true;
            }
            if (!isOpenCredit) {
              this.handleSubmitInterceptTips('open', {
                type: 'credit',
              });
              return true;
            }
            const quotationData = getCurQuotationItem(brand, quotation, delivery_type);
            const { lscqItem, pscqItem } = formatServiceData(quotationData);
            const { amount: lscqFee } = lscqItem || {};
            const { amount: pscqFee } = pscqItem || {};
            const isChaoqu = lscqFee * 1 > 0 || pscqFee * 1 > 0;
            if (isChaoqu && !this.chaoquTipsLock) {
              this.handleSubmitInterceptTips('open', {
                type: 'chaoqu',
              });
              this.chaoquTipsLock = true;
              return true;
            }
          },
          formatRequest: ({ extraData: [action = 'submit'] = [], ...req }) => {
            this.submitAction = action || 'submit';
            const { send_save, receive_save } = req;
            const {
              relationInfo: {
                type: relationType,
                brand,
                platform = '',
                dynamicForms = {},
                delivery_type,
              } = {},
              extraInfo: {
                goods_name,
                goods_weight,
                goods_remark,
                package_images,
                reserve_start_time = '1h',
                reserve_end_time = '1h',
                volume = {},
                service,
                brand: print_brand,
                product_type: print_product_type,
                package_num = 1,
              } = {},
              quotation,
            } = this.state;
            const quotationData = getCurQuotationItem(brand, quotation, delivery_type) || '';
            const {
              goods_name: dynamicGoods_name,
              appointmentTime: dynamicAppointmentTime,
              service: dynamicService,
              volume: dynamicVolume,
            } = dynamicForms || {};
            const { oService, cost_value, keep_account, floor = 0 } = service || {};
            let addressData = transferWkdAddress(req);
            req = {
              ...addressData,
              is_fresh:
                isFresh(goods_name) || (dynamicGoods_name && dynamicGoods_name.customFresh)
                  ? '1'
                  : '0', //标志生鲜
              package_info: goods_name,
              package_weight: goods_weight,
              package_note: goods_remark,
              package_num: package_num,
              check_pics: isArray(package_images) ? package_images.join(',') : '',
            };
            if (dynamicAppointmentTime && dynamicAppointmentTime.isShow) {
              req.reserve_start_time = reserve_start_time;
              req.reserve_end_time = reserve_end_time;
              if (reserve_start_time == '1h' || !reserve_start_time) {
                req.reserve_start_time = '1h';
                req.reserve_end_time = '1h';
              }
            }
            if (dynamicService && dynamicService.isShow) {
              const { insuranceItem = {} } = formatServiceData(quotationData);
              req = {
                ...req,
                decVal: keep_account, //声明物品价值
                proPrice: cost_value > 0 ? cost_value : insuranceItem.amount, //保价
                ...(isObject(oService) ? oService : {}),
              };
            }
            if (dynamicVolume && dynamicVolume.isShow && isObject(volume)) {
              if (volume.checked) {
                req = {
                  ...req,
                  ...volume,
                };
              }
            }
            req.shipper_default = send_save || 0;
            req.shipping_default = receive_save || 0;
            req.channel = `mina_${Taro.systemInfo.platform}`;
            switch (relationType) {
              case 'brand':
                req.brand = brand;
                if (platform == 'yjkd_brand') {
                  req.platform = 'yjkd';
                  if (brand === 'htky' && floor == 1) {
                    service.pickup_way = 'pickup_way';
                  }
                  req.service_info = service;
                  if (req.volume) {
                    req.service_info = {
                      ...(req.service_info || {}),
                      ...volume,
                    };
                  }
                  if (quotationData && quotationData.discount_price) {
                    req.freight = quotationData.discount_price;
                  }
                  req.product_code = quotationData.delivery_type;
                }
                break;
            }
            if (print_brand && relationType != 'brand') {
              req.brand = print_brand;
              if (print_product_type) {
                req.waybillType = findProductTypeEn(print_brand, print_product_type);
              }
            }
            return req;
          },
          onCustomCheck: (req) => {
            console.log('req', req);
            const {
              relationInfo: { dynamicForms = {}, brand, platform } = {},
              agree,
              quotation,
            } = this.state;
            const { weightLimitMax, weightLimitMin = 30, package_num_limit } = dynamicForms || {};
            const { package_info, package_weight, package_num = 1 } = req;
            if (!agree) {
              return { code: 101, msg: '请勾选服务协议!' };
            }
            if (!brand) {
              return { code: 101, msg: '请选择下单品牌' };
            }
            if (!package_info) {
              return { code: 101, msg: '请选择物品类型!' };
            }
            if (weightLimitMin * 1 > 0) {
              if (package_weight * 1 < weightLimitMin * 1) {
                return { code: 101, msg: `默认寄件最小重量为${weightLimitMin}kg ` };
              }
            }
            if (weightLimitMax * 1 > 0) {
              if (package_weight * 1 > weightLimitMax * 1) {
                return { code: 101, msg: `限制寄件最大重量为${weightLimitMax}kg` };
              }
            }
            if (package_num_limit * 1 > 0) {
              if (package_num * 1 > package_num_limit * 1) {
                return { code: 101, msg: `包裹最大数量为${package_num_limit}件` };
              }
            }
            if (platform == 'yjkd_brand') {
              const { available, unavailable_msg } = getQuotationDetail(brand, quotation) || {};
              if (available <= 0 && unavailable_msg) {
                return { code: 101, msg: unavailable_msg };
              }
            }
          },
          onThen: this.createDone,
        },
        onUpdate: (data) => {
          const { eventType, data: addressData } = data;
          if (eventType === 'clean') {
            // 清除操作
            this.cleanFormInfo(['addressData', 'extraInfoData', 'estimatedData'], { addressData });
          }
        },
        onReady,
      },
      this,
    );
  };

  fixFormUpdateData = (data = {}) => {
    if (this.formIns) {
      data && this.formIns.update(data);
      this.formFixData = null;
    } else {
      this.formFixData = data;
    }
  };

  // 创建订单完成
  createDone = ({ code, data }, req) => {
    console.log('req', req);
    this.jumpZffCheck = false;
    let {
      order_number,
      order_id,
      describe,
      realname,
      upload,
      notActive,
      notInRange,
      online_pay,
      pass_time,
      ordersNum,
      succeed_order_ids,
      wx_after_pay,
      wx_after_pay_bind,
      collect_code,
      card_id,
      async_create_order,
      fail = [],
      reason = '',
      result = [],
    } = data || {};
    const { collection_amount, to_pay_amount, is_fresh, package_info } = req || {};
    const {
      relationInfo,
      relationInfo: {
        type: relationType,
        brand,
        platform,
        dynamicForms,
        courier,
        longitude,
        latitude,
        inn_name,
        address,
        smjData,
      },
    } = this.state;
    const { pay, cutPayDesc } = dynamicForms || {};
    // 触发协议签署
    this.agreeActionRef.current.signAgreement();
    logger.info('下单完成', code, data, req);
    logger.setFilterMsg(req.shipper_mobile || '000');
    if (req.brand === 'yjkd' && !req.platform) {
      logger.setFilterMsg('异常快递品牌');
    }
    if (code == 0) {
      this.chaoquTipsLock = false;
      const { query: { q } = {} } = Taro.getLaunchOptionsSync() || {};
      if (q && relationType == 'brand') {
        logger.info('订单提交完成', brand, platform);
        logger.setFilterMsg('非快递员');
      }
      if (upload || realname) {
        this.realnameRef.current.interceptRealname({
          describe,
          realname,
          upload,
        });
        return;
      }
      if (notActive) {
        Taro.kbModal({
          content: notActive,
          confirmText: '我知道了',
        });
        return;
      }
      if (notInRange) {
        Taro.kbModal({
          content: notInRange,
        });
        return;
      }
      // 批量下单失败-同步模式
      if (fail && isArray(result) && result.length > 0) {
        reason && result.unshift(reason);
        if (isArray(fail) && fail.length > 0) {
          const list = this.receiveList.filter((item, index) => {
            return fail.findIndex((i) => i == index) > -1;
          });
          if (
            this.addressEditRef.current &&
            this.addressEditRef.current.updateFormDataByReceiveList
          ) {
            this.addressEditRef.current.updateFormDataByReceiveList(list);
            this.updateReceiveList(list);
          }
        }
        Taro.kbModal({
          content: result,
        });
        return;
      }
      // 强制开通支付分，如极兔品牌
      if (platform == 'yjkd_brand' && pay == 2 && !order_id) {
        if (wx_after_pay <= 0) {
          this.interceptCreditFailOrder('auth_fail', true);
          return;
        } else if (wx_after_pay_bind <= 0) {
          this.interceptCreditFailOrder('bind_fail', true);
          return;
        }
      }
      // 驿站小程序，半屏打开后下单成功上报
      const { source } = Taro.launchParams || {};
      if (source) {
        // 上报
        reportAnalytics({
          key: 'launch_source',
          source: `${source}_order_success`,
        });
      }
      // 清除编辑状态
      this.cleanEditStatus(() => {
        // 批量异步下单，不做处理
        if (async_create_order == 1) return;
        let url = 'order/result';
        let options = {};
        //跳转页面
        order_id = order_number || order_id;
        if (this.submitAction == 'submitTemporary') {
          Taro.navigator({
            url,
            options: {
              source: 'temporary',
              status: 'start',
              order_id,
            },
          });
          return;
        }
        if (smjData) {
          // 扫码寄
          options = {
            label: 'scan',
            is_fresh,
            package_info,
            collect_code,
            collection_amount,
            to_pay_amount,
          };
        }
        switch (relationType) {
          case 'courier':
            if (process.env.PLATFORM_ENV !== 'swan') {
              if (online_pay > 0) {
                url = 'order/pay';
              }
            }
            options = {
              ...options,
              customer_id: req.customer_id || '',
              status:
                card_id && card_id > 0
                  ? 'equity'
                  : collection_amount || to_pay_amount
                  ? ''
                  : 'credit',
            };
            break;
          case 'brand':
            if (platform == 'yjkd_brand') {
              // 兼容报价单数据可能存在的异步清除问题
              Taro.kbSetGlobalData('quotationList', this.state.quotation || []);
            }
            options = {
              brand: platform == 'yjkd_courier' ? courier.brand : brand,
              platform,
              status: 'credit',
              cutPayDesc,
              estimatedFee: platform == 'yjkd_courier' ? this.estimatedFee : 0,
              wx_after_pay,
              wx_after_pay_bind,
            };
            break;
          case 'team':
            url = 'order/detail';
            options = {
              source: '',
              type: 'team',
            };
            break;
          case 'dak':
            options = {
              ...options,
              customer_id: req.customer_id || '',
              longitude,
              latitude,
              inn_name,
              address,
            };
            break;
        }
        this.sendNotice(order_id);
        //批量寄订单id
        let order_ids = succeed_order_ids ? succeed_order_ids : [order_id];
        Taro.kbSetGlobalData('order_ids', order_ids);
        options = {
          source: relationType,
          order_id,
          order_ids,
          pass_time,
          ordersNum,
          ...options,
          pageSource: 'dh',
        };
        const navigatorPage = () => {
          // 首页弹窗统计
          if (Taro.homeActivityRecord) {
            homeActivityReportAnalytics('下单', Taro.homeActivityRecord);
            if (platform == 'yjkd_brand' && brand) {
              homeActivityReportAnalytics(`下单-${brand}`, Taro.homeActivityRecord);
            }
            Taro.homeActivityRecord = '';
          }
          relationInfo.storageWay = 'order';
          if (smjData) {
            relationInfo.smjData = null;
            Taro.kbUpdateRelationInfo(relationInfo);
          }
          // Taro.kbTriggerStorageRelation(relationInfo);
          if (process.env.PLATFORM_ENV === 'swan') {
            // 百度去除支付分标记
            const { status } = options;
            options.status = status === 'credit' ? '' : status;
          }
          Taro.navigator({
            url,
            options,
          });
        };
        if (
          process.env.PLATFORM_ENV == 'alipay' &&
          platform == 'yjkd_brand' &&
          (pay == 2 || pay == 3) &&
          order_id &&
          wx_after_pay == 1 &&
          wx_after_pay_bind == 1
        ) {
          console.info('检查是否需要下单二次确认', pay);
          //芝麻分用户确认逻辑
          confirmCredit({
            order_id,
            type: 'create',
            returnBackLink: creatSchemeLink({
              page: '/pages/order/result/index',
              query: 'creditSource=CreditSuccess',
            }),
            cancelBackLink: creatSchemeLink({
              page: pay == 2 ? '/pages/order/edit/index' : '/pages/order/result/index',
              query: `creditSource=${pay == 2 ? 'CreditAndOrderFail' : 'CreditFail'}`,
            }),
          }).then((cRes) => {
            console.info('二次确认跳转');
            console.info(cRes);
            if (cRes.code == 0 && cRes.data && cRes.data.sign) {
              console.info('需要=>二次确认跳转');
              //需要确认
              //缓存数据
              let quotation = Taro.kbGetGlobalDataOnce('quotationList') || [];
              let welfare = Taro.kbGetGlobalDataOnce('welfare') || {};
              setStorage({
                key: 'LastOrderResultData',
                data: {
                  quotation,
                  welfare,
                  pay,
                  sh_order_number: cRes.data.order_number,
                  ...options,
                },
              });
              const ZhiMaCredit = Taro.requirePlugin('ZhiMaCredit');
              ZhiMaCredit.startService({
                type: cRes.data.type,
                sign_str: cRes.data.sign,
                zm_service_id: cRes.data.zm_service_id,
                success: () => {
                  console.info('芝麻分插件跳转成功');
                },
                fail: () => {
                  console.info('芝麻分插件跳转失败');
                  Taro.kbToast({
                    text: '芝麻分插件跳转失败',
                  });
                },
                complete: () => {},
              });
            } else {
              //不需要确认
              navigatorPage();
            }
          });
        } else {
          navigatorPage();
        }
      });
    }
  };

  // 清除编辑状态或清除批量寄件列表
  cleanFormInfo = (keys, replaceData) => {
    this.setState(cleanOrderEditFormInfo(keys, replaceData));
  };
  cleanEditStatus = (then = noop) => {
    Taro.kbSetGlobalData('pickupAddress', {});
    this.formIns.clean();
    then();
    this.updateReceiveList();
    removeStorage({
      key: receiveStorageKey,
    });
    removeStorage({
      key: 'extraInfo',
    });
    this.setState({
      extraInfo: this.defaultExtraInfo,
    });
  };
  handleSubmitForm = async (action = '') => {
    const { relationInfo: { customer, type, courier_id } = {} } = this.state;
    //如果大客户未通过审核，无法下单
    const customer_id = (customer && customer.id) || '';
    if (customer_id) {
      var customerAuthStatus = await checkCustomerAuthStatus(customer_id);
      if (customerAuthStatus != 1) return true;
    }
    if (type == 'courier') {
      const unPayOrders = await getUnPayOrders({
        courierId: courier_id,
        customer_id,
      });
      if (unPayOrders.length > 0) {
        this.setState({
          unPayOrders,
        });
        return;
      }
    }
    getAuthCode().then(() => {
      this.onSubmit_form(action);
    });
  };
  // 统一处理地址校验提示
  handleCheck = () => {
    const res = this.formIns.check();
    res.msg = res.msg;
    // res.msg = res.msg + '\n请重新输入后再提交订单';
    return res;
  };
  // 兼容老的订阅方案
  sendNotice = (order_id) => {
    if (!order_id) return;
    const { relationInfo } = this.state;
    const { type: relationType } = relationInfo || {};
    sendNoticeWkd({
      template_title: relationType === 'brand' ? 'grab_order' : 'order_notice',
      order_id,
    });
  };
  // 更新收件人类表
  updateReceiveList = updateReceiveList;
  // 检测支付分开通情况
  handleCheckCreditService = handleCheckCreditService;
  // 强制开通支付分提示
  interceptCreditFailOrder = interceptCreditFailOrder;
  // 处理二次确认失败订单
  handleSecondConfirmOrder = handleSecondConfirmOrder;
  // 执行开通支付分逻辑
  handleOpenCredit = handleOpenCredit;

  handlePackageChange = (v) => {
    this.onChange('info', {
      data: {
        package_num: v,
      },
    });
  };

  handleProPriceClick = handleProPriceClick;

  handleSubmitInterceptTips = (key, data) => {
    switch (key) {
      case 'close':
        this.setState({
          oSubmitIntercept: {},
        });
        break;
      case 'open':
        this.setState({
          oSubmitIntercept: data,
        });
        break;
    }
  };

  render() {
    const {
      total,
      relationInfo,
      relationData,
      relationInfoLoading,
      form: { data: formData },
      dynamicForms,
      addressData,
      extraInfo = {},
      extraInfoData,
      estimatedFeeActive,
      estimatedData,
      agree,
      isOpenCredit,
      nowTimer,
      order_type,
      cur,
      quotation,
      oSubmitIntercept = {},
      ...rest
    } = this.state;
    const { brand, delivery_type } = relationInfo || {};
    const { goods_weight, volume, package_num = 1, service } = extraInfo || {};
    const { cost_value = 0 } = service || {};

    // console.log('relationInfo', relationInfo);
    // console.log('dynamicForms', dynamicForms);
    // console.log('formData', formData);
    // console.log('extraInfo', extraInfo);
    // console.log('quotation', quotation);

    const isPickupAddress = false;
    const quotationData = getCurQuotationItem(brand, quotation, delivery_type) || '';
    const { insuranceItem = {} } = formatServiceData(quotationData);

    return (
      <KbPage allowAuthPopup {...rest} onUpdate={this.onUpdate} cover={false}>
        <KbScrollView
          full
          renderHeader={
            <Fragment>
              {getUserStateByComplete() ? (
                <OrderNotice isDh />
              ) : (
                <View className='kb-login__auth--box'>
                  <KbLoginAuthAndBind className='kb-button__middle' />
                </View>
              )}
            </Fragment>
          }
          renderFooter={
            <View className='kb-box at-row at-row__align--center'>
              <View className='at-col kb-spacing-md-l'>
                <KbDHEstimatedFee
                  relationInfo={relationInfo}
                  address={formData}
                  extraInfo={extraInfo}
                  volume={volume}
                  quotationData={quotationData}
                  total={total}
                  onProPriceClick={handleProPriceClick}
                />
              </View>
              <View className='kb-spacing-md'>
                <KbSubmitBar
                  onClick={this.handleSubmitForm}
                  onCheck={this.handleCheck}
                  total={total}
                />
              </View>
            </View>
          }
          ts={nowTimer}
        >
          <View className='kb-send'>
            <View className='kb-send__item'>
              <KbAddressEdit
                pageName='edit'
                locked
                useDefault
                supportBatch={false}
                supportTemporary={false}
                switchAddress
                isPickupAddress={isPickupAddress}
                ref={this.addressEditRef}
                actionRef={this.realnameRef}
                extraInfo={extraInfo}
                relationInfo={relationInfo}
                data={addressData}
                onChange={this.onChange.bind(this, 'address')}
              />
            </View>
            {/* 物品信息 */}
            <View className='kb-block'>
              <GoodsInfo
                relationInfo={relationInfo}
                data={extraInfo}
                dynamicForms={dynamicForms}
                onChange={this.onChange.bind(this, 'goodsInfo')}
              />
              <View className='kb-nav-item kb-packageNum'>
                <View className='kb-nav-item-label'>包裹数量</View>
                <View className='kb-nav-item-value'>
                  <KbInputNumber
                    className='kb-input-center'
                    width={100}
                    step={1}
                    max={999}
                    min={1}
                    placeholder='数量'
                    alwaysEmbed
                    value={package_num}
                    type='digit'
                    onChange={this.handlePackageChange}
                  />
                </View>
              </View>
            </View>
            {/* 保价 */}
            <View className='kb-block'>
              <View
                className='kb-nav-item kb-proPrice'
                onClick={this.handleProPriceClick}
                hoverClass='kb-hover'
              >
                <View className='kb-nav-item-label'>保价服务</View>
                <View className='kb-nav-item-value'>
                  {cost_value * 1 > 0 ? (
                    <Text>{cost_value + '元'}</Text>
                  ) : insuranceItem.amount * 1 > 0 ? (
                    <Text>{insuranceItem.amount + '元'}</Text>
                  ) : (
                    <Text className='kb-color__grey'>未保价</Text>
                  )}
                  <AtIcon
                    prefixClass='kb-icon'
                    value='arrow'
                    className='kb-icon2 kb-color__grey kb-icon-size__base'
                  />
                </View>
              </View>
              <BrandInfo
                data={relationData}
                relationInfo={relationInfo}
                address={formData}
                extraInfo={extraInfo}
                weight={goods_weight}
                volume={volume}
                onChange={this.updateRelationInfo}
                onQuotationChange={this.onChange.bind(this, 'quotation')}
              />
            </View>
            {/* 增值服务 */}
            <AddValueService
              relationInfo={relationInfo}
              dynamicForms={dynamicForms}
              extraInfo={extraInfo}
              address={formData}
              weight={goods_weight}
              volume={volume}
              onChange={this.onChange.bind(this, 'info')}
            />
            {/* 服务协议 */}
            <View className='at-row at-row__justify--between at-row__align--center kb-margin-md-t kb-margin-xl-b'>
              <View>
                <View className='at-row at-row__align--center'>
                  <KbCheckbox
                    label='我已阅读并同意'
                    checked={agree}
                    onChange={this.onSwitchAgree}
                    className='kb-color__black'
                  />
                  <KbAgreement agreeType='serviceAgreement' actionRef={this.agreeActionRef} />
                </View>
              </View>
            </View>
          </View>
          <KbModal
            isOpened={!!oSubmitIntercept.type}
            closeOnClickOverlay={false}
            closable={false}
            top3={{
              text: oSubmitIntercept.type === 'credit' ? '下单失败' : '温馨提示',
            }}
            onClose={this.handleSubmitInterceptTips.bind(this, 'close')}
            onCancel={this.handleSubmitInterceptTips.bind(this, 'close')}
            onConfirm={this.handleSubmitInterceptTips.bind(this, 'close')}
            confirmText='我知道啦'
          >
            {oSubmitIntercept.type === 'credit' ? (
              <View className='kb-submitInterceptTips'>
                <View className='kb-submitInterceptTips-notice'>
                  由于您未开通微信支付分授权，导致您无 法使用先寄后付尊享服务；
                </View>
                <View>注：建议您返回微快递首页，重新选择品牌下单完成寄件； </View>
              </View>
            ) : oSubmitIntercept.type === 'chaoqu' ? (
              <View className='kb-submitInterceptTips'>
                <View className='kb-submitInterceptTips-notice'>
                  <View>揽收/派件超区费提示： </View>
                  <View>您填写的揽件/派件地址超出快递员服务区域，需额外支付超区费。</View>
                </View>
                <View>具体加收金额可查看-费用明细。</View>
              </View>
            ) : null}
          </KbModal>
        </KbScrollView>
      </KbPage>
    );
  }
}

export default Index;
