/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro from '@tarojs/taro';
import { Image } from '@tarojs/components';
import { useBrandInfo } from './_utils';

const specification = {
  mini: '25rpx',
  sm: '50rpx',
  md: '80rpx',
  lg: '100rpx',
};
const Index = (props) => {
  const { size, brand, circle, className } = props;
  const { brandInfo } = useBrandInfo({ brand });
  const style = {
    width: specification[size],
    height: specification[size],
  };
  return (
    <Image
      className={className}
      circle={circle}
      defaultSource='https://cdn-img.kuaidihelp.com/brand_logo/icon_other.png'
      src={brandInfo.logo_link}
      style={style}
    />
  );
};
Index.defaultProps = {
  size: 'sm',
  brand: '',
  circle: true,
};
Index.options = {
  addGlobalClass: true,
};
export default Index;
