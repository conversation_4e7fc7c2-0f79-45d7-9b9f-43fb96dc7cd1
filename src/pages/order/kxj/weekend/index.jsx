/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { Component, Fragment } from '@tarojs/taro';
import { View, Image, Swiper, SwiperItem, Text, Button } from '@tarojs/components';
import { AtIcon } from 'taro-ui';
import { get } from '@/actions/brands';
import { connect } from '@tarojs/redux';
import KbPage from '@base/components/page';
import KbLongList from '@base/components/long-list';
import KbModal from '@base/components/modal';
import KbAvatar from '@/components/_pages/user/avatar';
import SimpleCountDown from '@/components/_pages/order/kxj/countDown/simple';
import {
  activityRulesList,
  createListData,
  getBannerAds,
  createCouponList,
  getUserVipInfoData,
  handleAdClick,
  handleBarClick,
  handleRules,
  handleVip,
  handleGuideShare,
  handleShareUser,
  handleShareSuccess,
  handelRetentionNoUse,
  handelRetentionNoReceive,
  inflateCoupon,
  dealShareEnter,
  handleNavClick,
} from '@/components/_pages/order/kxj/weekend/_utls';
import { dateCalendar } from '@base/utils/utils';
import { tabItemTapCall } from '@/components/_pages/_utils';
import { getShareAppMessage } from '@/utils/share';
import './index.scss';

@connect(
  ({ global }) => ({
    brands: global.brands,
  }),
  {
    dispatchGet: get,
  },
)
class Index extends Component {
  config = {
    navigationBarTitleText: '周末特惠',
    backgroundColorTop: '#f2f2f2',
    navigationStyle: 'custom',
  };

  constructor() {
    this.state = {
      list: [],
      isOpened: false,
    };
    this.listData = createListData();
  }

  // 登录状态更新
  onUpdate = (loginRes) => {
    const { logined } = loginRes || {};
    if (logined) {
      this.props.dispatchGet();
      this.getBannerAds();
      this.getUserVipInfoData();
      this.dealShareEnter();
    }
  };

  componentDidShow() {
    this.getUserVipInfoData();
  }

  onTabItemTap = tabItemTapCall;

  // 分享
  onShareAppMessage = (ev) => {
    if (ev.from === 'button') {
      const { isOpenedGuide = {} } = this.state;
      const { userInfo = {} } = Taro.kbLoginData || {};
      const { user_id, avatar_url, nickname } = userInfo || {};
      if (isOpenedGuide.is_inflate != 1) {
        inflateCoupon(isOpenedGuide.card_id).then(() => {
          this.listIns.loader();
          this.handleGuideShare('close');
        });
      }
      return getShareAppMessage(ev, {
        page: 'activity.weekend',
        info: {
          ...isOpenedGuide,
          user_id,
          avatar_url,
          nickname,
        },
      });
    }
  };

  onReady(ins) {
    this.listIns = ins;
  }

  dealShareEnter = dealShareEnter;

  getUserVipInfoData = getUserVipInfoData;

  getBannerAds = getBannerAds;

  handleAdClick = handleAdClick;

  handleRules = handleRules;

  handleVip = handleVip;

  handleBarClick = handleBarClick;

  handleGuideShare = handleGuideShare;

  handleShareUser = handleShareUser;

  handleShareSuccess = handleShareSuccess;

  handelRetentionNoReceive = handelRetentionNoReceive;

  handelRetentionNoUse = handelRetentionNoUse;

  handleNavClick = handleNavClick;

  render() {
    const { brands = {} } = this.props;
    const {
      adList,
      list = [],
      isOpened = false,
      isOpenedGuide = false,
      isOpenedShare = false,
      isOpenedShareSuccess = false,
      isOpenedRetentionNoReceive = false,
      isOpenedRetentionNoUse = false,
      userVipData = {},
      ...rest
    } = this.state;
    const discountList = createCouponList(list, { type: 'discount' });
    return (
      <KbPage
        onUpdate={this.onUpdate.bind(this)}
        {...rest}
        navProps={{
          onBack: this.handleNavClick.bind(this),
        }}
      >
        <KbLongList data={this.listData} enableMore={false} onReady={this.onReady.bind(this)}>
          {/* 轮播图 */}
          <View className='kb-couponWeekend-swiper'>
            <Swiper className='swiper' autoplay circular indicatorDots={adList.length > 1}>
              {adList.map((item) => {
                return (
                  <SwiperItem className='swiper-item' key={item.id}>
                    <Image
                      className='swiper-img'
                      src={item.imgUrl}
                      onClick={this.handleAdClick.bind(this, item)}
                      hoverClass='kb-hover-opacity'
                    />
                  </SwiperItem>
                );
              })}
            </Swiper>
            <View
              className='rule'
              onClick={this.handleRules.bind(this, 'open')}
              hoverClass='kb-hover-opacity'
            >
              规则
            </View>
          </View>
          {/* 优寄会员引导 */}
          <View className='kb-couponWeekend-vip' onClick={this.handleVip.bind(this)}>
            <Image
              className='kb-couponWeekend-vip__img'
              src='https://cdn-img.kuaidihelp.com/wkd/miniApp/activity/center/img_vip.png'
            />
            <View className='kb-couponWeekend-vip__body'>
              <View className='title'>
                {userVipData.status == 1 ? (
                  <Fragment>
                    优享寄<Text className='kb-color__red'>{userVipData.name}</Text>会员
                  </Fragment>
                ) : userVipData.status == 2 ? (
                  <Fragment>
                    续费限时特惠<Text className='kb-color__red'>5.1</Text>元
                  </Fragment>
                ) : (
                  <Fragment>
                    首开专享价低至<Text className='kb-color__red'>{userVipData.first_price}</Text>元
                  </Fragment>
                )}
              </View>
              <View className='desc'>
                {userVipData.status == 1
                  ? `温馨提示：${dateCalendar(userVipData.expires_time, { timer: true })}到期`
                  : userVipData.status == 2
                  ? '会员寄件，享多重优惠'
                  : '优寄寄件 单单享返现'}
              </View>
            </View>
            <AtIcon className='kb-size__sm kb-color__grey' prefixClass='kb-icon' value='arrow' />
          </View>
          {/* 每日神券 */}
          <View className='kb-couponWeekend-day'>
            <View className='kb-couponWeekend-title'>
              <View>
                <View className='at-row at-row__align--center'>
                  <Image
                    className='img'
                    src='https://cdn-img.kuaidihelp.com/wkd/miniApp/activity/center/icon_coupon01.png'
                  />
                  <Text className='title'>每日神券</Text>
                </View>
              </View>
              <View className='desc'>开通微信支付分可用</View>
            </View>
            <View className='kb-couponWeekend-day__box'>
              <View className='kb-couponWeekend-day__block'>
                <View className='at-row at-row__align--center at-row__justify--center'>
                  <Image
                    className='img'
                    mode='widthFix'
                    src='https://cdn-img.kuaidihelp.com/wkd/miniApp/activity/center/icon_user01.png'
                  />
                  <Text className='user'>普通用户</Text>
                </View>
                <View className='desc'>
                  每日限领取<Text className='kb-color__red'>1</Text>张
                </View>
              </View>
              <View className='kb-couponWeekend-day__block'>
                <View className='at-row at-row__align--center at-row__justify--center'>
                  <Image
                    className='img'
                    mode='widthFix'
                    src='https://cdn-img.kuaidihelp.com/wkd/miniApp/activity/center/icon_user02.png'
                  />
                  <Text className='user user-vip'>会员用户</Text>
                </View>
                <View className='desc'>
                  每日<Text className='kb-color__red'>不限量</Text>领券
                </View>
              </View>
            </View>
            <View className='kb-couponWeekend-discount__list'>
              {discountList.map((item) => {
                const brandData = brands[item.brand] || {};
                return (
                  <View className='kb-couponWeekend-discount__item' key={item.card_id}>
                    <View className='kb-couponWeekend-discount__inner'>
                      <View className='left'>
                        <View className='price'>
                          {item.discount_fee}
                          <Text className='unit'>折</Text>
                        </View>
                        {item.min_weight > 0 && (
                          <View className='desc'>限重{item.min_weight}KG</View>
                        )}
                      </View>
                      <View className='right'>
                        <View className='title'>
                          {brandData.short_name}
                          {item.discount_fee}折券
                        </View>
                        <View className='text'>有效期至领券之日起，当天有效</View>
                        {item.message && <View className='tag'>{item.message}</View>}
                      </View>
                    </View>
                    {item.is_receive == 1 && item.is_use == 1 ? (
                      <View
                        className='kb-couponWeekend-discount__btn'
                        onClick={this.handleBarClick.bind(this, 'used', item)}
                        hoverClass='kb-hover'
                      >
                        已使用
                      </View>
                    ) : item.is_receive == 1 ? (
                      <View
                        className='kb-couponWeekend-discount__btn kb-couponWeekend-discount__btn2'
                        onClick={this.handleBarClick.bind(this, 'edit', item)}
                        hoverClass='kb-hover'
                      >
                        去寄件
                      </View>
                    ) : (
                      <View
                        className='kb-couponWeekend-discount__btn'
                        onClick={this.handleBarClick.bind(this, 'one', item)}
                        hoverClass='kb-hover'
                      >
                        立即领取
                      </View>
                    )}
                  </View>
                );
              })}
            </View>
            <View
              className='kb-couponWeekend-btnAll'
              onClick={this.handleBarClick.bind(this, 'allDiscount')}
              hoverClass='kb-hover'
            >
              <Image
                className='tag'
                mode='widthFix'
                src='https://cdn-img.kuaidihelp.com/wkd/miniApp/activity/center/icon_vip.png?v=1'
              />
              一键领取全部折扣券
            </View>
            {userVipData.status != 1 && (
              <View
                className='kb-couponWeekend-openVip'
                onClick={this.handleVip.bind(this)}
                hoverClass='kb-hover'
              >
                <View className='kb-couponWeekend-openVip-inner'>去开通会员</View>
              </View>
            )}
            <View className='kb-couponWeekend-notice'>
              <View>注：</View>
              <View>
                EMS目前只开放浙江、山东、辽宁、陕西地区寄件，暂未开放全国寄件。更多神券，敬请期待！
              </View>
            </View>
          </View>
          {/* 寄件服务 */}
          <Image
            className='kb-couponWeekend-block'
            mode='widthFix'
            src='https://cdn-img.kuaidihelp.com/wkd/miniApp/activity/center/img_send.png?v=1'
          />
        </KbLongList>
        {/* 规则弹窗 */}
        <KbModal
          title='领券规则'
          top={false}
          closable={false}
          confirmText='我知道了'
          isOpened={isOpened}
          onClose={this.handleRules.bind(this, 'close')}
          onCancel={this.handleRules.bind(this, 'close')}
          onConfirm={this.handleRules.bind(this, 'close')}
        >
          <View className='kb-couponWeekend-rules-list'>
            {activityRulesList.map((item, index) => {
              return (
                <View className='kb-couponWeekend-rules-list-item' key={item}>
                  <View className='kb-couponWeekend-rules-list-index'>{index + 1}、</View>
                  <View className='kb-couponWeekend-rules-list-content'>{item}</View>
                </View>
              );
            })}
          </View>
        </KbModal>
        {/* 引导分享弹窗 */}
        <KbModal
          isOpened={isOpenedGuide}
          title=''
          top={false}
          className='kb-couponWeekend-guide-modal'
          closePosition='bottom'
          confirmText=''
          hideFooter
          onClose={this.handleGuideShare.bind(this, 'close')}
          onCancel={this.handleGuideShare.bind(this, 'close')}
        >
          {isOpenedGuide && isOpenedGuide.card_id && (
            <View className='kb-couponWeekend-guide'>
              <View className='kb-couponWeekend-guide-header'>
                <Image
                  className='logo'
                  mode='widthFix'
                  src='https://cdn-img.kuaidihelp.com/wkd/miniApp/activity/weekend/logo_wkd.png'
                />
                <Text className='logo-text'>微快递</Text>
                <AtIcon className='close kb-icon2' prefixClass='kb-icon' value='wrong' />
                <Image
                  className='brand'
                  mode='widthFix'
                  src={`https://cdn-img.kuaidihelp.com/wkd/miniApp/activity/weekend/brand_${isOpenedGuide.brand}.png`}
                />
              </View>
              <View className='kb-couponWeekend-guide-text'>
                <View>
                  恭喜您！成功领取<Text className='num'>{isOpenedGuide.discount_fee}</Text>折券
                </View>
                <View>
                  分享好友，最高可膨胀至
                  <Text className='num'>{isOpenedGuide.inflate_discount_fee}</Text>折
                </View>
              </View>
              <View className='kb-couponWeekend-guide-coupon'>
                <Image
                  className='img-bg'
                  mode='widthFix'
                  src='https://cdn-img.kuaidihelp.com/wkd/miniApp/activity/weekend/red_01.png'
                />
                <View className='num num1'>
                  {isOpenedGuide.discount_fee}
                  <Text className='unit'>折</Text>
                </View>
                <View className='num num2'>
                  {isOpenedGuide.inflate_discount_fee}
                  <Text className='unit'>折</Text>
                </View>
              </View>
              <View className='kb-couponWeekend-guide-desc'>
                仅需分享至<Text className='kb-color__red'>1</Text>位好友，可膨胀
              </View>
              <View className='kb-couponWeekend-guide-footer'>
                <Button
                  className='btn btn-send'
                  onClick={this.handleGuideShare.bind(this, 'send', isOpenedGuide)}
                >
                  <View className='btn-send-inner'>立即寄件</View>
                </Button>
                <Button className='btn btn-share' openType='share'>
                  分享好友
                </Button>
              </View>
            </View>
          )}
        </KbModal>
        {/* 分享进入弹窗 */}
        <KbModal
          isOpened={isOpenedShare}
          title=''
          top={false}
          className='kb-couponWeekend-share-modal'
          closePosition='bottom'
          confirmText=''
          hideFooter
          onClose={this.handleShareUser.bind(this, 'close')}
          onCancel={this.handleShareUser.bind(this, 'close')}
        >
          <View className='kb-couponWeekend-share'>
            <View className='kb-couponWeekend-share-header'>
              <Image
                className='logo'
                mode='widthFix'
                src='https://cdn-img.kuaidihelp.com/wkd/miniApp/activity/weekend/logo_wkd.png'
              />
              <Text className='logo-text'>微快递</Text>
              <AtIcon className='close kb-icon2' prefixClass='kb-icon' value='wrong' />
              <Image
                className='brand'
                mode='widthFix'
                src={`https://cdn-img.kuaidihelp.com/wkd/miniApp/activity/weekend/brand_${isOpenedShare.brand}.png`}
              />
            </View>
            <View className='kb-couponWeekend-share-user'>
              <KbAvatar src={isOpenedShare.avatar_url} />
              <View className='user-name'>{isOpenedShare.nickname}</View>
            </View>
            <View className='kb-couponWeekend-share-text'>
              <View>
                好友已成功领取<Text className='num'>快递寄件折扣券!</Text>
              </View>
            </View>
            <View className='kb-couponWeekend-share-coupon'>
              <Image
                className='img-bg'
                mode='widthFix'
                src='https://cdn-img.kuaidihelp.com/wkd/miniApp/activity/weekend/red_02.png'
              />
              <View className='num'>
                {isOpenedShare.inflate_discount_fee}
                <Text className='unit'>折</Text>
              </View>
              <View className='coupon-name'>寄件折扣券</View>
            </View>
            <View
              className='kb-couponWeekend-share-footer'
              onClick={this.handleShareUser.bind(this, 'close')}
            >
              <Button className='btn btn-join'>我要参与</Button>
            </View>
          </View>
        </KbModal>
        {/* 膨胀成功弹窗 */}
        <KbModal
          isOpened={isOpenedShareSuccess}
          title=''
          top={false}
          className='kb-couponWeekend-shareSuccess-modal'
          closePosition='bottom'
          confirmText=''
          hideFooter
          onClose={this.handleShareSuccess.bind(this, 'close')}
          onCancel={this.handleShareSuccess.bind(this, 'close')}
        >
          <View className='kb-couponWeekend-shareSuccess'>
            <View className='kb-couponWeekend-shareSuccess-header'>
              <Image
                className='logo'
                mode='widthFix'
                src='https://cdn-img.kuaidihelp.com/wkd/miniApp/activity/weekend/logo_wkd.png'
              />
              <Text className='logo-text'>微快递</Text>
              <AtIcon className='close kb-icon2' prefixClass='kb-icon' value='wrong' />
              <Image
                className='brand'
                mode='widthFix'
                src={`https://cdn-img.kuaidihelp.com/wkd/miniApp/activity/weekend/brand_${isOpenedShareSuccess.brand}.png`}
              />
            </View>
            <View className='kb-couponWeekend-shareSuccess-text'>
              <View>恭喜您！膨胀成功</View>
              <View>
                享最高折扣，至
                <Text className='num'>{isOpenedShareSuccess.inflate_discount_fee}</Text>折
              </View>
            </View>
            <View className='kb-couponWeekend-shareSuccess-coupon'>
              <Image
                className='img-bg'
                mode='widthFix'
                src='https://cdn-img.kuaidihelp.com/wkd/miniApp/activity/weekend/red_02.png'
              />
              <View className='num'>
                {isOpenedShareSuccess.inflate_discount_fee}
                <Text className='unit'>折</Text>
              </View>
              <View className='coupon-name'>寄件折扣券</View>
            </View>
            <View className='kb-couponWeekend-shareSuccess-footer'>
              <Button
                className='btn btn-join'
                onClick={this.handleShareSuccess.bind(this, 'send', isOpenedShareSuccess)}
              >
                立即寄件
              </Button>
            </View>
          </View>
        </KbModal>
        {/* 未领券挽留弹窗 */}
        <KbModal
          isOpened={isOpenedRetentionNoReceive}
          top={false}
          title='确认要放弃优惠吗？'
          closePosition='bottom'
          onClose={this.handelRetentionNoReceive.bind(this, 'close')}
          onCancel={this.handelRetentionNoReceive.bind(this, 'close')}
          confirmText=''
          cancelText=''
        >
          <View className='kb-guideReceiveCouponModal'>
            <View className='kb-guideReceiveCouponModal-tips'>领券寄件最高可省188元</View>
            <View className='kb-guideReceiveCouponModal-coupon'>
              <Image
                className='kb-guideReceiveCouponModal-coupon--img'
                mode='widthFix'
                src='https://cdn-img.kuaidihelp.com/wkd/miniApp/activity/center/img_coupon.png?v=1'
              />
            </View>
            <View className='kb-guideReceiveCouponModal-footer'>
              <View
                className='confirm'
                onClick={this.handelRetentionNoReceive.bind(this, 'confirm')}
                hoverClass='kb-hover'
              >
                领券寄件
              </View>
              <View
                className='cancel'
                onClick={this.handelRetentionNoReceive.bind(this, 'cancel')}
                hoverClass='kb-hover'
              >
                残忍离开
              </View>
            </View>
          </View>
        </KbModal>
        {/* 领券未使用挽留弹窗 */}
        <KbModal
          isOpened={isOpenedRetentionNoUse}
          className='kb-guideUseCouponModal-modal'
          top={false}
          title='亲，您有寄件券未使用'
          closePosition='bottom'
          onClose={this.handelRetentionNoUse.bind(this, 'close')}
          onCancel={this.handelRetentionNoUse.bind(this, 'close')}
          confirmText=''
          cancelText=''
        >
          <View className='kb-guideUseCouponModal'>
            <View className='kb-guideUseCouponModal-tips'>
              <SimpleCountDown endTime={isOpenedRetentionNoUse.endTime} />
              后失效
            </View>
            <View className='kb-guideUseCouponModal-coupon'>
              {isOpenedRetentionNoUse.list &&
                isOpenedRetentionNoUse.list.length > 0 &&
                isOpenedRetentionNoUse.list.map((item) => {
                  const brandData = brands[item.brand] || {};
                  return (
                    <View className='kb-couponWeekend-discount__item2' key={item}>
                      <View className='kb-couponWeekend-discount__inner'>
                        <View className='left'>
                          <View className='price'>
                            {item.discount_fee}
                            <Text className='unit'>折</Text>
                          </View>
                          {item.min_weight > 0 && (
                            <View className='desc'>限重{item.min_weight}KG</View>
                          )}
                        </View>
                        <View className='right'>
                          <View className='title'>
                            {brandData.short_name}
                            {item.discount_fee}折券
                          </View>
                          <View className='text'>有效期至领券之日起，当天有效</View>
                          {item.message && <View className='tag'>{item.message}</View>}
                        </View>
                      </View>
                    </View>
                  );
                })}
            </View>
            <View className='kb-guideUseCouponModal-footer'>
              <View
                className='confirm'
                onClick={this.handelRetentionNoUse.bind(this, 'confirm')}
                hoverClass='kb-hover'
              >
                继续寄件
              </View>
              <View
                className='cancel'
                onClick={this.handelRetentionNoUse.bind(this, 'cancel')}
                hoverClass='kb-hover'
              >
                残忍离开
              </View>
            </View>
          </View>
        </KbModal>
      </KbPage>
    );
  }
}

export default Index;
