/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb-query__header {
  padding: $spacing-v-md $spacing-h-md;
  background-color: $color-brand;
}
.kb-scrollView {
  width: 100%;
  height: 100%;
  background-color: $color-white;
  .kb-search__history {
    padding: $spacing-h-md;
    &-head {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: $spacing-h-md;
      &__title {
        color: $color-grey-0;
        font-weight: 500;
        font-size: $font-size-lg;
      }
    }
    &-content {
      display: flex;
      flex-wrap: wrap;
      .kb-search__cardWrap {
        position: relative;
        // max-width: 45%;
      }
      .kb-search__card {
        // max-width: 100%;
        margin-right: $spacing-h-md;
        margin-bottom: $spacing-v-md;
        padding: $spacing-h-sm $spacing-h-md;
        overflow: hidden;
        color: $color-black-1;
        font-size: $font-size-base2;
        // white-space: nowrap;
        // text-overflow: ellipsis;
        background-color: $color-grey-6;
        border-radius: $border-radius-arc;
      }
      .kb-hot {
        position: absolute;
        top: -20px;
        right: 20px;
        padding: 0 $spacing-h-sm;
        color: $color-white;
        font-size: $font-size-xs;
        background-color: $color-red;
        border-radius: $border-radius-arc;
        &::after {
          position: absolute;
          top: 23px;
          left: 20px;
          width: 0px;
          height: 0px;
          border-top: 10px solid $color-red;
          border-right: 10px solid transparent;
          border-left: 10px solid transparent;
          content: '';
        }
      }
    }
  }
  .kb-ads {
    padding: $spacing-h-md;
    overflow: hidden;
    // background-color: $color-white;
    border-radius: $border-radius-lg;
    &__swiper {
      width: 100%;
      height: 280px;
      &--item,
      &--image {
        width: 100%;
        height: 100%;
        border-radius: $border-radius-lg;
      }
      &--item {
        overflow: hidden;
      }
    }
  }
}

.kb-clearModal {
  &__title {
    margin-bottom: $spacing-v-xl;
    color: $color-grey-0;
    font-weight: 500;
    font-size: $font-size-xl;
    text-align: center;
  }
  &__content {
    margin-bottom: $spacing-v-xl;
    color: $color-grey-2;
    text-align: center;
  }
  &__footer {
    display: flex;
    justify-content: space-between;
    padding: 0 $spacing-h-md;
    &-btn {
      width: 44%;
      .at-button {
        height: 80px;
      }
    }
  }
}
