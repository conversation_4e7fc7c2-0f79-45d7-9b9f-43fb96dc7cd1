/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { get } from '@/actions/brands';
import KbExternalAd from '@/components/_pages/ad-extension/ad';
import KbAdInsertScreen from '@/components/_pages/ad-extension/ad/insertScreen';
import {
  adNavigator,
  createInterstitialAdWrap,
  loadAdminAd,
} from '@/components/_pages/ad-extension/sdk';
import { getAdStorageKey } from '@/components/_pages/ad-extension/_utils';
import KbOfficialAccount from '@/components/_pages/official-account';
import KbCouponCard from '@/components/_pages/order/card/coupon-card';
import KbCreditLabel from '@/components/_pages/order/credit/credit-label';
import GiveVipModal2 from '@/components/_pages/order/kxj/businessCoupon/giveVipModal';
import GiveVipModal from '@/components/_pages/order/kxj/giveVipModal';
import { fillOrderArrivePayPrice, orderAction } from '@/components/_pages/order/_utils';
import {
  checkCreditService,
  CreditConfig,
  openCreditService,
  orderCreditFail,
} from '@/components/_pages/order/_utils/order.credit-pay';
import {
  addOrderDetailInfo,
  formatResponseOrderDetail,
  getApiUrlAndData,
} from '@/components/_pages/order/_utils/order.detail';
import { getServiceList } from '@/components/_pages/order/_utils/order.detail.service-list';
import { triggerRewardReportAnalytics } from '@/components/_pages/out/reward/_utils/reportAnalytics';
import { PLATFORM_NAME } from '@/utils/config';
import { scanCode } from '@/utils/scan';
import { getShareAppMessage } from '@/utils/share';
import KbButton from '@base/components/button';
import KbCanvas from '@base/components/canvas';
import KbLinkService from '@base/components/link-service';
import KbModal from '@base/components/modal';
import KbNoticeBar from '@base/components/notice-bar';
import KbPage from '@base/components/page';
import { getHoldDay, getNowDay, setHoldDay } from '@base/components/retention/_utils/limit';
import KbSubscribe from '@base/components/subscribe';
import { getLaunchParams } from '@base/utils/navigator';
import request from '@base/utils/request';
import { StorageVisits } from '@base/utils/storage-visits';
import {
  getPage,
  getStorage,
  getStorageSync,
  removeStorage,
  removeStorageSync,
  reportAnalytics,
  setStorage,
} from '@base/utils/utils';
import { Image, Switch, Text, View } from '@tarojs/components';
import { connect } from '@tarojs/redux';
import Taro, { Component, Fragment } from '@tarojs/taro';
import dayjs from 'dayjs';
import qs from 'qs';
import { AtAvatar, AtButton, AtIcon } from 'taro-ui';

import '../index.wkd.scss';

const STORAGE_AD_TIMER = getAdStorageKey('order.result.1');

/**
 * @param source pay支付/tcjs同城急送/dak驿站/brand快递公司/temporary临时单/yhj商家优惠寄/默认无,代表快递员
 * @param status equity开通权益次卡/credit需要绑定支付分/
 * @param label scan 扫码寄
 * @param order_id 订单id
 * @param ordersNum 当大于1时代表批量寄/默认无
 */
@connect(
  ({ global }) => ({
    brands: global.brands,
    relationInfo: global.relationInfo || {},
    isVipProps: global.isVip,
  }),
  {
    dispatchGet: get,
  },
)
class Index extends Component {
  config = {
    navigationBarTitleText: '下单结果',
    navigationStyle: 'custom',
  };

  constructor(props) {
    super(props);
    const { isVipProps } = props;
    this.state = {
      creditCodeMap: {
        10: '',
        20: `您是${CreditConfig.text}用户，享受先寄件，后付款服务，记得提醒快递员线上推送订单哦！`,
        30: '先寄件，后付款服务已关闭，订单已按快递员普通线下单提交成功。',
        40: '先寄件，后付款服务开启失败，订单已按快递员普通线下单提交成功。',
      },
      creditCode: '',
      switchCreditService: true,
      bars: [],
      isVip: isVipProps,
      isOpenInsertScreen: false,
      wkdSupportPrint: false,
    };
    if (process.env.PLATFORM_ENV == 'alipay') {
      //兼容芝麻分确认页面跳转进入时，页面参数的处理
      const { creditSource } = getLaunchParams(this);
      const { source } = this.$router.params;

      console.info('下单结果页.creditSource');
      console.info(creditSource);
      const LastOrderResultData = getStorageSync('LastOrderResultData').data;
      console.info('下单结果页.LastOrderResultData');
      console.info(LastOrderResultData);
      LastOrderResultData && removeStorage({ key: 'LastOrderResultData' });
      if (
        (creditSource == 'CreditSuccess' || creditSource == 'CreditFail') &&
        LastOrderResultData
      ) {
        console.info('下单结果页.执行二次确认逻辑');
        const { quotation } = LastOrderResultData || {};
        quotation && Taro.kbSetGlobalData('quotation', quotation);
        let params = this.$router.params || {};
        this.$router.params = {
          ...params,
          ...LastOrderResultData,
        };
        if (creditSource == 'CreditFail') {
          this.$router.params.wx_after_pay_bind = 0;
          orderCreditFail(this.$router.params.order_id);
        }
      }
      if (source == 'dak' || source == 'courier') {
        this.storageVisits = new StorageVisits({ key: 'order.result.subscribe' });
      }
    }
    //模拟参数
    // this.$router.params = {
    //   // source: 'brand',
    //   source: "courier",
    //   // source: "pay",
    //   label: "scan",
    //   status: 'credit',
    //   order_id: '666888',
    //   pageSource: 'tuihuo',//dh大货物流、tuihuo网购退货
    //   isReturnActivityNewUser: 1,
    //   // ordersNum: 3,
    //   brand: 'sto',
    //   platform: 'yjkd_brand',
    //   status: 'credit',
    //   cutPayDesc: '在包裹被收件人签收后',
    //   estimatedFee: '0',
    //   // collect_code: "K174000452",
    //   wx_after_pay: 0,
    //   wx_after_pay_bind: 0,
    // };
    // Taro.kbSetGlobalData('order_ids', [111, 222, 333]);
  }

  componentDidMount() {
    console.log('this.props', this.props);
    console.log('this.$router.params', this.$router.params);
    const { source, status, order_id, ordersNum, platform } = this.$router.params;
    Taro.setNavigationBarTitle({
      title: `${source == 'pay' || source == 'tcjs' ? '支付' : '下单'}结果`,
    });
    this.order_ids = Taro.kbGetGlobalDataOnce('order_ids') || [];
    let isBatch = ordersNum > 1;
    //处理支付分
    if (status == 'credit' && !isBatch) {
      if (platform == 'yjkd_brand') {
        this.getQuotationData();
      }
      this.handleCheckCreditService();
    }
    //处理分享
    if (source == 'dak' || ordersNum > 1) {
      Taro.hideShareMenu();
    }
    //检查支付赠优惠券逻辑
    if (source == 'pay') {
      this.getCouponData(order_id);
    }

    //检查广告逻辑
    this.showInterstitialAd();
    this.getOrderDetail().then((resData) => {
      const { wkdSupportPrint } = resData || {};
      this.setState({
        orderDetail: resData || {},
      });
      this.createBars({ supportPrint: wkdSupportPrint });
      this.triggerGetServiceData(resData);
    });

    this.props.dispatchGet();
    this.triggerGetAds();
  }

  componentDidShow() {
    if (this.CreditFlag) {
      this.CreditFlag = false;
      this.handleCheckCreditService();
    }
  }

  componentWillUnmount() {
    this.CreditFlag = false;
    removeStorageSync('btnType');
  }

  triggerGetServiceData = (resData) => {
    if (process.env.MODE_ENV === 'wkd') {
      const { source } = this.$router.params;
      getServiceList(resData, { orderType: source })
        .then(({ oService }) => {
          this.serviceData = oService;
        })
        .catch((err) => console.log(err));
    }
  };

  triggerGetAds = () => {
    // 优寄快递跳过广告
    const { platform } = this.$router.params;
    if (platform == 'yjkd_brand' || platform == 'yjkd_courier') return;
    if (this.state.isVip) return;
    // 获取详情按钮点击广告
    loadAdminAd(27).then((res) => {
      // 记录广告，点击时判断是否跳转
      this.adData = res[0];
    });
    // 获取导航栏返回按钮点击广告
    this.triggerGetAds_back();
    getHoldDay('order.result').then((res) => {
      this.setState({ limit: res });
    });
  };

  // 订单详情按钮点击跳转广告
  triggerClickAds = () => {
    if (this.state.isVip || !this.adData) return;
    adNavigator(this.adData);
  };

  createBars({ supportPrint = false }) {
    const { source, status, order_id, label } = this.$router.params;
    const { customer } = Taro.kbRelationInfo.data || {};
    const isTemporary = source === 'temporary' && status === 'start';
    const shareBtn = {
      key: 'share',
      openType: 'share',
      page: 'order.detail',
      info: {
        order_id,
        order_state: isTemporary ? 'temporary' : '',
      },
      label: isTemporary ? '邀请好友完善信息' : '分享给收件人',
    };
    const detailBtn = {
      key: order_id ? 'detail' : 'order_list',
      label: order_id ? '查看订单详情' : '查看订单列表',
      type: 'secondary',
    };
    const locationBtn = {
      key: 'location',
      label: '查看驿站位置',
    };
    const orderBtn = {
      key: 'order',
      label: '继续下单',
      type: 'secondary',
    };
    const printBtn = {
      key: 'print',
      label: '打印面单',
    };
    // 快递员也支持打印面单
    const is_print = !!(customer && customer.is_print == 1) || supportPrint;
    let bars = is_print ? [printBtn, shareBtn, detailBtn] : [shareBtn, detailBtn];
    switch (source) {
      case 'dak':
        bars = [locationBtn, detailBtn];
        break;
      case 'brand':
        bars = [detailBtn];
        break;
      case 'temporary':
        if (status == 'end') {
          bars = [detailBtn];
        }
        break;
    }
    if (!order_id) {
      bars = is_print ? [printBtn, detailBtn] : [detailBtn];
    }
    if (label == 'scan') {
      bars = [shareBtn, detailBtn, orderBtn];
    }
    this.setState({
      bars,
    });
  }

  getIntersitialAdTimer() {
    return new Promise((resolve) => {
      if (this.intersitialAdTimer) {
        resolve(this.intersitialAdTimer);
        return;
      }
      getStorage({
        key: STORAGE_AD_TIMER,
      })
        .then((res) => resolve(res ? res.data : null))
        .catch(() => resolve());
    });
  }

  getQuotationData() {
    /**
     * @description 获取报价单和福利券数据
     */
    const { brand } = this.$router.params;
    let quotation = {};
    let quotationData = Taro.kbGetGlobalDataOnce('quotationList') || [];
    if (quotationData && quotationData.length > 0) {
      quotation = quotationData.find((item) => item.brand == brand) || {};
    }
    this.quotation = quotation;
    this.setState({
      quotation,
    });
  }

  showInterstitialAd() {
    const { platform, hideInterstitialAd = '' } = this.$router.params;
    const { isVip } = this.state;
    //优寄、VIP快递员禁止插屏广告
    if (process.env.PLATFORM_ENV != 'weapp' || platform || isVip || hideInterstitialAd == 1) {
      return;
    }
    this.setState({
      isOpenInsertScreen: true,
    });
    //微信插屏广告
    createInterstitialAdWrap(() => {
      this.getIntersitialAdTimer().then((storageToday) => {
        let today = dayjs().format('YYYY-MM-DD');
        if (storageToday == today) {
          console.log('限制用户一天只能看一次插屏广告');
          return;
        }
        this.intersitialAd = Taro.createInterstitialAd({
          adUnitId: 'adunit-0b6279a459673f34',
        });
        this.intersitialAd.onLoad(() => {
          reportAnalytics({
            key: 'cpad_onload',
          });
          this.intersitialAd.show().catch((err) => {
            console.log('cpad_err', err);
            reportAnalytics({
              key: 'cpad_errormsg',
              errormsg: err.errMsg,
            });
          });
          this.intersitialAdTimer = today;
          setStorage({
            key: STORAGE_AD_TIMER,
            data: today,
          });
        });
      });
    });
  }

  getCouponData(order_id) {
    if (!order_id) return;
    request({
      url: '/v1/WeApp/getCoupon',
      data: {
        order_id,
      },
      onThen: ({ code, data }) => {
        if (code == 0 && data && data.cost > 0) {
          this.setState({
            couponData: data,
          });
        }
      },
    });
  }

  onHandleBarClick = ({ key }) => {
    const {
      order_id,
      source,
      status,
      label,
      longitude,
      latitude,
      inn_name: name,
      address,
      cancelOrder = '',
    } = this.$router.params;
    const { customer: { id: customer_id } = {} } = Taro.kbRelationInfo.data || {};
    const { orderDetail = {} } = this.state;
    switch (key) {
      case 'detail':
        let oType = source;
        if (source == 'temporary' && status == 'end') {
          oType = 'receive';
        }
        if (order_id) {
          console.info('下单结果页.跳转订单详情====>', cancelOrder);
          console.info(order_id);
          Taro.navigator({
            url: `order/detail?${qs.stringify({ order_id, type: oType, cancelOrder })}`,
            target: 'self',
          });
        } else {
          Taro.navigator({
            url: `order`,
            target: 'self',
          });
        }
        this.triggerClickAds();
        break;
      case 'location':
        Taro.openLocation({
          longitude: Number(longitude),
          latitude: Number(latitude),
          name,
          address,
        });
        break;
      case 'order_list':
        Taro.navigator({
          url: `order`,
          target: 'self',
        });
        break;
      case 'order':
        if (label == 'scan') {
          // 扫码寄再来一单直接调起扫描框
          scanCode({ mode: 'global' });
          return;
        }
        orderAction({
          action: 'edit',
        });
        break;
      case 'print':
        const newOrderDetail = {
          ...orderDetail,
          order_id: order_id ? [order_id] : this.order_ids,
          customer_id,
        };
        fillOrderArrivePayPrice(
          {
            action: 'gprint',
            data: { ...newOrderDetail, serviceData: this.serviceData },
            order_id,
          },
          () => {
            // 更新增值服务详情
            this.triggerGetServiceData(orderDetail);
          },
        ).then(() => {
          orderAction({
            action: 'gprint',
            data: newOrderDetail,
          });
        });
        break;
    }
  };

  onShareAppMessage = (e) => {
    return getShareAppMessage(e, {
      page: 'order.result',
      info: this.$router.params,
    });
  };

  getOrderDetail = () => {
    return new Promise((resolve) => {
      const { order_id, source: type } = this.$router.params;
      request({
        ...getApiUrlAndData({ order_id, type }),
        formatResponse: ({ data }, req) =>
          formatResponseOrderDetail({ data, orderType: type }, req),
        toastLoading: false,
        onThen: ({ code, data }) => {
          resolve(code == 0 && data ? data : null);
        },
      });
    });
  };

  getShareAppImage = () => {
    if (this.shareImage) {
      return this.shareImage;
    }
    return this.getOrderDetail().then((data) =>
      data
        ? Taro.kbDrawImage('order.detail', addOrderDetailInfo(data, { brands: Taro.brands })).then(
            (res) => {
              this.shareImage = res.tempFilePath;
              return res;
            },
          )
        : '',
    );
  };

  // 支付分相关
  handleCheckCreditService() {
    const { platform, wx_after_pay = 0, wx_after_pay_bind = 0 } = this.$router.params;
    if (platform == 'yjkd_brand' || platform == 'yjkd_courier') {
      this.dealCreditStatus({ wx_after_pay, wx_after_pay_bind });
      return;
    }
    checkCreditService().then((serviceStatus) => {
      if (serviceStatus) {
        this.checkOrderCreditStatus();
      } else {
        this.dealCreditStatus({ wx_after_pay: 0, wx_after_pay_bind: 0 });
      }
    });
  }
  checkOrderCreditStatus() {
    const { order_id } = this.$router.params;
    request({
      url: '/g_order_core/v2/mina/Payment/getWxPayAuthStatus',
      data: {
        order_id,
      },
      toastLoading: false,
      onThen: (res) => {
        if (res.code == 0) {
          const { authStatus } = res.data;
          this.dealCreditStatus({
            wx_after_pay: 1,
            wx_after_pay_bind: authStatus == 1 ? 1 : 0,
          });
        }
      },
    });
  }
  dealCreditStatus({ wx_after_pay = 0, wx_after_pay_bind = 0 }) {
    const { platform, pageSource, isReturnActivityNewUser } = this.$router.params;
    if (wx_after_pay <= 0) {
      //没授权支付分
      if (platform == 'yjkd_brand' || platform == 'yjkd_courier') {
        //没授权的用户,不用授权支付分了
        this.setState({
          creditCode: 11,
        });
        return;
      }
      this.setState({
        creditCode: 10,
      });
    } else if (wx_after_pay > 0 && wx_after_pay_bind <= 0) {
      //授权成功，绑单失败
      this.setState({
        creditCode: 40,
      });
    } else {
      //授权成功，绑单成功
      this.setState({
        creditCode: 20,
      });
      if (platform == 'yjkd_brand') {
        if (pageSource === 'tuihuo' && isReturnActivityNewUser == '1') {
          // 网购退货新用户赠会员
          this.setState({
            openGiveThVip: true,
          });
          return;
        }
        this.showYjkdTips();
      } else {
        this.showInterstitialAd();
      }
    }
  }
  handleOpenCredit = () => {
    const { order_id } = this.$router.params;
    openCreditService({ order_id }).then(() => {
      this.CreditFlag = true;
    });
  };
  handleCloseOrderWxCredit = () => {
    const { order_id } = this.$router.params;
    const { switchCreditService } = this.state;
    if (switchCreditService) {
      request({
        url: '/g_order_core/v2/mina/Payment/WxPayCancel',
        data: {
          order_id,
        },
        toastError: true,
        onThen: (res) => {
          if (res.code == 0) {
            this.setState({
              switchCreditService: false,
              creditCode: 30,
            });
          }
        },
      });
    }
  };

  continueReturns = () => {
    orderAction({
      action: 'edit',
      data: {
        editOrderType: 'th',
      },
    });
  };

  pageToVipHome = () => {
    Taro.navigator({
      url: 'user/member',
    });
  };

  pageToVipDetail = () => {
    Taro.navigator({
      url: 'user/member/right',
    });
  };

  showYjkdTips() {
    //优寄提示弹窗
    const { isActivity } = this.$router.params;
    if (isActivity == 1) {
      return;
    }
    this.setState({
      isOpenYjkdTips: true,
    });
  }

  //返回按钮广告
  triggerClickAds_back = () => {
    const {
      $scope: { route },
    } = getPage(-2);

    // 是否有挽留页判断返回几层
    const prevPage = route.includes('/order/result') ? 2 : 1;
    console.log('prevPage', prevPage);
    // 广告限制
    console.log('this.state.limit', this.state.limit);
    const Adlimit = this.state.limit === getNowDay();
    console.log('是否vip', this.state.isVip);
    console.log('是否存在返回按钮广告', this.adData_back);
    console.log('是否限制广告', Adlimit);

    if (this.state.isVip || !this.adData_back || Adlimit) {
      console.log('限制返回按钮广告-回调');
      Taro.navigateBack();
    } else {
      adNavigator({
        ...this.adData_back,
        delta: prevPage,
      });
      setTimeout(() => {
        setHoldDay('order.result');
      }, 1000);
    }
  };

  customBackEvent = () => {
    this.triggerClickAds_back();
  };

  triggerGetAds_back = () => {
    if (this.state.isVip) return;
    loadAdminAd(42).then((res) => {
      // 记录广告，点击时判断是否跳转
      console.log('adData_back', res[0]);
      this.adData_back = res[0];
    });
  };

  handleSubscribe = (res) => {
    const { code } = res;
    if (code === 0) {
      // 已订阅
      this.storageVisits.record();
      // 统计-订阅成功
      triggerRewardReportAnalytics({ action: 'subscribe' });
    }
  };

  render() {
    const {
      bars,
      creditCode,
      creditCodeMap,
      switchCreditService,
      couponData,
      isVip,
      isOpenInsertScreen,
      isOpenYjkdTips,
      quotation = {},
      openGiveThVip,
      ...rest
    } = this.state;
    const {
      source,
      status,
      label,
      ordersNum,
      platform,
      brand,
      pass_time,
      estimatedFee,
      cutPayDesc,
      collect_code,
      collection_amount,
      to_pay_amount,
      is_fresh,
      package_info,
      customer_id,
      isGiveVip = 0,
      pageSource,
    } = this.$router.params;
    const { brands } = this.props;
    let creditCodeDesc = creditCodeMap[creditCode];
    const oBrandData = (brands && brands[brand]) || {};
    // 引导开通支付分
    const isShowAuthCredit = label !== 'scan' && !customer_id;
    const shareBtnIndex = bars.findIndex((val) => val.key == 'share');
    const shareConfig = bars[shareBtnIndex] || {};
    const showShare = shareBtnIndex >= 0;
    const newBars = [...bars];
    const btnType = getStorageSync('btnType'); // 是否通过原价寄件进入

    showShare && newBars.splice(shareBtnIndex, 1);
    const is_member =
      quotation &&
      quotation.user_discount_status &&
      quotation.user_discount_status.includes('member_');
    return (
      <KbPage
        {...rest}
        navProps={{ onBack: this.customBackEvent }}
        renderHeader={
          <Fragment>
            {status == 'credit' && creditCodeDesc && source == 'courier' && (
              <KbNoticeBar
                renderMore={
                  <Fragment>
                    {creditCode == 20 && (
                      <View className='switch'>
                        <Switch
                          checked={switchCreditService}
                          onChange={this.handleCloseOrderWxCredit}
                          color='#099fff'
                        />
                        {!switchCreditService && <View className='switch-mask' />}
                      </View>
                    )}
                  </Fragment>
                }
              >
                {creditCodeDesc}
              </KbNoticeBar>
            )}
            <KbOfficialAccount navigateId='5' />
          </Fragment>
        }
      >
        {status == 'credit' && creditCode == 10 && isShowAuthCredit ? (
          <View className='kb-box kb-spacing-md kb-spacing-max-tb kb-margin-md'>
            <View className='at-row at-row__justify--center at-row__align--center'>
              <AtIcon
                prefixClass='kb-icon'
                value={CreditConfig.icon}
                className={`kb-size__xxl ${CreditConfig.colorClass}`}
              />
              <Text className='kb-size__base kb-margin-sm-l'>{CreditConfig.text}</Text>
            </View>
            <View className='kb-size__bold kb-spacing-max-tb kb-margin-lg-lr kb-border-b'>
              <View className='kb-text__center'>先寄后付</View>
              <View className='kb-text__center kb-margin-md-t'>订单完成后再付钱</View>
            </View>
            <View className='wxCredit-tips'>
              <View className='wxCredit-tips__item'>授权开通{CreditConfig.text}</View>
              <View className='wxCredit-tips__item'>订单完成后，再收取快递费</View>
            </View>
            <View>
              <AtButton
                className='wxCredit-btn'
                type='primary'
                size='small'
                circle
                onClick={this.handleOpenCredit}
              >
                开启{CreditConfig.text}
              </AtButton>
              <KbCreditLabel show='weapp' />
              <AtButton
                className='wxCredit-btn'
                type='secondary'
                size='small'
                circle
                onClick={this.onHandleBarClick.bind(this, { key: 'detail' })}
              >
                暂不开启，查看订单详情
              </AtButton>
            </View>
          </View>
        ) : (
          <View className='kb-res kb-box kb-spacing-md kb-spacing-max-tb kb-margin-md'>
            {source == 'yhj' && <View className='yhj-tag'>商家优惠寄</View>}
            <View className='kb-text__center'>
              <Image
                lazyLoad
                className='kb-res-img'
                src={
                  label == 'scan'
                    ? 'https://cdn-img.kuaidihelp.com/wkd/miniApp/package.png'
                    : 'https://cdn-img.kuaidihelp.com/wkd/miniApp/order-res.png'
                }
              />
            </View>
            <View className='kb-size__bold kb-text__center kb-margin-md-t kb-margin-lg-b'>
              {source == 'pay' ? (
                <View>订单支付成功！</View>
              ) : (
                <View>
                  {source === 'brand' && status == 'credit' && creditCode != 20 ? '线下' : ''}
                  订单提交成功！
                </View>
              )}
              {ordersNum && ordersNum > 0 && (
                <View className='kb-size__sm kb-color__brand kb-margin-sm-t'>
                  (已成功提交{ordersNum}单)
                </View>
              )}
              {status == 'credit' && creditCode == 20 && (
                <Fragment>
                  <KbCreditLabel
                    msg={process.env.PLATFORM_ENV == 'alipay' ? '' : '微信支付分先寄后付'}
                  />
                </Fragment>
              )}
            </View>
            {source === 'brand' && status == 'credit' && creditCode != 20 && (
              <View className='kb-res-offlineOrder'>
                <AtAvatar
                  circle
                  size='small'
                  image='https://upload.kuaidihelp.com/touxiang/counterman_.jpg'
                />
                <View className='desc'>寄件费用付款给快递员</View>
              </View>
            )}
            <View className='kb-res-tips'>
              {label == 'scan' ? (
                <Fragment>
                  <View class='kb-res-tips__item'>
                    <View>
                      请将 <Text className='kb-color__red'>&quot;包裹联&quot;</Text>{' '}
                      如图所示贴在包裹上
                    </View>
                    <View>快递员揽收后，可通过{PLATFORM_NAME}扫一扫扫码查件</View>
                  </View>
                </Fragment>
              ) : source == 'temporary' ? (
                <Fragment>
                  <View class='kb-res-tips__item'>
                    {status == 'start'
                      ? '快去邀请好友填写信息吧，完善信息后可自动生成订单'
                      : '您的收件信息已提交成功，等待快递员揽件'}
                  </View>
                  {status == 'start' && (
                    <View class='kb-res-tips__item'>
                      邀请好友填写的订单可至订单列表查看最新的订单状态哦~
                    </View>
                  )}
                  {status == 'start' && (
                    <View class='kb-res-tips__item'>
                      若好友暂时没有填写，也可自行去订单详情去填写哦！
                    </View>
                  )}
                </Fragment>
              ) : source == 'pay' ? (
                <Fragment>
                  <View class='kb-res-tips__item'>快递员会尽快为您处理包裹</View>
                  <View class='kb-res-tips__item'>如有问题可直接联系此快递员</View>
                </Fragment>
              ) : source == 'dak' ? (
                <Fragment>
                  <View class='kb-res-tips__item'>此驿站暂不支持上门取件</View>
                  <View class='kb-res-tips__item'>下单完成后，请前往驿站投递包裹</View>
                </Fragment>
              ) : source == 'brand' ? (
                status == 'credit' ? (
                  <Fragment>
                    {creditCode == 20 ? (
                      <Fragment>
                        {platform == 'yjkd_courier' ? (
                          <Fragment>
                            <View class='kb-res-tips__item'>
                              包裹发出后扣除运费
                              <Text className='kb-color__red'>￥{estimatedFee}元</Text>
                            </View>
                            <View class='kb-res-tips__item'>
                              若包裹实际重量超出，快递员线下收取续重费
                            </View>
                          </Fragment>
                        ) : (
                          <Fragment>
                            <View class='kb-res-tips__item'>无需支付运费给揽件快递员</View>
                            <View class='kb-res-tips__item'>
                              <Text className='kb-size__bold'>{cutPayDesc}，</Text>
                              自动从您的{PLATFORM_NAME}账户扣取相应运费。
                              {pageSource === 'dh' ? '未签收，则不产生任何计费。' : ''}
                            </View>
                          </Fragment>
                        )}
                      </Fragment>
                    ) : (
                      <Fragment>
                        {pageSource === 'tuihuo' && btnType === 'cancel' ? (
                          <Fragment>
                            <View class='kb-res-tips__item'>已将订单推送给{oBrandData.name}</View>
                            <View class='kb-res-tips__item'>请耐心等待快递员与您联系</View>
                          </Fragment>
                        ) : (
                          <Fragment>
                            <View class='kb-res-tips__item'>
                              已按线下订单推送给
                              {platform == 'yjkd_courier' ? '快递员' : oBrandData.name}
                              ，请耐心等待快递员与您联系
                            </View>
                            {creditCode == 11 ? (
                              <View className='kb-res-tips__item'>
                                由于您未开通{CreditConfig.text}
                                授权，导致您无法使用先寄后付尊享服务，需要由您线下支付运费给快递员
                              </View>
                            ) : (
                              <View className='kb-res-tips__item'>
                                由于您先寄后付订单超过数量上限，本单无法享受先寄后付服务，需要由您线下支付运费给快递员
                              </View>
                            )}
                          </Fragment>
                        )}
                      </Fragment>
                    )}
                  </Fragment>
                ) : (
                  <Fragment>
                    <View class='kb-res-tips__item'>
                      已将订单推送给{oBrandData.name || '快递公司'}
                    </View>
                    <View class='kb-res-tips__item'>请耐心等待快递员与您联系</View>
                    {pass_time > 0 && (
                      <View class='kb-res-tips__item'>
                        现已超出网点营业时间，快递员将于明日上门取件，敬请谅解！
                      </View>
                    )}
                  </Fragment>
                )
              ) : source == 'tcjs' ? (
                <Fragment>
                  <View class='kb-res-tips__item'>
                    订单已成功推送至${oBrandData.name}，请耐心等待配送员取件
                  </View>
                  <View class='kb-res-tips__item'>
                    若包裹实际重量超出，超重费用需由您线下支付配送员
                  </View>
                </Fragment>
              ) : source == 'yhj' ? (
                <Fragment>
                  <View class='kb-res-tips__item'>无需支付运费给揽件业务员</View>
                  <View class='kb-res-tips__item'>
                    订单受理后，请将取件码标志至您的包裹上，以便业务员揽收时核对；
                  </View>
                </Fragment>
              ) : status == 'equity' ? (
                <Fragment>
                  <View class='kb-res-tips__item'>订单已提交成功，请耐心等待快递员与您联系</View>
                  <View class='kb-res-tips__item'>
                    本次寄件使用了权益次卡，已抵扣首重运费（1KG内免费寄），若包裹重量超出，由快递员线下收取续重费
                  </View>
                </Fragment>
              ) : (
                <Fragment>
                  <View class='kb-res-tips__item'>已通知快递员，请耐心等待快递员与你取得联系</View>
                  <View class='kb-res-tips__item'>若此包裹比较紧急可直接电话联系快递员处理呦</View>
                  <View class='kb-res-tips__item'>等待快递员的过程可继续下单呦</View>
                </Fragment>
              )}
            </View>
            {label == 'scan' && status == 'credit' && creditCode == 10 ? (
              <View className='wxCredit-mini'>
                <View className='wxCredit-tips'>
                  <View className='wxCredit-tips__item'>授权开通{CreditConfig.text}</View>
                  <View className='wxCredit-tips__item'>订单完成后，再收取快递费</View>
                </View>
                <View>
                  <AtButton
                    className='wxCredit-btn'
                    type='primary'
                    size='small'
                    circle
                    onClick={this.handleOpenCredit}
                  >
                    开启{CreditConfig.text}
                  </AtButton>
                </View>
                <View>
                  <KbCreditLabel show='weapp' />
                </View>
              </View>
            ) : null}
            <View className='at-row at-row__justify--center'>
              {newBars &&
                newBars.map((item) => {
                  return (
                    <KbButton
                      key={item.key}
                      className='kb-margin-sm'
                      openType={item.openType}
                      type={item.type || 'primary'}
                      size='small'
                      circle
                      onClick={this.onHandleBarClick.bind(this, item)}
                      info={item.info}
                      page={item.page}
                    >
                      {item.label}
                    </KbButton>
                  );
                })}
            </View>
            {showShare && (
              <View className='kb-res__share'>
                <KbButton
                  key='share'
                  openType='share'
                  type='custom'
                  page={shareConfig.page}
                  info={shareConfig.info}
                  onClick={this.onHandleBarClick.bind(this, shareConfig)}
                >
                  <View>
                    <AtIcon
                      prefixClass='kb-icon'
                      value='share_result'
                      className='kb-size__base2 kb-color__brand'
                    />
                  </View>
                  <View className='kb-color__brand'>分享</View>
                </KbButton>
              </View>
            )}
          </View>
        )}
        {collect_code && (
          <View className='kb-navigator kb-navigator-noarrow kb-margin-md-lr'>
            <View className='kb-navigator__placeholder'>揽件码</View>
            <View className='kb-navigator__value'>K267228</View>
          </View>
        )}
        {collection_amount && (
          <View className='kb-navigator kb-navigator-noarrow kb-margin-md-lr'>
            <View className='kb-navigator__placeholder'>代收金额</View>
            <View className='kb-navigator__value'>{collection_amount}</View>
          </View>
        )}
        {to_pay_amount && (
          <View className='kb-navigator kb-navigator-noarrow kb-margin-md-lr'>
            <View className='kb-navigator__placeholder'>到付金额</View>
            <View className='kb-navigator__value'>{to_pay_amount}</View>
          </View>
        )}
        {is_fresh == 1 && (
          <View className='kb-navigator kb-navigator-noarrow kb-margin-md-lr'>
            <View className='kb-navigator__placeholder'>生鲜件</View>
            <View className='kb-navigator__value'>{package_info}</View>
          </View>
        )}
        {source == 'pay' && couponData && (
          <View className='kb-margin-md kb-margin-lg-t'>
            <View className='kb-size__base kb-color__greyer kb-margin-md-b'>
              <View className='kb-margin-sm-b'>
                额外惊喜，快递员{couponData.account_name}送您1张抵用
              </View>
              <View>券已放入您的账户，下次在线支付享立减</View>
            </View>
            <KbCouponCard card={couponData} />
          </View>
        )}
        {!isVip && (
          <View className='kb-margin-md'>
            <KbExternalAd adUnitIdIndex='order.result' />
          </View>
        )}
        <KbModal
          isOpened={isOpenYjkdTips}
          closeOnClickOverlay={false}
          closable={false}
          top3
          confirmText='我知道啦'
          onConfirm={() => {
            this.setState({
              isOpenYjkdTips: false,
            });
          }}
        >
          <View>
            <View
              className={`yjkd-tips__tips ${
                is_member ? 'yjkd-tips__item--tips' : ''
              } kb-size__sm kb-color__orange kb-margin-md-b`}
            >
              <Text className='kb-size__bold'>本单运费{cutPayDesc}，</Text>按实际重量从您的
              {PLATFORM_NAME}账户中扣除，不用线下支付。
              {quotation.desc ? `【${quotation.desc}】` : ''}
            </View>
            <View className='yjkd-tips__item'>
              本单运费：
              <Text className='value'>
                首重￥{quotation.f_fee || '--'}，续重￥{quotation.s_fee || '--'}/KG
              </Text>
            </View>
            <View className='yjkd-tips__item'>
              优寄补贴：<Text className='value'>￥{quotation.discount_total_amount || 0}</Text>
            </View>
            <View className='yjkd-tips__item'>
              预估费用：
              <Text className='value'>
                {quotation.price
                  ? `￥${quotation.price} - 优寄补贴￥${quotation.discount_total_amount || 0} = ￥${
                      quotation.discount_price
                    }`
                  : '--'}
              </Text>
            </View>
          </View>
        </KbModal>
        <KbAdInsertScreen isOpen={isOpenInsertScreen} adType='order.result' />
        <KbLinkService mode='float' />
        <KbCanvas />
        {process.env.MODE_ENV === 'wkd' && process.env.PLATFORM_ENV === 'alipay' ? (
          <GiveVipModal2 open={isGiveVip == 1} />
        ) : null}
        {process.env.MODE_ENV === 'wkd' && process.env.PLATFORM_ENV == 'weapp' ? (
          <GiveVipModal mode='th' open={openGiveThVip} />
        ) : null}

        {['courier', 'dak'].includes(source) && (
          <KbSubscribe
            action='order.result'
            onSubscribe={this.handleSubscribe}
            className='kb-display__none'
            auto
            sendAlways={false}
          />
        )}
      </KbPage>
    );
  }
}

export default Index;
