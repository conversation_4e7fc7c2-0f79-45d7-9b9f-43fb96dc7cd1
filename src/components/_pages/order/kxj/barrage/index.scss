/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.barrage {
  position: relative;
  height: 280px;
  margin-top: -920px;
  margin-bottom: 400px;
  &-container {
    display: flex;
    &-transition {
      transition: all 1s linear;
    }
  }
  &-item {
    display: flex;
    align-items: center;
    width: 500px;
    height: 58px;
    margin: 15px 30px;
    padding: 0 30px;
    color: #ffffff;
    font-size: 24px;
    white-space: nowrap;
    background: linear-gradient(88deg, #f64031, #f67142, #f64031);
    border-radius: 29px;
  }
  @keyframes scroll {
    0% {
      transform: translateX(0);
    }
    100% {
      transform: translateX(-120%);
    }
  }
}
