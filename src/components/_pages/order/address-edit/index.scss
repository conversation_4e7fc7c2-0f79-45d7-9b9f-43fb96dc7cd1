/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

$address-placeholder-height: 150px;
$address-border-radius: $border-radius-lg;

.kb-address {
  position: relative;
  overflow: hidden;
  border-radius: 10px;
  &-qiehuan {
    position: absolute;
    top: 50%;
    left: 10px;
    padding: $spacing-h-md;
    transform: translateY(-50%);
    .kb-icon-qiehuan {
      color: #b5b5b5;
      font-size: 28px;
    }
  }

  &-type {
    display: inline-block;
    width: 40px;
    height: 40px;
    overflow: hidden;
    line-height: 40px;
    text-align: center;
    border-radius: $border-radius-lg;
  }

  &__item {
    // overflow: hidden;
    background-color: $color-white;
    transition: all $animation-duration-slow ease-in-out;

    .item {
      &-bar {
        &__item {
          &--icon,
          &--bar {
            padding: 2 * $spacing-v-md $spacing-h-md;
            line-height: 1;

            &.kb-hover {
              border-radius: 0;
            }
          }

          &--icon::before {
            display: inline-block;
            width: 0;
            height: 100%;
            vertical-align: middle;
            content: '';
          }

          &--bar {
            display: flex;
            justify-content: center;
            box-sizing: border-box;
            padding: $spacing-v-xs $spacing-h-md;
            font-size: $font-size-base;
            line-height: 1;
            border: $width-base solid currentColor;

            &,
            &.kb-hover {
              border-radius: $border-radius-arc;
            }
          }

          &--bar,
          &:first-child::after {
            display: inline-block;
            vertical-align: middle;
          }

          &--lib::before,
          &:first-child::after {
            height: 45px;
            margin: 0 $spacing-h-md 0 2 * $spacing-h-md;
            border-right: $border-lightest;
            content: '';
          }

          &--lib {
            display: flex;
            align-items: center;
            padding-right: 2 * $spacing-h-md;
            padding-left: 0;

            &::before {
              margin: 0 2 * $spacing-h-md 0 0;
            }
          }
        }
      }

      &-info {
        align-items: stretch;
        word-break: break-all;

        &__text {
          margin-right: $spacing-h-md;

          &:last-child {
            margin-right: 0;
          }
        }

        &__placeholder {
          flex: 1;
          flex: 1;
          color: $color-grey-2;
          font-size: $font-size-lg;
          line-height: $address-placeholder-height;
        }

        &__value {
          display: flex;
          flex-direction: column;
          justify-content: center;
          box-sizing: border-box;
          min-height: $address-placeholder-height;
          padding: $spacing-v-md $spacing-h-md $spacing-v-md 0;

          &--desc {
            color: $color-grey-2;
            font-size: $font-size-base;
          }

          &--label {
            font-weight: bold;
          }
        }
      }

      &-container {
        border-bottom: $border-lightest;
      }

      &-icon {
        position: relative;
        padding: 0 $spacing-h-md;

        &__text {
          position: absolute;
          top: 50%;
          left: 50%;
          color: $color-white;
          font-size: $font-size-base;
          transform: translate(-50%, -60%);
        }
      }
    }

    &:first-child {
      border-radius: $address-border-radius $address-border-radius 0 0;
    }

    &-last,
    &:last-child {
      margin-bottom: 0;
      border-radius: 0 0 $address-border-radius $address-border-radius;
      .item {
        &-container {
          border-bottom: none !important;
        }
      }
    }

    &--send {
      .item {
      }
    }
  }

  &-spacing {
    .kb-hover {
      border-radius: $address-border-radius;
    }
  }

  &-spacing &__item {
    margin-bottom: $spacing-v-md;
    border-radius: $address-border-radius;

    .item {
      &-container {
        border-bottom: 0;
      }
    }
  }

  &__item--open {
    .item {
      &-bar {
        &__item {
          &--lib {
            padding-left: $spacing-h-md;

            &::before {
              display: none;
            }
          }
        }
      }
    }

    & .kb-hover {
      border-radius: $address-border-radius $address-border-radius 0 0;
    }
  }

  .kb-form {
    position: relative;

    &__item {
      &--ai {
        background-color: $color-grey-8;
        border-radius: $address-border-radius;
      }

      &--button {
        padding-top: 1.5 * $spacing-v-md;
        padding-bottom: 0.5 * $spacing-v-md;
        text-align: center;
      }

      &--save {
        height: 90px;
        line-height: 90px;
      }
    }

    &::before {
      position: absolute;
      top: 0;
      right: 0;
      left: $spacing-h-md;
      z-index: 1;
      height: $width-base;
      border-top: $border-lightest;
      content: '';
    }
  }

  .item-content__edit {
    border-top: $border-lightest;
  }
  .address-info-active {
    position: relative;
    &-0 {
      animation: down 0.5s linear;
    }
    &-1 {
      animation: up 0.5s linear;
    }
    @keyframes down {
      from {
        top: 0px;
      }
      to {
        top: 120px;
      }
    }
    @keyframes up {
      from {
        top: 0px;
      }
      to {
        top: -120px;
      }
    }
  }
}
.kb-background {
  &__orange {
    background-color: $color-orange;
  }
  &__brand {
    background-color: $color-brand;
  }
}
