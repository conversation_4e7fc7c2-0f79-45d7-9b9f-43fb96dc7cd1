/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { Component, Fragment } from '@tarojs/taro';
import { View, Image, Swiper, SwiperItem, Text } from '@tarojs/components';
import { AtIcon } from 'taro-ui';
import { get } from '@/actions/brands';
import { connect } from '@tarojs/redux';
import KbPage from '@base/components/page';
import KbLongList from '@base/components/long-list';
import KbModal from '@base/components/modal';
import {
  activityRulesList,
  createListData,
  createSubtractBars,
  getCouponCenterAds,
  createCouponList,
  getUserVipInfoData,
  handleAdClick,
  handleBarClick,
  handleRules,
  handleSubtractBarClick,
  handleVip,
  retentionUserToLQZX,
  showXLightAd,
} from '@/components/_pages/order/kxj/center/_utls';
import { dateCalendar } from '@base/utils/utils';
import classNames from 'classnames';
import { tabItemTapCall } from '@/components/_pages/_utils';
import './index.scss';

@connect(
  ({ global }) => ({
    brands: global.brands,
  }),
  {
    dispatchGet: get,
  },
)
class Index extends Component {
  config = {
    navigationBarTitleText: '领券中心',
    backgroundColorTop: '#f2f2f2',
  };

  constructor() {
    this.state = {
      list: [],
      isOpened: false,
      currentSubtractBar: 1,
      subtractBars: createSubtractBars(),
    };
    this.listData = createListData();
  }

  // 登录状态更新
  onUpdate = (res) => {
    if (res.logined) {
      this.props.dispatchGet();
      this.getCouponCenterAds();
      this.getUserVipInfoData();
    }
  };

  componentDidMount() {
    this.showXLightAd();
  }

  componentDidShow() {
    this.getUserVipInfoData();
  }

  onTabItemTap = tabItemTapCall;

  componentDidHide() {
    this.retentionUserToLQZX('add');
  }

  onReady(ins) {
    this.listIns = ins;
  }

  retentionUserToLQZX = retentionUserToLQZX;

  getUserVipInfoData = getUserVipInfoData;

  getCouponCenterAds = getCouponCenterAds;

  handleAdClick = handleAdClick;

  handleRules = handleRules;

  handleVip = handleVip;

  handleSubtractBarClick = handleSubtractBarClick;

  handleBarClick = handleBarClick;

  showXLightAd = showXLightAd;

  render() {
    const { brands = {} } = this.props;
    const {
      adList,
      list = [],
      isOpened,
      userVipData = {},
      subtractBars = [],
      currentSubtractBar = 1,
      ...rest
    } = this.state;
    const subtractList = createCouponList(list, { type: 'subtract', key: currentSubtractBar });
    const discountList = createCouponList(list, { type: 'discount' });
    return (
      <KbPage onUpdate={this.onUpdate.bind(this)} {...rest}>
        <KbLongList data={this.listData} enableMore={false} onReady={this.onReady.bind(this)}>
          {/* 轮播图 */}
          <View className='kb-couponCenter-swiper'>
            <Swiper className='swiper' autoplay circular indicatorDots={adList.length > 1}>
              {adList.map((item) => {
                return (
                  <SwiperItem className='swiper-item' key={item.id}>
                    <Image
                      className='swiper-img'
                      src={item.imgUrl}
                      onClick={this.handleAdClick.bind(this, item)}
                      hoverClass='kb-hover-opacity'
                    />
                  </SwiperItem>
                );
              })}
            </Swiper>
            <View
              className='rule'
              onClick={this.handleRules.bind(this, 'open')}
              hoverClass='kb-hover-opacity'
            >
              规则
            </View>
          </View>
          {/* 优寄会员引导 */}
          <View className='kb-couponCenter-vip' onClick={this.handleVip.bind(this)}>
            <Image
              className='kb-couponCenter-vip__img'
              src='https://cdn-img.kuaidihelp.com/wkd/miniApp/activity/center/img_vip.png'
            />
            <View className='kb-couponCenter-vip__body'>
              <View className='title'>
                {userVipData.status == 1 ? (
                  <Fragment>
                    优享寄<Text className='kb-color__red'>{userVipData.name}</Text>会员
                  </Fragment>
                ) : userVipData.status == 2 ? (
                  <Fragment>
                    续费限时特惠<Text className='kb-color__red'>5.1</Text>元
                  </Fragment>
                ) : (
                  <Fragment>
                    首开专享价低至<Text className='kb-color__red'>1.6</Text>元
                  </Fragment>
                )}
              </View>
              <View className='desc'>
                {userVipData.status == 1
                  ? `温馨提示：${dateCalendar(userVipData.expires_time, { timer: true })}到期`
                  : userVipData.status == 2
                  ? '会员寄件，享多重优惠'
                  : '优寄寄件 单单享返现'}
              </View>
            </View>
            <AtIcon className='kb-size__sm kb-color__grey' prefixClass='kb-icon' value='arrow' />
          </View>
          {/* 每日神券 */}
          <View className='kb-couponCenter-day'>
            <View className='kb-couponCenter-title'>
              <View>
                <View className='at-row at-row__align--center'>
                  <Image
                    className='img'
                    src='https://cdn-img.kuaidihelp.com/wkd/miniApp/activity/center/icon_coupon01.png'
                  />
                  <Text className='title'>每日神券</Text>
                </View>
              </View>
              <View className='desc'>开通芝麻分用户可用</View>
            </View>
            <View className='kb-couponCenter-day__box'>
              <View className='kb-couponCenter-day__block'>
                <View className='at-row at-row__align--center at-row__justify--center'>
                  <Image
                    className='img'
                    mode='widthFix'
                    src='https://cdn-img.kuaidihelp.com/wkd/miniApp/activity/center/icon_user01.png'
                  />
                  <Text className='user'>普通用户</Text>
                </View>
                <View className='desc'>
                  每日限领取<Text className='kb-color__red'>1</Text>张
                </View>
              </View>
              <View className='kb-couponCenter-day__block'>
                <View className='at-row at-row__align--center at-row__justify--center'>
                  <Image
                    className='img'
                    mode='widthFix'
                    src='https://cdn-img.kuaidihelp.com/wkd/miniApp/activity/center/icon_user02.png'
                  />
                  <Text className='user'>会员用户</Text>
                </View>
                <View className='desc'>
                  每日<Text className='kb-color__red'>不限量</Text>领券
                </View>
              </View>
            </View>
          </View>
          {process.env.PLATFORM_ENV === 'alipay' && (
            <View className='kb-margin-md-lr kb-margin-md-b'>
              {/* eslint-disable-next-line react/no-unknown-property */}
              <ad unit-id='ad_tiny_2017062807585646_202403062200084361' />
            </View>
          )}
          {/* 满减神券 */}
          <View className='kb-couponCenter-subtract'>
            <View className='kb-couponCenter-title'>
              <View className='at-row at-row__align--center'>
                <Image
                  className='img img2'
                  src='https://cdn-img.kuaidihelp.com/wkd/miniApp/activity/center/icon_coupon02.png'
                />
                <Text className='title'>满减神券</Text>
              </View>
              <View className='kb-couponCenter-subtract__tabs'>
                {subtractBars.map((item) => {
                  const itemCls = classNames('kb-couponCenter-subtract__tabItem', {
                    'kb-couponCenter-subtract__tabItem--active': currentSubtractBar == item.key,
                  });
                  return (
                    <View
                      className={itemCls}
                      key={item.key}
                      onClick={this.handleSubtractBarClick.bind(this, item)}
                      hoverClass='kb-hover'
                    >
                      {item.label}
                    </View>
                  );
                })}
              </View>
            </View>
            <View className='kb-couponCenter-subtract__list'>
              {subtractList.map((item) => {
                const brandData = brands[item.brand] || {};
                return (
                  <View className='kb-couponCenter-subtract__item' key={item.card_id}>
                    <View className='brand-tag'>
                      <Image className='img-brand' mode='widthFix' src={brandData.logo_link} />
                      {brandData.name}专享
                    </View>
                    <View className='content'>
                      <View className='content-price'>
                        <Text className='unit'>￥</Text>
                        {item.discount_fee}
                      </View>
                      <View className='content-desc'>满{item.min_freight}元可用</View>
                    </View>
                    <View className='footer'>
                      {item.is_receive == 1 ? (
                        <View
                          className='btn2'
                          onClick={this.handleBarClick.bind(this, 'edit', item)}
                          hoverClass='kb-hover'
                        >
                          去寄件
                          <Image
                            className='img-arrow'
                            mode='widthFix'
                            src='https://cdn-img.kuaidihelp.com/wkd/miniApp/activity/center/icon_arrow.png?v=1'
                          />
                        </View>
                      ) : (
                        <View
                          className='btn'
                          onClick={this.handleBarClick.bind(this, 'one', item)}
                          hoverClass='kb-hover'
                        >
                          立即领取
                        </View>
                      )}
                    </View>
                  </View>
                );
              })}
            </View>
            <View
              className='kb-couponCenter-btnAll'
              onClick={this.handleBarClick.bind(this, 'allSubtract')}
              hoverClass='kb-hover'
            >
              <Image
                className='tag'
                mode='widthFix'
                src='https://cdn-img.kuaidihelp.com/wkd/miniApp/activity/center/icon_vip.png?v=1'
              />
              一键领取全部满减券
            </View>
          </View>
          {/* 天天折扣 */}
          <View className='kb-couponCenter-discount'>
            <View className='kb-couponCenter-title'>
              <View>
                <View className='at-row at-row__align--center'>
                  <Image
                    className='img img2'
                    src='https://cdn-img.kuaidihelp.com/wkd/miniApp/activity/center/icon_coupon02.png'
                  />
                  <Text className='title'>天天折扣</Text>
                </View>
              </View>
              <View className='desc'>开通芝麻分用户可用</View>
            </View>
            <View className='kb-couponCenter-discount__list'>
              {discountList.map((item) => {
                const brandData = brands[item.brand] || {};
                return (
                  <View className='kb-couponCenter-discount__item' key={item.card_id}>
                    <View className='kb-couponCenter-discount__inner'>
                      <View className='left'>
                        <View className='price'>
                          {item.discount_fee}
                          <Text className='unit'>折</Text>
                        </View>
                        {item.min_weight > 0 && (
                          <View className='desc'>限重{item.min_weight}KG</View>
                        )}
                      </View>
                      <View className='right'>
                        <View className='title'>
                          {brandData.short_name}
                          {item.discount_fee}折券
                        </View>
                        <View className='text'>有效期至领券之日起，当天有效</View>
                        {item.message && <View className='tag'>{item.message}</View>}
                      </View>
                    </View>
                    {item.is_receive == 1 ? (
                      <View
                        className='kb-couponCenter-discount__btn kb-couponCenter-discount__btn2'
                        onClick={this.handleBarClick.bind(this, 'edit', item)}
                        hoverClass='kb-hover'
                      >
                        去寄件
                      </View>
                    ) : (
                      <View
                        className='kb-couponCenter-discount__btn'
                        onClick={this.handleBarClick.bind(this, 'one', item)}
                        hoverClass='kb-hover'
                      >
                        立即领取
                      </View>
                    )}
                  </View>
                );
              })}
            </View>
            <View
              className='kb-couponCenter-btnAll'
              onClick={this.handleBarClick.bind(this, 'allDiscount')}
              hoverClass='kb-hover'
            >
              <Image
                className='tag'
                mode='widthFix'
                src='https://cdn-img.kuaidihelp.com/wkd/miniApp/activity/center/icon_vip.png?v=1'
              />
              一键领取全部折扣券
            </View>
          </View>
          {/* 寄件服务 */}
          <Image
            className='kb-couponCenter-block'
            mode='widthFix'
            src='https://cdn-img.kuaidihelp.com/wkd/miniApp/activity/center/img_send.png?v=1'
          />
        </KbLongList>
        <KbModal
          title='活动说明'
          top={false}
          closable={false}
          confirmText='我知道了'
          isOpened={isOpened}
          onClose={this.handleRules.bind(this, 'close')}
          onCancel={this.handleRules.bind(this, 'close')}
          onConfirm={this.handleRules.bind(this, 'close')}
        >
          <View className='kb-couponCenter-rules-list'>
            {activityRulesList.map((item, index) => {
              return (
                <View className='kb-couponCenter-rules-list-item' key={item}>
                  <View className='kb-couponCenter-rules-list-index'>{index + 1}、</View>
                  <View className='kb-couponCenter-rules-list-content'>{item}</View>
                </View>
              );
            })}
          </View>
        </KbModal>
      </KbPage>
    );
  }
}

export default Index;
