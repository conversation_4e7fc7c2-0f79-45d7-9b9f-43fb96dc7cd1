/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { compatibleGetCurrentPage } from '@/components/_pages/_utils';
import { noop } from '@base/utils/utils';
import Taro, { Component } from '@tarojs/taro';

if (process.env.PLATFORM_ENV === 'alipay') {
  if (!my.canIUse('plugin') && !my.isIDE) {
    my.ap && my.ap.updateAlipayClient && my.ap.updateAlipayClient();
  }

  // 补充环境变量
  Taro.env = my.env;

  // 右上角复制链接
  Taro.onCopyUrl = my.onCopyUrl || noop;
  Taro.offCopyUrl = my.offCopyUrl || noop;

  // 支付宝订阅消息
  Taro.requestSubscribeMessage = my.requestSubscribeMessage
    ? (opts) => {
        return new Promise((resolve, reject) => {
          my.requestSubscribeMessage({
            entityIds: opts.tmplIds || [],
            thirdTypeAppId: opts.thirdTypeAppId,
            success: resolve,
            fail: reject,
          });
        });
      }
    : null;

  // 支付宝广告兼容
  // 激励广告
  Taro.createRewardedVideoAd = (opts) => {
    const rewardedAd = my.createRewardedAd(opts);
    return rewardedAd;
  };
  // 全屏、插屏广告
  Taro.createInterstitialAd = (opts) => {
    const interstitialAd = my.createInterstitialAd(opts);
    let runLoad = () => {};
    let runErr = () => {};
    const { load } = interstitialAd;
    interstitialAd.onLoad = (callback) => {
      runLoad = callback;
    };
    interstitialAd.onError = (callback) => {
      runErr = callback;
    };
    interstitialAd.load = () => {
      return load()
        .then(runLoad)
        .catch(() => runErr(new Error('广告拉取失败')));
    };
    return interstitialAd;
  };

  // 是否已收藏
  Taro.isCollected = () => {
    return new Promise((resolve) => {
      my.isCollected({
        success: (res) => {
          resolve(res);
        },
        fail: () => {
          resolve({ isCollected: false });
        },
      });
    });
  };
}

if (process.env.PLATFORM_ENV === 'weapp') {
  // 右上角复制链接
  Taro.onCopyUrl = wx.onCopyUrl;
  Taro.offCopyUrl = wx.offCopyUrl;
}

if (process.env.PLATFORM_ENV === 'swan') {
  require('@/styles/diff/index.swan.scss');
  Component.options = { addGlobalClass: true }; // 允许组件全局样式
  class ComponentExtend extends Component {
    constructor() {
      super();
      Promise.resolve().then(() => {
        if (this.$router.path) {
          compatibleGetCurrentPage(this);
        }
      });
    }
  }
  Taro.Component = ComponentExtend;
}
