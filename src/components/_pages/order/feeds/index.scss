/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb-adFeeds {
  width: 750px;
  min-height: 600px;
  margin-top: -10px;
  background: #ffffff;
  border-radius: 20px 20px 0px 0px;
  &-tab {
    display: flex;
    align-items: center;
    justify-content: space-around;
    height: 100%;
    background: #ffffff;
    border-radius: 20px 20px 0 0;
    &--item {
      color: #999999;
      font-weight: 500;
      font-size: 28px;
    }
    &--active {
      position: relative;
      color: #333333;
      font-weight: bold;
      font-size: 34px;
      &::after {
        position: absolute;
        bottom: -10px;
        left: 50%;
        width: 36px;
        height: 6px;
        margin-left: -18px;
        background: #009fff;
        border-radius: 3px;
        content: '';
      }
    }
    &--holder {
      height: 90px;
    }
    &--box {
      height: 90px;
    }
  }
  &-listBox {
    height: 100%;
  }
  &-list {
    display: flex;
    padding: 0 20px 40px;
  }
  &-col {
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    width: 50%;
    padding: 0 15px;
  }
  &-item {
    box-sizing: border-box;
    width: 330px;
    margin-top: 20px;
    overflow: hidden;
    border-radius: 20px;
    .img {
      display: block;
      width: 100%;
    }
  }
}
