/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb-guideCouponModal {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  &-tips {
    position: relative;
    width: 440px;
    height: 57px;
    margin-bottom: 40px;
    color: #ff4c4c;
    font-size: 26px;
    line-height: 57px;
    text-align: center;
    background: #fff4f4;
    border: $width-base solid #ff4c4c;
    border-radius: 50px;
    &::after {
      position: absolute;
      bottom: -12px;
      left: 50%;
      width: 20px;
      height: 20px;
      background: red;
      background: #fff4f4;
      border: $width-base solid #ff4c4c;
      border-top-color: #fff4f4;
      border-left-color: #fff4f4;
      transform: translateX(-50%) rotate(45deg);
      content: '';
    }
  }
  &-coupon {
    position: relative;
    box-sizing: border-box;
    width: 280px;
    height: 280px;
    margin-bottom: 20px;
    &--img {
      width: 100%;
      height: 100%;
    }
    &--fee {
      position: absolute;
      top: 60px;
      right: 0;
      left: 0;
      color: #878787;
      font-weight: 800;
      font-size: 32rpx;
      text-align: center;
      .num {
        font-size: 70px;
      }
    }
    &--desc {
      position: absolute;
      right: 0;
      bottom: 40px;
      left: 0;
      color: #fef8f2;
      font-weight: bold;
      font-size: 32rpx;
      text-align: center;
    }
  }
}
