/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

$color_red: #ff4c4c;
$color_brand: #139bfb;

.kb-card {
  position: relative;
  margin-bottom: $spacing-h-md;
  overflow: hidden;
  border-radius: $border-radius-lg;

  &__group {
    padding: $spacing-v-md $spacing-h-md;
  }

  &__group & {
    margin-bottom: $spacing-v-md;

    &:last-child {
      margin-bottom: 0;
    }
  }

  &__checked {
    border: $width-base solid $color_brand;
  }

  &__checkbox {
    position: absolute;
    top: -20px;
    right: -20px;
    z-index: 1;
    width: 60px;
    height: 60px;
    color: $color-white;
    background-color: $color-grey-3;
    border-radius: 40px;
    transform: rotate(45deg);
    transform-origin: center;

    &::before {
      position: absolute;
      bottom: 12px;
      left: 23px;
      font-size: 19px;
      font-family: 'kb-icon';
      transform: rotate(-45deg);
      content: '\e625';
    }

    &--checked {
      color: $color-white;
      background-color: $color_brand;
    }
  }

  &__info {
    position: relative;
    height: 234px;
    color: $color-white;
    background: $color-white;
    &--wrapper {
      height: 168px;
    }
    &--content {
      display: flex;
      flex: 1;
      align-items: center;
      justify-content: space-between;
    }
    &--name {
      position: relative;
      position: relative;
      display: flex;
      flex: 1;
      flex-direction: column;
      justify-content: space-around;
      box-sizing: border-box;
      height: 100%;
      padding: $spacing-h-md $spacing-h-md $spacing-h-sm $spacing-h-md;
      color: $color-black-1;
      text-align: left;
      background-color: $color-white;
    }

    &--btn {
      box-sizing: border-box;
      min-width: 150px;
      height: 48px;
      color: $color_red;
      font-weight: bold;
      font-size: 24px;
      line-height: 48px;
      text-align: center;
      border: $width-base solid $color_red;
      border-radius: $border-radius-arc;
      &__count {
        color: $color_brand;
        border-color: $color_brand;
      }
    }

    // border-left: $width-base dashed rgba(255, 255, 255, 0.2);
    &--use {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 188px;
      height: 100%;
      text-align: center;
      &__center {
        display: inline-block;
        height: 100%;
        line-height: 180px;
      }
    }
    &--footer {
      position: relative;
      height: 66px;
      font-size: $font-size-xs + 2px;
      line-height: 66px;
      border-top: $width-base dashed $color-grey-8;
      border-top: $width-base dashed $color-grey-8;
    }
    &--title {
      display: flex;
      &-name {
        display: inline-block;
        margin-right: $spacing-h-sm;
        font-size: $font-size-xl - 4px;
        vertical-align: middle;
      }
      &-tag {
        padding: 5px 10px;
        color: $color_red;
        font-size: $font-size-sm - 2px;
        background: #ffeded;
        border-radius: $border-radius-lg;
      }
    }

    &--desc {
      padding-top: 5px;
      color: #999;
      font-size: 22px;
    }

    &--money,
    &--count {
      display: inline-block;
      width: 100%;
      height: 100%;
      color: $color-brand;
      font-weight: bold;
      font-size: 2 * $font-size-lg - 2px;
      text-align: center;
      vertical-align: middle;
      background: rgba(19, 155, 251, 0.06);
    }
    &--money {
      position: relative;
      &-count {
        font-size: $font-size-xs;
        line-height: 25px;
        text-align: center;
      }
      &::before {
        font-size: $font-size-xs + 2px;
        line-height: 10px;
        content: '￥';
      }
      &-coupon {
        position: relative;
        width: 100%;
        height: 100%;
        color: $color_red;
        background: rgba(255, 76, 76, 0.06);
        &::after {
          position: absolute;
          top: 0;
          left: 0;
          display: block;
          width: 108px;
          height: 32px;
          color: $color-white;
          font-size: $font-size-xs;
          line-height: 32px;
          text-align: center;
          background-color: $color_red;
          border-bottom-right-radius: $border-radius-lg;
          content: '优惠券';
        }
      }
    }

    &--count {
      position: relative;
      &::before {
        position: absolute;
        top: 0;
        left: 0;
        display: block;
        width: 108px;
        height: 32px;
        color: $color-white;
        font-size: $font-size-xs;
        line-height: 32px;
        background: $color-brand;
        border-bottom-right-radius: $border-radius-lg;
        content: '权益次卡';
      }
      &::after {
        font-size: $font-size-xs;
        line-height: 25px;
        content: '次';
      }
    }

    &--expired_img {
      width: 106px;
      height: 66px;
    }
    &--coupon_num {
      padding: 5px 10px;
      color: $color_red;
      font-size: 18px;
      background: rgba(255, 76, 76, 0.06);
      border-radius: 4px;
    }

    &--fee {
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
      color: $color_red;
      background: rgba(255, 76, 76, 0.06);
      &-block {
        display: flex;
        align-items: baseline;
        justify-content: center;
      }
      &-money {
        font-weight: bold;
        font-size: 2 * $font-size-lg - 2px;
      }
      &-unit {
        font-size: $font-size-xs + 2px;
        line-height: 10px;
      }
      &-tag {
        position: absolute;
        top: 0;
        left: 0;
        display: block;
        width: fit-content;
        height: 32px;
        padding: 0 20px;
        color: $color-white;
        font-size: $font-size-xs;
        line-height: 32px;
        text-align: center;
        background-color: $color_red;
        border-bottom-right-radius: $border-radius-lg;
      }
      &-tag2 {
        margin-top: -10px;
        color: $color_red;
        font-size: 20px;
      }
    }
    &--tag {
      width: fit-content;
      padding: $spacing-h-xs $spacing-h-md;
      color: $color-blue;
      font-size: $font-size-xs;
      line-height: 1;
      background-color: $color-grey-4;
      border-radius: $border-radius-arc;
    }
  }

  &__footer {
    display: none;
    overflow: hidden;
    color: $color-grey-2;
    font-size: $font-size-base;
    background-color: $color-white;
    &--bar {
      padding: 7px $spacing-h-md $spacing-h-md $spacing-h-md;
      color: $color-grey-1;
      &__icon {
        position: relative;
        font-size: $font-size-xs + 2px;
        &::after {
          position: absolute;
          right: -30px;
          display: inline-block;
          color: $color-grey-0;
          font-family: 'kb-icon';
          transform: rotate(90deg);
          transition: transform $animation-duration-slow;
          content: '\e620';
        }
      }
    }

    &--detail {
      height: 0;
      overflow: hidden;
      color: $color-grey-2;
      background-color: $color-white;
      transform: translateY(-100%);
      transition: transform $animation-duration-slow;
    }

    &--open {
      display: block !important;
      color: $color-grey-0;
      &--rotate {
        &::after {
          transform: rotate(270deg) !important;
        }
      }
    }

    &--open &--detail {
      height: auto;
      padding: 0 $spacing-h-md $spacing-v-md $spacing-h-md;
      transform: translateY(0);
    }
  }

  &__alert {
    line-height: 1;
  }

  &__color--orange &__info {
    background-image: linear-gradient(to right, #fd535a, #fdb581);
  }

  &__disabled {
    &-wxCredit {
      padding: 0 $spacing-h-sm $spacing-h-sm;
      background: #f8a3a3;
      &_row {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: $spacing-h-md 0;
        color: $color-white;
        font-size: $font-size-base;
        &_action {
          display: flex;
          align-items: center;
        }
      }
    }
    .kb-card__info {
      color: #ccc !important;
      background-image: linear-gradient(to right, #fff, #fff);
      -webkit-filter: grayscale(100%); /* Chrome, Safari, Opera */
      filter: grayscale(100%);
      .kb-card__info--fee {
        color: #ccc !important;
      }
      .kb-card__info--fee-tag {
        color: #fff;
        background: #ccc;
      }
      .kb-card__info--desc {
        .kb-color__grey {
          color: #ccc !important;
        }
      }
      .kb-card__info--title-name {
        color: #ccc !important;
      }
      .kb-card__info--btn {
        color: #ccc !important;
        border-color: #ccc !important;
      }
    }
    .kb-card__info--footer {
      color: #ccc !important;
    }
    .kb-card__footer--bar {
      color: #ccc !important;
    }
    .kb-card__footer--detail {
      color: #ccc !important;
    }
    .kb-card__footer--bar__icon::after {
      color: #ccc !important;
    }
  }

  &__disabled &__checkbox {
    display: none;
  }

  .kb-background__white {
    background: $color-white;
  }
}
.kb-invalid-text {
  padding-right: $spacing-h-md;
  text-align: right;
}

.kb-block {
  display: block;
}

.kb-size__xxs {
  font-size: 16px;
}
