/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { save } from '@base/actions/global';
import { useDidShowCom, useUpdate } from '@base/hooks/page';
import request from '@base/utils/request';
import { getPage } from '@base/utils/utils';
import { useDispatch, useSelector } from '@tarojs/redux';
import Taro from '@tarojs/taro';

/**
 *
 * @description 获取关注公众号配置
 */
export const getOfficialConfig = (isFLS) => {
  if (process.env.MODE_ENV === 'wkd') {
    const page = getPage(-1);
    const { $router: { path = '' } = {} } = page || {};
    const pathMap = {
      order: isFLS
        ? {
            h5: 'https://kd1.cn/gz1-1',
            name: '微快递助手-订单列表',
          }
        : {
            h5: 'https://kd1.cn/gz1-2',
            name: '订单列表',
          },
      'order/edit/send': isFLS
        ? {
            h5: 'https://kd1.cn/gz1-1',
            name: '微快递助手-寄快递页',
          }
        : {
            h5: 'https://kd1.cn/gz1-2',
            name: '寄快递页',
          },
      'order/result': isFLS
        ? {
            h5: 'https://kd1.cn/gz1-1',
            name: '微快递助手-下单结果页',
          }
        : {
            h5: 'https://kd1.cn/gz1-2',
            name: '下单结果页',
          },
      'order/detail': isFLS
        ? {
            h5: 'https://kd1.cn/gz1-1',
            name: '微快递助手-订单详情页',
          }
        : {
            h5: 'https://kd1.cn/gz1-2',
            name: '订单详情页',
          },
      query: isFLS
        ? {
            h5: 'https://kd1.cn/gz1-1',
            name: '微快递助手-查快递',
          }
        : {
            h5: 'https://kd1.cn/gz1-2',
            name: '查快递',
          },
      'query/detail': isFLS
        ? {
            h5: 'https://kd1.cn/gz1-1',
            name: '微快递助手-查件结果页',
          }
        : {
            h5: 'https://kd1.cn/gz1-2',
            name: '查件结果页',
          },
    };
    const pathMatch = path.match(/pages\/(.+)\/index/);
    const key = pathMatch ? pathMatch[1] : 'order';
    return {
      ...pathMap.order,
      ...pathMap[key],
    };
  }
  return {
    h5: 'https://mp.weixin.qq.com/s/n-FDyzu9RCJpWnIY-zuR1g',
    name: '关注公众号',
  };
};

// 获取是否已关注
let followStatusLock = false;
export function getFollowStatus(opt) {
  const { refresh } = opt || {};
  return new Promise((resolve, reject) => {
    if (!refresh) {
      if (followStatusLock) {
        reject();
        return;
      }
      followStatusLock = true;
    }
    request({
      url:
        process.env.MODE_ENV === 'wkd'
          ? '/g_wkd/v2/Account/isFocusWkdGzh'
          : '/api/weixin/mini/user/Bind/followStatus',
      toastLoading: false,
      onThen: ({ data }) => {
        const {
          follow_status = '1',
          is_focus = follow_status == '1',
          gzh_name = process.env.MODE_ENV == 'wkd'
            ? '微快递'
            : process.env.MODE_ENV !== 'third.post'
            ? '快宝驿站'
            : '中邮驿站',
          follow_url,
        } = data || {};
        resolve({ is_focus, name: gzh_name, follow_url });
      },
    });
  });
}

/**
 *
 * @description 获取关注状态
 */
export function useGetFollowStatus() {
  const dispatch = useDispatch();
  const { loginData = {} } = useSelector((state) => state.global);
  const unlockGet = () => {
    followStatusLock = false;
  };
  const triggerUpdateFollowStatus = () => {
    if (!loginData.logined) return;
    getFollowStatus().then((followAccount) => {
      dispatch(save({ followAccount }));
    });
  };
  useUpdate(triggerUpdateFollowStatus);
  useDidShowCom(triggerUpdateFollowStatus);
  return [unlockGet];
}

export const gzhMap = {
  wkdzs: {
    name: '微快递助手',
    dataIndex: 'wt_weixin_205',
    img: 'https://cdn-img.kuaidihelp.com/wkd/gzh_assistant.png?v=********',
    img2: 'https://cdn-img.kuaidihelp.com/wkd/gzh/wkdzs.png?v=********',
  },
  wkdhy: {
    name: '微快递会员号',
    dataIndex: 'wt_weixin_162',
    img: 'https://cdn-img.kuaidihelp.com/wkd/gzh_huiyuan.png?v=********',
    img2: 'https://cdn-img.kuaidihelp.com/wkd/gzh/wkdhy.png?v=********',
  },
  wkd: {
    name: '微快递',
    dataIndex: 'weixin',
    img: 'https://cdn-img.kuaidihelp.com/wkd/gzh_wkd.png?v=********',
  },
  yz: {
    name: '快宝驿站',
    dataIndex: 'weixin_shop_5',
    img: 'https://cdn-img.kuaidihelp.com/wkd/miniApp/gzh/yz.png?v=********',
  },
};

// 获取是否已关注__微信微快递福利社公众号
let followStatusLock_FLS = false;
function getFollowStatus_FLS(key) {
  return new Promise((resolve) => {
    if (followStatusLock_FLS) return;
    followStatusLock_FLS = true;
    if (process.env.MODE_ENV === 'wkd' && process.env.PLATFORM_ENV == 'weapp') {
      request({
        url: '/g_wkd/v2/Account/platformUserSubscribeStatus',
        toastLoading: false,
        onThen: ({ data, code }) => {
          if (code > 0) {
            followStatusLock_FLS = false;
          }
          resolve(code == 0 && data[gzhMap[key].dataIndex] == 1);
          // const {
          //   wt_weixin_205微快递助手
          //   wt_weixin_162 = "1",
          //   isFollowAccount_FLS = wt_weixin_162 == "1"
          // } = data||{}
          // resolve(isFollowAccount_FLS);
        },
      });
    } else {
      // 除微快递微信端外，其他端默认关注福利社
      resolve(true);
    }
  });
}

/**
 *
 * @description 获取关注状态__微信微快递福利社公众号
 */
export function useGetFollowStatus_FLS(key) {
  const dispatch = useDispatch();
  const { loginData = {} } = useSelector((state) => state.global);
  const unlockGet_FLS = () => {
    followStatusLock_FLS = false;
  };
  const triggerUpdateFollowStatus = () => {
    if (!loginData.logined) return;
    getFollowStatus_FLS(key).then((isFollowAccount) => {
      dispatch(save({ isFollowAccount_FLS: isFollowAccount }));
    });
  };
  useUpdate(triggerUpdateFollowStatus);
  useDidShowCom(triggerUpdateFollowStatus);
  return [unlockGet_FLS];
}

/**
 * @description 是否符合原生关注公众号组件展示条件
 */
export function isShowOfficialAccount() {
  const { scene } = Taro.getLaunchOptionsSync();
  const sceneArr = [1011, 1017, 1025, 1047, 1124];
  return sceneArr.includes(scene);
}

/**
 * 获取各种公众号订阅状态
 * todo: 以上老的获取公众号订阅的方式都是一个公众号一个接口，需要统一处理，逐步实现吧
 */
// 初始化公众号关注状态
export const initGzhStatus = {
  wkd: true,
  wkdzs: true,
  wkdhy: true,
  yz: true,
};
// 获取是否关注的状态
let followGZHStatusLock = false;
function getFollowGZHStatus(key) {
  return new Promise((resolve) => {
    if (followGZHStatusLock) return;
    followGZHStatusLock = true;
    if (process.env.MODE_ENV === 'wkd' && process.env.PLATFORM_ENV == 'weapp') {
      request({
        url: '/g_wkd/v2/Account/platformUserSubscribeStatus',
        toastLoading: false,
        onThen: ({ data, code }) => {
          if (code > 0) {
            followGZHStatusLock = false;
          }
          const getKey = (k) => (k && gzhMap[k] && gzhMap[k].dataIndex) || '';
          // 微快递公众号、或其他gzhMap配置的公众号
          const GzhStatus = { ...initGzhStatus };
          // 微快递公众号
          if (key && gzhMap[key] && gzhMap[key].dataIndex) {
            // todo: 此处为骚操作，后续应该被废除，这里暂时做兼容处理
            GzhStatus.wkd = data && data[getKey(key)] == '1';
          } else {
            GzhStatus.wkd = data && data[getKey('wkd')] == '1';
          }
          // 微快递助手公众号
          GzhStatus.wkdzs = data && data[getKey('wkdzs')] == '1';
          // 微快递会员公众号
          GzhStatus.wkdhy = data && data[getKey('wkdhy')] == '1';
          // 快宝驿站公众号
          GzhStatus.yz = data && data[getKey('yz')] == '1';
          resolve(GzhStatus);
        },
      });
    } else {
      resolve(initGzhStatus);
    }
  });
}
// 获取关注公众号hooks
export function useGetFollowGZHStatus(key) {
  const dispatch = useDispatch();
  const { loginData = {} } = useSelector((state) => state.global);
  const unlockGetGZH = () => {
    followGZHStatusLock = false;
  };
  const triggerUpdateFollowStatus = () => {
    if (!loginData.logined) return;
    getFollowGZHStatus(key).then((d) => {
      dispatch(save({ followGZHStatus: d }));
    });
  };
  useUpdate(triggerUpdateFollowStatus);
  useDidShowCom(triggerUpdateFollowStatus);
  return {
    unlockGetGZH,
  };
}
