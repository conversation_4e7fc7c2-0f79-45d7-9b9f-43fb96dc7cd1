/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

$z-index-guide: 9000;

.kb-businessCoupon-guide {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: $z-index-guide + 1;
  color: #fff;

  .img {
    width: 100%;
    height: 100%;
  }
  .opt {
    position: absolute;
    bottom: 120px;
    left: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    &-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 64px;
      font-size: 28px;
      border-radius: 32px;
      &-cancel {
        width: 168px;
        color: #ffffff;
        border: 2px solid #ffffff;
      }
      &-next {
        width: 248px;
        margin-left: 30px;
        color: #157eff;
        background: #ffffff;
      }
    }
  }

  &-next01 {
    position: absolute;
    right: 20px;
    bottom: 0;
    width: 624px;
    height: 436px;
  }

  &-next02 {
    position: absolute;
    top: 300px;
    left: 40px;
    width: 630px;
    height: 510px;
    .opt {
      bottom: 220px !important;
      left: 210px !important;
    }
  }

  &-next03 {
    position: absolute;
    top: 500px;
    left: 40px;
    width: 636px;
    height: 476px;
  }

  &-mask {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: $z-index-guide;
    background: rgba(0, 0, 0, 0.8);
    transform: scale3d(1, 1, 0);
    opacity: 0;
    transition: all 0.3s ease-in-out;
    pointer-events: none;

    &__show {
      transform: scale3d(1, 1, 1);
      opacity: 1;
      pointer-events: all;
    }
  }
}
