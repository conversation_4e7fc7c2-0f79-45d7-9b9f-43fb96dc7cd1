/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb-selectBox-item {
  margin: $spacing-h-md;
  padding: $spacing-h-md;
  padding-top: 0;
  background: #ffffff;
  border-radius: 10px;
  .title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-sizing: border-box;
    height: 98px;
    color: #333333;
    font-size: 30px;
    border-bottom: $width-base solid #eaeaea;
    &-text {
      display: flex;
      align-items: center;
    }
    &-description {
      color: #999999;
      font-weight: 500;
      font-size: 24px;
    }
    &-extra {
      color: #999999;
      font-size: 24px;
    }
  }
  .list {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -10px;
    padding-top: $spacing-h-md;
    &-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 186px;
      margin: 10px;
      padding: $spacing-h-sm;
      color: #666666;
      font-weight: 500;
      font-size: 28px;
      background: #ffffff;
      border: $width-base solid #eaeaea;
      border-radius: 6px;
      &--active {
        position: relative;
        color: $color-brand;
        border-color: $color-brand;
        &::before {
          position: absolute;
          top: 0;
          right: 0;
          width: 32px;
          height: 30px;
          background: $color-brand;
          border-bottom-left-radius: 30px;
          content: '';
        }
        &::after {
          position: absolute;
          top: 2px;
          right: 3px;
          width: 12px;
          height: 6px;
          border: $width-base solid #fff;
          border-top-color: transparent;
          border-right-color: transparent;
          transform: rotate(-45deg);
          content: '';
        }
      }
      .img {
        width: 130px;
        height: 130px;
        margin-bottom: 10px;
      }
    }
  }
}
