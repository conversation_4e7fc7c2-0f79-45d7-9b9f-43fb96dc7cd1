/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

$color-economy: #1480ff;

.economy {
  font-weight: 500;
  &-header {
    position: relative;
    width: 100%;
    height: 1691px;
    overflow: hidden;
    background-image: url('https://cdn-img.kuaidihelp.com/wkd/miniApp/economy/header.png?t=03');
    background-size: 100% auto;
    &_operate {
      position: absolute;
      top: 240px;
      right: 0;
      width: 38px;
      height: 80px;
      margin: 0;
      padding: 0;
      color: $color-economy;
      font-size: $font-size-sm;
      line-height: 1.2;
      white-space: pre-wrap;
      text-align: center;
      background-color: #fff;
      border: none;
      border-radius: 10px 0px 0px 10px;
      opacity: 0.8;
      &.exp {
        top: calc(240px + 80px + 20px);
      }
    }
    &-swiper {
      position: absolute;
      top: 500px;
      left: 0;
      width: 100%;
      height: 80px;
      &-item {
        height: 80px;
      }
    }
    &-title {
      position: absolute;
      top: 1120px;
      left: 50%;
      color: $color-black-1;
      font-weight: bold;
      font-size: 42px;
      white-space: nowrap;
      transform: translateX(-50%);
    }
    &-btn {
      position: absolute;
      bottom: 80px;
      left: 50%;
      transform: translateX(-50%);
    }
  }
  &-brand {
    position: relative;
    width: 100%;
    height: 1527px;
    margin-top: -1px;
    background-image: url('https://cdn-img.kuaidihelp.com/wkd/miniApp/economy/brand_1.png?t=03');
    background-size: 100% auto;
    &-operate {
      position: absolute;
      top: 580px;
      left: 50%;
      box-sizing: border-box;
      width: 100%;
      transform: translateX(-50%);
      &_bottom {
        top: unset;
        bottom: 120px;
      }
    }
    &_bottom {
      height: 1484px;
      background-image: url('https://cdn-img.kuaidihelp.com/wkd/miniApp/economy/brand_2.png?t=04');
      background-size: 100% auto;
      .economy-brand-operate {
        top: 540px;
        &_bottom {
          top: unset;
          bottom: 120px;
        }
      }
    }
  }
  &-vip {
    position: relative;
    width: 100%;
    height: 1413px;
    margin-top: -1px;
    background-image: url('https://cdn-img.kuaidihelp.com/wkd/miniApp/economy/vip.png?t=03');
    background-size: 100% auto;
    &-tips {
      position: absolute;
      top: 620px;
      left: 50%;
      color: $color-grey-1;
      font-weight: 500;
      font-size: $font-size-sm;
      transform: translateX(-50%);
    }
  }
  &-footer {
    position: relative;
    width: 100%;
    height: 366px;
    margin-top: -1px;
    background-image: url('https://cdn-img.kuaidihelp.com/wkd/miniApp/economy/footer.png?t=03');
    background-size: 100% auto;
  }
  &-btn {
    width: 360px;
    height: 66px;
    color: $color-white;
    font-weight: bold;
    font-size: 30px;
    background: linear-gradient(90deg, $color-economy 0%, #49b8ff 100%);
    border-radius: 33px;
    &-arrow {
      width: 14px;
      height: 22px;
      margin-left: $spacing-h-sm;
      background-image: url('https://cdn-img.kuaidihelp.com/wkd/miniApp/economy/arrow.png?t=03');
      background-size: 100% auto;
      &__blue {
        background-image: url('https://cdn-img.kuaidihelp.com/wkd/miniApp/economy/arrow_brand.png?t=03');
      }
    }
    &-small {
      width: 220px;
      color: $color-economy;
      background: $color-white;
      border: 2px solid $color-economy;
    }
  }
  &-swiperWrap {
    position: absolute;
    bottom: 50px;
    left: 50%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    width: 90%;
    height: 500px;
    transform: translateX(-50%);
  }
  &-scrollBrands {
    width: 100%;
    height: 250px;
    &-wrap {
      display: flex;
      white-space: nowrap;
    }
    &_bottom {
      // bottom: 60px;
      transform: rotate(180deg);
      .economy-brandsItem {
        transform: rotate(180deg);
      }
    }
  }
  &-brandsItem {
    position: relative;
    flex-direction: column;
    // padding: $spacing-h-xl;
    .img {
      width: 80px;
      height: 80px;
      margin-bottom: 15px;
      // border: 1px solid $color-border-split;
      border-radius: $border-radius-circle;
    }
    .tips {
      position: relative;
      width: unset;
      margin-bottom: 20px;
      padding: 0 $spacing-h-sm;
      color: $color-white;
      font-size: $font-size-xs;
      background-color: $color-red;
      border-radius: $border-radius-xl;
      &::after {
        position: absolute;
        top: calc(100% - 2px);
        left: 50%;
        width: 0;
        height: 0;
        border: 10px solid transparent;
        border-top-color: $color-red;
        transform: translateX(-50%);
        content: '';
      }
    }
    .kb-weight {
      font-weight: 500;
    }
  }
}

.kb-guide--btn {
  width: 400px;
  height: 66px;
  margin-bottom: 35px;
  font-size: $font-size-lg;
  border-radius: 33px;
}
.kb-guide--list {
  &-item {
    display: flex;
    margin-bottom: 8px;
    color: $color-grey-1;
    font-size: $font-size-base2;
    text-align: justify;
  }
  &-index {
    width: 40px;
  }
}

.w280 {
  width: 280px;
}

#scrollIntoViewId {
  color: transparent;
  opacity: 0;
}

.kb-economy-modal {
  padding: 44px 30px 50px 30px;
}
