/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb-interceptOrder {
  &-title {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 80px;
    margin-top: 20px;
    font-size: 32px;
  }
  &-content {
    padding: 0 $spacing-v-lg;
  }
  &-code {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  &-refresh {
    margin-top: 5px;
    color: #969799;
    font-size: 24px;
    text-align: center;
  }
  &-inputBox {
    box-sizing: border-box;
    height: 80px;
    margin: 40px 0 10px;
    border: $width-base solid #ebedf0;
    border-radius: 8px;
    .input {
      height: 100%;
      padding: 0 20px;
    }
  }
  &-tips {
    margin-bottom: $spacing-v-md;
    color: #e34d59;
    font-size: 24px;
    line-height: 33px;
    text-align: center;
  }
  &-complete {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 86px;
    color: $color-brand;
    font-size: 32px;
    border-top: $width-base solid #ebedf0;
    &--disable {
      color: #c8c9cc;
    }
  }
}
