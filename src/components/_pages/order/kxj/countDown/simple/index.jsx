/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { View, Fragment } from '@tarojs/components';
import { useCountDown } from '../_uitls';

const SimpleCountDown = (props) => {
  const { count } = useCountDown(props);

  const { hour, mint, seconds } = count || {};
  return (
    <Fragment>
      <View className='at-row at-row__align--center'>
        {hour || '00'}:{mint || '00'}:{seconds || '00'}
      </View>
    </Fragment>
  );
};

SimpleCountDown.options = {
  addGlobalClass: true,
};

export default SimpleCountDown;
