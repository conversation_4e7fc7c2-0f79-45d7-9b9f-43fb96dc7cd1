/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { frequencyLimitByMinute, reportAnalytics } from '@base/utils/utils';
import Taro from '@tarojs/taro';

export function intersitialAd({ adId, storageKey, minutes }) {
  if (process.env.PLATFORM_ENV == 'weapp') {
    const intersitialAd = Taro.createInterstitialAd({
      adUnitId: adId,
    });
    frequencyLimitByMinute('check', storageKey, minutes).then((status) => {
      if (!status) {
        intersitialAd.onLoad(() => {
          reportAnalytics({
            key: 'cpad_onload',
          });
          intersitialAd.show().catch((err) => {
            console.log('cpad_err', err);
            reportAnalytics({
              key: 'cpad_errormsg',
              errormsg: err.errMsg,
            });
          });
          frequencyLimitByMinute('limit', storageKey);
        });
      }
    });
    return intersitialAd;
  }

  // return intersitialAd.destroy()
}

// 灯火广告最新接入方式
export const createInterstitialAd = (opt) => {
  const {
    adUnitId = '', // spacecode
    onClose = () => {},
  } = opt || {};
  return new Promise((resolve, reject) => {
    if (process.env.PLATFORM_ENV == 'alipay') {
      if (my.canIUse('createInterstitialAd')) {
        const triggerClose = (action = 'fail', e) => {
          if (onClose) {
            onClose(action, e);
          }
        };
        const interstitialAd = my.createInterstitialAd({
          adUnitId: adUnitId,
        });
        interstitialAd.onClose((res) => {
          // 广告关闭事件触发
          console.log('灯火广告关闭', res);
          triggerClose('close', res);
        });
        const openAd = () => {
          // 广告加载和展示
          return new Promise((resolve2, reject2) => {
            // load 加载广告
            interstitialAd
              .load()
              .then(() => {
                // 加载广告完成之后，通过 show 显示广告
                interstitialAd
                  .show()
                  .then(() => {
                    resolve2();
                  })
                  .catch((err) => {
                    console.log('灯火广告显示失败', err);
                    reject2();
                    triggerClose('fail', err);
                  });
              })
              .catch((err) => {
                console.log('灯火广告加载失败', err);
                reject2();
                triggerClose('fail', err);
              });
          });
        };
        interstitialAd.openAd = openAd;
        resolve(interstitialAd);
      } else {
        reject();
      }
    } else {
      reject();
    }
  });
};
