import { useSelector } from '@tarojs/redux';
import { useMemo, useState } from '@tarojs/taro';
import { loadAdminAd } from '~/components/_pages/ad-extension/sdk';
import { checkIsReturnOrder } from '~/lib/config/config.page';
import { useUpdate } from '~base/hooks/page';
import { check } from '~base/utils/rules';

/**
 *
 * @param {'yj'|'gj'|'dh'|'wg'|'xsj'|'kdy'|'yz'} type
 * @param {{remark:string;title:string;}[]} list
 */
function findNotice(type, list) {
  if (type) {
    // 对应包含#[type]的
    const forType = list.find((item) => item.title.includes(`#${type}`));
    if (forType) return forType.remark;

    // 通用的，不包含#[type]
    const commType = list.find((item) => check('orderNoticeType', item.title).code !== 0);
    if (commType) return commType.remark;
  }
  return '';
}

export function useGetOrderNotice(props) {
  const { isStudentSend, isDh, isGj, dakExtra } = props;
  const [list, setList] = useState([]);
  const { relationInfo } = useSelector((state) => state.global);
  const { type, platform } = relationInfo || {};

  useUpdate((loginData) => {
    if (!loginData.logined) return;
    loadAdminAd('14').then(setList);
  });

  const text = useMemo(() => {
    if (!list.length || !relationInfo) return '';
    const isYjkd = type === 'brand' && platform === 'yjkd_brand'; // 优寄快递
    const isKdy = type === 'courier'; // 快递员
    const isDak = type === 'dak'; // 驿站
    const isReturnOrder = checkIsReturnOrder(relationInfo); // 网购退货
    const findType = isGj
      ? 'gj' // 国际
      : isDh
      ? 'dh' // 大货（经济货运）
      : isStudentSend
      ? 'xsj' // 学生寄
      : isReturnOrder
      ? 'wg' // 网购退货
      : isYjkd
      ? 'yj' // 优寄快递
      : isKdy
      ? 'kdy' // 快递员
      : isDak
      ? 'yz' // 驿站
      : '';
    const t = findNotice(findType, list);
    return t;
  }, [list, relationInfo, isStudentSend, isDh]);

  // 驿站额外提示
  const extra = useMemo(() => {
    return type === 'dak' && dakExtra ? dakExtra : '';
  }, [dakExtra, type]);

  return {
    text,
    extra,
  };
}
