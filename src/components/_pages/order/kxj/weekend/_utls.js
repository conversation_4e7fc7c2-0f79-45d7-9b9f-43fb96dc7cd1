/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro from '@tarojs/taro';
import { getPage } from '@base/utils/utils';
import request from '@base/utils/request';
import { getAdConfig } from '@/components/_pages/ad-extension/_utils';
import { adNavigator } from '@/components/_pages/ad-extension/sdk';
import { getUserVipInfo, getVipConfigList } from '@/components/_pages/user/_utils';
import { dealPageNavBack } from '@base/components/page/nav-bar/_utils';
import dayjs from 'dayjs';
import { addReceiveCoupon, createKxjCouponListApiAndData } from '../_utils';
import { orderAction } from '../../_utils';

export const handleAdClick = (item) => {
  adNavigator(item);
};

export const getBannerAds = () => {
  const _this = getPage();
  getAdConfig(
    {
      position: 40,
    },
    true,
  ).then((list) => {
    _this.setState({
      adList: list,
    });
  });
};

export const handleRules = (key) => {
  const _this = getPage();
  switch (key) {
    case 'open':
      _this.setState({
        isOpened: true,
      });
      break;
    case 'close':
      _this.setState({
        isOpened: false,
      });
      break;
  }
};

export const dealSend = (data = {}) => {
  orderAction({
    action: 'edit',
    data: {
      relation: {
        brand: data.brand,
      },
    },
  });
};

export const handleVip = () => {
  Taro.navigator({
    url: 'user/member',
  });
};

export const activityRulesList = [
  '通过“微快递"平台指定活动专属入口内参与，可领取优惠券；',
  '本券为平台补贴券，限在微快递微信小程序寄快递线上开通微信支付分“先享后付”支付时使用，扫码下单或给专属快递员下单时不可用；',
  '货物超出限重无法使用优惠券，成功下单将按照官方无折扣原价计费扣款；',
  '活动合作折扣快递公司为京东、德邦、EMS等，此优惠券适用产品以优惠券上标注的使用规则为准。',
  '此优惠券每次寄件限用一张，且只能用于寄件人付款的邮件，多张优惠券不可叠加使用。',
  '此优惠券上标有优惠券有效期，可前往我的优惠券查看。过期失效不予补偿，请尽快使用。',
  '如有退件，优惠券金额不予退还。此活动优惠，不可与其他活动优惠同享；',
  '暂不支持港澳台，以及海外寄件参与活动优惠；',
  '活动最终解释权归微快递所有！',
];

export const getUserVipInfoData = () => {
  const _this = getPage();
  getUserVipInfo().then((userVipData) => {
    if (userVipData && userVipData.status == 1) {
      getVipConfigList().then(({ code, data }) => {
        let card = {};
        if (code == 0 && data.length > 0) {
          card = data.filter((item) => item.id == userVipData.card_id)[0];
        }
        _this.setState({
          userVipData: {
            ...card,
            ...userVipData,
          },
        });
      });
    } else {
      _this.setState({
        userVipData,
      });
    }
  });
};

export const createListData = () => {
  const _this = getPage();
  return {
    api: {
      ...createKxjCouponListApiAndData(),
      data: {
        activity: 'inflate_coupon',
      },
      onThen: (list) => {
        _this.setState({
          list,
        });
      },
    },
  };
};

export const createCouponList = (list = [], opt) => {
  const { type = '', key = 1 } = opt || {};
  if (!list || list.length <= 0) {
    return [];
  }
  if (type === 'subtract') {
    return list.filter((item) => item.discount_type == type && item.subtract_key.includes(key * 1));
  } else {
    return list.filter((item) => item.discount_type == type);
  }
};

export const handleBarClick = (action = 'one', item = {}) => {
  const _this = getPage();
  const { userVipData = {}, list = [] } = _this.state;
  const isVip = userVipData.status == 1;
  const batch = !(action === 'one');
  if (action === 'used') {
    Taro.kbToast({
      text: '当前周末优惠券，已使用',
    });
    return;
  }
  if (action === 'edit') {
    if (item.is_inflate == 1) {
      dealSend(item);
    } else {
      _this.handleGuideShare('open', item);
    }
    return;
  }
  // 领券限制
  if (isVip) {
    if (batch) {
      const isNotReceive = list.every((item) => item.is_receive == 1);
      if (isNotReceive) {
        Taro.kbToast({
          text: '尊敬的会员用户，您已完成全部优惠券领取！',
        });
        return;
      }
    }
  } else {
    // 非会员
    if (batch) {
      Taro.kbToast({
        text: '非会员用户，暂不支持领取全部优惠券！',
      });
      return;
    }
  }
  // 参数处理
  const params = {
    activity: 'inflate_coupon',
    coupon_id: item.card_id || '',
    rate_type: action == 'allSubtract' ? 'subtract' : action == 'allDiscount' ? 'discount' : '',
  };
  addReceiveCoupon(params).then((res) => {
    if (res.code == 0) {
      _this.listIns.loader();
      if (batch) {
        Taro.kbToast({
          text: `已为您领取全部优惠券，可前往“我的优惠券”查看`,
        });
      } else {
        _this.handleGuideShare('open', item);
      }
    }
  });
};

export const handleGuideShare = (key, data = {}) => {
  const _this = getPage();
  const _close = () => {
    _this.setState({
      isOpenedGuide: false,
    });
  };
  switch (key) {
    case 'close':
      _close();
      break;
    case 'open':
      _this.setState({
        isOpenedGuide: data,
      });
      break;
    case 'send':
      dealSend(data);
      break;
  }
};

export const handleShareUser = (key, data = {}) => {
  const _this = getPage();
  const _close = () => {
    _this.setState({
      isOpenedShare: false,
    });
  };
  switch (key) {
    case 'close':
      _close();
      break;
    case 'open':
      _this.setState({
        isOpenedShare: data,
      });
      break;
  }
};

export const handleShareSuccess = (key, data = {}) => {
  const _this = getPage();
  const _close = () => {
    _this.setState({
      isOpenedShareSuccess: false,
    });
  };
  switch (key) {
    case 'close':
      _close();
      break;
    case 'open':
      _this.setState({
        isOpenedShareSuccess: data,
      });
      break;
    case 'send':
      dealSend(data);
      break;
  }
};

export const handelRetentionNoReceive = (key) => {
  const _this = getPage();
  const close = () => {
    _this.setState({
      isOpenedRetentionNoReceive: false,
    });
  };
  switch (key) {
    case 'close':
      close();
      break;
    case 'open':
      _this.setState({
        isOpenedRetentionNoReceive: true,
      });
      break;
    case 'cancel':
      dealPageNavBack();
      close();
      break;
    case 'confirm':
      close();
      break;
  }
};

export const handelRetentionNoUse = (key, data = {}) => {
  const _this = getPage();
  const { list } = _this.state;
  const close = () => {
    _this.setState({
      isOpenedRetentionNoUse: false,
    });
  };
  switch (key) {
    case 'close':
      close();
      break;
    case 'open':
      _this.setState({
        isOpenedRetentionNoUse: data,
      });
      break;
    case 'cancel':
      dealPageNavBack();
      close();
      break;
    case 'confirm':
      const aList = list.filter((i) => i.is_receive == 1 && i.is_use != 1);
      dealSend(aList[0]);
      close();
      break;
  }
};

// 膨胀券
export const inflateCoupon = (coupon_id) => {
  return new Promise((resolve) => {
    request({
      url: '/g_order_core/v2/WkdCoupon/shareCoupon',
      data: {
        coupon_id,
      },
      toastLoading: false,
      toastError: true,
      onThen: (res) => {
        resolve(res);
      },
    });
  });
};

// 处理分享弹窗逻辑
export const dealShareEnter = () => {
  const _this = getPage();
  const { userInfo } = Taro.kbLoginData;
  const routerParams = _this.$router.params || {};
  const { fromShare, user_id, nickname, avatar_url } = routerParams;
  if (fromShare == 1) {
    const isSelf = !!(user_id == userInfo.user_id);
    const shareInfo = {
      ...routerParams,
      nickname: decodeURIComponent(nickname),
      avatar_url: decodeURIComponent(avatar_url),
    };
    console.log('shareInfo', shareInfo);
    if (isSelf) {
      _this.handleShareSuccess('open', shareInfo);
    } else {
      _this.handleShareUser('open', shareInfo);
    }
  }
};

export const handleNavClick = () => {
  const _this = getPage();
  const { list } = _this.state;
  const isNotReceive = list.every((i) => i.is_receive != 1);
  const isNotUse = list.some((i) => i.is_receive == 1 && i.is_use != 1);
  if (isNotReceive) {
    _this.handelRetentionNoReceive('open');
  } else if (isNotUse) {
    _this.handelRetentionNoUse('open', {
      endTime: `${dayjs().format('YYYY-MM-DD')} 23:59:59`,
      list: list.filter((i) => i.is_receive == 1 && i.is_use != 1),
    });
  } else {
    dealPageNavBack();
  }
};
