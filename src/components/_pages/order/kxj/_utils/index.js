/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro from '@tarojs/taro';
import { debounce } from '@base/utils/utils';
import { getUserVipInfo } from '@/components/_pages/user/_utils';
import { formatKxjCoupon } from '@/pages/order/card/utils';
import request from '@base/utils/request';
import dayjs from 'dayjs';
import duration from 'dayjs/plugin/duration';
import { REFRESH_KEY_KXJ_COUPON, refreshControl } from '@/utils/refresh-control';
import isUndefined from 'lodash/isUndefined';

dayjs.extend(duration);

/**
 * 开学寄活动=>双节活动
 */

export const activityEndTime = `2023-10-20 23:59:59`;

export const addrList = [
  {
    key: 'send',
    placeholder: ['从哪寄件？'],
    color: 'brand',
    tag: '寄',
    selector: true,
  },
  {
    key: 'receive',
    placeholder: ['寄到哪个学校？'],
    color: 'orange',
    tag: '收',
  },
];

export const checkReceiveCouponStatus = () => {
  return new Promise((resolve) => {
    request({
      url: '/g_order_core/v2/WkdCoupon/checkReceiveCouponStatus',
      toastLoading: false,
      onThen: (res) => {
        resolve(!!(res.code == 0 && res.data));
      },
    });
  });
};

export const getReceiveCouponList = () => {
  return new Promise((resolve) => {
    request({
      url: '/g_order_core/v2/WkdCoupon/getReceiveCouponList',
      toastLoading: false,
      onThen: (res) => {
        if (res.data && res.data.length > 0) {
          res.data = formatKxjCoupon(
            res.data.map((item) => {
              return {
                ...item,
                ...(item.coupon_info || {}),
              };
            }),
          );
        }
        resolve(res.data && res.data.length > 0 ? res.data : []);
      },
    });
  });
};

/**
 * 获取用户参与活动详情
 * @returns
 */
export const getActivityDetail = debounce(
  (opt) => {
    const { vip = false, activity = '' } = opt || {};
    return new Promise((resolve) => {
      request({
        url: '/g_order_core/v2/activity/Activity/checkIsParticipateActivity',
        toastLoading: false,
        data: {
          activity,
        },
        onThen: (res) => {
          const activityData = res.code == 0 && res.data;
          const { is_new, is_use } = activityData || {};
          // 处理活动tab显示隐藏逻辑
          if (is_new == 1 && is_use != 1) {
            // 新用户且没用券
            // Taro.showTabBar(2);
          } else {
            // Taro.hideTabBar(2);
          }
          if (vip) {
            getUserVipInfo().then((userVipData) => {
              resolve(
                activityData
                  ? {
                      is_vip: userVipData && userVipData.status == 1,
                      userVipData: userVipData,
                      ...activityData,
                    }
                  : null,
              );
            });
          } else {
            resolve(activityData ? activityData : null);
          }
        },
      });
    });
  },
  1000,
  {
    leading: true,
    trailing: false,
  },
);

/**
 * 获取弹幕列表
 * @returns
 */
export const getBarrageLists = () => {
  return new Promise((resolve) => {
    request({
      url: '/g_order_core/v2/activity/Activity/getBarrageLists',
      toastLoading: false,
      onThen: (res) => {
        resolve(res.data && res.data.length > 0 ? res.data : []);
      },
    });
  });
};

/**
 * 隐藏活动tab
 */
export const hideActivityTabBar = () => {
  Taro.hideTabBar(2);
};

/**
 * 获取活动优惠券配置列表
 */
export const getActivityCouponConfig = (opt) => {
  const { activity, coupon_id = '', toastError } = opt || {};
  return new Promise((resolve) => {
    request({
      url: '/g_order_core/v2/WkdCoupon/getCouponConfig',
      toastError: !isUndefined(toastError) ? toastError : true,
      data: {
        activity: activity,
        coupon_id,
      },
      onThen: (res) => {
        resolve(res);
      },
    });
  });
};

/**
 * 自动匹配活动优惠券
 */
export const getMatchActivityCoupon = (opt) => {
  const { brand, money, weight = 1 } = opt || {};
  return new Promise((resolve) => {
    request({
      url: '/g_order_core/v2/activity/Activity/matchingCoupons',
      data: {
        activity: 'new_customer',
        brand,
        money,
        weight,
      },
      toastLoading: false,
      toastError: true,
      onThen: (res) => {
        resolve(res.data);
      },
    });
  });
};

/**
 * 新客首单送会员弹框
 */
export const checkActivityIsPopFrame = (opt) => {
  const { brand, price } = opt || {};
  return new Promise((resolve) => {
    request({
      url: '/g_order_core/v2/activity/Activity/checkIsPopFrame',
      data: {
        activity: 'new_customer',
        brand,
        price,
      },
      toastLoading: false,
      onThen: (res) => {
        resolve(res.data);
      },
    });
  });
};

// 领券
export const addReceiveCoupon = (opt) => {
  const { activity, rate_type = '', coupon_id = '', source = '', channel = '' } = opt || {};
  return new Promise((resolve) => {
    request({
      url: '/g_order_core/v2/WkdCoupon/addReceiveCoupon',
      toastError: true,
      data: {
        activity,
        rate_type,
        coupon_id,
        source,
        channel,
      },
      onThen: (res) => {
        refreshControl(REFRESH_KEY_KXJ_COUPON);
        resolve(res);
      },
    });
  });
};

export const createKxjCouponListApiAndData = () => {
  return {
    url: '/g_order_core/v2/WkdCoupon/getCouponConfig',
    data: {},
    formatResponse: (res) => {
      let list = [];
      if (res.code == 0 && res.data && res.data.length > 0) {
        list = formatKxjCoupon(res.data);
      }
      return list.length > 0
        ? {
            code: 0,
            data: {
              list,
            },
          }
        : { data: void 0 };
    },
  };
};
