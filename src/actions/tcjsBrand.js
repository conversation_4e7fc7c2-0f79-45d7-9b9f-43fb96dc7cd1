/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { save } from '@base/actions/global';
import CallbacksPool from '@base/utils/callbacksPool';
import request from '@base/utils/request';
import Taro from '@tarojs/taro';
import isArray from 'lodash/isArray';
import isObject from 'lodash/isObject';

const filter = (keys) => {
  const brands = Taro.brands_tcjs;
  if (!keys) return brands;
  const data = {};
  if (isArray(keys)) {
    keys.map((key) => {
      data[key] = brands[key];
    });
    return data;
  } else {
    return brands[keys];
  }
};

export const getAvailableBrands = (brands) => {
  let list = [];
  if (isObject(brands)) {
    Object.keys(brands).map((key) => {
      list.push(brands[key]);
    });
  } else {
    list = brands;
  }
  const arr = isArray(list) ? list.filter((item) => item.status == 0) : [];
  return arr.length > 0 ? arr.map((item) => item.brand) : [];
};

let locked = false;
const callbackIns = new CallbacksPool();

/**
 *
 * @description 补充缓存获取逻辑
 * @returns
 */
function getBrandsFromCache() {
  return new Promise((resolve) => {
    resolve();
  });
}

/**
 *
 * @description 后台拉取品牌列表
 * @returns
 */
function getBrandsFromRemote() {
  return new Promise((resolve) => {
    request({
      mastLogin: false,
      toastSuccess: false,
      toastLoading: false,
      toastError: false,
      url: '/g_wkd/v2/rushOrder/Order/getBrands',
      onThen: ({ data }) => {
        const formatData = {};
        if (data && data.length > 0) {
          data.map((item) => {
            const { code, name, desc, url, ...rest } = item || {};
            formatData[code] = {
              brand: code,
              name,
              logo_link: url,
              desc,
              ...rest,
            };
          });
        }
        const mergeData = {
          ...formatData,
        };
        Taro.brands_tcjs = mergeData;
        resolve(mergeData);
      },
    });
  });
}

export const getBrandsTCJS = (params = {}) => {
  const { filterKeys, dataKey = 'brands_tcjs', then = () => {} } = params;
  return (dispatch) => {
    const triggerSet = (data) => {
      dispatch(
        save({
          [dataKey]: filter(filterKeys),
        }),
      );
      then(data);
    };
    getBrandsFromCache().then(() => {
      if (Taro.brands_tcjs) {
        triggerSet(Taro.brands_tcjs);
        return;
      }
      if (locked) {
        // 保存回调方法，获取数据后依次回调
        callbackIns.push(then);
        return;
      }
      getBrandsFromRemote().then((data) => {
        triggerSet(data);
        // 保证所有回调正确执行
        callbackIns.trigger(data);
        locked = false;
      });
      locked = true;
    });
  };
};
