/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { Fragment } from '@tarojs/taro';
import { Text } from '@tarojs/components';
import isNaN from 'lodash/isNaN';
import numeral from 'numeral';
import './index.scss';

function formatNumber(num = '') {
  let numPrice = num * 1,
    unit = '';
  if (isNaN(numPrice) || !numPrice) {
    return false;
  } else {
    if (numPrice > 100000000) {
      // 亿
      numPrice = numPrice / 100000000;
      unit = '亿';
    } else if (numPrice > 10000) {
      // 万
      numPrice = numPrice / 10000;
      unit = '万';
    }
    const str = numPrice.toFixed(2);
    const [front = '0', end = '00'] = str.split('.');
    return {
      front,
      end,
      unit,
    };
  }
}

const KbNumber = (props) => {
  const { number, fontsize = [], empty, formatter, label = '', unit = '', color } = props;
  const [frontSize, endSize] = fontsize || [];

  const { front = '0', end = '00', unit: _unit = '' } = formatNumber(number) || [];

  if (!number) {
    return <Text style={{ fontSize: `${Taro.pxTransform(frontSize)}` }}>{empty}</Text>;
  }

  const handleClick = () => {
    Taro.kbToast({
      text: `${label}${number}${unit}`,
    });
  };

  return (
    <Fragment>
      <Text
        className='kb-size__40'
        style={{ fontSize: `${Taro.pxTransform(frontSize)}`, color: color }}
        onClick={handleClick}
      >
        {numeral(front).format(formatter)}
      </Text>
      {!!end && (
        <Text style={{ fontSize: `${Taro.pxTransform(endSize)}`, color: color }}>
          .{end}
          {_unit}
        </Text>
      )}
    </Fragment>
  );
};

KbNumber.defaultProps = {
  number: 0,
  fontsize: [40, 24],
  empty: '--',
  formatter: '0,0',
};
KbNumber.options = {
  addGlobalClass: true,
};
export default KbNumber;
