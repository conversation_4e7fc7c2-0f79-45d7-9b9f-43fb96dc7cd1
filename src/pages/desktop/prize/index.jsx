/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { Component } from '@tarojs/taro';
import { connect } from '@tarojs/redux';
import KbPage from '@base/components/page';
import { View } from '@tarojs/components';
import { AtTabs, AtTabsPane } from 'taro-ui';
import KbDesktopPrizeList from '@/components/_pages/desktop/prize-list';
import './index.scss';

@connect(({ global }) => ({
  loginData: global.loginData,
}))
class Index extends Component {
  config = {
    navigationBarTitleText: '我的奖品',
  };

  constructor(props) {
    super(props);
    this.state = {
      current: 0,
      tabs: [
        {
          title: '全部',
          key: 'all',
        },
        {
          title: '未使用',
          key: 'normal',
        },
        {
          title: '已过期',
          key: 'invalid',
        },
      ],
    };
  }

  handleUpdate(data) {
    if (!data.logined) return;
  }

  handleSwitchTab = (current) => {
    this.setState({
      current,
    });
  };

  render() {
    const { current, tabs, ...rest } = this.state;
    return (
      <KbPage {...rest} onUpdate={this.handleUpdate.bind(this)}>
        <View className='kb-desktopPrize'>
          <AtTabs tabList={tabs} onClick={this.handleSwitchTab} current={current} swipeable={false}>
            {tabs.map((val, index) => {
              const { key } = val;
              return (
                <AtTabsPane key={key}>
                  <KbDesktopPrizeList mode={key} active={index == current} />
                </AtTabsPane>
              );
            })}
          </AtTabs>
        </View>
      </KbPage>
    );
  }
}

export default Index;
