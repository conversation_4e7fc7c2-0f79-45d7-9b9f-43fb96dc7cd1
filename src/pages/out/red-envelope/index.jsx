/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { Component } from '@tarojs/taro';
import KbPage from '@base/components/page';
import { Image, View } from '@tarojs/components';
import { AtButton } from 'taro-ui';
import KbAdFloat from '~/components/_pages/ad-extension/ad/float';
import { adNavigator, createAd, loadAdminAd } from '~/components/_pages/ad-extension/sdk';
import { saveRemoteImageToPhotosAlbum } from '~/utils/qy';
import './index.scss';

class Index extends Component {
  config = {
    navigationBarTitleText: '天天领红包',
    navigationBarBackgroundColor: '#fff',
    usingComponents: {
      feeds: 'plugin://xlightPlugin/feeds',
    },
  };
  constructor() {
    this.state = {
      imgUrl: 'https://cdn-img.kuaidihelp.com/miniapp/wkd/red-envelope.jpg',
      ad: null,
    };
  }

  componentDidMount() {
    this.insertAd();
    this.loadBannerAndRedEnvelopeAd();
  }

  // 插入广告
  insertAd = () => {
    createAd(`out.red_envelope.full`, {
      filter: () => false,
      onClose: () => {
        createAd(`out.red_envelope.screen`);
      },
    });
  };

  // 点击保存红包
  handleClickSave = () => {
    saveRemoteImageToPhotosAlbum(
      {
        filePath: this.state.imgUrl,
      },
      {
        toastSuccess: false,
        toastError: false,
      },
    )
      .then(() => {
        my.alert({
          title: '温馨提示',
          content: '红包码已保存至您的相册，打开扫一扫，选择相册中的红包码图片，即可领取红包',
          buttonText: '打开扫一扫',
          success: () => {
            if (process.env.PLATFORM_ENV === 'alipay') {
              my.ap.openAlipayApp({
                appCode: 'alipayScan',
              });
            } else {
              Taro.scanCode({
                success: (res) => {
                  console.log('scanCode', res);
                },
              });
            }
          }
        });
        // Taro.kbModal({
        //   title: '温馨提示',
        //   content: '红包码已保存至您的相册，打开扫一扫，选择相册中的红包码图片，即可领取红包',
        //   cancelText: false,
        //   confirmText: '打开扫一扫',
        //   onConfirm: () => {

        //   },
        // });
      })
      .catch((err) => {
        const { error, errorMessage } = err;
        Taro.kbToast({
          text: `${error}` === '2001' ? '允许保存才可领取红包哦~' : errorMessage,
        });
      });
  };

  // banner广告
  handleClickBannerAd = () => {
    const { ad } = this.state;
    adNavigator(ad);
  };
  // 获取群和红包二维码图片
  loadBannerAndRedEnvelopeAd = () => {
    loadAdminAd('61').then((list) => {
      const index = list.findIndex((item) => item.title.startsWith('红包-'));
      if (index >= 0) {
        this.setState({
          imgUrl: list[index].imgUrl,
        });
        // 此条为红包图片，过滤掉，其余的用作banner位置的广告
        list.splice(index, 1);
      }
      const [ad = null] = list;
      this.setState({
        ad,
      });
    });
  };

  render() {
    const { ad, imgUrl, ...rest } = this.state;

    return (
      <KbPage className='out-red-envelope' cover={false} {...rest}>
        <View className='red-envelope__img'>
          <Image src={imgUrl} mode='widthFix' className='red-envelope__img--img' />
          <AtButton type='primary' onClick={this.handleClickSave}>
            点击保存红包扫一扫
          </AtButton>
        </View>
        {ad && (
          <View onClick={this.handleClickBannerAd} className='red-envelope__banner'>
            <Image src={ad.imgUrl} mode='widthFix' className='red-envelope__banner--img' />
          </View>
        )}
        <View className='red-envelope__feeds'>
          {/* eslint-disable-next-line */}
          <feeds spaceCode='27_2024121222700210711' />
        </View>
        <KbAdFloat position='60' closeable={false} />
      </KbPage>
    );
  }
}

export default Index;
