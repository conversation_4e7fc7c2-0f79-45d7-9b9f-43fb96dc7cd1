/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { Fragment } from '@tarojs/taro';
import { Image, Text, View } from '@tarojs/components';
import { AtCurtain } from 'taro-ui';
import { useLookAd } from './_utils';
import './index.scss';

const KbLookAd = (props) => {
  const { idName, count, success, adDetail, isOpened, tabStyles, handleClose, handleClick } =
    useLookAd(props);
  const { id, title, imgUrl } = adDetail || {};

  return (
    <Fragment>
      {id && (
        <View className='kb-lookAd'>
          <View className='kb-lookAd-title'>{title}</View>
          <View id={idName} className='kb-lookAd-content'>
            <View className='kb-lookAd-content__inner' style={tabStyles}>
              {success ? (
                <View
                  className='kb-lookAd-content__end'
                  onClick={() => handleClick(adDetail)}
                  hoverClass='kb-hover'
                >
                  <Image
                    className='img'
                    mode='widthFix'
                    src='https://cdn-img.kuaidihelp.com/wkd/aliapp/icon_ad.png'
                  />
                  <Text>已完成浏览任务，奖励已发放</Text>
                </View>
              ) : (
                <Text>滑动浏览以下内容{count}秒获得奖励</Text>
              )}
            </View>
          </View>
          <AtCurtain isOpened={isOpened} onClose={handleClose}>
            <View className='kb-curtain' onClick={() => handleClick(adDetail)}>
              <Image mode='widthFix' src={imgUrl} />
            </View>
          </AtCurtain>
        </View>
      )}
    </Fragment>
  );
};

KbLookAd.options = {
  addGlobalClass: true,
};

export default KbLookAd;
