import { View } from '@tarojs/components';
import {
  formatCountdownSuffix,
  myFormat,
} from '@/components/_pages/welfare/_utils/welfare.lotterys';
import { useEffect, useState } from '@tarojs/taro';

const CountDown = ({ time, lottery_type }) => {
  const [pageData, setPageData] = useState({});

  const { hour, minute, second, timeStatus } = pageData;

  useEffect(() => {
    let now_time = +new Date();
    var remaining = myFormat(time) - now_time;
    let timer = setInterval(() => {
      //防止出现负数
      if (remaining > 1000) {
        remaining -= 1000;
        let day = Math.floor(remaining / 1000 / 3600 / 24);
        let hour = Math.floor((remaining / 1000 / 3600) % 24);
        let minute = Math.floor((remaining / 1000 / 60) % 60);
        let second = Math.floor((remaining / 1000) % 60);
        hour = day * 24 + hour;
        setPageData({
          hour: hour < 10 ? '0' + hour : hour,
          minute: minute < 10 ? '0' + minute : minute,
          second: second < 10 ? '0' + second : second,
          timeStatus: true,
        });
      } else {
        clearInterval(timer);
        setPageData({
          hour: '00',
          minute: '00',
          second: '00',
          timeStatus: false,
        });
      }
    }, 1000);
  }, [time]);

  return timeStatus ? (
    <View>
      {hour}:{minute}:{second} {formatCountdownSuffix(lottery_type)}
    </View>
  ) : (
    <View />
  );
};

export default CountDown;
