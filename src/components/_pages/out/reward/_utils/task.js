import Taro from '@tarojs/taro';
import { adNavigator, createAd } from '@/components/_pages/ad-extension/sdk';
import { triggerRewardReportAnalytics } from './reportAnalytics';

export const defaultMoney = 0.01;
export const defaultTime = 5;

// 任务是否完成
export function checkIsDone(status) {
  return `${status}` === '1';
}

// 是否为灯火任务
export function checkIsTask(type) {
  return `${type}` === '3';
}

// 任务列表补丁
export function patchTaskList(data) {
  return data.map(({ title, time, money, ...item }) => {
    const patchTime = time || defaultTime;
    return {
      ...item,
      title: title || `浏览${patchTime}秒广告`,
      money: money || defaultMoney,
      time: patchTime,
      isDone: checkIsDone(item.status),
    };
  });
}

// 完成任务
export function doneTask(item, position) {
  return new Promise((resolve) => {
    const { id, type, spaceCode, time, isDone, path, title } = item;
    if (isDone) {
      resolve({ errMsg: '该任务已完成' });
    } else {
      // 统计-点击任务
      triggerRewardReportAnalytics({ taskId: id, action: 'task_click' }, position);
      if (path) {
        // 任务页面
        adNavigator({
          adUrl: path,
          title,
          position: 'out.reward',
          options: {
            spaceCode,
            id,
          },
        });
        resolve({ isAd: true });
      } else {
        const adKey = 'out.reward.task';
        createAd(adKey, {
          closeShield: true,
          patchAdIdsMap: {
            [adKey]: {
              id: spaceCode,
              label: '天天赚现金任务',
              // type:0-全屏、1-插屏、2-激励、3-任务
              type: `${type}` === '2' ? 'rewardedVideo' : 'interstitial',
            },
          },
          onClose: (isEnded) => {
            // 统计-任务成功或失败
            triggerRewardReportAnalytics({
              taskId: id,
              action: isEnded ? 'task_success' : 'task_fail',
            }, position);
            resolve(isEnded ? { isEnded } : { errMsg: '浏览时长不足，无奖励' });
          },
          enableCloseMinTime: time * 1000,
          max: Number.MAX_SAFE_INTEGER,
        }).catch((err) => {
          resolve({
            errMsg: err.message,
          });
        });
      }
    }
  });
}
