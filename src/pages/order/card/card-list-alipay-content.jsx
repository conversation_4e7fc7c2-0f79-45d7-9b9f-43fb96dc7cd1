/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import KbLongList from '@base/components/long-list';
import { Image, Text, View } from '@tarojs/components';
import { connect } from '@tarojs/redux';
import Taro, { Component } from '@tarojs/taro';
import { dateCalendar } from '@base/utils/utils';
import isArray from 'lodash/isArray';
import classNames from 'classnames';
import { checkCreditService } from '@/components/_pages/order/_utils/order.credit-pay';
import './card-list-alipay-content.scss';

@connect(({ global }) => ({
  loginData: global.loginData,
}))
class KbCardListContentAlipay extends Component {
  constructor(props) {
    super(props);
    console.log('props', props);
    this.state = {
      list: [],
      authCode: '',
    };
    this.listData = {
      pageKey: 'page_num',
      api: {
        url: '/g_order_core/v2/WkdCoupon/userAlipayVoucherList',
        data: {
          code: this.state.authCode,
        },
        formatResponse: ({ data: list }) => {
          if (list && list.length > 0) {
            list.map((item) => {
              const { discount_floor_amount, discount, voucher_type } = item || {};
              if (voucher_type == 'DISCOUNT_VOUCHER') {
                item.floor_amount = discount_floor_amount;
                item.amount = discount;
              }
            });
            return {
              data: {
                list,
              },
            };
          }
          return {
            data: void 0,
          };
        },
        onThen: (list) => {
          this.setState({
            list,
          });
        },
      },
    };
  }

  componentDidShow() {
    checkCreditService().then((isOpenCredit) => {
      this.setState({
        isOpenCredit,
      });
    });
    my.getAuthCode({
      scopes: 'voucher_query',
      success: (res) => {
        const { authCode } = res;
        this.setState(
          {
            authCode,
          },
          () => {
            this.listIns.loader({ code: authCode });
          },
        );
      },
    });
  }

  onReady(Ins) {
    this.listIns = Ins;
  }

  handleUse = (item) => {
    const { pageSource, isReturnModule } = this.props;
    console.log('this.props', this.props);
    console.log('item', item);
    if (isArray(item.brand) && item.brand.length > 0) {
      Taro.kbUpdateRelationInfo({ brand: item.brand[0], isReturnModule });
    }
    if (pageSource === 'order_edit') {
      Taro.navigator({
        post: {
          type: 'cardSelect',
          data: {
            couponInfo: item,
          },
        },
      });
    } else {
      Taro.navigator({
        url: 'order/edit',
        target: 'self',
        key: 'routerParamsChange',
        options: {
          couponInfo: item,
        },
        onArrived: () => {},
      });
    }
  };

  render() {
    const { list, authCode, isOpenCredit = false } = this.state;
    const { active } = this.props;
    return (
      <KbLongList
        data={this.listData}
        onReady={this.onReady.bind(this)}
        active={active && authCode}
        enableRefresh={false}
      >
        <View className='kb-card__group'>
          {list &&
            list.length > 0 &&
            list.map((item) => {
              const {
                voucher_id,
                amount,
                floor_amount,
                coupon_name,
                valid_end_time,
                voucher_type,
              } = item || {};
              const disabled = !isOpenCredit;
              const itemRootCls = classNames('kb-alipay-coupon-item__container', {
                'kb-alipay-coupon-item__container--disabled': disabled,
              });
              return (
                <View className={itemRootCls} key={voucher_id}>
                  {disabled && (
                    <View className='disabled-reason'>
                      不可用原因：开通芝麻信用分后，可享用抵扣券
                    </View>
                  )}
                  <View className='kb-alipay-coupon-item'>
                    <View className='kb-alipay-coupon-item__app'>
                      <Image
                        className='app-logo'
                        mode='widthFix'
                        src='https://cdn-img.kuaidihelp.com/wkd/miniApp/login_wkd.png'
                      />
                      <View className='app-name'>小程序</View>
                    </View>
                    <View className='kb-alipay-coupon-item__coupon'>
                      <View className='coupon-app'>微快递</View>
                      <View className='coupon-box'>
                        <View className='coupon-left'>
                          <View className='coupon-left-fee'>
                            {amount}
                            <Text className='kb-size__sm'>
                              {voucher_type == 'DISCOUNT_VOUCHER' ? '折' : '元'}
                            </Text>
                          </View>
                          <View className='coupon-left-desc'>满{floor_amount}元可用</View>
                        </View>
                        <View className='coupon-right'>
                          <View className='coupon-right-box'>
                            <View className='coupon-right-title'>{coupon_name}</View>
                            <View className='coupon-right-desc'>限指定门店可用</View>
                            <View className='coupon-right-desc'>
                              {dateCalendar(valid_end_time)}过期
                            </View>
                          </View>
                          <View
                            className='coupon-right-btn'
                            onClick={disabled ? () => {} : this.handleUse.bind(this, item)}
                            hoverClass='kb-hover'
                          >
                            去使用
                          </View>
                        </View>
                      </View>
                    </View>
                  </View>
                </View>
              );
            })}
        </View>
      </KbLongList>
    );
  }
}

export default KbCardListContentAlipay;
