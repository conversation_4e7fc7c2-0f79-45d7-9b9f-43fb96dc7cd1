.kb-custom-tab {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 9999;
  display: flex;
  height: 100px;
  padding-bottom: env(safe-area-inset-bottom);
  background: white;
  box-shadow: 0 -1px $width-base #eaeaea;
  transform: translateY(0);
  transition: 0.3s;
  &.tab-hidden {
    transform: translateY(105%);
  }
}

.kb-custom-tab__border {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 0;
  width: 100%;
  height: $width-base;
  background-color: rgba(0, 0, 0, 0.05);
  transform: scaleY(0.5);
}

.kb-custom-tab__item {
  position: relative;
  display: flex;
  flex: 1;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  &--hidden {
    display: none;
  }
}

.kb-custom-tab__item .kb-custom-tab__image {
  width: 50px;
  height: 50px;
}

.tab-size-full .kb-custom-tab__image {
  width: 80px;
  height: 80px;
}

.kb-custom-tab__item .kb-custom-tab__text {
  margin-top: 8px;
  font-size: 20px;
}

/* 右上角徽标 */
.kb-custom-tab__item-badge {
  position: absolute;
  top: 2px;
  background: #ff3531;
}

.badge-text {
  min-width: 10px;
  height: 30px;
  padding: 0 10px;
  color: #fff;
  font-size: 16px;
  line-height: 30px;
  white-space: nowrap;
  text-align: center;
  border-radius: 30px;
}

.badge-right {
  left: 55%;
}

.badge-left {
  position: absolute;
  left: -20px;
}

.badge-dot {
  top: 8px;
  left: 65%;
  width: 15px;
  height: 15px;
  border-radius: 50%;
}

.badge-left::after {
  position: absolute;
  right: -8px;
  bottom: -8px;
  border: 8px solid transparent;
  border-left-color: #ff3531;
  transform: rotate(45deg);
  content: '';
}

/* 动画 */
.badge-animation-shake {
  animation: shake 0.6s infinite ease;
}

@keyframes shake {
  0% {
    left: -15px;
  }

  50% {
    left: -10px;
  }

  100% {
    transform: -5px;
  }
}

/* 活动图标 */
.kb-custom-tab__item--activity {
  .kb-custom-tab__image {
    position: relative;
    top: -28px;
    width: 140px;
    height: 140px !important;
  }
  .kb-custom-tab__item-badge {
    top: -15px;
    right: -25px;
    left: unset;
  }
  .badge-left::after {
    right: unset;
    left: -5px;
  }

  .badge-animation-shakeY {
    animation: shakeY 0.6s infinite ease;
  }

  @keyframes shakeY {
    0% {
      top: -20px;
    }

    50% {
      top: -15px;
    }

    100% {
      top: -10px;
    }
  }
}
