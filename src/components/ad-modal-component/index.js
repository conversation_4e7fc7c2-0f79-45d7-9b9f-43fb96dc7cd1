/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

// xlightPlugin 是项目 app.json 中引入广告插件的变量名，这里以 xlightPlugin 为例
/* eslint-disable */
const adPlugin = requirePlugin('xlightPlugin');

Component({
  /* eslint-disable */
  data: {
    interstitialAd: null,
  },
  props: {
    spaceCode: '',
    statisticsAction: '',
    statistics: () => {},
    onEnd: () => {}, // 广告关闭或加载失败回调
  },
  didMount: function didMount() {
    const { statisticsAction, statistics, onEnd, spaceCode } = this.props;
    const interstitialAd = new adPlugin.CreateInterstitialAd();
    this.setData({
      interstitialAd,
    });

    // 广告插件加载成功
    this.data.interstitialAd.onLoad(() => {
      console.log('全屏广告加载成功11', spaceCode);
      statistics(statisticsAction, '全屏广告加载成功');
      // 调用 show 方法，请求广告
      this.data.interstitialAd.show({
        spaceCode, // 填入资源位创建后对应的 unit-id
      });
    });

    // 广告关闭/跳转监听事件
    this.data.interstitialAd.onClose((value) => {
      onEnd && onEnd();
      console.log('全屏广告关闭11', value, spaceCode);
      const { type = 'null', bizId } = value;
      statistics(statisticsAction, `关闭类型${type - bizId}`);
    });

    // 广告加载失败监听事件
    this.data.interstitialAd.onError((err) => {
      onEnd && onEnd();
      console.log('全屏广告失败11', err, spaceCode);
      statistics(statisticsAction, `全屏广告加载失败-${err}`);
    });
  },
});
