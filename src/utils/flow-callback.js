import request from '@base/utils/request';
import { getLaunchParams } from '@base/utils/navigator';

let isAlipayFlow = false;
let lock = false;
export function flowCallback(data, { query } = {}) {
  if (process.env.PLATFORM_ENV === 'alipay') {
    const { type } = data || {};
    if (type === 'access') {
      if (lock) {
        // 初始进入上报，仅一次
        return;
      }
      lock = true;
    }
    const { source } = query || getLaunchParams();
    if (isAlipayFlow || source === 'alipay_flow') {
      isAlipayFlow = true;
      // 支付宝投流进入
      request({
        url: '/g_wkd/v2/aliapp/AdConversion/conversion',
        toastLoading: false,
        data,
        onThen: (res) => {
          if (`${res.code}` !== '0') {
            lock = false;
          }
        },
      });
    }
  }
}
