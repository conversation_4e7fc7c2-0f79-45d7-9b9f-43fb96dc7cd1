/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import KbAdExtension from '@/components/_pages/ad-extension';
import { getAdExtensionReq } from '@/components/_pages/ad-extension/_utils';
import { preloadAd } from '@/components/_pages/ad-extension/sdk';
import { receiveStorageKey } from '@/components/_pages/address/_utils';
import KbAgreement from '@/components/_pages/agreement';
import KbOfficialAccount from '@/components/_pages/official-account';
import KbAddressEdit from '@/components/_pages/order/address-edit';
import KbCardBar from '@/components/_pages/order/card-bar';
import KbScanCoupon from '@/components/_pages/order/coupon/scan-coupon';
import KbCouponModal from '@/components/_pages/order/courier/coupon-modal';
import KbEstimatedFee from '@/components/_pages/order/estimated-fee';
import KbEstimatedFeeList from '@/components/_pages/order/estimated-fee/list';
import KbExtraInfo from '@/components/_pages/order/extra-info';
import BusinessCouponGuide from '@/components/_pages/order/kxj/businessCoupon/guide';
import KbSubmitBar from '@/components/_pages/order/submit-bar';
import KbUnPayOrders from '@/components/_pages/order/unPayOrders';
import KbWaitPay from '@/components/_pages/order/waitpay';
import {
  fixDynamicFormsData,
  getForm,
  isFresh,
  transferWkdAddress,
} from '@/components/_pages/order/_utils';
import {
  confirmCredit,
  CreditConfig,
  openCreditService,
} from '@/components/_pages/order/_utils/order.credit-pay';
import {
  batchSubmitOrder,
  bindActivity,
  checkAsyncBatchStatus,
  checkYjkdCourier,
  cleanOrderEditFormInfo,
  clearBatchStatusCheck,
  getAuthCode,
  getCurQuotationItem,
  getQuotationDetail,
  getUnPayOrders,
  handelDpWeightTips,
  handleAlipayCoupon,
  handleBatchOrderTips,
  handleCheckCreditService,
  handleECode,
  handleExtraData,
  handleHTBrandModal,
  handleMiniPostPrinter,
  handleOpenCredit,
  handleSecondConfirmOrder,
  handleSFKYDefaultWeight,
  handleSpringFestive,
  interceptChannel,
  interceptCreditFailOrder,
  loadCourierConfig,
  onShowFeeWay,
  updateReceiveList,
  checkActivityWithReturn,
  checkIsReturnActivityNewUser,
  BAD_ORDER_INTERCEPT_CODE,
  handleBadOrderIntercept,
  checkIsAddFee,
  showAddFeeModal,
} from '~/components/_pages/order/_utils/order.edit';
import KbStoreCardSelector from '~/components/_pages/store-card/selector';
import { checkCustomerAuthStatus } from '~/components/_pages/store-card/_utils';
import { homeActivityReportAnalytics } from '~/components/_pages/user/activity/_utils';
import { getUserVipInfo } from '~/components/_pages/user/_utils';
import { tabItemTapCall } from '~/components/_pages/_utils';
import apis from '~/utils/apis';
import { scanAction } from '~/utils/scan';
import { getShareAppMessage } from '~/utils/share';
import { getShareTimeline } from '~/utils/shareTimeline';
import { sendNoticeWkd } from '~/utils/subscribe';
import KbAntForest from '~base/components/ant-forest';
import KbButton from '~base/components/button';
import KbCheckbox from '~base/components/checkbox';
import KbLinkService from '~base/components/link-service';
import KbLoginAuthAndBind from '~base/components/login/authAndBind';
import KbModal from '~base/components/modal';
import KbPage from '~base/components/page';
import KbScrollView from '~base/components/scroll-view';
import Form from '~base/utils/form';
import logger from '~base/utils/logger';
import { getLaunchParams } from '~base/utils/navigator';
import {
  creatSchemeLink,
  debounce,
  getBoundingClientRect,
  getStorageSync,
  getUserStateByComplete,
  noop,
  removeStorage,
  reportAnalytics,
  scanParse,
  setStorage,
  setStorageSync,
  now,
} from '@base/utils/utils';
import { Image, Text, View } from '@tarojs/components';
import { connect } from '@tarojs/redux';
import Taro, { Component, createRef, Fragment } from '@tarojs/taro';
import isArray from 'lodash/isArray';
import isObject from 'lodash/isObject';
import isEqual from 'lodash/isEqual';
import { AtButton, AtCurtain, AtFloatLayout, AtIcon } from 'taro-ui';
import GuideReceiveCoupon from '@/components/_pages/order/kxj/center/guideReceiveCoupon';
import { flowCallback } from '@/utils/flow-callback';
import { getActivityDetail } from '~/components/_pages/order/kxj/_utils';
import { REFRESH_KEY_KXJ_COUPON, refreshControl } from '~/utils/refresh-control';
import ChooseCoupon from '~/components/_pages/order/kxj/businessCoupon/chooseCoupon';
import GuideUseCoupon from '~/components/_pages/order/kxj/guideUseCoupon';
import KbBadOrderIntercept from '~/components/_pages/order/edit/bad-order-intercept';
import KbDeclarationAgreement from '~/components/_pages/order/edit/declaration';
import KbStopReceiveOrder from '~/components/_pages/order/edit/stop-receive-order';
import OrderNotice from '~/components/_pages/order/notice';
import AuthPrompt from '~/components/_pages/auth-prompt';
import { checkIsReturnOrder } from '~/lib/config/config.page';
import './index.scss';

const adAgs = getAdExtensionReq('order.edit');
const dakExtra = '此驿站暂不支持上门取件，下单后，请前往驿站投递包裹。';

@connect(({ global }) => ({
  loginData: global.loginData,
  serviceConfig: global.serviceConfig,
  cacheRelationInfo: global.relationInfo,
}))
class Index extends Component {
  constructor() {
    this.state = {
      realnamed: false,
      relationInfo: {
        dakId: '',
        courier_id: '',
        relation_id: '',
      },
      cur: 0,
      stepInfo: ['填写寄件信息', '填写商家退货地址', '退货下单'],
      relationData: null,
      form: { data: {}, disabled: true },
      dynamicForms: fixDynamicFormsData(),
      addressData: null,
      total: 1,
      extraInfo: {},
      extraInfoData: null,
      estimatedFeeActive: false,
      estimatedData: {},
      equityCard: {},
      isOpenCredit: false,
      agree: true,
      scanCoupon: '',
      miniPostPrinterData: '',
      hTBrandTipsData: 0,
      springFestiveOpen: false,
      interceptChannelOpen: false,
      relationInfoLoading: false,
      oAsyncBatchStatus: {},
      courierConfig: {},
      cacheAddress: getStorageSync('address'),
      declarationAgree: false,
    };
    this.checkEstimatedFee = debounce(this.checkEstimatedFee, 300, {
      trailing: true,
    });
    this.checkYjkdCourier = debounce(this.checkYjkdCourier, 300, {
      trailing: true,
    });
    this.handleSubmitForm = debounce(this.handleSubmitForm, 500, {
      leading: true,
      trailing: false,
    });
    this.checkAsyncBatchStatus = debounce(this.checkAsyncBatchStatus, 300, {
      leading: false,
      trailing: true,
    });
    this.interceptChannel = debounce(this.interceptChannel, 500, {
      trailing: true,
    });
    // refs
    this.realnameRef = createRef();
    this.extraInfoRef = createRef();
    this.addressEditRef = createRef();
    this.agreeActionRef = createRef();
    this.estimatedFeeListRef = createRef();
    this.couponModalRef = createRef();
  }
  config = {
    navigationBarTitleText: '寄快递',
    backgroundColorTop: '#f2f2f2',
  };

  onTabItemTap = tabItemTapCall;

  checkIsReturn = () => checkIsReturnOrder(this.props.cacheRelationInfo);

  componentDidMount() {
    const { q } = this.$router.params;
    if (q) {
      const { url } = scanParse(q);
      url === 'dd' && this.dealRelationLoading(true);
    }
    this.createForm(() => {
      this.fixFormUpdateData(this.formFixData, 'createForm');
    });
    const { source } = Taro.launchParams || {};
    if (source) {
      // 上报
      reportAnalytics({
        key: 'launch_source',
        source,
      });
    }
    checkIsReturnActivityNewUser().then((isReturnActivityNewUser) => {
      this.setState({
        isReturnActivityNewUser,
      });
    });
  }

  componentDidShow() {
    Taro.setNavigationBarTitle({
      title: this.checkIsReturn() ? '网购退货' : '寄快递',
    });
    if (this.checkIsReturn()) {
      const { brand } = this.props.cacheRelationInfo || {};
      const cacheBrand = getStorageSync('returnInitBrand').data;
      const initBrand = brand || cacheBrand || 'yt';
      Taro.kbUpdateRelationInfo({ isReturnModule: 1, brand: initBrand });
      const hasReceive = this.state.cacheAddress && this.state.cacheAddress.data.receive_address;

      hasReceive && this.setState({ cur: 1 });
    }
    this.handleCheckCreditService();
    this.handleSecondConfirmOrder();
    preloadAd(['order.result']);
    this.checkAsyncBatchStatus();
    // 获取会员状态
    getUserVipInfo().then((data) => {
      this.setState({
        userVipData: data,
      });
    });
    const { type: relationType, courier_id } = this.state.relationInfo;
    if (relationType == 'courier') {
      this.loadCourierConfig(courier_id);
    }
    if (refreshControl(REFRESH_KEY_KXJ_COUPON, 'check')) {
      this.checkIsReceiveCoupon();
    }
  }

  componentDidHide() {
    this.clearBatchStatusCheck();
  }

  componentWillUnmount() {
    this.htBrandTimer && clearInterval(this.htBrandTimer);
  }

  //分享
  onShareAppMessage = getShareAppMessage;
  // 朋友圈分享
  onShareTimeline = getShareTimeline;

  // 登录状态更新
  onUpdate = (data) => {
    const { logined } = data;
    this.logined = logined;
    if (logined) {
      this.checkIsReceiveCoupon();
      this.checkAsyncBatchStatus();
      // 扫码寄
      scanAction(null, this)
        .then((res) => {
          console.log('寄件页.扫码寄.参数', res);
        })
        .catch((err) => console.log(err));
      // 外部进入时带着下单关系的场景
      let params = getLaunchParams(this);
      if (params.scene) {
        let { query = {} } = scanParse({ scene: params.scene });
        params = {
          ...params,
          ...query,
        };
      }
      this.onPostMessage('routerParamsChange', {
        params,
      });
    }
  };

  // 监听
  onPostMessage = (key, data) => {
    console.log('key', key, data);

    switch (key) {
      case 'routerParamsChange':
        // 路由参数变更
        const {
          params: {
            couponInfo,
            address,
            extraInfo,
            extraData,
            type = '',
            sign,
            uid,
            sub_uid,
            weight,
            ...rest
          } = {},
          source,
          data: formData,
        } = data;
        if (source === 'goods') {
          this.setState({
            extraInfoData: formData,
          });
        }
        if (address) {
          this.setState({
            addressData: address,
          });
          if (extraInfo) {
            this.setState({
              extraInfoData: extraInfo,
            });
          }
          if (type === 'clone') {
            // 再来一单，重新下单
            this.cleanFormInfo(['estimatedData']);
          }
        }
        if (sign) {
          // 快递码
          this.handleECode({ sign });
        }
        if (uid || sub_uid) {
          // 邀请下单码-小程序码、分享
          bindActivity(uid || sub_uid);
        }
        if (extraData && extraData.type) {
          this.handleExtraData(extraData);
        }
        if (couponInfo) {
          this.onPostMessage('cardSelect', couponInfo);
        }
        if (weight) {
          this.setState({
            extraInfoData: {
              goods_weight: weight,
            },
            urlQuery: data.params || {},
          });
        }
        // 进入寄快递携带下单信息
        const {
          brand,
          courier_phone,
          phone = courier_phone,
          join_code,
          dakId,
          dak_id = dakId,
          customer_id,
        } = rest;

        const relationList = [brand, phone, join_code, dak_id, customer_id];
        const hasRelation = relationList.filter((item) => !!item).length > 0;
        if (hasRelation) {
          this.setState({
            relationData: rest,
          });
        }
        break;
      case 'temporaryBack':
        this.handleSubmitForm('submitTemporary');
        break;
      case 'cardSelect':
        if (process.env.PLATFORM_ENV === 'alipay' && data) {
          const _couponInfo = data.couponInfo || data || {};
          console.log('券信息', _couponInfo);
          this.setState({
            activityCouponInfo: _couponInfo,
          });
          if (_couponInfo.card_type === 'kxj') {
            this.setState({
              alipayActivityCoupon: null,
            });
          } else if (_couponInfo.voucher_id) {
            this.setState({
              alipayActivityCoupon: _couponInfo,
              activityCouponInfo: null,
            });
          }
        }
        break;
    }
  };

  // 更新下单关系数据
  updateRelationInfo = (data) => {
    const { dynamicForms, isDefault, smjData, type, brand, courier_id } = data || {};
    let state = {
      relationInfo: {
        ...data,
        disabled: !!smjData, //扫码寄切换下单对象
      },
    };
    state.dynamicForms = fixDynamicFormsData(dynamicForms ? dynamicForms : {});
    this.setState({ ...state }, () => {
      this.checkYjkdCourier(isDefault);
      this.handleSFKYDefaultWeight();
      this.checkEstimatedFee();
      this.interceptChannel((notActive) => {
        this.setState({
          interceptChannelOpen: notActive,
        });
      });
      if (type == 'brand' && brand == 'ht') {
        this.handleHTBrandModal('open');
      }
      if (type == 'courier') {
        this.loadCourierConfig(courier_id);
      }
    });
    logger.info('下单关系变化-updateRelationInfo', data);
  };

  // 处理下单对象loading
  dealRelationLoading(relationInfoLoading = false) {
    this.setState({
      relationInfoLoading,
    });
  }

  // 处理变化事件
  onChange = (key, e) => {
    const { data = {}, nextData = {}, receiveList } = e || {};
    switch (key) {
      case 'address':
        const { receive_province, send_province } = nextData;
        receive_province && send_province && (data.disabled = false);
        this.fixFormUpdateData(data, 'onChange');
        this.updateReceiveList(receiveList);
        this.checkYjkdCourier();
        this.checkEstimatedFee();
        break;
      case 'info':
        // 物品信息变化
        this.setState(
          {
            extraInfo: nextData,
          },
          () => {
            this.handleSFKYDefaultWeight();
            this.checkEstimatedFee();
          },
        );
        break;
      case 'estimatedFee':
        this.estimatedFee = data.estimatedFee || 0;
        break;
      case 'equityCard':
        this.equityCard = data;
        break;
      case 'coupon':
        this.coupon = data;
        break;
    }
  };

  // 处理滑动区域相关事件
  onHandleMoveArea = (key = 'size', data = {}) => {
    // console.log('处理滑动区域相关事件', key, data);
    const { moveAreaStatus, relationInfo = {} } = this.state;
    const { changeSource, quotationList, brand: chooseBrand } = data || {};
    if (key == 'status') {
      this.setState({
        moveAreaStatus: data.status,
      });
    } else if (key == 'change') {
      if (data.product_code) {
        this.setState({
          extraInfoData: data,
        });
        return;
      }
      if (changeSource == 'list') {
        if (moveAreaStatus !== 'max') {
          this.onHandleMoveArea('size');
        }
        if (quotationList) {
          this.handleAlipayCoupon();
          this.quotationList = quotationList;
          const { order_type = 'sto' } =
            getCurQuotationItem(relationInfo.brand || '', quotationList) || {};
          this.setState({
            order_type,
          });
        }
      }
      if (changeSource == 'chooseBrand') {
        this.handleAlipayCoupon(chooseBrand);
        const { weight: min_goods_weight, brand: url_brand } = this.state.urlQuery || {};
        const { goods_weight: cur_goods_weight } = this.state.extraInfoData || {};
        const goods_weight =
          chooseBrand == url_brand && cur_goods_weight < min_goods_weight
            ? min_goods_weight
            : cur_goods_weight;
        this.setState({
          extraInfoData: {
            goods_weight,
          },
        });
      }
    } else {
      const {
        relationInfo: { platform },
      } = this.state;
      this.minHeight = 150;
      this.smallHeight = 50;
      if (platform == 'yjkd_brand' || this.checkIsReturn()) {
        try {
          Promise.all(
            [
              '.yj-container-msg',
              {
                selector: '.kb-estimatedFeeList-list--content',
                scope: this.estimatedFeeListRef.current.$scope,
              },
            ].map((item) => getBoundingClientRect(item)),
          ).then((e) => {
            const [cRes, cRes2] = e;
            if (!cRes || !cRes2) {
              this.onHandleMoveArea(key, data);
              return;
            }
            let contentHeightMax = cRes.top - 120;
            let contentHeight = cRes2.height;
            this.maxHeight = Math.min(contentHeight, contentHeightMax);
            this.setState({
              moveAreaData: {
                curHeight: this.minHeight, // 当前高度
                smallHeight: this.smallHeight, // 最小高度
                minHeight: this.minHeight, // 默认高度
                maxHeight: this.maxHeight, // 最大高度
              },
              moveAreaPagePadding: cRes.height + this.minHeight,
            });
          });
        } catch (e) {
          this.onHandleMoveArea(key, data);
        }
      }
    }
  };

  // 切换是否同意
  onSwitchAgree = (agree) => {
    this.setState({ agree });
  };

  // 创建表单&表单变化
  onChange_form = () => {};
  onSubmit_form = () => {};
  createForm = (onReady) => {
    // 所有表单只包含地址信息，其他数据通过formatRequest合并
    // 这种方式可以利用form.disabled 判断是否展开物品信息
    // this.receiveList 批量下单时的收件人列表数据
    this.formIns = new Form(
      {
        form: getForm(),
        enableEmpty: false,
        api: {
          url: () => {
            const {
              relationInfo: { type: relationType, platform },
            } = this.state;
            if (this.submitAction == 'submitTemporary') {
              return apis[`order.edit.temporary`];
            }
            if (platform == 'yjkd_brand' || platform == 'yjkd_courier') {
              return apis[`order.edit.yjkd`];
            }
            return apis[`order.edit.${relationType}`];
          },
          toastError: true,
          onIntercept: (req, onThen) => {
            // 拦截批量请求
            const {
              relationInfo: { type: relationType, platform, brand, dynamicForms } = {},
              isOpenCredit,
            } = this.state;
            const { pay } = dynamicForms || {};
            if (this.submitAction == 'submitTemporary') {
              if (relationType != 'courier') {
                Taro.kbToast({
                  text: '该下单对象暂不支持临时单!',
                });
                return true;
              }
            }
            if (this.receiveList) {
              if (platform === 'yjkd_brand' && !isOpenCredit && pay == 2) {
                this.interceptCreditFailOrder('no_auth2');
                return true;
              }
              this.batchSubmitOrder({ reqData: req })
                .then((res) => onThen(res, req))
                .catch((err) => console.log(err));
              return true;
            }
            //下单立即开通支付分;
            if (process.env.PLATFORM_ENV == 'alipay') {
              // 用户勾选支付宝券，未开通【先寄后付】，下单选择支持快递员线下单品牌渠道，点击-提交订单,拦截用户提示
              if (
                !this.IsBackFromCreditPage &&
                !isOpenCredit &&
                platform == 'yjkd_brand' &&
                pay == 3 &&
                req.activity == 'alipay_marketing_voucher'
              ) {
                this.IsBackFromCreditPage = false;
                Taro.kbModal({
                  content: '如未开通【先寄后付】则无法使用优惠折扣；',
                  confirmText: '去开通',
                  cancelText: '放弃优惠',
                  onConfirm: () => {
                    this.handleOpenCredit({
                      isForceOpen: 0,
                      req,
                    });
                  },
                });
                return true;
              }
              if (
                !this.IsBackFromCreditPage &&
                !isOpenCredit &&
                platform == 'yjkd_brand' &&
                (pay == 2 || pay == 3) &&
                !req.coupon_id
              ) {
                this.IsBackFromCreditPage = false;
                this.handleOpenCredit({
                  isForceOpen: pay == 2 ? 1 : 0,
                  req,
                });
                return true;
              }
            }
            // 德邦重量提示
            let dpWeight = req.package_weight || 1;
            let isOpenDpWeightNoMore = this.handelDpWeightTips('check');
            if (
              platform === 'yjkd_brand' &&
              brand === 'dp' &&
              isOpenCredit &&
              dpWeight < 3 &&
              !isOpenDpWeightNoMore &&
              this.submitAction != 'dpWeightTipsConfirm'
            ) {
              this.handelDpWeightTips('open');
              return true;
            }
            // 未使用券提示
            if (
              this.checkActivity() &&
              !isOpenCredit &&
              req.activity != 'alipay_marketing_voucher' &&
              req.activity != 'ali_dd_promotion' &&
              req.coupon_id &&
              !this.withoutCouponLock
            ) {
              this.handelGuideCoupon('open');
              return true;
            }
            this.withoutCouponLock = false;
          },
          formatRequest: ({ extraData: [action = 'submit'] = [], ...req }) => {
            this.submitAction = action || 'submit';
            const { send_save, receive_save, order_interception } = req;
            const {
              relationInfo: {
                type: relationType,
                account_phone,
                dak_id,
                join_code,
                brand,
                platform = '',
                courier = {},
                smjData,
                customer,
                dynamicForms = {},
              } = {},
              extraInfo: {
                goods_name,
                goods_weight,
                goods_remark,
                package_images,
                orderPrice,
                reserve_start_time = '1h',
                reserve_end_time = '1h',
                product_code = '',
                volume = {},
                service,
                brand: print_brand,
                product_type: print_product_type,
              } = {},
              courierConfig = {},
              alipayActivityCoupon,
              activityDetail = {},
              total = 0,
            } = this.state;
            const isOpenOnlinePay = courierConfig && courierConfig.isOpenOnlinePay == 1;
            const {
              goods_name: dynamicGoods_name,
              appointmentTime: dynamicAppointmentTime,
              service: dynamicService,
              product_code: dynamicProduct_code,
              volume: dynamicVolume,
            } = dynamicForms || {};
            const { oService, arrive_pay, collection, cost_value, keep_account } = service || {};
            let addressData = transferWkdAddress(req);
            req = {
              ...req,
              ...addressData,
              is_fresh:
                isFresh(goods_name) || (dynamicGoods_name && dynamicGoods_name.customFresh)
                  ? '1'
                  : '0', //标志生鲜
              package_info: goods_name,
              package_weight: goods_weight,
              package_note: goods_remark,
              check_pics: isArray(package_images) ? package_images.join(',') : '',
            };
            // 恶意下单拦截-校验通过
            if (order_interception) {
              req.order_interception = order_interception;
            }
            if (dynamicAppointmentTime && dynamicAppointmentTime.isShow) {
              req.reserve_start_time = reserve_start_time;
              req.reserve_end_time = reserve_end_time;
              if (reserve_start_time == '1h' || !reserve_start_time) {
                req.reserve_start_time = '1h';
                req.reserve_end_time = '1h';
              }
            }
            if (dynamicService && dynamicService.isShow) {
              if (platform == 'yjkd_brand' && isObject(oService)) {
                req = {
                  ...req,
                  ...oService,
                };
              } else {
                console.log('cost_value', cost_value);
                req = {
                  ...req,
                  decVal: keep_account, //声明物品价值
                  proPrice: cost_value, //保价
                  collection_amount: collection, //代收货款
                  to_pay_amount: arrive_pay, //到付
                };
              }
            }
            if (dynamicProduct_code && dynamicProduct_code.isShow) {
              req.product_code = product_code;
            }
            if (this.checkIsReturn()) {
              req.is_merchant_return = 1;
            }
            if (dynamicVolume && dynamicVolume.isShow && isObject(volume)) {
              if (volume.checked) {
                req = {
                  ...req,
                  ...volume,
                };
              }
            }
            req.shipper_default = this.checkIsReturn() ? 1 : send_save || 0;
            req.shipping_default = this.checkIsReturn() ? 1 : receive_save || 0;
            req.channel = `mina_${Taro.systemInfo.platform}`;
            switch (relationType) {
              case 'courier':
                req.counterman_mobile = account_phone;
                req.source = Taro.expSource || 'focus';
                if (orderPrice > 0) {
                  req.freight = orderPrice;
                }
                if (this.equityCard && this.equityCard.card_id && !isOpenOnlinePay) {
                  req.card_id = this.equityCard.card_id;
                }
                // 填写快递员取件地址
                const isPickupAddress = courierConfig && courierConfig.isPickupAddress == '1';
                const pickupAddress = Taro.kbGetGlobalData('pickupAddress') || {};
                if (isPickupAddress && pickupAddress && pickupAddress.address) {
                  req.new_shipper_name = pickupAddress.name;
                  req.new_shipper_mobile = pickupAddress.mobile;
                  req.new_shipper_address = `${pickupAddress.province} ${pickupAddress.city} ${pickupAddress.district} ${pickupAddress.address}`;
                }
                // 预约时间
                const is_reserve = courierConfig && courierConfig.is_reserve == '1';
                if (is_reserve) {
                  req.reserve_start_time = reserve_start_time;
                  req.reserve_end_time = reserve_end_time;
                  if (reserve_start_time == '1h' || !reserve_start_time) {
                    req.reserve_start_time = '2h';
                    req.reserve_end_time = '2h';
                  }
                }
                break;
              case 'dak':
                req.inn_id = dak_id;
                break;
              case 'team':
                req.source = Taro.expSource || 'focus';
                req.join_code = join_code;
                break;
              case 'brand':
                req.brand = brand;
                if (platform == 'yjkd_courier') {
                  req.platform = 'yjkd';
                  req.courier_id = courier.courier_id;
                  if (this.coupon && this.coupon.id) {
                    req.couponId = this.coupon.id;
                  }
                } else if (platform == 'yjkd_brand') {
                  req.platform = 'yjkd';
                  let quotationData = Taro.kbGetGlobalData('quotation') || [];
                  let quotationItem = {};
                  if (quotationData && quotationData.length > 0) {
                    quotationItem = quotationData.find((item) => item.brand == brand) || {};
                  }
                  if (quotationItem && quotationItem.discount_price) {
                    req.freight = quotationItem.discount_price;
                  }
                  if (this.checkActivityWithReturn() && total <= 1) {
                    const curQuotationItem = getCurQuotationItem(brand, this.quotationList);
                    if (process.env.PLATFORM_ENV === 'alipay') {
                      const { voucher_id, activity_id } = alipayActivityCoupon || {};
                      if (voucher_id) {
                        req.coupon_id = voucher_id;
                        req.activity = 'alipay_marketing_voucher';
                        req.alipay_activity_id = activity_id;
                      } else if (
                        curQuotationItem &&
                        curQuotationItem.coupon_id &&
                        !curQuotationItem.exists_alipay_coupon
                      ) {
                        req.coupon_id = curQuotationItem.coupon_id;
                        if (curQuotationItem.activity) {
                          req.activity = curQuotationItem.activity;
                        }
                      }
                    } else {
                      if (activityDetail && activityDetail.is_new == 1) {
                        req.activity = 'new_customer';
                      }
                      if (curQuotationItem && curQuotationItem.coupon_id) {
                        req.coupon_id = curQuotationItem.coupon_id;
                        if (curQuotationItem.activity) {
                          req.activity = curQuotationItem.activity;
                        }
                      }
                    }
                  }
                }
                break;
            }
            if (print_brand && relationType != 'brand') {
              req.brand = print_brand;
              if (print_product_type) {
                req.waybillType = print_product_type;
              }
            }
            if (smjData && smjData.relation_info) {
              req.source = 'ship_code';
              req.relation_id = smjData.relation_info.relation_id;
            }
            if (customer && customer.id) {
              req.customer_id = customer.id;
            }
            if (this.submitAction == 'submitTemporary') {
              // 临时单情况下，去掉收件地址参数
              for (let key in req) {
                if (key.includes('shipping')) {
                  delete req[key];
                }
              }
            }
            return req;
          },
          onCustomCheck: (req) => {
            const {
              relationInfo: {
                type: relationType,
                dynamicForms = {},
                smjData,
                platform,
                brand,
              } = {},
              agree,
            } = this.state;
            const { serviceConfig } = this.props;
            const { isDecVal } = serviceConfig || {};
            const {
              service: dynamicService,
              weightLimitMax,
              weightLimitMin,
              pay,
            } = dynamicForms || {};
            const { relation_info: smjRelationInfo } = smjData || {};
            const { is_collection } = smjRelationInfo || {};
            const { package_info, package_weight, counterman_mobile } = req;
            if (!agree) {
              return { code: 101, msg: '请勾选服务协议!' };
            }
            if (!relationType || (relationType == 'courier' && !counterman_mobile)) {
              return { code: 101, msg: '请选择揽件快递员' };
            }
            if (!package_info) {
              return { code: 101, msg: '请选择物品类型!' };
            }
            if (dynamicService && dynamicService.isShow) {
              //检查顺丰快运-保价增值服务
              if (!req.proPrice && req.brand == 'sfky') {
                return { code: 101, msg: '请勾选保价服务!' };
              }
              //开启强制声明物品值，没有声明，需阻止
              if (isDecVal && !req.decVal) {
                return { code: 101, msg: '请声明物品价值后下单!' };
              }
            }
            if (weightLimitMax && package_weight > weightLimitMax * 1) {
              return {
                code: 101,
                msg: `此品牌暂不支持${weightLimitMax}KG以上的货物寄递；`,
              };
            }
            if (weightLimitMin && package_weight < weightLimitMin * 1) {
              return {
                code: 101,
                msg: `此品牌暂不支持低于${weightLimitMin}KG的货物寄递；`,
              };
            }
            if (smjRelationInfo) {
              //扫码寄验证必要增值服务是否填写
              if (this.receiveList) {
                return { code: 101, msg: '扫码寄无法批量寄件!' };
              }
              if (is_collection == 1 && !req.collection_amount) {
                return { code: 101, msg: '请在增值服务中填写代收金额！' };
              }
              if (is_collection == 2 && !req.to_pay_amount) {
                return { code: 101, msg: '请在增值服务中填写到付金额！' };
              }
            }
            if (platform == 'yjkd_brand') {
              const { available, unavailable_msg } = getQuotationDetail(brand) || {};
              if (available <= 0 && unavailable_msg) {
                return { code: 101, msg: unavailable_msg };
              }
              if (
                this.checkIsReturn() &&
                !this.outCheck &&
                !this.state.isOpenCredit &&
                !this.jumpZffCheck
              ) {
                this.interceptCreditFailOrder('no_auth', false, {
                  isForceOpen: pay == 2 ? 1 : 0,
                  req,
                });
                return {
                  code: -1,
                };
              }
              this.jumpZffCheck = false;
            }
          },
          onThen: this.createDone,
        },
        onUpdate: (data) => {
          const { eventType, data: addressData } = data;
          if (eventType === 'clean') {
            // 清除操作
            this.quotationList = [];
            this.setState({
              alipayActivityCoupon: '',
            });
            this.cleanFormInfo(['addressData', 'extraInfoData', 'estimatedData'], { addressData });
          }
        },
        onReady,
      },
      this,
    );
  };

  fixFormUpdateData = (data = {}, source) => {
    if (this.formIns) {
      data && this.formIns.update(data);
      this.formFixData = null;
      if (data && data.receive_name) {
        logger.collect({
          address: {
            data,
            ts: now(),
            source,
          },
        });
      } else {
        logger.collect({
          emptyAddress: {
            data,
            ts: now(),
            source,
          },
        });
      }
    } else {
      this.formFixData = data;
    }
  };

  getFormData = () => {
    // 主要用于弥补主表单和子表单可能数据不一致的问题
    const { formIns: subFormIns } = this.addressEditRef.current || {};
    const { nextData = {} } = subFormIns || {};
    const _formData = nextData;
    if (this._formData && isEqual(this._formData, _formData)) {
      return this._formData;
    }
    this._formData = _formData;
    return _formData;
  };

  // 创建订单完成
  createDone = ({ code, data }, req) => {
    console.log('createDone==>', req);
    this.jumpZffCheck = false;
    let {
      order_number,
      order_id,
      describe,
      realname,
      upload,
      notActive,
      notInRange,
      mustCredit,
      online_pay,
      pass_time,
      ordersNum,
      succeed_order_ids,
      wx_after_pay,
      wx_after_pay_bind,
      collect_code,
      card_id,
      async_create_order,
      fail = [],
      reason = '',
      result = [],
    } = data || {};
    const { collection_amount, to_pay_amount, is_fresh, package_info } = req || {};
    const {
      relationInfo,
      relationInfo: {
        type: relationType,
        brand,
        platform,
        dynamicForms,
        courier,
        longitude,
        latitude,
        inn_name,
        address,
        smjData,
      },
      isReturnActivityNewUser,
    } = this.state;
    const { pay, cutPayDesc } = dynamicForms || {};
    // 触发协议签署
    this.agreeActionRef.current.signAgreement();
    logger.info('下单完成', code, data, req);
    logger.setFilterMsg(req.shipper_mobile || '000', '2025-04-01');
    if (req.brand === 'yjkd' && !req.platform) {
      logger.setFilterMsg('异常快递品牌');
    }
    if (code == 0) {
      // 支付宝流量回调
      flowCallback({ type: 'order' });
      if (this.checkIsReturn()) {
        setStorageSync('returnInitBrand', brand);
        this.setState({ cur: 0 });
      }
      const { query: { q } = {} } = Taro.getLaunchOptionsSync() || {};
      if (q && relationType == 'brand') {
        logger.info('订单提交完成', brand, platform);
        logger.setFilterMsg('非快递员');
      }
      // 拦截处理
      if (mustCredit > 0) {
        this.handleSpringFestive('open');
        return;
      }
      if (upload || realname) {
        this.realnameRef.current.interceptRealname({
          describe,
          realname,
          upload,
        });
        return;
      }
      if (notActive) {
        Taro.kbModal({
          content: notActive,
          confirmText: '我知道了',
        });
        return;
      }
      if (notInRange) {
        Taro.kbModal({
          content: notInRange,
          confirmText: '重选快递品牌',
          onConfirm: () => {
            Taro.navigator({
              url: 'order/relation',
              options: {
                type: relationType,
              },
            });
          },
        });
        return;
      }
      // 批量下单失败-同步模式
      if (fail && isArray(result) && result.length > 0) {
        reason && result.unshift(reason);
        if (isArray(fail) && fail.length > 0) {
          const list = this.receiveList.filter((item, index) => {
            return fail.findIndex((i) => i == index) > -1;
          });
          if (
            this.addressEditRef.current &&
            this.addressEditRef.current.updateFormDataByReceiveList
          ) {
            this.addressEditRef.current.updateFormDataByReceiveList(list);
            this.updateReceiveList(list);
          }
        }
        Taro.kbModal({
          content: result,
        });
        return;
      }
      // 强制开通支付分，如极兔品牌
      if (platform == 'yjkd_brand' && pay == 2 && !order_id) {
        if (wx_after_pay <= 0) {
          this.interceptCreditFailOrder('auth_fail', false, {
            isForceOpen: pay == 2 ? 1 : 0,
            req,
          });
          return;
        } else if (wx_after_pay_bind <= 0) {
          this.interceptCreditFailOrder('bind_fail', false, {
            isForceOpen: pay == 2 ? 1 : 0,
            req,
          });
          return;
        }
      }
      // 驿站小程序，半屏打开后下单成功上报
      const { source } = Taro.launchParams || {};
      if (source) {
        // 上报
        reportAnalytics({
          key: 'launch_source',
          source: `${source}_order_success`,
        });
      }
      // 清除编辑状态
      this.cleanEditStatus(() => {
        // 批量异步下单，不做处理
        if (async_create_order == 1) return;
        let url = 'order/result';
        let options = {};
        //跳转页面
        order_id = order_number || order_id;
        if (this.submitAction == 'submitTemporary') {
          Taro.navigator({
            url,
            options: {
              source: 'temporary',
              status: 'start',
              order_id,
            },
          });
          return;
        }
        if (smjData) {
          // 扫码寄
          options = {
            label: 'scan',
            is_fresh,
            package_info,
            collect_code,
            collection_amount,
            to_pay_amount,
          };
        }
        switch (relationType) {
          case 'courier':
            if (process.env.PLATFORM_ENV !== 'swan') {
              if (online_pay > 0) {
                url = 'order/pay';
              }
            }
            options = {
              ...options,
              customer_id: req.customer_id || '',
              status:
                card_id && card_id > 0
                  ? 'equity'
                  : collection_amount || to_pay_amount
                  ? ''
                  : 'credit',
            };
            break;
          case 'brand':
            if (platform == 'yjkd_brand') {
              // 兼容报价单数据可能存在的异步清除问题
              let quotationData = Taro.kbGetGlobalDataOnce('quotation') || [];
              Taro.kbSetGlobalData('quotationList', quotationData);
            }
            const { is_new, is_order } = this.activityDetail || {};
            options = {
              brand: platform == 'yjkd_courier' ? courier.brand : brand,
              platform,
              status: platform == 'yjkd_courier' || pay == 2 || pay == 3 ? 'credit' : '',
              cutPayDesc,
              estimatedFee: platform == 'yjkd_courier' ? this.estimatedFee : 0,
              wx_after_pay,
              wx_after_pay_bind,
              isActivity: req.activity ? 1 : 0,
              isGiveVip: is_new == 1 && is_order != 1 ? 1 : 0,
              pageSource: this.checkIsReturn() ? 'tuihuo' : '',
              isReturnActivityNewUser: isReturnActivityNewUser || 0,
            };
            if (this.checkIsReturn() && isReturnActivityNewUser == 1) {
              this.setState({
                isReturnActivityNewUser: 0,
              });
            }
            break;
          case 'team':
            url = 'order/detail';
            options = {
              source: '',
              type: 'team',
            };
            break;
          case 'dak':
            options = {
              ...options,
              customer_id: req.customer_id || '',
              longitude,
              latitude,
              inn_name,
              address,
            };
            break;
        }
        this.sendNotice(order_id);
        //批量寄订单id
        let order_ids = succeed_order_ids ? succeed_order_ids : [order_id];
        Taro.kbSetGlobalData('order_ids', order_ids);
        options = {
          source: relationType,
          order_id,
          order_ids,
          pass_time,
          ordersNum,
          ...options,
        };
        const navigatorPage = () => {
          this.setState({
            declarationAgree: false,
          });
          // 首页弹窗统计
          if (Taro.homeActivityRecord) {
            homeActivityReportAnalytics('下单', Taro.homeActivityRecord);
            if (platform == 'yjkd_brand' && brand) {
              homeActivityReportAnalytics(`下单-${brand}`, Taro.homeActivityRecord);
            }
            Taro.homeActivityRecord = '';
          }
          relationInfo.storageWay = 'order';
          if (smjData) {
            relationInfo.smjData = null;
            Taro.kbUpdateRelationInfo(relationInfo);
          }
          console.log('缓存relationInfo', relationInfo);

          const editOrderType = this.checkIsReturn()
            ? 'th'
            : relationType == 'brand'
            ? 'yjkd'
            : relationType == 'dak'
            ? 'dak'
            : 'courier';
          Taro.kbTriggerStorageRelation(relationInfo, null, editOrderType);
          if (process.env.PLATFORM_ENV === 'swan') {
            // 百度去除支付分标记
            const { status } = options;
            options.status = status === 'credit' ? '' : status;
          }
          Taro.navigator({
            url,
            options,
          });
        };
        if (
          process.env.PLATFORM_ENV == 'alipay' &&
          platform == 'yjkd_brand' &&
          (pay == 2 || pay == 3) &&
          order_id &&
          wx_after_pay == 1 &&
          wx_after_pay_bind == 1
        ) {
          console.info('检查是否需要下单二次确认', pay);
          //芝麻分用户确认逻辑
          confirmCredit({
            order_id,
            type: 'create',
            returnBackLink: creatSchemeLink({
              page: '/pages/order/result/index',
              query: 'creditSource=CreditSuccess',
            }),
            cancelBackLink: creatSchemeLink({
              page: pay == 2 ? '/pages/order/edit/send/index' : '/pages/order/result/index',
              query: `creditSource=${pay == 2 ? 'CreditAndOrderFail' : 'CreditFail'}`,
            }),
          }).then((cRes) => {
            console.info('二次确认跳转');
            console.info(cRes);
            if (cRes.code == 0 && cRes.data && cRes.data.sign) {
              console.info('需要=>二次确认跳转');
              //需要确认
              //缓存数据
              let quotation = Taro.kbGetGlobalDataOnce('quotationList') || [];
              setStorage({
                key: 'LastOrderResultData',
                data: {
                  quotation,
                  pay,
                  sh_order_number: cRes.data.order_number,
                  ...options,
                },
              });
              const ZhiMaCredit = Taro.requirePlugin('ZhiMaCredit');
              ZhiMaCredit.startService({
                type: cRes.data.type,
                sign_str: cRes.data.sign,
                zm_service_id: cRes.data.zm_service_id,
                success: () => {
                  console.info('芝麻分插件跳转成功');
                },
                fail: () => {
                  console.info('芝麻分插件跳转失败');
                  Taro.kbToast({
                    text: '芝麻分插件跳转失败',
                  });
                },
                complete: () => {},
              });
            } else {
              //不需要确认
              navigatorPage();
            }
          });
        } else {
          navigatorPage();
        }
      });
    } else if (code === BAD_ORDER_INTERCEPT_CODE) {
      this.handleBadOrderIntercept('open');
    }
  };

  // 清除编辑状态或清除批量寄件列表
  cleanFormInfo = (keys, replaceData) => {
    this.setState(cleanOrderEditFormInfo(keys, replaceData));
  };
  cleanEditStatus = (then = noop) => {
    refreshControl(REFRESH_KEY_KXJ_COUPON);
    Taro.kbSetGlobalData('pickupAddress', {});
    this.formIns.clean();
    then();
    this.updateReceiveList();
    removeStorage({
      key: receiveStorageKey,
    });
  };
  handleSubmitForm = async (action = '') => {
    const {
      relationInfo: { customer, type, courier_id, platform, brand } = {},
      form,
      extraInfo,
      declarationAgree,
    } = this.state;
    const { data: formData } = form || {};
    const { send_city } = formData || {};
    const { reserve_start_time } = extraInfo || {};
    if (this.checkIsShowDeclaration() && !declarationAgree && action != 'agreeDeclaration') {
      this.setState({
        isOpenDeclaration: Math.random() + 1,
      });
      return;
    }
    //如果大客户未通过审核，无法下单
    const customer_id = (customer && customer.id) || '';
    if (customer_id) {
      var customerAuthStatus = await checkCustomerAuthStatus(customer_id);
      if (customerAuthStatus != 1) return true;
    }
    if (type == 'courier') {
      const unPayOrders = await getUnPayOrders({
        courierId: courier_id,
        customer_id,
      });
      if (unPayOrders.length > 0) {
        this.setState({
          unPayOrders,
        });
        return;
      }
    }
    if (process.env.PLATFORM_ENV === 'alipay') {
      if (type === 'brand' && platform === 'yjkd_brand') {
        this.activityDetail = await getActivityDetail({ activity: 'ali_new_customer' });
      }
    }
    // 春节期间下单规则调整(https://tower.im/teams/258300/todos/109436/)
    // 检测是否存在加收费
    if (type == 'brand' && platform == 'yjkd_brand' && !this.checkIsAddFee) {
      const checkAddFeeRes = await checkIsAddFee({
        brand: brand,
        shipper_city: send_city,
        receive_start_time: reserve_start_time,
      });
      if (checkAddFeeRes) {
        showAddFeeModal(checkAddFeeRes).then(() => {
          this.checkIsAddFee = true;
          this.onSubmit_form();
        });
        this.setState({
          submitting: false,
        });
        return true;
      }
    }
    this.checkIsAddFee = false;
    getAuthCode().then(() => {
      this.onSubmit_form(action);
    });
  };
  handleCheck = () => {
    // outCheck 标记区分外部检查
    this.outCheck = true;
    const res = this.formIns.check();
    res.msg = res.msg + '\n请重新输入后再提交订单';
    const isError = res.msg.includes('收件人姓名不可为空');
    const formData = this.getFormData();
    if (isError && formData && formData.receive_name) {
      const { form: { data: PageFormData } = {} } = this.state;
      logger.setFilterMsg('下单报错', '2024-06-01');
      logger.collect({
        compFormData: formData,
        PageFormData: PageFormData,
        formIns: this.formIns,
      });
      logger.info('2024-06-01', 'log-collect', 'order-submit');
      this.formIns.update(formData);
    }
    this.outCheck = false;
    return res;
  };

  // 兼容老的订阅方案
  sendNotice = (order_id) => {
    if (!order_id) return;
    const { relationInfo } = this.state;
    const { type: relationType } = relationInfo || {};
    sendNoticeWkd({
      template_title: relationType === 'brand' ? 'grab_order' : 'order_notice',
      order_id,
    });
  };

  pageToStep = () => {
    Taro.navigator({
      url: 'order/edit/return-step',
    });
  };

  // 更新收件人类表
  updateReceiveList = updateReceiveList;
  // 处理额外数据
  handleExtraData = handleExtraData;
  // 处理快递码逻辑
  handleECode = handleECode;
  // 检测预估运费
  checkEstimatedFee = () => {
    this.setState({
      estimatedFeeActive: !this.state.estimatedFeeActive,
    });
  };
  // 检测支付分开通情况
  handleCheckCreditService = handleCheckCreditService;
  // 检测是否存在优寄快递员
  checkYjkdCourier = checkYjkdCourier;
  // 拦截下单对象:不活跃快递员;
  interceptChannel = interceptChannel;
  // 强制开通支付分提示
  interceptCreditFailOrder = interceptCreditFailOrder;
  // 处理二次确认失败订单
  handleSecondConfirmOrder = handleSecondConfirmOrder;
  // 优寄快递员计费说明
  onShowFeeWay = onShowFeeWay;
  // 执行开通支付分逻辑
  handleOpenCredit = handleOpenCredit;
  handleAccountChange = () => {
    this.setState({ nowTimer: new Date().getTime() });
  };
  // 处理小油桶打印机引导
  handleMiniPostPrinter = handleMiniPostPrinter;
  // 处理百世快递转换极兔快递弹窗
  handleHTBrandModal = handleHTBrandModal;
  // 春节快递快递品牌线上付通知
  handleSpringFestive = handleSpringFestive;
  // 不活跃快递员
  handleConfirmIntercept = () => {
    this.handleCloseIntercept();
    const relationInfo = { brand: 'sto', storageWay: 'scan' };
    Taro.kbUpdateRelationInfo(relationInfo);
    Taro.kbTriggerStorageRelation(relationInfo);
  };
  handleCloseIntercept = () => {
    this.setState({ interceptChannelOpen: false });
  };
  // 德邦下单重量提示
  handelDpWeightTips = handelDpWeightTips;
  // 处理顺丰快运默认重量
  handleSFKYDefaultWeight = handleSFKYDefaultWeight;
  // 批量提交订单
  batchSubmitOrder = batchSubmitOrder;
  // 检查异步批量状态
  checkAsyncBatchStatus = checkAsyncBatchStatus;
  clearBatchStatusCheck = clearBatchStatusCheck;
  // 处理批量提交相关事件
  handleBatchOrderTips = handleBatchOrderTips;
  loadCourierConfig = loadCourierConfig;
  // 清除未支付订单信息
  handleInterceptModalClose = () => {
    this.setState({
      unPayOrders: [],
    });
  };
  // 获取微信新客立减活动信息
  checkIsReceiveCoupon = () => {
    if (process.env.PLATFORM_ENV === 'weapp') {
      getActivityDetail({ vip: true }).then((res) => {
        this.setState({
          activityDetail: res,
        });
      });
    }
  };
  checkActivity = () => {
    const { relationInfo } = this.state;
    const { type: relationType, platform } = relationInfo || {};
    return relationType === 'brand' && platform == 'yjkd_brand' && this.checkActivityWithReturn();
  };
  // 商家券引导
  handelGuideCoupon = (key) => {
    switch (key) {
      case 'open':
        this.setState({
          isOpenGuideCoupon: Math.random() + 1,
        });
        break;
      case 'cancel':
        this.withoutCouponLock = true;
        this.handleSubmitForm();
        break;
      case 'confirm':
        this.withoutCouponLock = true;
        openCreditService();
        break;
    }
  };
  // 获取支付宝商家券
  handleAlipayCoupon = handleAlipayCoupon;
  // 检查商家券活动是否可用于网购退货
  checkActivityWithReturn = checkActivityWithReturn;
  handleNewUserActivityClick = () => {
    Taro.navigator({
      url: 'order/edit/return-step',
    });
  };
  // 恶意下单拦截
  handleBadOrderIntercept = handleBadOrderIntercept;
  // 是否显示快递员声明
  checkIsShowDeclaration = () => {
    const { serviceConfig } = this.props;
    const { declaration, isDeclared } = serviceConfig || {};
    const { relationInfo } = this.state;
    const { type: relationType } = relationInfo || {};
    return relationType === 'courier' && isDeclared && !!declaration;
  };
  handleDeclarationSwitchAgree = (agree) => {
    this.setState({
      declarationAgree: agree,
    });
  };

  render() {
    const {
      total,
      relationInfo,
      relationInfo: {
        type: relationType,
        platform = '',
        dynamicForms: rDynamicForms,
        disabled: disSwitch,
        courier_id,
        brand,
      } = {},
      relationData,
      relationInfoLoading,
      form: { disabled, data: formData },
      dynamicForms,
      addressData,
      extraInfo = {},
      extraInfoData,
      estimatedFeeActive,
      estimatedData,
      agree,
      moveAreaData,
      moveAreaStatus,
      moveAreaPagePadding,
      isOpenCredit,
      nowTimer,
      scanCoupon,
      miniPostPrinterData,
      hTBrandTipsData,
      springFestiveOpen,
      interceptChannelOpen,
      isOpenDpWeightTips,
      isOpenDpWeightNoMore,
      courierConfig,
      oAsyncBatchStatus = {},
      userVipData,
      unPayOrders,
      order_type,
      cur,
      stepInfo,
      activityDetail,
      activityCouponInfo,
      isOpenGuideCoupon,
      alipayActivityCoupon,
      receiveList,
      isReturnActivityNewUser,
      urlQuery,
      activity,
      badOrderInterceptIsOpen,
      declarationAgree = false,
      isOpenDeclaration = false,
      ...rest
    } = this.state;
    const { serviceConfig } = this.props;
    const { declaration } = serviceConfig || {};
    const curQuotationItem = getCurQuotationItem(brand, this.quotationList);
    const isOpenOnlinePay = courierConfig && courierConfig.isOpenOnlinePay == '1';
    const isPickupAddress =
      relationType === 'courier' && courierConfig && courierConfig.isPickupAddress == '1';
    const { card: dynamicCard = {} } = dynamicForms || {};
    const { pay } = rDynamicForms || {};
    const { reserve_start_time = '' } = extraInfo || {};
    const { quotation_brand } = extraInfoData || {};
    // console.log('alipayActivityCoupon', alipayActivityCoupon);
    // console.log('relationInfo', relationInfo);
    // console.log('extraInfo', extraInfo);
    // console.log('formData', formData);
    // console.log('serviceConfig', serviceConfig);

    return (
      <KbPage allowAuthPopup {...rest} onUpdate={this.onUpdate} cover={false}>
        <Fragment>
          <KbScrollView
            full={moveAreaStatus != 'min'}
            // eslint-disable-next-line taro/render-props
            renderHeader={
              this.checkIsReturn() ? (
                <OrderNotice dakExtra={dakExtra} />
              ) : (
                <Fragment>
                  {getUserStateByComplete() ? (
                    <Fragment>
                      <KbOfficialAccount
                        navigateId='5'
                        showModal
                        customImg
                        modalOfficialName='微快递助手'
                        modalReportDesc='弹窗原生关注组件-寄件'
                        onChange={this.handleAccountChange}
                      />
                      <OrderNotice dakExtra={dakExtra} />
                    </Fragment>
                  ) : (
                    <View className='kb-login__auth--box'>
                      <KbLoginAuthAndBind className='kb-button__middle' />
                    </View>
                  )}
                </Fragment>
              )
            }
            renderFooter={
              <Fragment>
                {this.checkIsReturn() || platform === 'yjkd_brand' ? (
                  <View
                    className='kb-box yj-container'
                    onTouchMove={(e) => {
                      e.stopPropagation();
                    }}
                  >
                    <View className='yj-container-list'>
                      <KbEstimatedFeeList
                        className='yj-container-list--content'
                        relationInfo={relationInfo}
                        reserve_start_time={reserve_start_time}
                        address={formData}
                        receiveList={receiveList}
                        weight={extraInfo.goods_weight}
                        volume={extraInfo.volume}
                        product_code={extraInfo.product_code}
                        quotation_brand={quotation_brand}
                        isOpenCredit={isOpenCredit}
                        userVipData={userVipData}
                        alipayActivityCoupon={alipayActivityCoupon}
                        moveAreaStatus={moveAreaStatus}
                        moveAreaData={moveAreaData}
                        onChange={this.onHandleMoveArea.bind(this, 'change')}
                        onChangeArea={this.onHandleMoveArea.bind(this, 'status')}
                        ref={this.estimatedFeeListRef}
                        checkIsReturn={this.checkIsReturn()}
                        activityCouponInfo={activityCouponInfo}
                        showDhGuide
                      />
                    </View>
                    <View className='yj-container-msg kb-spacing-md-lr'>
                      <KbExtraInfo
                        ref={this.extraInfoRef}
                        data={extraInfoData}
                        relationInfo={relationInfo}
                        dynamicForms={dynamicForms}
                        address={formData}
                        total={total}
                        unfold={!disabled}
                        mode='yjkd'
                        onChange={this.onChange.bind(this, 'info')}
                        isOpenCredit={isOpenCredit}
                        order_type={order_type}
                        urlQuery={urlQuery}
                      />
                      <View className='kb-box at-row at-row__align--center at-row__justify--between kb-spacing-md-b'>
                        <View className='at-row at-row__align--center'>
                          <KbCheckbox
                            label='我已阅读并同意'
                            checked={agree}
                            onChange={this.onSwitchAgree}
                            className='kb-color__black'
                          />
                          <KbAgreement
                            agreeType='serviceAgreement'
                            actionRef={this.agreeActionRef}
                          />
                        </View>
                        <View>
                          <KbSubmitBar
                            onClick={this.handleSubmitForm}
                            onCheck={this.handleCheck}
                            total={total}
                          />
                        </View>
                      </View>
                    </View>
                  </View>
                ) : (
                  <View className='kb-box at-row at-row__align--center'>
                    <View className='at-col kb-spacing-md-l'>
                      <KbEstimatedFee
                        active={estimatedFeeActive}
                        relationInfo={relationInfo}
                        formData={formData}
                        extraInfo={extraInfo}
                        isOpenOnlinePay={isOpenOnlinePay}
                        total={total}
                        data={estimatedData}
                        onChange={this.onChange.bind(this, 'estimatedFee')}
                        onEquityChange={this.onChange.bind(this, 'equityCard')}
                        onCouponChange={this.onChange.bind(this, 'coupon')}
                      />
                    </View>
                    <View className='kb-spacing-md'>
                      <KbSubmitBar
                        onClick={this.handleSubmitForm}
                        onCheck={this.handleCheck}
                        total={total}
                      />
                    </View>
                  </View>
                )}
              </Fragment>
            }
            ts={nowTimer}
          >
            <View
              className='kb-send'
              style={
                platform == 'yjkd_brand'
                  ? { paddingBottom: `${moveAreaPagePadding + 10 || 150}px` }
                  : ''
              }
            >
              <View className='kb-send__item'>
                <KbStoreCardSelector
                  showCreditLabel={
                    process.env.PLATFORM_ENV == 'alipay' &&
                    platform == 'yjkd_brand' &&
                    (pay == 2 || pay == 3) &&
                    isOpenCredit
                  }
                  onChange={this.updateRelationInfo}
                  onExtraData={this.handleExtraData.bind(this)}
                  data={relationData}
                  disabled={disSwitch}
                  loading={relationInfoLoading}
                />
              </View>
              {this.checkIsReturn() && (
                <View className='send-step'>
                  {stepInfo.map((d, idx) => (
                    <Fragment key={d}>
                      <Text className={`${idx <= cur ? 'first' : ''} ${cur === idx ? 'cur' : ''}`}>
                        {idx + 1}.{d}
                      </Text>
                      {idx !== 2 && (
                        <AtIcon className='kb-color__arrow' prefixClass='kb-icon' value='arrows' />
                      )}
                    </Fragment>
                  ))}
                </View>
              )}
              <View className='kb-send__item kb-margin-md-t'>
                <KbAddressEdit
                  pageName='edit'
                  locked
                  useDefault
                  supportBatch
                  supportTemporary
                  switchAddress
                  isPickupAddress={isPickupAddress}
                  ref={this.addressEditRef}
                  actionRef={this.realnameRef}
                  extraInfo={extraInfo}
                  relationInfo={relationInfo}
                  data={addressData}
                  onChange={this.onChange.bind(this, 'address')}
                />
              </View>
              {this.checkIsReturn() && isReturnActivityNewUser ? (
                <View
                  className='return-newUserActivity'
                  onClick={this.handleNewUserActivityClick.bind(this)}
                  hoverClass='kb-hover-opacity'
                >
                  【网购退货】新客首单<Text className='kb-color__red'>+送VIP</Text>会员卡{' '}
                  <Text className='kb-margin-sm-l kb-color__brand'>查看详情</Text>
                  <AtIcon
                    prefixClass='kb-icon'
                    value='arrow'
                    className='kb-icon-size__sm kb-color__brand'
                  />
                </View>
              ) : null}
              {this.checkActivity() && total <= 1 ? (
                <ChooseCoupon
                  alipayActivityCoupon={alipayActivityCoupon}
                  quotation={curQuotationItem}
                  activityDetail={activityDetail}
                  isReturnModule={this.checkIsReturn()}
                  activityCouponInfo={activityCouponInfo}
                />
              ) : null}
              {platform !== 'yjkd_brand' && !this.checkIsReturn() && (
                <Fragment>
                  <View className='kb-send__item'>
                    <KbExtraInfo
                      ref={this.extraInfoRef}
                      data={extraInfoData}
                      relationInfo={relationInfo}
                      dynamicForms={dynamicForms}
                      address={formData}
                      total={total}
                      courierConfig={courierConfig}
                      onChange={this.onChange.bind(this, 'info')}
                    />
                  </View>
                  <View className='at-row at-row__justify--between at-row__align--center'>
                    <View>
                      <View className='at-row at-row__align--center'>
                        <KbCheckbox
                          label='我已阅读并同意'
                          checked={agree}
                          onChange={this.onSwitchAgree}
                          className='kb-color__black'
                        />
                        <KbAgreement agreeType='serviceAgreement' actionRef={this.agreeActionRef} />
                        <KbAgreement agreeType='mzAgreement' />
                      </View>
                    </View>
                    {total <= 1 && dynamicCard.isShow && process.env.PLATFORM_ENV !== 'swan' && (
                      <KbCardBar data={relationInfo} action='buy' />
                    )}
                  </View>
                  {this.checkIsShowDeclaration() && (
                    <View>
                      <KbDeclarationAgreement
                        declaration={declaration}
                        open={isOpenDeclaration}
                        agree={declarationAgree}
                        onSwitchAgree={this.handleDeclarationSwitchAgree}
                        onConfirm={() => this.handleSubmitForm('agreeDeclaration')}
                      />
                    </View>
                  )}
                </Fragment>
              )}
              {process.env.PLATFORM_ENV === 'alipay' && <KbAntForest />}
              {platform == 'yjkd_courier' && (
                <View
                  className='at-row at-row__justify--center at-row__align--center kb-color__brand kb-size__base'
                  onClick={this.onShowFeeWay}
                >
                  <AtIcon
                    className='kb-margin-sm-r'
                    prefixClass='kb-icon'
                    value='alert'
                    size='16'
                  />
                  支付方式与计费说明
                </View>
              )}
            </View>
            {process.env.MODE_ENV.includes('wkd') && <KbAdExtension data={adAgs} showRemark />}
            <KbCouponModal actionRef={this.couponModalRef} />
            <KbScanCoupon data={scanCoupon} />
            <KbModal
              isOpened={!!miniPostPrinterData}
              onClose={this.handleMiniPostPrinter.bind(this, 'close')}
              onCancel={this.handleMiniPostPrinter.bind(this, 'cancel')}
              onConfirm={this.handleMiniPostPrinter.bind(this, 'confirm')}
              confirmText='继续下单'
              cancelText='保存新下单码'
            >
              <View>
                <View>
                  为更好地服务你，你的寄件下单码已更新，请保存使用。继续下单请点击“继续下单”。
                </View>
                <KbLinkService />
              </View>
            </KbModal>
            <KbModal
              isOpened={!!hTBrandTipsData}
              closable={false}
              closeOnClickOverlay={false}
              onClose={this.handleHTBrandModal.bind(this, 'close')}
              onConfirm={this.handleHTBrandModal.bind(this, 'confirm')}
              title='品牌更新告知'
              content='尊敬的客户，因”百世快递“现已跟“极兔速递”进行业务融合，您的下单品牌将切换为“极兔速递”为您服务;你也可在下单页继续选择其他快递品牌进行寄递，感谢您对“微快递”平台的大力支持!'
              confirmText={`${hTBrandTipsData > 0 ? hTBrandTipsData + 's' : '知道了'}`}
            />
            <AtCurtain
              className='kb-curtain__large'
              isOpened={
                springFestiveOpen &&
                !isOpenCredit &&
                relationType == 'brand' &&
                platform == 'yjkd_brand'
              }
              onClose={this.handleSpringFestive.bind(this, 'close')}
              closeBtnPosition='top-right'
            >
              <View className='kb-spring'>
                <View className='kb-spring__body'>
                  <View className='kb-spring__title'>暂停寄件服务公示</View>
                  <View className='kb-margin-md-b'>
                    尊敬的客户，春节期间“微快递”平台将关停快递品牌现付服务(线下支付运费)，如您需要寄件，需要您在下单前开通
                    ”{CreditConfig.text}“，以享受“春节寄件不打烊”福利;
                  </View>
                  <View className='kb-size__base'>开通后优势: </View>
                  <View>1.享受春节期间寄件服务</View>
                  <View>2.无需支付运费给快递员，揽收后自动 扣除运费</View>
                  <View>3.运费享有专属优惠价</View>
                  <View className='kb-size__base kb-margin-sm'>春节期间: 2020.1.26-2020.2.7</View>
                  <View
                    className='kb-spring__btn'
                    onClick={this.handleSpringFestive.bind(this, 'confirm')}
                  >
                    立即开通
                  </View>
                  <View className='kb-spring__desc'>微快递祝所有用户虎年大吉！</View>
                </View>
              </View>
            </AtCurtain>
            <KbModal
              isOpened={!!isOpenDpWeightTips}
              top={false}
              title='温馨提示'
              onClose={this.handelDpWeightTips.bind(this, 'close')}
              onCancel={this.handelDpWeightTips.bind(this, 'close')}
              confirmText=''
              cancelText=''
            >
              <View className='kb-dpWightTips'>
                <View className='kb-dpWightTips-content'>
                  <View className='kb-dpWightTips-dot'>
                    尊敬的客户为了给您提供最优惠的寄件体验，当您包裹实际重量大于等于3kg时，系统建议您录入物品重量后下单,此时系统结算价格会更优惠;
                  </View>
                  <View className='kb-dpWightTips-dot'>
                    货物重量在不大于3kg时切勿随意录入重量，以免造成扣款损失;
                  </View>
                </View>
                <View className='kb-dpWightTips-tips'>
                  注:大于60kg的货物会按照原价计费无优惠， 请谨慎下单!
                </View>
                <View>
                  <View>
                    <AtButton
                      type='primary'
                      circle
                      onClick={this.handelDpWeightTips.bind(this, 'confirm')}
                    >
                      我已知晓
                    </AtButton>
                  </View>
                  <View className='kb-text__center kb-margin-md-t'>
                    <KbCheckbox
                      label='不再提示'
                      checked={!!isOpenDpWeightNoMore}
                      onChange={this.handelDpWeightTips.bind(this, 'noMore')}
                      className='kb-color__black'
                    />
                  </View>
                </View>
              </View>
            </KbModal>
            <KbModal
              isOpened={!!oAsyncBatchStatus.status}
              top={false}
              title={oAsyncBatchStatus.status == 1 ? '' : '订单提交结果页'}
              closeOnClickOverlay={false}
              closable={oAsyncBatchStatus.status == 1 ? false : true}
              onClose={this.handleBatchOrderTips.bind(this, 'close')}
              onCancel={this.handleBatchOrderTips.bind(this, 'close')}
              confirmText=''
              cancelText=''
            >
              <View className='kb-batchOrderTips'>
                {oAsyncBatchStatus.status == 1 ? (
                  <View className='kb-batchOrderTips__submit'>
                    <View className='at-row at-row__justify--center at-row__align--center'>
                      <AtIcon className='kb-color__brand' prefixClass='kb-icon' value='loading' />
                      <Text className='kb-size__bold kb-color__black kb-margin-sm-l'>
                        拼命加载中
                      </Text>
                    </View>
                    <View className='kb-margin-lg-t'>
                      <View>系统正在拼命提交订单数据</View>
                      <View>请您耐心等待！</View>
                    </View>
                  </View>
                ) : oAsyncBatchStatus.status == 2 ? (
                  <View className='kb-batchOrderTips__fail'>
                    <View className='kb-color__black'>
                      共
                      <Text className='kb-batchOrderTips__fail-num'>
                        {oAsyncBatchStatus.total_order || 0}
                      </Text>
                      单
                    </View>
                    <View className='at-row at-row__justify--center'>
                      <View className='kb-color__green'>
                        成功
                        <Text className='kb-batchOrderTips__fail-num'>
                          {oAsyncBatchStatus.success_count || 0}
                        </Text>
                        单，
                      </View>
                      <View className='kb-color__red'>
                        失败
                        <Text className='kb-batchOrderTips__fail-num'>
                          {oAsyncBatchStatus.fail_count || 0}
                        </Text>
                        单
                      </View>
                    </View>
                    <View className='kb-margin-lg'>
                      <AtButton
                        className='kb-batchOrderTips__fail-copy'
                        type='primary'
                        circle
                        onClick={this.handleBatchOrderTips.bind(
                          this,
                          'copy',
                          oAsyncBatchStatus.fail_order_file,
                        )}
                      >
                        复制失败订单列表下载链接
                      </AtButton>
                      <View className='kb-size__base kb-margin-sm-t'>(该链接7天内有效)</View>
                    </View>
                    <View className='kb-size__base kb-color__grey'>
                      下单失败明细已生成“Excel”文件，您可以点击“复制失败订单列表下载链接”按钮在浏览器中打开下载失败列表明细数据
                    </View>
                  </View>
                ) : oAsyncBatchStatus.status == 3 ? (
                  <View className='kb-batchOrderTips__success'>
                    <View className='kb-size__lg kb-margin-xl-b'>
                      您批量提交的订单已全部下单成功
                    </View>
                    <AtButton
                      className='kb-batchOrderTips__fail-copy'
                      type='primary'
                      circle
                      onClick={this.handleBatchOrderTips.bind(this, 'close')}
                    >
                      我知道啦
                    </AtButton>
                  </View>
                ) : null}
              </View>
            </KbModal>
          </KbScrollView>
          <View className='kb-custom-float'>
            <AtFloatLayout isOpened={interceptChannelOpen} onClose={this.handleCloseIntercept}>
              <View>
                <Image
                  className='kb-layout--img'
                  mode='widthFix'
                  src='https://cdn-img.kuaidihelp.com/wkd/v961/catch-title.png?v=1'
                />
              </View>
              <View className='kb-spacing-lg'>
                <View className='kb-margin-lg-b kb-layout--dot'>
                  您所选择的下单快递员已长时间未登录账号，可能存在休假或离职等异常情况，继续下单可能导致您的订单长时间无人响应揽收!
                </View>
                <View className='kb-layout--dot'>是否接受系统为您智能分配下单对象</View>
              </View>
              <View className='kb-spacing-lg kb-spacing-xl-b'>
                <KbButton type='primary' circle onClick={this.handleConfirmIntercept}>
                  同意系统分配
                </KbButton>
                <View
                  className='kb-button__link  kb-layout--text kb-margin-lg-t'
                  onClick={this.handleCloseIntercept}
                >
                  继续下单给快递员
                </View>
              </View>
            </AtFloatLayout>
          </View>
          <KbWaitPay mode='modal' />
          <KbUnPayOrders
            courier_id={courier_id}
            order_ids={unPayOrders}
            onClose={this.handleInterceptModalClose}
          />
          {process.env.PLATFORM_ENV === 'alipay' && (
            <Fragment>
              <BusinessCouponGuide />
              <GuideReceiveCoupon />
            </Fragment>
          )}
          <GuideUseCoupon
            open={isOpenGuideCoupon}
            quotation={curQuotationItem}
            onCancel={this.handelGuideCoupon.bind(this, 'cancel')}
            onConfirm={this.handelGuideCoupon.bind(this, 'confirm')}
          />
          <KbBadOrderIntercept
            open={badOrderInterceptIsOpen}
            onComplete={this.handleBadOrderIntercept.bind(this, 'complete')}
          />
          <AuthPrompt source='edit' />
          <KbStopReceiveOrder relationInfo={relationInfo} courierConfig={courierConfig} />
        </Fragment>
      </KbPage>
    );
  }
}

export default Index;
