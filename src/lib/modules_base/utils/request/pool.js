import request from '.';
import CallbacksPool from '../callbacksPool';

/**
 *
 * @description 需要登录后自动触发的请求
 * 1、request请求时，必须登录的，但未登录的，或者登录时效时，使用 loginCallbacksCollect 收集；
 * 2、Taro.kbLogin 触发登录成功后，调用 loginCallbacksRun；
 */

const loginCallbacksPoolIns = new CallbacksPool();

// 收集登录回调请求
export function loginCallbacksCollect(params, this_) {
  const { autoRequestAfterLogin, mastLogin } = params;
  if (autoRequestAfterLogin && mastLogin) {
    loginCallbacksPoolIns.push(() =>
      request(
        {
          ...params,
          autoRequestAfterLogin,
        },
        this_,
      ),
    );
    return true;
  }
  return false;
}

// 触发所有回调
export function loginCallbacksRun() {
  loginCallbacksPoolIns.trigger();
}
