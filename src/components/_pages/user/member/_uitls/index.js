/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import request from '@base/utils/request';
import { requestPayment } from '@/utils/qy';
import { debounce, reportAnalytics } from '@base/utils/utils';
import {
  IsBackFromCredit,
  checkCreditService,
  openCreditService,
} from '@/components/_pages/order/_utils/order.credit-pay';
import Taro, { useDidShow, useRef } from '@tarojs/taro';
import isArray from 'lodash/isArray';

export const GIFT_TOAST_KEY = 'gift_key';

// 支付vip费用
const payForVipService = (card_id) => {
  return new Promise((resolve) => {
    request({
      url: '/g_wkd/v2/memberCard/MemberCard/memberCardSign',
      data: {
        card_id,
      },
      toastError: true,
      onThen: ({ code, data }) => {
        if (code == 0) {
          requestPayment(data).then(resolve).catch();
        }
      },
    });
  });
};

// 检查是否开通会员自动续费
const queryAutoVipService = (queryCode) => {
  return new Promise((resolve, reject) => {
    request({
      url: '/g_wkd/v2/memberCard/MemberCard/queryAutoBuyMemberServiceResult',
      data: {
        code: queryCode,
      },
      quickTriggerThen: true,
      toastError: true,
      onThen: ({ code, data, msg }, req) => {
        console.log('检查是否开通会员自动续费...', req);
        if (code == 0) {
          resolve(data);
        } else {
          reject(msg);
        }
      },
    });
  });
};

// 开通自动续费vip服务接口
const openAutoVipService = (card_id) => {
  return new Promise((resolve, reject) => {
    request({
      url: '/g_wkd/v2/memberCard/MemberCard/openAutoBuyMemberService',
      data: {
        card_id,
      },
      toastError: true,
      onThen: ({ code, data, msg }, req) => {
        console.log('开通自动续费vip服务接口', req);
        if (code == 0) {
          resolve(data && data.code);
        } else {
          reject(msg);
        }
      },
    });
  });
};

// 开通自动续费vip
const openAutoVip = (card_id) => {
  let timer = null,
    n = 0;
  return new Promise((resolve, reject) => {
    const fail = (msg) => {
      console.log('开通失败');
      Taro.hideLoading();
      n = 0;
      timer && clearInterval(timer);
      if (msg) {
        setTimeout(() => {
          Taro.kbToast({
            text: msg,
          });
        }, 1000);
      }
      reject(msg);
    };
    const success = () => {
      Taro.hideLoading();
      timer && clearInterval(timer);
      n = 0;
      resolve();
    };
    // 调用开通接口
    Taro.showLoading({
      title: '开通中，请稍后...',
      mask: true,
    });
    openAutoVipService(card_id)
      .then((queryCode) => {
        if (!queryCode) {
          success();
        } else {
          // 查询开通状态
          timer && clearInterval(timer);
          timer = setInterval(() => {
            console.log('发起查询请求...');
            n++;
            if (n > 15) {
              fail('查询超时，开通失败');
              return;
            }
            queryAutoVipService(queryCode)
              .then((queryRes) => {
                if (queryRes == 1) {
                  success();
                }
              })
              .catch(fail);
          }, 1000);
        }
      })
      .catch(fail);
  });
};

// 开通成功
const openVipSuccess = () => {
  reportAnalytics({
    key: 'welfare.lotterys',
    title: `会员页-会员购买数`,
  });
  reportAnalytics({
    key: 'welfare.lotterys',
    title: `会员日总开通数`,
  });
};

// 开通vip
const openVipService = (opt) => {
  const { card = {} } = opt || {};
  return new Promise((resolve) => {
    reportAnalytics({
      key: 'welfare.lotterys',
      title: `会员页-点击开通数`,
    });
    if (card.type == 1) {
      // 自动续费
      checkCreditService(true).then((isOpenCredit) => {
        // 已开通支付分
        if (isOpenCredit) {
          openAutoVip(card.id).then(() => {
            openVipSuccess();
            resolve('success');
          });
        } else {
          // 未开通支付分走开通逻辑
          openCreditService();
          resolve('wait');
        }
      });
    } else {
      payForVipService(card.id).then(() => {
        openVipSuccess();
        resolve('success');
      });
    }
  });
};

// 开通vip hooks
export const useOpenVipService = (opt) => {
  const { onSuccess = () => {}, onFail = () => {} } = opt || {};
  const comRef = useRef({ status: '', card: {} });

  useDidShow(() => {
    if (comRef.current.status === 'wait') {
      IsBackFromCredit().then(() => {
        checkCreditService().then((res) => {
          if (res) {
            const { id } = comRef.current.card;
            openAutoVip(id).then(() => {
              openVipSuccess();
              onSuccess();
            });
          } else {
            Taro.kbModal({
              top: false,
              title: '温馨提示',
              content:
                '由于您暂无法使用微信 支付分服务开通自动续费会员服务 系统为您推荐以下套餐类型满足 您的开通会员需求',
              confirmText: '知道了',
            });
            onFail(comRef.current.card);
          }
        });
      });
    }
  });

  const open = debounce(
    (opt) => {
      comRef.current.status = '';
      openVipService(opt).then((res) => {
        comRef.current.status = res;
        comRef.current.card = opt.card;
        if (res === 'success') {
          comRef.current.status = 'success';
          if (onSuccess) {
            onSuccess();
          }
        }
      });
    },
    1000,
    {
      leading: true,
      trailing: false,
    },
  );

  return {
    open,
  };
};

export const vipRights = [
  { icon: 'ddf', name: '单单返现金', desc: '申通/德邦/韵达/极兔/优速' },
  ...(process.env.PLATFORM_ENV === 'weapp' && process.env.MODE_ENV === 'wkd'
    ? [
        { icon: 'xjfb', name: '签到现金翻倍', desc: '会员最高10倍奖励' },
        { icon: 'lottery', name: '现金抽奖助力', desc: '会员最高获得3倍助力' },
      ]
    : []),
  { icon: 'vip', name: '会员共享', desc: '一人开通，全家会员' },
  { icon: 'invite', name: '邀新奖励', desc: '邀人寄件，即得现金' },
  { icon: 'service', name: '金牌客服', desc: '一次开通，终生享受' },
];

/**
 * 用户开通自动续费会员服务记录
 * @returns
 */
export const getVipAutoServiceRecord = () => {
  return new Promise((resolve) => {
    request({
      url: '/g_wkd/v2/memberCard/MemberCard/userOpenMemberServiceRecord',
      onThen: (res) => {
        if (isArray(res.data) && res.data.length > 0) {
          res.data.map((item) => {
            if (item.status_desc) {
              item._status =
                {
                  已过期: 1,
                  已取消: 2,
                }[item.status_desc] || '';
            }
            if (item.level) {
              item.level_name = ['月卡', '季卡', '年卡'][item.level * 1 - 1];
              item.level_short_name = ['月', '季', '年'][item.level * 1 - 1];
            }
          });
        }
        resolve(res.data);
      },
    });
  });
};

/**
 * 取消自动续费会员服务
 * @returns
 */
export const cancelAutoVip = () => {
  return new Promise((resolve) => {
    request({
      url: '/g_wkd/v2/memberCard/MemberCard/cancelAutoBuyMemberService',
      onThen: (res) => {
        resolve(!!(res.code == 0));
      },
    });
  });
};

export const handleRightsBarClick = (key) => {
  Taro.navigator({
    url: 'user/member/right',
    options: {
      keys: key,
    },
  });
};
