/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { Image, View, Fragment } from '@tarojs/components';
import { useEffect, useState } from '@tarojs/taro';
import KbModal from '@base/components/modal';
import { openCreditService } from '../../_utils/order.credit-pay';
import './index.scss';

// 活动挽留弹窗
const RetentionModal = (props) => {
  const { open = false, activityCouponInfo = {}, onCancel, onConfirm } = props;
  const [isOpened, setIsOpened] = useState(false);

  useEffect(() => {
    setIsOpened(open);
  }, [open]);

  const handelBar = (key, ev) => {
    console.log('key,ev', key, ev);
    switch (key) {
      case 'close':
        setIsOpened(false);
        break;
      case 'open':
        setIsOpened(true);
        break;
      case 'cancel':
        if (ev === 'button') {
          if (onCancel) {
            onCancel();
          }
        }
        setIsOpened(false);
        break;
      case 'confirm':
        openCreditService();
        setIsOpened(false);
        if (onConfirm) {
          onConfirm();
        }
        break;
    }
  };

  const { discount_type, discount_fee, card_tag, card_name } = activityCouponInfo || {};

  return (
    <Fragment>
      <KbModal
        isOpened={isOpened}
        top={false}
        title='确定要原价寄件吗？'
        onClose={handelBar.bind(null, 'close')}
        onCancel={handelBar.bind(null, 'cancel')}
        onConfirm={handelBar.bind(null, 'confirm')}
        confirmText='尝试优惠寄件'
        cancelText='继续提交订单'
        footerLayout='column'
      >
        <View className='kb-guideCouponModal'>
          <View className='kb-guideCouponModal-tips'>原价寄件，将不能享受平台折扣</View>
          <View className='kb-guideCouponModal-coupon'>
            <Image
              className='kb-guideCouponModal-img'
              src='https://cdn-img.kuaidihelp.com/wkd/miniApp/activity2/quan-4.png?v=01'
            />
            {discount_type === 'subtract' ? (
              <View className='kb-guideCouponModal-fee'>
                <View className='kb-size__sm'>￥</View>
                <View className='fee'>{discount_fee}</View>
              </View>
            ) : (
              <View className='kb-guideCouponModal-fee'>
                <View className='fee'>{discount_fee}</View>
                <View className='kb-size__sm'>折</View>
              </View>
            )}
            <View className='kb-guideCouponModal-name'>{card_tag || card_name}</View>
          </View>
        </View>
      </KbModal>
    </Fragment>
  );
};

RetentionModal.options = {
  addGlobalClass: true,
};

export default RetentionModal;
