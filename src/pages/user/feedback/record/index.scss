/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb-feedback-record {
  margin: $spacing-h-md;
  overflow: hidden;
  border-radius: $border-radius-base;
  &-item {
    box-sizing: border-box;
    padding: $spacing-h-md;
    background-color: $color-white;
  }
  &-avatar {
    width: 80px;
    height: 80px;
    color: $color-white;
    font-size: $font-size-lg;
    line-height: 80px;
    text-align: center;
    background-color: $color-brand;
    border-radius: 50%;
  }
  .kb-flex-1 {
    flex: 1;
  }
}
