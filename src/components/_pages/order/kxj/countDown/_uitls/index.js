/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { useEffect, useState } from '@tarojs/taro';
import { fixZero } from '@base/utils/utils';
import dayjs from 'dayjs';
import duration from 'dayjs/plugin/duration';

dayjs.extend(duration);

/**
 * 计算剩余时间
 * @param {*} end
 * @returns
 */
export const calculateRestDay = (end) => {
  if (end == 0) {
    return {
      day: 0,
      hour: 0,
      mint: 0,
    };
  }
  const startDate = dayjs();
  const endDate = dayjs(end);
  const duration = dayjs.duration(endDate.diff(startDate), 'milliseconds');
  const day = parseInt(duration.asDays());
  const hour = duration.hours();
  const mint = duration.minutes();
  const seconds = duration.seconds();
  return {
    day: fixZero(day <= 0 ? 0 : day),
    hour: fixZero(hour <= 0 ? 0 : hour),
    mint: fixZero(mint <= 0 ? 0 : mint),
    seconds: fixZero(seconds <= 0 ? 0 : seconds),
  };
};

export const useCountDown = (opt = {}) => {
  const { endTime = 0 } = opt || {};

  const [count, setCount] = useState({});

  useEffect(() => {
    if (!endTime || endTime == 0) {
      return;
    }
    setCount(calculateRestDay(endTime));
    const timer = setInterval(() => {
      setCount(calculateRestDay(endTime));
    }, 1000);
    return () => {
      timer && clearInterval(timer);
    };
  }, [endTime]);

  return {
    count,
  };
};
