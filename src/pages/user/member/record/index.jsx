/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { Component } from '@tarojs/taro';
import { connect } from '@tarojs/redux';
import KbPage from '@base/components/page';
import { Image, Text, View } from '@tarojs/components';
import { getUserVipInfo } from '@/components/_pages/user/_utils';
import { getVipAutoServiceRecord } from '@/components/_pages/user/member/_uitls';
import { AtIcon } from 'taro-ui';
import './index.scss';

@connect(({ global }) => ({
  loginData: global.loginData,
}))
class Index extends Component {
  config = {
    navigationBarTitleText: '购买记录',
  };

  constructor(props) {
    super(props);
    this.state = {
      recordList: [],
    };
  }

  componentDidShow() {
    this.getUserVipData();
  }

  handleUpdate(data) {
    if (!data.logined) return;
  }

  getUserVipData() {
    getUserVipInfo({ isFull: true }).then((data) => {
      this.setState({
        userVipData: data,
      });
    });
    getVipAutoServiceRecord().then((res) => {
      this.setState({
        recordList: res || [],
      });
    });
  }

  handleManage = () => {
    Taro.navigator({
      url: 'user/member/manage',
    });
  };

  handleClick = (item) => {
    Taro.navigator({
      url: 'user/member/record/detail',
      key: 'routerParamsChange',
      options: {
        recordData: item,
      },
      onArrived: () => {
        console.log('通过postMessage传递大量数据');
      },
    });
  };

  render() {
    const { userVipData = {}, recordList = [], ...rest } = this.state;
    const isExpire = userVipData.status != 1;
    console.log('userVipData', userVipData);
    return (
      <KbPage {...rest} onUpdate={this.handleUpdate.bind(this)}>
        <View className='kb-vip-record'>
          <View className='kb-vip-record__head'>
            <View className='kb-vip-record__head--bg' />
            <View className='kb-recordCard'>
              <View>
                <View className='kb-recordCard-cardName'>优享寄-VIP{userVipData.level_name}</View>
                <View className='kb-recordCard-body'>
                  <View>
                    {isExpire ? '已' : '将'}于{userVipData.expires_time_format}到期
                  </View>
                  {userVipData.open_auto == 1 && (
                    <View className='kb-margin-md-l' onClick={this.handleManage}>
                      管理自动续费
                    </View>
                  )}
                </View>
              </View>
              <View className='kb-recordCard-footer'>
                <View className='kb-recordCard-cardSimpleName'>{userVipData.level_name}</View>
                <View className='kb-recordCard-cardType'>优寄{userVipData.name}</View>
              </View>

              {!isExpire ? (
                <View className='kb-recordCard-cardTag'>
                  <Text className='kb-recordCard-cardTag_arrow' />
                  生效中
                </View>
              ) : (
                <View className='kb-recordCard-cardTag kb-recordCard-cardTag-expire'>
                  <AtIcon
                    prefixClass='kb-icon'
                    value='expire'
                    size={13}
                    className='kb-icon__text--mr'
                  />
                  <Text>已过期</Text>
                </View>
              )}
            </View>
          </View>
          {recordList && recordList.length > 0 && (
            <View className='kb-vip-record__list'>
              {recordList.map((item) => {
                const {
                  pay_number,
                  member_card_name,
                  status_desc,
                  buy_date,
                  first_buy,
                  next_price,
                  price,
                  level_short_name,
                  is_free_member,
                } = item || {};
                return (
                  <View
                    className='kb-vip-record__list--item'
                    key={pay_number}
                    onClick={this.handleClick.bind(this, item)}
                  >
                    <View className='kb-flex'>
                      <View className='kb-size__lg kb-color__black'>{member_card_name}</View>
                      <View>
                        {status_desc}
                        <AtIcon
                          prefixClass='kb-icon'
                          value='arrow'
                          className='kb-color__grey kb-icon-size__sm'
                        />
                      </View>
                    </View>
                    {buy_date && <View className='kb-margin-sm-t'>{buy_date}</View>}
                    <View className='kb-flex'>
                      <View>
                        {first_buy == 1 ? (
                          <View className='first-tag'>首次购买用户专享</View>
                        ) : is_free_member == 1 ? (
                          <View className='first-tag'>微快递+赠送会员</View>
                        ) : (
                          <View>
                            每{level_short_name}起
                            <Text className='kb-color__brand'>
                              ¥{next_price}/{level_short_name}
                            </Text>
                            续费，可随时取消
                          </View>
                        )}
                      </View>
                      <View className='kb-color__black'>
                        <Text>￥</Text>
                        <Text className='kb-size__xl'>
                          {(is_free_member == 1 ? next_price : price) || 0}
                        </Text>
                      </View>
                    </View>
                  </View>
                );
              })}
              <View className='kb-vip-record__list--desc'> - 仅展示近3个月购买记录 - </View>
            </View>
          )}
          <View>
            <Image
              className='kb-vip-record__list--xc_img'
              mode='widthFix'
              src='https://cdn-img.kuaidihelp.com/wkd/miniApp/vip2/img_xc.png'
            />
          </View>
        </View>
      </KbPage>
    );
  }
}

export default Index;
