/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb-nav-bar {
  background: transparent !important;
  &__wrapper {
    position: fixed !important;
  }
}

.kb-activity {
  position: relative;
  background: #f44235;
  &-bg {
    width: 100%;
    height: 1400px;
    margin-top: 20px;
  }
  &-subscribe {
    position: absolute;
    top: 300px;
    right: 0;
    left: 0;
    display: flex;
    align-items: center;
    height: 66px;
    padding: 0 $spacing-h-md;
    color: #ffffff;
    font-size: 24px;
    background: rgba(0, 0, 0, 0.4);
    &__text {
      flex: 1;
    }
    &__btn {
      height: 40px;
      color: #fc5551;
      font-size: 22px;
      line-height: 40px;
      background: #fff;
      border-color: #fff;
    }
  }
  &-coupon {
    position: relative;
    width: 680px;
    margin: 10px auto 0;
    text-align: center;
    background: #ffffff;
    border-radius: 20px;
    box-shadow: 4px 4px 6px 0px rgba(219, 143, 66, 0.72);
    &__title {
      width: 436px;
      height: 78px;
      margin: -32px auto 0;
    }
    &__coupon {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 600px;
      height: 168px;
      margin: $spacing-h-md auto;
      overflow: hidden;
      color: #ffefa8;
      background: #fc5551;
      border-radius: 20px 14px 20px 20px;
      .coupon {
        &-head {
          position: relative;
          display: flex;
          align-items: baseline;
          justify-content: center;
          width: 180px;
          &::after {
            position: absolute;
            top: 50%;
            right: 0;
            height: 108px;
            border-right: $width-base dashed #c01f23;
            transform: translateY(-50%);
            content: '';
          }
        }
        &-unit {
          font-size: 26px;
        }
        &-fee {
          font-weight: bold;
          font-size: 50px;
        }
        &-body {
          flex: 1;
          padding-left: 40px;
          text-align: left;
        }
        &-title {
          font-size: 34px;
        }
        &-desc {
          color: #fff5eb;
          font-weight: 500;
          font-size: 22px;
        }
        &-tag {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 260px;
          height: 36px;
          margin-top: 10px;
          color: #c7010b;
          font-size: 20px;
          background: linear-gradient(180deg, #fce09f, #fdf4df);
          border: $width-base solid #fff5eb;
          border-radius: 16px;
        }
        &-num {
          position: absolute;
          top: 10px;
          left: -30px;
          height: 40px;
          padding: 0 35px;
          color: #2e2e2e;
          font-size: 22px;
          line-height: 40px;
          background: #fff5eb;
          transform: rotate(-45deg);
        }
      }
    }
    &__desc {
      padding-bottom: 20px;
      color: #666;
      font-size: 22px;
      text-align: center;
    }
  }
  &-block {
    position: relative;
    display: block;
    width: 680px;
    margin: 30px auto;
    &__vip {
      position: relative;
      &--right {
        position: absolute;
        right: 0;
        bottom: 30px;
        left: 0;
        display: flex;
        justify-content: center;
        color: #666666;
        font-size: 24px;
        .link {
          color: #fc6500;
          &::after {
            @include chevron-right(#fc6500, $font-size-xs);
          }
        }
      }
    }
  }
  &-door {
    position: absolute;
    top: 380px;
    right: 0;
    z-index: 2;
    width: 36px;
    padding: 5px 0;
    color: #ffffff;
    font-size: 20px;
    text-align: center;
    background: rgba(0, 0, 0, 0.18);
    border-radius: 10px 0px 0px 10px;
    &__share {
      overflow: hidden;
      &-btn {
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        background: transparent;
      }
    }
    &__rule {
      top: 460px;
    }
    &__coupon {
      top: 540px;
    }
  }
  &__desc {
    position: absolute;
    top: 50px;
    right: 0;
    height: 54px;
    padding: 0 $spacing-h-md 0 $spacing-h-lg;
    color: #fff;
    font-size: $font-size-base;
    line-height: 54px;
    background: #f4400e;
    border-radius: 50px 0 0 50px;
    &-list {
      &-item {
        display: flex;
        margin-bottom: 8px;
        font-size: $font-size-base;
        text-align: justify;
      }
      &-index {
        width: 40px;
      }
    }
  }

  &__footer {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: $spacing-h-md;
    .footer-btn {
      width: 680px;
      height: 88px;
      color: #fff5cf;
      background: #f44235;
      border-color: #f44235;
    }
    .footer-tag {
      position: absolute;
      top: -10px;
      left: 390px;
      width: 298px;
      height: 42px;
      font-size: 24px;
      line-height: 42px;
      text-align: center;
      background: #fbe5b0;
      border-radius: 10px;
      &::after {
        position: absolute;
        bottom: 0;
        left: -10px;
        border: 15px solid #fbe5b0;
        border-right-color: transparent;
        border-bottom-color: transparent;
        border-left-color: transparent;
        border-left-width: 10px;
        transform: rotate(170deg);
        content: '';
      }
    }
  }
  .color-red {
    color: #fc6500;
  }
  .kb-arrow {
    &__right {
      &::after {
        @include chevron-right($color-grey-3, $font-size-xs);
      }
    }
  }
}

.kb-page__footer {
  background: #ffffff;
}
