/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import {
  frequencyLimitByMinute,
  getStorageSync,
  now,
  removeStorageSync,
  setStorageSync,
} from '~base/utils/utils';
import { createInterstitialAd } from '~/components/_pages/ad-extension/ad/intersitialAd';

export const showXLightAd = async () => {
  const key_full = 'query_xLight_cp_full';
  const key = 'query_xLight_cp2';
  // 全屏广告
  const limit_full = await frequencyLimitByMinute('check', key_full, 'full');
  console.log('展示全屏广告-limit', limit_full);
  if (limit_full) return;
  createInterstitialAd({
    adUnitId: '50_2024012525000080343',
    onClose: async () => {
      // 插屏广告
      const limit = await frequencyLimitByMinute('check', key, 'insert');
      console.log('展示插屏广告-limit', limit);
      if (limit) return;
      createInterstitialAd({
        adUnitId: '50_2024040125000087951',
      }).then((interstitialAd) => {
        if (interstitialAd && interstitialAd.openAd) {
          interstitialAd.openAd().then(() => {
            frequencyLimitByMinute('limit', key);
          });
        }
      });
    },
  }).then((interstitialAd) => {
    if (interstitialAd && interstitialAd.openAd) {
      interstitialAd.openAd().then(() => {
        frequencyLimitByMinute('limit', key_full);
      });
    }
  });
};

export const showXLightAdTest = () => {};

const key = 'bill_back-subscribe';
export const handleBillBackSubscribe = (action = 'get') => {
  return new Promise((resolve) => {
    if (action == 'get') {
      const { ts, data } = getStorageSync(key) || {};
      if (now() - ts < 2 * 60 * 1000) {
        // 6s后触发
        setTimeout(() => {
          removeStorageSync(key);
          resolve(data);
        }, 6000);
      } else {
        resolve();
      }
    } else {
      setStorageSync(key, 'bill.integralCollect');
      resolve();
    }
  });
};
