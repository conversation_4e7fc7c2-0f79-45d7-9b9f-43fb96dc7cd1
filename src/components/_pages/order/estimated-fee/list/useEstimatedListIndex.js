import { transferWkdAddress } from '@/components/_pages/order/_utils/index';
import { getBrandConfig } from '@/components/_pages/order/_utils/order.relation';
import apis from '@/utils/apis';
import { useUpdate } from '@base/hooks/page';
import request from '@base/utils/request';
import { debounce, isEqualExtend, isFullData, noop } from '@base/utils/utils';
import Taro, { useCallback, useEffect, useRef, useState } from '@tarojs/taro';
import isArray from 'lodash/isArray';
import isEmpty from 'lodash/isEmpty';
import isObject from 'lodash/isObject';
import { checkIsBatch, formatBatchQuotationReqData, getBatchQuotationList } from '../utils';

export function useEstimatedListIndex(props) {
  const {
    relationInfo,
    address,
    weight,
    volume,
    product_code = '',
    quotation_brand = '',
    isOpenCredit = false,
    moveAreaData,
    onChange = noop,
    checkIsReturn = false,
    activityCouponInfo,
    alipayActivityCoupon,
    receiveList,
    order_id: transform_order_id,
    activity,
    reserve_start_time,
  } = props || {};
  const { voucher_id = '' } = alipayActivityCoupon || {};
  const isBatch = checkIsBatch(receiveList);

  const { brand } = relationInfo || {};
  const [loadingBrand, setLoadingBrand] = useState('');
  const [loading, setLoading] = useState(false);
  const [quotationList, setQuotationList] = useState([]);

  const actionRefCurrent = useRef({});
  actionRefCurrent.current.getYjQuotation = ({
    addrData,
    weight,
    volume,
    product_code,
    quotation_brand,
    checkIsReturn,
    receiveList,
    transform_order_id,
    activity,
    reserve_start_time,
  }) => {
    console.log('请求报价单接口');
    setLoading(true);
    actionRefCurrent.current.loading = true;
    let vol = {};
    if (volume && volume.checked) {
      vol = volume;
    }
    const isBatch = checkIsBatch(receiveList);
    let reqData = {
      ...addrData,
      weight,
      delivery_type: product_code,
      brand: quotation_brand,
      ...vol,
      is_merchant_return: checkIsReturn ? 1 : undefined,
      other_service: {
        transform_order_id,
      },
      activity,
      reserve_start_time,
    };

    if (isBatch) {
      reqData = formatBatchQuotationReqData({
        ...reqData,
        receiveList,
      });
    }

    const isSingleGet = !!(product_code && quotation_brand); // 点击当日取时，仅拉取当前品牌对应的报价
    if (isSingleGet) {
      // 除brand、delivery_type以外的其他请求数据有变更，则清空brand
      if (!isEqualExtend(reqData, actionRefCurrent.current.oldReq, ['delivery_type', 'brand'])) {
        reqData.brand = '';
        setLoadingBrand('');
      } else {
        setLoadingBrand(quotation_brand);
      }
    }

    actionRefCurrent.current.oldReq = reqData;

    const completeFn = async (data, req) => {
      setLoading(false);
      actionRefCurrent.current.loading = false;
      let { quotation } = data || {};
      // 接口返回报价单null时，置空价格
      if (actionRefCurrent.current.hasList && !quotation) {
        quotation = actionRefCurrent.current.list.map(({ available, unavailable_msg, brand }) => {
          return {
            available,
            unavailable_msg,
            brand,
          };
        });
      }

      if (isArray(quotation)) {
        const { brand } = req;

        let list = [];
        let oBrandConfig = await getBrandConfig();

        // 单个拉取时，合并原有数据
        if (isSingleGet && actionRefCurrent.current.orgList) {
          const quotationItem = { ...quotation.find((item) => item.quotation_brand === brand) };
          quotation = actionRefCurrent.current.orgList;
          const brandForIndex = quotation.findIndex((item) => item.quotation_brand === brand);
          if (brandForIndex >= 0) {
            quotation[brandForIndex] = quotationItem;
          }
        }

        list = quotation.map((item, index) => {
          let channelItem = oBrandConfig[item.brand] || {};
          item = { ...item, ...channelItem, goods_weight: weight };
          index == 0 && (item.label = 'min');
          const { quotation: itemQuotation } = item;
          if (!item.quotation_brand && isArray(itemQuotation)) {
            item.quotation_brand = itemQuotation[0].quotation_brand;
          }
          item.weightLimitMax = item.weightLimitMax * 1;
          item.weightLimitMin = item.weightLimitMin * 1;
          if (item.weightLimitMax && weight * 1 > item.weightLimitMax) {
            item.available = 0;
            item.unavailable_msg = `此品牌暂不支持${item.weightLimitMax}KG以上的货物寄递；`;
          }
          if (item.weightLimitMin && weight * 1 < item.weightLimitMin) {
            item.available = 0;
            item.unavailable_msg = `此品牌暂不支持低于${item.weightLimitMin}KG的货物寄递；`;
          }
          return item;
        });
        actionRefCurrent.current.orgList = quotation;
        triggerUpdate(list);
      } else {
        actionRefCurrent.current.orgList = [];
      }
    };
    if (isBatch) {
      getBatchQuotationList(reqData).then(({ data }) => {
        completeFn(data, reqData);
      });
    } else {
      request({
        url: apis['order.quotation.yj'],
        data: reqData,
        toastLoading: false,
        onThen: ({ data }) => {
          completeFn(data, reqData);
        },
      });
    }
  };

  const getYjQuotationDebounce = useCallback(
    debounce(actionRefCurrent.current.getYjQuotation, 500, { trailing: true }),
    [],
  );

  useUpdate(
    (loginData) => {
      const { logined } = loginData || {};
      if (!logined) return;
      if (isEmpty(address)) return;
      let addrData = transferWkdAddress(address, 'address');
      if (!isFullData(addrData)) {
        triggerUpdate([]);
        return;
      }
      getYjQuotationDebounce({
        addrData,
        weight,
        volume,
        product_code,
        quotation_brand,
        checkIsReturn,
        receiveList,
        transform_order_id,
        activity,
        reserve_start_time,
      });
    },
    [
      address,
      weight,
      product_code,
      quotation_brand,
      volume,
      isOpenCredit,
      checkIsReturn,
      receiveList,
      transform_order_id,
      activity,
      reserve_start_time,
    ],
  );

  const triggerChange = () => {
    onChange({ changeSource: 'list', quotationList: actionRefCurrent.current.list });
  };

  // 列表更新
  // 百度渲染延迟才能正常展示选项卡；
  useEffect(() => {
    // 此处主要是更新布局
    if (!actionRefCurrent.current.hasList) return;
    triggerChange();
  }, [quotationList]);

  useEffect(() => {
    const { hasList, count } = actionRefCurrent.current;
    if (!hasList || count > 3) return;
    const { maxHeight = 0 } = moveAreaData || {};
    if (maxHeight) return;
    // maxHeight === 0且尝试次数小于3，应再次重置高度
    triggerChange();
    actionRefCurrent.current.count++;
  }, [moveAreaData]);

  const onChooseBrand = (item, e) => {
    if (isObject(e)) {
      e.stopPropagation();
    }
    const { brand, available, unavailable_msg } = item || {};
    if (available <= 0 && unavailable_msg) {
      Taro.kbToast({
        text: unavailable_msg,
      });
      return;
    }
    Taro.kbUpdateRelationInfo &&
      Taro.kbUpdateRelationInfo({ brand, isReturnModule: checkIsReturn ? 1 : undefined });
    onChange({ changeSource: 'chooseBrand', brand });
    if (actionRef && actionRef.current) {
      actionRef.current.setScrollTopFn && actionRef.current.setScrollTopFn();
    }
  };

  const onNavigatorDetail = (item) => {
    item.exists_alipay_coupon = !!voucher_id;
    if (item.brand === brand) {
      item.activityCouponInfo = activityCouponInfo;
    }
    Taro.kbSetGlobalData('yjBrandQuotationInfo', item);
    Taro.navigator({
      url: 'order/amount',
    });
  };

  const triggerUpdate = (list = []) => {
    actionRefCurrent.current.count = 0;
    actionRefCurrent.current.hasList = list && list.length > 0;
    actionRefCurrent.current.list = list;
    Taro.kbSetGlobalData('quotation', list);
    setQuotationList(list);
  };

  const handleChange = (key, data, quotation_brand) => {
    if (key == 'product_code') {
      onChange({ changeSource: 'product_code', product_code: data, quotation_brand });
    }
  };

  const actionRef = useRef();
  const handleClickMask = () => {
    actionRef.current.switchStatus('min');
  };

  return {
    actionRefCurrent,
    actionRef,
    loading,
    loadingBrand,
    brand,
    isBatch,
    quotationList,
    handleClickMask,
    handleChange,
    onNavigatorDetail,
    onChooseBrand,
  };
}
