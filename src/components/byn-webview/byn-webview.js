/* eslint-disable no-undef */
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    src: String,
  },

  /**
   * 组件的初始数据
   */
  data: {
    unsupportedUrl: '',
  },

  /**
   * 组件的方法列表
   */
  methods: {
    onError(e) {
      const { fullUrl, errMsg } = e.detail;
      if (errMsg && errMsg.includes('not in domain list')) {
        this.setData({
          unsupportedUrl: fullUrl,
        });
      }
    },
    copyUrl() {
      wx.setClipboardData({
        data: this.data.unsupportedUrl,
        success: () => {
          wx.showToast({ title: '复制成功', icon: 'success' });
        },
        fail: (e) => {
          wx.showToast({ title: e.errMsg, icon: 'none', duration: 2500 });
        },
      });
    },
  },
});
