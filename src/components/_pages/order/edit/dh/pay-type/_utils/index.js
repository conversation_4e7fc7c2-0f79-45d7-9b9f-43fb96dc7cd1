/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { useState, useEffect, useMemo } from '@tarojs/taro';
import { checkCouponCanUse } from '~/pages/order/card/utils';

export const JFF_VALUE = '4';
export const SFF_VALUE = '3';
export const createPayTypes = () => {
  return [
    {
      label: '先寄后付',
      value: '1',
    },
    {
      label: '寄方付',
      desc: '快递取件时，寄方当面付款给快递员',
      value: JFF_VALUE, // 现付
    },
    {
      label: '收方付',
      desc: '快递签收时，收方当面付款给快递员',
      value: SFF_VALUE, // 到付
    },
  ];
};

export const usePayType = (props) => {
  const { pay_method, payTypes, isStudentSend, isOpenCredit, quotation, onChange } = props;
  const [payMethod, setPayMethod] = useState('1');
  const [isOpened, setIsOpened] = useState(false);
  const [isOpenedInterceptTips, setIsOpenedInterceptTips] = useState(false);

  const triggerChange = (v) => {
    if (onChange) {
      onChange({
        data: {
          pay_method: v,
        },
      });
    }
  };

  useEffect(() => {
    setPayMethod(pay_method ? pay_method : '1');
  }, [pay_method]);

  const handleClose = () => {
    setIsOpened(false);
  };

  const handleOpen = () => {
    setIsOpened(true);
  };

  const handleChoose = (item) => {
    setPayMethod(item.value);
  };

  const handleConfirm = () => {
    if (payMethod != '1' && checkCouponCanUse({ coupon: quotation })) {
      handleChangeInterceptTips('open');
    } else {
      handleClose();
      triggerChange(payMethod);
    }
  };

  const handleChangeInterceptTips = (key, ev) => {
    switch (key) {
      case 'close': {
        setPayMethod('1');
        setIsOpenedInterceptTips(false);
        break;
      }
      case 'open': {
        setIsOpenedInterceptTips(true);
        break;
      }
      case 'cancel': {
        if (ev == 'icon') {
          handleChangeInterceptTips('close');
        } else {
          handleChangeInterceptTips('close');
          handleClose();
          triggerChange(payMethod);
        }
        break;
      }
      case 'confirm': {
        handleClose();
        setPayMethod('1');
        setIsOpenedInterceptTips(false);
        break;
      }
    }
  };

  const formatList = useMemo(() => {
    const arr = createPayTypes();
    let _list = [];
    const creditPay = arr.find((i) => i.value == 1);
    if (payTypes && payTypes.length > 0) {
      _list = arr.filter((item) => {
        return item.value == 1 || payTypes.find((i) => `${i}` === `${item.value}`);
      });
      return _list;
    }
    return [creditPay];
  }, [payTypes]);

  // 处理默认的付款方式
  useEffect(() => {
    if (formatList && formatList.length > 0) {
      const cur_pay_method = formatList.find((i) => pay_method == i.value);
      const hasJFF = formatList.find((i) => i.value == JFF_VALUE); // 是否存在寄方付
      if (isStudentSend && !isOpenCredit && hasJFF) {
        // 学生寄用户完成认证并未开通支付分，选择30KG以上方式下单寄件，【付款方式】默认为-寄方付；
        triggerChange(JFF_VALUE);
      } else if (!cur_pay_method) {
        const firstItem = formatList[0] || {};
        triggerChange(firstItem.value);
      }
    }
  }, [pay_method, formatList, isStudentSend, isOpenCredit]);

  return {
    pay_method,
    payMethod,
    list: formatList,
    isOpened,
    handleClose,
    handleOpen,
    handleChoose,
    handleConfirm,
    isOpenedInterceptTips,
    handleChangeInterceptTips,
  };
};
