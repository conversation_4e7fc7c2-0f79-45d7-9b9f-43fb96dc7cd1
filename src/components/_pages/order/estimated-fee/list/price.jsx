/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { Text, View } from '@tarojs/components';
import Taro, { Fragment } from '@tarojs/taro';
import { AtIcon } from 'taro-ui';
import './price.scss';

const Price = (props) => {
  const { item, loading, isOpenCredit, alipayActivityCoupon } = props;
  const { voucher_id = '' } = alipayActivityCoupon || {};
  const { coupon_id } = item || {};

  const onNavigatorDetail = () => {
    item.exists_alipay_coupon = !!voucher_id;
    Taro.kbSetGlobalData('yjBrandQuotationInfo', item);
    Taro.navigator({
      url: 'order/amount',
    });
  };

  const mode = voucher_id ? '4' : coupon_id ? (isOpenCredit ? '3' : '2') : '1';

  return (
    <Fragment>
      {process.env.PLATFORM_ENV === 'alipay' ? (
        <View className='quotation-price'>
          <View
            className='at-row at-row__justify--end at-row__align--center kb-freight__row'
            onClick={onNavigatorDetail.bind(null, item, (e) => e.stopPropagation())}
            hoverClass='kb-hover-opacity'
            hoverStopPropagation
          >
            <View className='kb-size__sm'>{mode == '3' ? '优惠' : '原价'}：</View>
            <View className='kb-freight'>
              {loading ? (
                <AtIcon
                  prefixClass='kb-icon'
                  value='loading'
                  className='kb-icon-size__base kb-color__grey'
                />
              ) : (
                <Fragment>
                  {mode == '2' || mode == '4' ? (
                    <Text>{item.price > 0 ? item.price : '--'}</Text>
                  ) : (
                    <Text>{item.discount_price > 0 ? item.discount_price : '--'}</Text>
                  )}
                  <Text className='kb-freight__uint'>元</Text>
                </Fragment>
              )}
            </View>
            <View className='kb-icon kb-icon-arrow kb-icon-size__xs' />
          </View>
          <View className='quotation-price-line kb-size__sm kb-color__grey'>
            {mode == '3' ? (
              <View className='quotation-price-yuanjia'>
                <View>原价：</View>
                <View className='kb-freight kb-color__grey'>
                  {item.price > 0 ? item.price : '-'}元
                </View>
              </View>
            ) : mode == '2' ? (
              <View className='quotation-price-yh'>
                <View>优惠：</View>
                <View className='kb-freight kb-color__grey'>
                  {item.discount_price > 0 ? item.discount_price : '-'}元
                </View>
              </View>
            ) : null}
          </View>
        </View>
      ) : (
        <View>
          <View
            className='at-row at-row__justify--end at-row__align--center kb-freight__row'
            onClick={onNavigatorDetail.bind(null, item, (e) => e.stopPropagation())}
            hoverClass='kb-hover-opacity'
            hoverStopPropagation
          >
            <View className='kb-size__sm'>预估：</View>
            <View className='kb-freight'>
              {loading ? (
                <AtIcon
                  prefixClass='kb-icon'
                  value='loading'
                  className='kb-icon-size__base kb-color__grey'
                />
              ) : (
                <Fragment>
                  <Text>{item.discount_price > 0 ? item.discount_price : '--'}</Text>
                  <Text className='kb-freight__uint'>元</Text>
                </Fragment>
              )}
            </View>
            <View className='kb-icon kb-icon-arrow kb-icon-size__xs' />
          </View>
        </View>
      )}
    </Fragment>
  );
};

Price.options = {
  addGlobalClass: true,
};

export default Price;
