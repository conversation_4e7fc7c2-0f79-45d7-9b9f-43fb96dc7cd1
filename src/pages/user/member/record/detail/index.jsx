/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { Component } from '@tarojs/taro';
import { Image, Text, View } from '@tarojs/components';
import { AtIcon } from 'taro-ui';
import { connect } from '@tarojs/redux';
import KbPage from '@base/components/page';
import { setClipboardData } from '@/utils/qy';
import { GIFT_TOAST_KEY, cancelAutoVip } from '@/components/_pages/user/member/_uitls';
import classNames from 'classnames';
import './index.scss';
import { removeStorageSync } from '@base/utils/utils';

@connect(({ global }) => ({
  loginData: global.loginData,
}))
class Index extends Component {
  config = {
    navigationBarTitleText: '购买记录详情',
  };

  constructor(props) {
    super(props);
    this.state = {
      recordData: {},
    };
  }

  handleUpdate(data) {
    if (!data.logined) return;
  }

  // 数据回传
  onPostMessage = (type, e) => {
    const { params } = e || {};
    const { recordData = {} } = params || {};
    switch (type) {
      case 'routerParamsChange':
        this.setState({
          recordData,
        });
        break;
    }
  };

  handleCopy = () => {
    const { recordData = {} } = this.state;
    setClipboardData(recordData.pay_number);
  };

  handleTips = () => {
    Taro.kbModal({
      top: false,
      title: '温馨提示',
      content: '权益已经发放，不支持退款',
      centered: true,
      confirmText: '我已知晓',
    });
  };

  handleClose = () => {
    cancelAutoVip().then((res) => {
      if (res) {
        const { recordData = {} } = this.state;
        this.setState({
          recordData: {
            ...recordData,
            status_desc: '已取消',
            _status: 2,
          },
        });
        Taro.kbModal({
          top: false,
          title: '温馨提示',
          content: '关闭自动续费服务成功',
          confirmText: '我已知晓',
        });
        removeStorageSync(GIFT_TOAST_KEY);
      }
    });
  };

  render() {
    const { recordData = {}, ...rest } = this.state;
    const {
      _status,
      status,
      level,
      pay_method,
      price,
      buy_date,
      pay_number,
      member_card_name,
      first_buy,
      next_price,
      level_short_name,
      is_free_member,
    } = recordData || {};
    const disabled = !!(_status == 1 || _status == 2);
    const rootCls = classNames('kb-vipDetail', {
      'kb-vipDetail--disabled': disabled,
    });
    const waitPayOrder = status == 3;
    return (
      <KbPage {...rest} onUpdate={this.handleUpdate.bind(this)}>
        <View className={rootCls}>
          <View className='kb-vipDetail-head'>
            <View className='kb-vipDetail-logo'>
              <Image
                className='img'
                mode='widthFix'
                src={`https://cdn-img.kuaidihelp.com/wkd/miniApp/vip2/icon_card_${
                  level == 3 ? 'nian' : level == 2 ? 'ji' : 'yue'
                }.png`}
              />
            </View>
            <View>
              <View className='kb-margin-md-t'>
                优享寄-{true ? `Vip${level_short_name}` : '自动续费卡'}-{member_card_name}
              </View>
              <View className='kb-vipDetail-price'>
                <View className='price kb-color__red'>
                  <Text className='kb-size__sm'>￥</Text>
                  <Text className='kb-size__xl'>{is_free_member == 1 ? next_price : price}</Text>
                </View>
                <View className='kb-vipDetail-desc'>
                  {first_buy == 1 ? (
                    <View className='first-tag'>首次购买用户专享</View>
                  ) : is_free_member == 1 ? (
                    <View className='first-tag'>微快递+赠送会员</View>
                  ) : (
                    <View>
                      每{level_short_name}起
                      <Text className='kb-color__brand'>
                        ¥{next_price}/{level_short_name}
                      </Text>
                      续费，可随时取消
                    </View>
                  )}
                </View>
              </View>
            </View>
          </View>
          <View className='kb-vipDetail-list'>
            <View className='kb-vipDetail-item'>
              <View className='kb-vipDetail-label'>支付方式</View>
              <View>{is_free_member == 1 ? '平台赠送' : pay_method || '-'}</View>
            </View>
            <View className='kb-vipDetail-item'>
              <View className='kb-vipDetail-label'>支付金额</View>
              <View>
                {is_free_member == 1 ? price : !waitPayOrder && price > 0 ? `￥${price}` : '-'}
              </View>
            </View>
            <View className='kb-vipDetail-item'>
              <View className='kb-vipDetail-label'>交易时间</View>
              <View>{buy_date || '-'}</View>
            </View>
            <View className='kb-vipDetail-item' onClick={this.handleCopy} hoverClass='kb-hover'>
              <View className='kb-vipDetail-label'>交易编号</View>
              <View>
                <Text>{pay_number || '-'}</Text>
                <AtIcon
                  prefixClass='kb-icon'
                  value='copy-text'
                  className='kb-icon-size__sm kb-margin-md-l'
                />
              </View>
            </View>
          </View>
          {waitPayOrder ? (
            <View className='kb-vipDetail-footer' onClick={this.handleClose} hoverClass='kb-hover'>
              关闭续费服务
            </View>
          ) : (
            <View className='kb-vipDetail-footer' onClick={this.handleTips} hoverClass='kb-hover'>
              <Text>本单不支持退款服务</Text>
              <AtIcon
                className='kb-icon-size__base kb-margin-sm-l'
                prefixClass='kb-icon'
                value='help2'
                size='16'
              />
            </View>
          )}
          {disabled && (
            <View className='kb-vipDetail--disabled-icon'>
              <Image
                className='img'
                mode='widthFix'
                src={`https://cdn-img.kuaidihelp.com/wkd/miniApp/vip2/icon_${
                  _status == 1 ? 'gq' : _status == 2 ? 'qx' : ''
                }.png`}
              />
            </View>
          )}
        </View>
      </KbPage>
    );
  }
}

export default Index;
