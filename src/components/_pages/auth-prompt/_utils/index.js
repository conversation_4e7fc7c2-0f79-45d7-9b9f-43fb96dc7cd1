/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import request from '@base/utils/request';
import { useSelector } from '@tarojs/redux';
import Taro, { useState } from '@tarojs/taro';
import isEmpty from 'lodash/isEmpty';
import { useUpdate } from '~base/hooks/page';
import { getStorageSync, setStorageSync } from '~base/utils/utils';
import { getLastUseRelation } from '../../store-card/_utils';

// {"status":"0","expire_time":"","syNum":0}}
// status 0未开通过 1开通中 2已过期   expire_time到期时间    syNum剩余有效期天数
export const checkSvipStatus = (opt) => {
  console.log('checkSvipStatus-参数===>', opt);
  const {
    courier_mobile,
    dak_id,
    courier_id: _courier_id,
    waybill,
    order_id,
    join_code,
  } = opt || {};
  const courier_id = _courier_id || dak_id;
  return new Promise((resolve) => {
    let reqData = {};
    if (join_code) {
      // 团队下单码
      reqData = { join_code };
    } else if (courier_id || courier_mobile) {
      reqData = {
        courier_id,
        courier_mobile,
      };
    } else if (waybill || order_id) {
      reqData = {
        waybill,
        order_id,
      };
    }
    if (isEmpty(reqData)) {
      return resolve({});
    }
    request({
      url: '/v1/WeApp/getCourierPureInfo',
      toastSleepTimer: 500,
      data: reqData,
      onThen: (res) => {
        // console.log('checkSvipStatus-结果===>', res.data);
        const svipData = res.data && !isEmpty(res.data) ? res.data : {};
        if (!isEmpty(svipData)) {
          svipData.params = reqData;
        }
        resolve(svipData);
      },
    });
  });
};

export const useAuthPrompt = (props) => {
  const { source } = props;
  const [isOpened, setIsOpened] = useState(false);
  const [svipData, setSvipData] = useState({});
  const { relationInfo } = useSelector((state) => state.global);
  const {
    type: relationInfoType,
    account_phone,
    courier_id,
    dak_id,
    storageWay,
  } = relationInfo || {};

  const storage = {
    key: 'svip_guide',
    isLimit: function (v) {
      if (!v) return true;
      const res = this.get();
      const { data: list } = res;
      return list && list.includes(v);
    },
    get: function () {
      const res = getStorageSync(this.key);
      return res && res.data ? res : { data: [] };
    },
    set: function (v) {
      const { data: list } = this.get();
      if (v && !list.includes(v)) {
        list.push(v);
        setStorageSync(this.key, list);
      }
    },
  };

  const triggerChange = (_mina_pro = {}) => {
    setIsOpened(!!(_mina_pro.status == 1));
    setSvipData(_mina_pro);
  };

  useUpdate(
    (loginData) => {
      if (!loginData.logined) return;
      if (storageWay === 'scan' && ['courier'].includes(relationInfoType)) {
        const reqData = {
          courier_id,
          courier_mobile: account_phone,
        };
        const v = Object.values(reqData)
          .filter((i) => !!i)
          .join('-');
        // 一个快递员仅提示一次
        if (!v || storage.isLimit(v)) return;
        checkSvipStatus(reqData).then((res) => {
          if (res.status == 1) {
            storage.set(v);
          }
          triggerChange(res);
        });
      }
    },
    [source, account_phone, courier_id, relationInfoType, storageWay],
  );

  const handleClose = () => {
    setSvipData({});
  };

  const handleJumpToWkd = () => {
    const app_id =
      process.env.PLATFORM_ENV === 'alipay' ? '****************' : 'wxcffe208b5bad2174';
    Taro.navigator({
      url:
        source === 'edit'
          ? `pages-1/pages/order/edit/send/index?courier_phone=${account_phone}&dak_id=${dak_id},${app_id}`
          : `pages/query/index,${app_id}`,
    });
  };

  return {
    isOpened,
    svipData,
    relationInfo,
    handleClose,
    handleJumpToWkd,
  };
};

export const checkIsSVip = (opts) => {
  const { waybill, order_id } = opts || {};
  return new Promise(async (resolve) => {
    // 1、验证全局快递员是否开通专业版
    // 2、验证订单号、运单号关联的快递员是否开通专业版
    let relationInfo = {},
      mina_pro = {};
    const resoleFn = (mina_pro, relationInfo) => {
      resolve({
        isSvip: !!(mina_pro.status == 1),
        mina_pro,
        relationInfo,
      });
    };
    try {
      relationInfo = (Taro.kbRelationInfo && Taro.kbRelationInfo.data) || {};
      console.log('relationInfo', relationInfo);
      if (!relationInfo.type) {
        relationInfo = await getLastUseRelation();
      }
      console.log('checkIsSVip-relationInfo===>', relationInfo);
      const { courier_id, account_phone, dak_id, join_code } = relationInfo || {};
      mina_pro = await checkSvipStatus({
        courier_id,
        courier_mobile: account_phone,
        dak_id,
        waybill,
        order_id,
        join_code,
      });
    } catch (err) {}
    resoleFn(mina_pro, relationInfo);
  });
};
