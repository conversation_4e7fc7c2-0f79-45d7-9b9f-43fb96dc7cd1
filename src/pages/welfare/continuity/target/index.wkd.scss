.kb-continuity {
  position: relative;
  height: 100%;

  &__top {
    padding-top: 10px;
  }

  &__swiperContainer {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
    width: 100%;
    height: 320px;
  }

  &__swiper {
    height: 320px;

    &__item {
      height: 100%;

      &__image {
        width: 100%;
        height: 100%;
      }
    }
  }

  &__container {
    position: absolute;
    top: 280px;
    left: 0;
    z-index: 2;
    width: 100%;
    height: 100%;

    &--static {
      position: static;
    }
  }

  &__iconArea {
    margin-top: 20px;
    margin-right: 20px;
    margin-left: 20px;
    background-color: #fff;
    border-radius: 20px;

    &__item {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 200px;

      &__image {
        width: 100px;
        height: 100px;
        border-radius: 50%;
      }

      &__desc {
        margin-top: 20px;
        color: #333;
        font-size: 24px;
        text-align: center;
      }
    }
  }

  &__hotTopic {
    margin-top: 20px;
    margin-right: 20px;
    margin-left: 20px;
    background-color: #fff8f1;
    border: $width-base solid #ffdfbf;
    border-radius: 20px;

    &__header {
      display: flex;
      align-items: center;
      justify-content: center;

      &__image {
        width: 316px;
        height: 62px;
      }
    }

    &__item {
      display: flex;
      flex-wrap: wrap;
      gap: 30px;
      padding: 30px;

      &__image {
        width: 310px;
        height: 448px;
      }
    }
  }

  &__sign {
    position: relative;
    height: 264px;
    margin-top: 20px;
    margin-right: 20px;
    margin-left: 20px;
    background: url('https://cdn-img.kuaidihelp.com/wkd/welfare/package.png') no-repeat;
    background-position: 0 0;
    background-size: cover;

    &__signSwitch {
      position: absolute;
      top: 30px;
      right: 30px;

      .kb-switch {
        padding: unset;
        background-color: transparent;
      }

      .at-switch__title {
        font-size: #666;
      }

      .at-switch__container {
        background-color: transparent;
        zoom: 0.6;
      }
    }
  }

  .at-curtain {
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.6);
    // position: relative;

    .popupMoney {
      position: absolute;
      top: 120px;
      left: 0;
      width: 100%;
      color: #da5300;
      font-weight: bold;
      font-size: 40px;
      text-align: center;

      .count {
        font-size: 56px;
      }
    }

    .vipCount {
      top: 138px;
      left: 29px;
      color: #d3492e;

      .count {
        font-size: 55px;
      }
    }

    .describe {
      position: absolute;
      top: 210px;
      left: 0;
      width: 100%;
      color: #da5300;
      font-weight: bold;
      font-size: 40px;
      text-align: center;
    }

    .button {
      position: absolute;
      bottom: 170px;
      left: 50%;
      transform: translateX(-50%);
    }

    .reject {
      position: absolute;
      bottom: 100px;
      left: 0;
      width: 100%;
      color: #ffa844;
      font-size: 30px;
      text-align: center;
    }

    .agreement {
      position: absolute;
      bottom: 280px;
      left: 60%;
      display: flex;
      align-items: center;
      width: 100%;
      transform: translateX(-48%);
      zoom: 0.8;

      .at-button__wxbutton {
        color: #f6d008;
      }
    }

    .save_button {
      position: absolute;
      bottom: 95px;
      left: 50%;
      width: 90%;
      height: 100px;
      color: #cf3e2d;
      font-weight: bold;
      font-size: $font-size-xxl;
      letter-spacing: 1px;
      border: none;
      transform: translateX(-49%);
    }

    .header {
      position: absolute;
      top: 500px !important;
      left: 50%;
      width: 100%;
      color: #fbe6c3 !important;
      font-weight: 500;
      font-size: 50px;
      text-align: center;
      transform: translateX(-50%);
    }

    .center {
      position: absolute;
      top: 570px !important;
      left: 50%;
      width: 90%;
      color: #000;
      color: #fbe6c3 !important;
      font-weight: 500;
      font-size: 60px;
      text-align: center;
      transform: translateX(-50%);
    }

    .fotter {
      position: absolute;
      top: 700px !important;
      left: 50%;
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 100%;
      height: 160px;
      color: #000;
      font-size: 30px;
      text-align: center;
      transform: translateX(-50%);

      &__btn {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 60%;
        height: 80px;
        margin: 20px 0;
        color: #eb3f33;
        font-weight: 500;
        font-size: 30px;
        line-height: 82px;
        letter-spacing: 5px;
        background: #f4b54e;
        border-radius: 50px;
        box-shadow: 0 8px 0 #f32525;
      }

      &__txt {
        color: #f7cd46;
        font-weight: 600;
        font-size: 22px;
      }
    }
  }

  .wkd_continuity {
    align-items: unset;
    width: 50px;
    height: auto;
    height: 60px;
    padding: 0;
    border: none;
  }
}

.kb-everyday {
  position: relative;
  height: 400px;
  margin-top: 20px;
  margin-right: 20px;
  margin-left: 20px;
  overflow-y: hidden;
  background: url('https://cdn-img.kuaidihelp.com/wkd/welfare/everyday.png') no-repeat;
  background-position: 0 0;
  background-size: cover;
  -webkit-overflow-scrolling: touch;

  &__title {
    position: absolute;
    top: 30px;
    right: 60px;

    &::after {
      position: absolute;
      top: 50%;
      right: -24px;
      border-top: 10px solid transparent;
      border-bottom: 10px solid transparent;
      border-left: 14px solid #5c0704;
      transform: translateY(-50%);
      content: '';
    }

    &__button {
      height: auto;
      padding: 0;
      line-height: 1;
      border: none;
    }

    &__text {
      position: relative;
      color: #5c0704;
      font-size: 26px;
    }
  }
}
