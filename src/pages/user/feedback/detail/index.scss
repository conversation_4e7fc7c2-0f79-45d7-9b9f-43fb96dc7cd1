/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb-feedback-detail {
  padding: $spacing-h-md;
  &-time {
    box-sizing: border-box;
    width: fit-content;
    margin: 0 auto;
    margin-top: $spacing-h-md;
    padding: 2px $spacing-h-md;
    color: $color-grey-6;
    font-size: $font-size-sm;
    background-color: $color-grey-3;
    border-radius: $border-radius-md;
  }
  &-content {
    position: relative;
    box-sizing: border-box;
    padding: $spacing-h-md;
    color: $color-white;
    background-color: $color-brand;
    border-radius: $border-radius-md;
    &::after {
      position: absolute;
      top: 50%;
      right: -10px;
      border-top: 10px solid transparent;
      border-bottom: 10px solid transparent;
      border-left: 10px solid $color-brand;
      transform: translateY(-50%);
      content: '';
    }
  }
  &-avatar {
    width: 80px;
    height: 80px;
    margin-left: $spacing-h-md;
    border-radius: 50%;
  }
  &-img {
    width: 150px;
    height: 150px;
    border-radius: $border-radius-md;
  }
  &-reply {
    .kb-feedback-detail-avatar {
      margin-right: $spacing-h-md;
      margin-left: 0;
    }
    .kb-feedback-detail-content {
      color: $color-grey-1;
      background-color: $color-white;
      &::after {
        right: unset;
        left: -20px;
        border-right: 10px solid $color-white;
        border-left: 10px solid transparent;
      }
    }
  }
  .kb-placeholder {
    width: 100px;
    height: 100px;
  }
}
