/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb-authPrompt {
  .aBox {
    position: absolute;
    bottom: 0;
    width: 100%;
    background: red;
  }
  .at-float-layout__container {
    overflow: visible;
    background: #f2f6f9;
    border-radius: 0 !important;
  }
  &-container {
    margin-top: -160px;
    .header {
      position: relative;
      max-height: 240px;
      margin-bottom: -15px;
      .bg-img {
        display: block;
        width: 100%;
      }
      .title {
        position: absolute;
        top: 80px;
        left: $spacing-v-md;
        color: #333333;
        font-weight: bold;
        font-size: 36px;
        &-tag {
          margin-top: 10px;
          margin-left: -10px;
          color: $color-brand;
        }
      }
    }
    .content {
      position: relative;
      overflow: hidden;
      &-block {
        margin-bottom: $spacing-v-md * 2;
        padding: 0 $spacing-v-md;
        &-title {
          margin-bottom: $spacing-v-md;
          font-size: 24px;
        }
        .card {
          position: relative;
          display: flex;
          align-items: center;
          justify-content: space-between;
          box-sizing: border-box;
          min-height: 168px;
          padding: $spacing-v-md;
          background: #ffffff;
          border-radius: 16px;
          &-left {
            margin-right: $spacing-v-md;
            .avatar {
              width: 88px !important;
              height: 88px !important;
              border-radius: 50%;
            }
          }
          &-body {
            flex: 1;
            .title {
              display: flex;
              align-items: center;
              color: #333333;
              font-weight: bold;
              font-size: 32px;
              &-tag {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 98px;
                height: 36px;
                margin-left: $spacing-v-sm;
                color: $color-brand;
                font-weight: bold;
                font-size: 24px;
                background: rgba(0, 159, 255, 0.1);
                border-radius: 6px;
              }
            }
            .desc {
              margin-top: 5px;
              color: #999999;
              font-size: 24px;
            }
          }
          &-extra {
            .btn {
              display: flex;
              align-items: center;
              justify-content: center;
              width: 188px;
              height: 54px;
              color: #ffffff;
              font-weight: bold;
              font-size: 26px;
              background: $color-brand;
              border-radius: 27px;
            }
          }
          &-tag {
            position: absolute;
            top: 0;
            right: 0;
            height: 36px;
            padding: 0 20px;
            color: $color-brand;
            font-weight: bold;
            font-size: 22px;
            background: rgba(0, 159, 255, 0.1);
            border-radius: 0px 16px 0px 10px;
          }
        }
      }
    }
  }
}
