/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import request from '@base/utils/request';

// 格式化整理报价单数据
export const formatResponseQuotation = (res) => {
  console.info('报价单返回', res);
  const { code, data } = res;
  if (code == 0 && data) {
    const { quotation: originalQuotation, user_discount_status } = data || {};
    let quotation = [];
    // 整理数据
    if (originalQuotation) {
      Object.keys(originalQuotation).map((key) => {
        const oBrandItem = originalQuotation[key] || {};
        let item = oBrandItem[user_discount_status] || {};
        item.brand = item.brand || key;
        item.user_discount_status = user_discount_status;
        quotation.push(item);
      });
    }
    res.data.originalQuotation = originalQuotation;
    res.data.quotation = quotation;
  }
  return res;
};

export const tabList = [
  {
    title: '寄大件',
  },
  {
    title: '寄小件',
  },
];

export const supportedBrands = [
  {
    brand: 'sto',
    current: 0,
  },
  {
    brand: 'yt',
    current: 0,
  },
  {
    brand: 'yd',
    current: 0,
  },
];

export const supportedBrandsDjj = [
  {
    brand: 'dp',
    current: 0,
  },
];

export const getUsedCouponQuotation = (params) => {
  return new Promise((resolve) => {
    request({
      url: '/g_wkd/v2/Yj/Order/useCouponQuotation',
      data: params,
      toastLoading: false,
      onThen: (res) => {
        resolve(res.data && res.data.brand ? res.data : {});
      },
    });
  });
};
