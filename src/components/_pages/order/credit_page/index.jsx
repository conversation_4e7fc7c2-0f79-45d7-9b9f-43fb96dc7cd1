import KbPage from '@base/components/page';
import { ScrollView } from '@tarojs/components';
import CreditFooter from './footer';
import CreditHeader from './header';
import CreditMain from './main';

const CreditIndex = (props) => {
  return (
    <KbPage
      {...props}
      renderHeader={<CreditHeader {...props} />}
      renderFooter={<CreditFooter {...props} />}
    >
      <ScrollView style={{ height: '100%' }} scrollY>
        <CreditMain {...props} />
      </ScrollView>
    </KbPage>
  );
};

CreditIndex.options = {
  addGlobalClass: true,
};

export default CreditIndex;
