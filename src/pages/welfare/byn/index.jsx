/* eslint-disable react/no-array-index-key */
/* eslint-disable no-unused-vars */
/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { Component, Fragment } from '@tarojs/taro';
import KbPage from '@base/components/page';
import { getUserStateByComplete } from '@base/utils/utils';

class Index extends Component {
  config = {
    usingComponents: {
      'mp-webview': '../../../components/byn-webview/byn-webview',
    },
  };
  constructor() {
    this.state = {};
  }

  componentDidMount() {}

  onUpdate = ({ userInfo }) => {
    if (getUserStateByComplete()) {
      this.initPlugins(userInfo);
    }
  };

  initPlugins = (info = {}) => {
    if (process.env.PLATFORM_ENV === 'weapp') {
      const bynPlugin = Taro.requirePlugin('bynPlugin');
      bynPlugin.init({
        // 必填，须填入必应鸟平台的应用对应的 appKey、appSecret
        appKey: 'exwvrx',
        appSecret: 'd1a22a36021f1aed951a55f3ddc84be8',
        openid: info.openid,
        outUid: info.user_id,
        parentUids: () => '1,2,3',
        icode: '',
        inviteCode: '',
        customNavigationStyle: false,
      });
    }
    console.log('页面参数', this.$router.params);
    Taro.navigator({
      url: this.$router.params.url,
      target: 'self',
      isFromByn: true,
    });
  };

  render() {
    const { ...rest } = this.state;

    return (
      <Fragment>
        <KbPage {...rest} onUpdate={this.onUpdate}>
          加载中...
        </KbPage>
      </Fragment>
    );
  }
}
export default Index;
