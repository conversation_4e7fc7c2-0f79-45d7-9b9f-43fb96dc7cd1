/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { useUpdate } from '@base/hooks/page';
import { frequencyLimitByMinute, getPage, reportAnalytics } from '@base/utils/utils';
import { View, Image } from '@tarojs/components';
import Taro, { useEffect, useState } from '@tarojs/taro';
import { AtFloatLayout, AtIcon } from 'taro-ui';
import { useSelector } from '@tarojs/redux';
import {
  gzhMap,
  initGzhStatus,
  useGetFollowGZHStatus,
} from '@/components/_pages/official-account/_utils';
import isEmpty from 'lodash/isEmpty';
import './drawer.scss';

const GzhDrawer = () => {
  const [isOpened, setIsOpened] = useState(false);
  const { followGZHStatus = initGzhStatus, relationInfo } = useSelector((state) => state.global);
  const { unlockGetGZH } = useGetFollowGZHStatus();
  const frequencyLimitKey = 'gzhDrawerFrequencyLimit';

  const showGuide = (item) => {
    frequencyLimitByMinute('limit', frequencyLimitKey);
    unlockGetGZH();
    setIsOpened(item);
    reportAnalytics({
      key: 'bind_mobile_phone',
      source: `展示推荐公众号-${item.name}`,
    });
  };

  useUpdate(
    (loginData) => {
      const { logined } = loginData || {};
      if (Taro._authPhoneLock) {
        // 已弹出，锁定
        return;
      }
      const { type: relationInfoType } = relationInfo || {};
      if (logined && !isEmpty(followGZHStatus)) {
        const { path = '' } = getPage().$router || {};
        const enablePaths = [
          'pages-2/pages/order/detail/index',
          'pages-0/pages/query/detail/target/index',
          'pages-2/pages/order/result/index',
        ];

        // 页面限制
        const isEnable = enablePaths.includes(`${path}`.replace(/^\//, ''));
        const relationInfoLimit = process.env.MODE_ENV === 'wkd' && relationInfoType === 'brand';
        if (isEnable && !relationInfoLimit) {
          frequencyLimitByMinute('check', frequencyLimitKey, 7 * 24 * 60).then((isLimit) => {
            if (!isLimit) {
              if (!followGZHStatus.wkdzs) {
                const gzhItem = gzhMap['wkdzs'];
                showGuide(gzhItem);
              } else if (!followGZHStatus.wkdhy) {
                const yzItem = gzhMap['wkdhy'];
                showGuide(yzItem);
              }
            }
          });
        }
      }
    },
    [followGZHStatus, relationInfo],
  );

  const handleCancel = () => {
    setIsOpened(false);
  };

  useEffect(() => {
    if (isOpened) {
      // 锁定弹窗，防止其他页面持续弹出
      Taro._authPhoneLock = true;
    }
  }, [isOpened]);

  return (
    <AtFloatLayout isOpened={isOpened} onClose={handleCancel}>
      <View className='kb-gzh-drawer'>
        <View
          className='kb-gzh-drawer__title-close'
          onClick={handleCancel}
          hoverClass='kb-hover-opacity'
        >
          <AtIcon className='kb-size__base kb-color__brand' prefixClass='kb-icon' value='wrong' />
        </View>
        <View className='kb-gzh-drawer__code'>
          <Image
            className='kb-gzh-drawer__code-img'
            showMenuByLongpress
            mode='widthFix'
            src={isOpened && isOpened.img2}
          />
        </View>
      </View>
    </AtFloatLayout>
  );
};

GzhDrawer.options = {
  addGlobalClass: true,
};

export default GzhDrawer;
