import apis from '@/utils/apis';
import Create from '@base/components/long-list/create';
import request from '@base/utils/request';
import { setStorage, getStorage } from '@base/utils/utils';

/**
 *
 * @description 获取城市列表
 * @param {*} onThen
 */
export function createCityLongList(onThen) {
  return {
    storageKey: 'citys',
    packetSize: 5,
    packetStorage: (data) => {
      // 格式化分组缓存
      const { province, city, district } = data;
      const district_1 = {},
        district_2 = {},
        district_3 = {},
        district_4 = {};
      Object.keys(district).map((key) => {
        if (0 < key && key <= 3732) {
          district_1[key] = district[key];
        } else if (3732 < key && key <= 6000) {
          district_2[key] = district[key];
        } else if (6000 < key && key <= 9000) {
          district_3[key] = district[key];
        } else {
          district_4[key] = district[key];
        }
      });
      return [
        { province, city },
        { district: district_1 },
        { district: district_2 },
        { district: district_3 },
        { district: district_4 },
      ];
    },
    api: {
      url: apis.city,
      onThen,
    },
  };
}

/**
 *
 * @description 获取城市信息
 */
export function getCities() {
  return new Promise((resolve) => {
    new Create(createCityLongList((_, res) => resolve(res.data)));
  });
}

export const CITY_VERSION_KEY = 'citys_version';

// 获取缓存的城市版本信息
export const getStorageVersion = () => {
  return new Promise((resolve) => {
    getStorage({
      key: CITY_VERSION_KEY,
    })
      .then((res) => {
        const { data: version } = res.data || {};
        resolve({ version });
      })
      .catch(resolve);
  });
};

// 检查是否需要更新数据
export const checkCityAndRefresh = () => {
  return new Promise((resolve) => {
    getStorageVersion().then((data) => {
      const { version: storageVersion = '' } = data || {};
      request({
        url: apis['city.version'],
        toastLoading: true,
        onThen: ({ data }) => {
          const { version } = data || {};
          resolve(!storageVersion || !version || storageVersion !== version);
          setStorage({
            key: CITY_VERSION_KEY,
            data: version,
          });
        },
      });
    });
  });
};

// 检测值类似
export const checkSimilar = (a, b) => a.includes(b) || b.includes(a);
