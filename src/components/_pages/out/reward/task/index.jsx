/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */
/* eslint-disable react/no-unknown-property */

import Taro, { Component, Fragment } from '@tarojs/taro';
import KbLongList from '@base/components/long-list';
import { View } from '@tarojs/components';
import { AtAvatar, AtButton } from 'taro-ui';
import { afterDoneTask } from '@/services/out-reward/task';
import { triggerRewardReportAnalytics } from '@/components/_pages/out/reward/_utils/reportAnalytics';
import { checkIsDone } from '@/components/_pages/out/reward/_utils/task';
import isFunction from 'lodash/isFunction';
import isEqual from 'lodash/isEqual';
import './index.scss';

class RewardTask extends Component {
  config = {
    usingComponents: {
      'ad-task-component': 'plugin://xlightPlugin/ad-task-component',
    },
  };
  constructor() {
    this.state = {
      taskList: [],
      isOpened: false,
    };

    this.listData = {
      api: {
        request: () =>
          new Promise((resolve) => {
            this.triggerLoad(resolve);
          }),
        onThen: (list) => {
          this.doneCount = 0;
          this.setState({
            taskList: list,
          });
        },
      },
    };
  }

  // 触发loadCallback
  triggerCallback = (res) => {
    const { loadCallback } = this;
    if (isFunction(loadCallback)) {
      loadCallback(res);
    }
  };

  // 触发请求
  triggerLoad = async (callback) => {
    this.loadCallback = callback;
    try {
      await this.initTask();
      await this.sdk.load();
    } catch (error) {
      this.triggerCallback({
        code: 1001,
        msg: error.message,
      });
    }
  };

  handleReady = (ins) => {
    this.listIns = ins;
  };

  triggerReload = () => {
    if (this.listIns) {
      this.listIns.loader();
    }
  };

  initTask = async () => {
    const {position, data = {}} = this.props;
    const { id, spaceCode } = data;
    if (!spaceCode || (this.sdk && spaceCode === this.spaceCode)) return;
    // 统计-任务4-任务加载
    triggerRewardReportAnalytics({
      action: 'task_init',
      taskId: id,
    }, position);
    // eslint-disable-next-line
    const plugin = requirePlugin('xlightPlugin'); // 插件引入
    this.spaceCode = spaceCode;
    this.sdk = plugin.createTaskAd({
      adUnitId: spaceCode,
    });
    // 任务完成的回调事件，会返回完成任务的流水号
    this.sdk.onTaskFinished((ad) => {
      const curAd = this.taskList.find((item) => item.adBizId === ad.adBizId);

      afterDoneTask({
        id,
        ad: {
          ...curAd,
          ...ad,
        },
      }, position).then((res) => {
        this.doneCount++;
        if (this.doneCount >= this.taskList.length) {
          this.triggerReload();
        }
        if (`${res.code}` === '20240822') {
          this.setState({
            isOpened: false,
          });
        }
      });
    });
    // 任务刷新的回调，每次刷新都会在该事件中返回刷新后的任务数据
    this.sdk.onUpdate((list) => {
      if (list.length > 0) {
        this.taskList = list;
        this.triggerCallback({
          code: 0,
          data: {
            list,
          },
        });
        this.setState({
          taskList: list,
        });
      }
    });
    // 任务错误的回调
    this.sdk.onError((error) => {
      triggerRewardReportAnalytics({
        action: 'task_init_error',
        taskId: id,
      }, position);
      this.triggerCallback({
        code: 1002,
        msg: error.message,
      });
    });
  };

  checkCanOpened = () => {
    const { data } = this.props;
    const { id, spaceCode, status } = data || {};
    // 有值，且未完成
    this.setState({
      isOpened: spaceCode && id && !checkIsDone(status),
    });
  };

  componentDidMount() {
    this.checkCanOpened();
  }
  componentDidUpdate(preProps) {
    const { data } = preProps;
    const { data: nextData } = this.props;
    if (!isEqual(data, nextData)) {
      this.checkCanOpened();
    }
  }

  render() {
    const { taskList, isOpened } = this.state;

    return (
      <Fragment>
        {isOpened ? (
          <KbLongList
            enableRefresh={false}
            className='kb-rewardTask'
            data={this.listData}
            height='auto'
            onReady={this.handleReady}
          >
            <View className='kb-list'>
              {taskList.map((item) => (
                <View className='kb-list__item--wrapper' key={item.adBizId}>
                  <View className='kb-list__item'>
                    {item.adMerchantLogo ? (
                      <View className='item-icon'>
                        <AtAvatar circle image={item.adMerchantLogo} />
                      </View>
                    ) : null}
                    <View className='item-content'>
                      <View className='item-content__title kb-color__brand'>{item.taskTitle}</View>
                      <View className='item-content__desc'>{item.taskSubTitle}</View>
                    </View>
                    <View className='kb-spacing-md-r'>
                      {item.taskStatus === 'FINISHED' ? (
                        <AtButton type='primary' circle size='small' disabled>
                          已完成
                        </AtButton>
                      ) : (item.taskStatus === 'INIT' || item.taskStatus === 'DOING') &&
                        item.adBizId ? (
                        <ad-task-component adBizId={item.adBizId}>
                          <AtButton type='primary' circle size='small'>
                            去看看
                          </AtButton>
                        </ad-task-component>
                      ) : null}
                    </View>
                  </View>
                </View>
              ))}
            </View>
          </KbLongList>
        ) : null}
      </Fragment>
    );
  }
}

export default RewardTask;
