/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */
import { useBoundingClientRect, useObserver } from '@base/hooks/observer';
import { useRef, useState } from '@tarojs/taro';
import { getAdConfigApiAndData } from '@/components/_pages/ad-extension/_utils';
import { adNavigator } from '@/components/_pages/ad-extension/sdk';
import isArray from 'lodash/isArray';
import orderBy from 'lodash/orderBy';

export const createFeedsTabs = () => {
  return [
    {
      title: '精选',
      key: 'jx',
    },
    {
      title: '本地生活',
      key: 'bdsh',
    },
    {
      title: '经济出行',
      key: 'jjcx',
    },
    {
      title: '好物商城',
      key: 'hwsc',
    },
  ];
};

export const useAffixTab = (opt) => {
  const { selector } = opt;
  const [tabBarHeight, setTabBarHeight] = useState(48);
  const [tabStyles, setTabStyles] = useState('');

  useBoundingClientRect(
    (resArr) => {
      const [{ height } = {}] = resArr;
      if (height > 0) {
        setTabBarHeight(height);
      }
    },
    [selector],
  );

  useObserver(
    (res) => {
      const { intersectionRatio } = res || {};
      if (intersectionRatio > 0) {
        // 取消吸顶
        setTabStyles('');
      } else {
        // 吸顶
        setTabStyles({ position: 'fixed', top: 0, left: 0, right: 0, zIndex: 1 });
      }
    },
    {
      selector: selector,
      triggerOpts: {
        top: -tabBarHeight,
      },
    },
  );

  return {
    tabStyles,
  };
};

export const createFormatList = (list) => {
  if (list && list.length > 0) {
    const arr1 = [],
      arr2 = [];
    list.map((item, index) => {
      if (index % 2) {
        arr2.push(item);
      } else {
        arr1.push(item);
      }
    });
    return [arr1, arr2];
  }
  return [];
};

export const useAdFeeds = () => {
  const [current, setCurrent] = useState(0);
  const [list, setList] = useState([]);
  const compRef = useRef({ current: 0 });

  const listData = {
    api: {
      ...getAdConfigApiAndData(),
      formatRequest: (req) => {
        const positionMap = ['34', '35', '36', '37'];
        req.position = positionMap[compRef.current.current];
        return req;
      },
      formatResponse: (res) => {
        const list = res.data;
        const hasList = isArray(list) && list.length;
        if (hasList) {
          return {
            code: 0,
            data: { list: orderBy(list, 'number', 'desc') },
          };
        }
        return {
          data: void 0,
        };
      },
      onThen: (list) => {
        setList(list);
      },
    },
  };

  const handleSwitchTab = (c) => {
    compRef.current.current = c;
    setCurrent(c);
  };

  const handleClick = ({ title, id, positionInfo, ...rest }) => {
    adNavigator({
      ...rest,
      report: {
        key: 'm_welfare',
        options: `${positionInfo}-${rest.title}`,
      },
    });
  };

  return {
    current,
    list,
    listData,
    handleSwitchTab,
    handleClick,
  };
};
