/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { useEffect, useRef, useState, Fragment } from '@tarojs/taro';
import { View } from '@tarojs/components';
import { noop, reportAnalytics } from '@base/utils/utils';
import { check } from '@base/utils/rules';
import './countdown.scss';

const reportKey = 'event_push_ad'; // 改用统一事件
const KbCountdown = (props) => {
  const { onEnd = noop, record, customStyle } = props;
  const actionRef = useRef({ countdownTimeout: 0 });
  const [countdown, setCountdown] = useState(3);
  const [show, setShow] = useState(false);

  const stopCount = () => {
    const { countdownTimeout } = actionRef.current;
    if (!countdownTimeout) return;
    clearTimeout(countdownTimeout);
  };

  const onCountDown = () => {
    if (record && !record.adUrl) return;
    if (check('url', record && record.adUrl).code !== 0) return;
    const { title, adConfig, position } = record;
    if (adConfig && adConfig.includes('b-1')) {
      setShow(true);
      actionRef.current.countdownTimeout = setTimeout(() => {
        if (countdown > 0) {
          setCountdown(countdown - 1);
        } else {
          onEnd();
          if (reportKey) {
            reportAnalytics({
              key: reportKey,
              position,
              title,
              status: 'count-down-end',
            });
          }
          stopCount();
          setShow(false);
        }
      }, 1000);
    }
  };

  useEffect(() => {
    onCountDown();
  }, [countdown, record]);

  useEffect(() => {
    return () => {
      stopCount();
      setShow(false);
    };
  }, []);

  return (
    <Fragment>
      {countdown > 0 && show ? (
        <View className='kb-count-down-wrapper'>
          <View className='kb-count-down' style={customStyle}>
            {countdown}秒后进入广告
          </View>
        </View>
      ) : null}
    </Fragment>
  );
};

export default KbCountdown;
