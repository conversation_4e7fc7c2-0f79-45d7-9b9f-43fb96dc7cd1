/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import KbPage from '@base/components/page';
import KbScrollView from '@base/components/scroll-view';
import { disturbAddress } from '@/components/_pages/address/_utils/address.edit';
import KbAddressEditGroup from '@/components/_pages/order/address-edit/edit-group';
import { formatAddress, getForm } from '@/components/_pages/order/_utils';
import Form from '@base/utils/form';
import { View, Image } from '@tarojs/components';
import { connect } from '@tarojs/redux';
import Taro, { Component } from '@tarojs/taro';
import { AtButton } from 'taro-ui';
import './index.scss';
import {
  formatParseImage,
  setAndPreviewParseImage,
} from '@/components/_pages/order/_utils/order.edit';

@connect(({ global }) => ({
  relationInfo: global.relationInfo,
  serviceConfig: global.serviceConfig,
}))
class Index extends Component {
  config = {
    navigationBarTitleText: '取件地址',
  };

  constructor(props) {
    super(props);
    this.tags = {
      send: '寄',
      receive: '收',
    };
    const {
      org, // 地址类型:收发件
      ...rest
    } = this.$router.params;
    this.state = {
      addressData: this.formatAddress(rest, org, { reverse: true }),
      form: {
        disabled: true,
        data: {},
      },
      is_default: 1,
    };
    this.bars = [
      {
        key: 'save',
        label: '保存',
      },
    ];
  }

  componentDidMount() {
    this.createForm();
  }

  formatAddress = (data, org, opts) => formatAddress(data, org, opts).data;

  // 操作行为
  actionData = () => {
    const { id, action, multiple } = this.$router.params;
    return !!id || (action === 'select' && multiple === '1')
      ? {
          label: '编辑',
          key: 'update',
          toast: '保存成功',
        }
      : {
          label: '添加',
          key: 'save',
          toast: '添加成功',
        };
  };

  // 创建表单
  // 表单输入变化
  onChange_form = () => {};
  onSubmit_form = () => {};
  createForm = () => {
    const { org } = this.$router.params;
    const form = getForm({
      list: [{ key: org }],
      action: 'address',
      merge: {
        name: {
          required: false,
        },
        mobile: {
          required: false,
        },
      },
    });
    this.formIns = new Form(
      {
        form: form,
        onSubmit: (req) => {
          Taro.kbSetGlobalData('pickupAddress', req);
          Taro.navigator({
            post: {
              type: 'pickupAddressEdit',
              data: req,
            },
          });
        },
        onReady: () => {
          this.formIns.update(this.$router.params);
        },
      },
      this,
    );
  };

  // 监听
  onPostMessage = (key, e) => {
    const { data, source, org } = e;
    let keys;
    switch (key) {
      case 'addressSelect':
        break;
      case 'citySelect':
        keys = ['province', 'city', 'district'];
        break;
      case 'routerParamsChange':
        switch (source) {
          case 'goods':
            this.handleChange({ data });
            break;
          default:
            break;
        }
        break;
    }
    if (org && data) {
      // 地址选择
      this.setState({
        addressData: this.formatAddress(data, org, { reverse: true, keys }),
      });
    }
  };
  handleSubmit = () => {
    const { form: { data: formData = {} } = {} } = this.state;
    const { province, city, district, address } = formData || {};
    const addressNotComplete = !province || !city || !district || !address;
    if (addressNotComplete) {
      Taro.kbToast({
        text: '未录入完整取件地址信息，请录入完整后尝试保存',
      });
      return;
    }
    this.onSubmit_form();
  };
  // 地址信息输入变化
  handleChange = (e) => {
    this.formIns && this.formIns.update(e.data);
  };
  handleAddressChange = (data) => {
    const { nextData } = data || {};
    nextData && this.handleChange(data);
  };
  handleParseImg = (key, data) => setAndPreviewParseImage(this, key, data);
  render() {
    const { isBatch, addressData, allowSetDefault, parseImg, ...rest } = this.state;
    const { org, source, action } = this.$router.params;
    const parseImgList = formatParseImage(parseImg);

    return (
      <KbPage {...rest}>
        <KbScrollView
          renderFooter={
            <View className='kb-address-edit__bars'>
              {this.bars.map((item) => (
                <View className='kb-address-edit__bars--item' key={item.key}>
                  <AtButton
                    type={item.type || 'primary'}
                    onClick={this.handleSubmit.bind(this)}
                    circle
                  >
                    {item.label}
                  </AtButton>
                </View>
              ))}
            </View>
          }
        >
          <KbAddressEditGroup
            onChange={this.handleAddressChange}
            onParseImg={this.handleParseImg.bind(this, 'img')}
            data={disturbAddress(addressData, org, 'clear')}
            org={org}
            action={action}
            allowUpload={false}
            hideBook
            allowAi
            allowSetDefault={false} // 大客户地址设置默认
            source={source}
            isPickupAddress
          />
          {parseImgList
            ? parseImgList.map((item) => (
                <View key={item} className='kb-box kb-spacing-md kb-margin-md kb-text__center'>
                  <Image
                    onClick={this.handleParseImg.bind(this, 'preview', item)}
                    src={item.img}
                    style={item.style}
                  />
                </View>
              ))
            : null}
        </KbScrollView>
      </KbPage>
    );
  }
}

export default Index;
