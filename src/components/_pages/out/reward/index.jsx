/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */
/* eslint-disable react/no-unknown-property */

import Taro, { Component, Fragment } from '@tarojs/taro';
import { Image, ScrollView, View } from '@tarojs/components';
import { iconMap } from '@/components/_pages/out/reward/_utils/icon';
import { debounce } from '@base/utils/utils';
import KbExternalAd from '@/components/_pages/ad-extension/ad';
import classNames from 'classnames';
import { getSignDetails } from '@/services/out-reward/sign';
import { formatSignDetail } from '@/components/_pages/out/reward/_utils/sign';
import { afterDoneTask, getTask } from '@/services/out-reward/task';
import { checkIsTask, doneTask } from '@/components/_pages/out/reward/_utils/task';
import KbLongList from '@base/components/long-list';
import KbAdFloat from '@/components/_pages/ad-extension/ad/float';
import { StorageVisits } from '@base/utils/storage-visits';
import { triggerRewardReportAnalytics } from '@/components/_pages/out/reward/_utils/reportAnalytics';
import KbAdCurtain from '@/components/_pages/ad-extension/ad/curtain';
// import { redemptionList } from '@/components/_pages/out/reward/_utils/integral';
import KbAdExtension from '@/components/_pages/ad-extension';
import RewardTask from './task';
import { getAdExtensionReq } from '../../ad-extension/_utils';
import GuideCollect from '../../guide/collect';
import { createAd } from '../../ad-extension/sdk';
import KbSignTask from './sign-task';
import KbDayPrize from './day-prize';
import './index.scss';

const adAgs = getAdExtensionReq('welfare.reward');

class RewardPage extends Component {
  config = {
    usingComponents: {
      feeds: 'plugin://xlightPlugin/feeds',
    },
  };
  constructor() {
    this.state = {
      signDetail: formatSignDetail(),
      taskList: [],
      task4Data: null,
    };
    this.handleTaskDone = debounce(this.handleTaskDone);
    this.updateSignDetails = debounce(this.updateSignDetails);
    const { position } = this.props;
    this.listData = {
      api: {
        request: () => getTask({ position }),
        onThen: (taskList) => {
          // 灯火任务底部渲染
          const task4Data = taskList.find((item) => checkIsTask(item.type));
          this.setState({
            taskList: taskList.filter((item) => !checkIsTask(item.type)),
            task4Data,
          });
        },
      },
    };

    // 记录订阅
    this.storageVisits = new StorageVisits({ key: 'out.reward.subscribe' });
  }

  componentDidMount() {
    const { routerParams } = this.props;
    const { secondPage, source } = routerParams || {};
    // 统计-订阅进入
    const { position } = this.props;
    if (source) {
      triggerRewardReportAnalytics(
        {
          action: 'source_back',
          source,
        },
        position,
      );
    }
    // 引导二次页面跳转
    if (secondPage) {
      Taro.navigator({
        url: secondPage,
      });
    } else {
      this.insertAd();
    }
  }

  componentDidShow() {
    if (this.readyRefresh) {
      this.readyRefresh = false;
      // 跳转任务页，完成回跳刷新页面
      this.updateTask();
      this.updateSignDetails();
    }
  }

  onDidShow() {
    console.log('onDidShow');
    this.updateSignDetails();
    if (Taro.isFromIntegralRecord) {
      Taro.isFromIntegralRecord = false;
      this.insertAd();
    }
  }

  handleSubscribeCheck = () => {
    return {
      code: this.storageVisits.check() ? 0 : 1001,
    };
  };
  handleCloseSubscribeBar = () => {
    // 已订阅，或关闭
    this.storageVisits.record();
  };
  handleSubscribe = (res) => {
    const { code } = res;
    const { position } = this.props;
    if (code === 0) {
      // 统计-订阅成功
      triggerRewardReportAnalytics({ action: 'subscribe' }, position);
      this.handleCloseSubscribeBar();
    }
  };

  // 插入广告
  insertAd = () => {
    const { position } = this.props;
    if (!position) return;
    const screenKey = `out.reward.screen${position}`;
    createAd(`out.reward.full${position}`, {
      filter: () => false,
      onClose: () => {
        createAd(screenKey);
      },
      onError: () => {
        createAd(screenKey);
      },
    });
  };

  handleLongListReady = (ins) => {
    this.listIns = ins;
  };
  // 更新任务列表
  updateTask = () => {
    this.listIns.loader();
  };

  // 获取签到详情：积分、现金、签到列表
  updateSignDetails = () => {
    getSignDetails().then((signDetail) => {
      this.setState({
        signDetail,
      });
      if (this.props.updateState) {
        this.props.updateState({
          signDetail,
        });
      }
    });
  };

  // 完成任务
  handleTaskDone = (item) => {
    const { position } = this.props;
    doneTask(item, position).then(({ errMsg, isEnded } = {}) => {
      if (errMsg) {
        Taro.kbToast({
          text: errMsg,
        });
      } else if (isEnded) {
        afterDoneTask({ id: item.id, money: item.money }, position).then((res) => {
          if (`${res.code}` === '0') {
            this.updateSignDetails();
            this.updateTask();
          }
        });
      }
    });
  };

  // 积分兑换
  handleIntegralRedemption = () => {
    Taro.kbToast({ text: '积分不足！' });
  };

  // 弹窗广告加载
  handleAdCurtainLoad = (has) => {
    if (!has) {
      // 无弹窗广告时
    }
  };

  render() {
    const { task4Data, signDetail, taskList } = this.state;
    const { position } = this.props;

    return (
      <Fragment>
        <GuideCollect />
        <ScrollView scrollY className='kb-scrollview'>
          <View className='container'>
            <View className='sign-guide__wrap' />
            <View className='box-group'>
              <KbSignTask signDetail={signDetail} updateSignDetails={this.updateSignDetails} />

              <KbDayPrize />

              <View className='box'>
                <KbExternalAd
                  closeShield
                  adUnitIdIndex={`out.reward${position}`}
                  implant={false}
                  wrapper={false}
                />
              </View>

              <View className='box'>
                <KbAdExtension data={adAgs} rootClassName='kb-ads_reward' />
              </View>

              <View className='box box-spacing box-task'>
                <View className='box-title'>
                  <View className='box-title__first'>天天赚现金</View>
                  <View className='box-title__second'>完成任务返回即可领取</View>
                </View>

                <KbLongList
                  enableRefresh={false}
                  data={this.listData}
                  height='auto'
                  onReady={this.handleLongListReady}
                >
                  <View className='box-content'>
                    {taskList.map((item) => (
                      <View
                        key={item.id}
                        className={classNames('box-task__item', {
                          ['box-task__item--done']: item.isDone,
                        })}
                        hoverClass={item.isDone ? 'none' : 'kb-hover-opacity'}
                        onClick={this.handleTaskDone.bind(this, item)}
                      >
                        <View className='box-task__item--label'>{item.title}</View>
                        <Image
                          className='box-task__item--image'
                          src={item.isDone ? iconMap.rewardDone : iconMap.reward}
                        />
                        {item.isDone ? null : (
                          <View className='box-task__item--money'>
                            {item.tips || `可领${item.money}元红包`}
                          </View>
                        )}
                      </View>
                    ))}
                  </View>
                </KbLongList>
              </View>

              <View>
                <RewardTask data={task4Data} position={position} />
              </View>
            </View>
            <View className='kb-reward-feeds'>
              <feeds spaceCode='27_2024082922700190506' />
            </View>
          </View>
        </ScrollView>
        <KbAdFloat position='49' closeShield placement='rb' />
        <KbAdCurtain position='50' allowFull onLoad={this.handleAdCurtainLoad} />
      </Fragment>
    );
  }
}

export default RewardPage;
