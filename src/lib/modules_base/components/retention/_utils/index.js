/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

// 挽留广告配置
const defaultConfig =
  process.env.PLATFORM_ENV === 'weapp'
    ? {
        'query.detail': {
          redirectUrl: '/pages-0/pages/query/detail/target/index?',
          source: '物流详情页',
          position: '19',
        },
        'lottery.detail': {
          redirectUrl: '/pages-0/pages/welfare/lotterys/target/index?',
          source: '抽奖详情页',
          position: '20',
        },
        'order.result': {
          redirectUrl: '/pages-2/pages/order/result/target/index?',
          source: '下单成功页',
          position: '41',
        },
      }
    : process.env.PLATFORM_ENV === 'alipay'
    ? {
        'query.detail': {
          redirectUrl: '/pages-0/pages/query/detail/target/index?',
          source: '物流详情页',
          position: '19',
        },
        'lottery.detail': {
          redirectUrl: '/pages-0/pages/welfare/lotterys/target/index?',
          source: '抽奖详情页',
          position: '',
        },
        'order.result': {
          redirectUrl: '/pages-2/pages/order/result/target/index?',
          source: '下单成功页',
          position: '',
        },
        'out.reward': {
          redirectUrl: '/pages-4/pages/out/reward/target/index?',
          source: '营销页面',
          position: '47',
        },
        'coupon.center': {
          redirectUrl: '/pages/order/kxj/center/index',
          source: '领券中心',
          position: '',
        },
      }
    : {};

function getAdPositionReq(key = '') {
  const { position = '' } = defaultConfig[key] || {};
  return {
    position,
  };
}

// 二次挽留广告
const defaultSecondaryConfig =
  process.env.PLATFORM_ENV === 'alipay'
    ? {
        'query.detail': {
          ...defaultConfig['query.detail'],
          position: '45',
        },
        'out.reward': {
          ...defaultConfig['out.reward'],
          position: '48',
        },
      }
    : {};

function getSecondaryAdPositionReq(key = '') {
  const { position = '' } = defaultSecondaryConfig[key] || {};
  return {
    position,
  };
}

function getRedirectConfig(key) {
  return defaultConfig[key];
}

function getAllRedirectPath() {
  return Object.values(defaultConfig).map((item) => {
    let path = item.redirectUrl;
    path = path.replace(/.*pages\//, '');
    path = path.replace(/\/target\/.*/, '');
    path = path.replace(/\/index.*/, '');
    return path;
  });
}

export { getAdPositionReq, getSecondaryAdPositionReq, getRedirectConfig, getAllRedirectPath };
