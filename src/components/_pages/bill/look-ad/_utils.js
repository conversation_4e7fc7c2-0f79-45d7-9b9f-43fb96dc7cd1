/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { useEffect, useRef, useState } from '@tarojs/taro';
import { getAdConfig } from '../../ad-extension/_utils';
import { adNavigator } from '../../ad-extension/sdk';
import { debounce, frequencyLimit, getSystemInfoSync, reportAnalytics } from '@base/utils/utils';
import { useBoundingClientRect, useObserver } from '@base/hooks/observer';

// 触发统计
function triggerReportAnalytics(title) {
  reportAnalytics({
    key: 'bill_look',
    title,
  });
}

const useCountDown = (opt) => {
  const { onEnd = () => {} } = opt || {};
  const compRef = useRef({ timer: null, s: 15, pause: false });
  const [count, setCount] = useState();

  const clear = () => {
    if (compRef.current.timer) {
      clearInterval(compRef.current.timer);
    }
  };

  const init = (s) => {
    compRef.current.s = s || 15;
    setCount(compRef.current.s);
  };

  const start = () => {
    let n = compRef.current.s;
    clear();
    compRef.current.timer = setInterval(() => {
      if (n <= 0) {
        setCount(0);
        compRef.current.pause = false;
        clear();
        onEnd();
        return;
      }
      if (compRef.current.pause) return;
      setCount(n--);
    }, 1000);
  };

  const pause = () => {
    compRef.current.pause = true;
  };

  const continueStart = () => {
    compRef.current.pause = false;
  };

  return {
    count,
    init,
    start,
    continueStart,
    pause,
  };
};

const useCurtainAd = () => {
  const [isOpened, updateIsOpened] = useState(false);

  const showCurtainAd = () => {
    updateIsOpened(true);
  };

  const handleClick = (adConfig) => {
    adNavigator(adConfig);
    updateIsOpened(false);
    triggerReportAnalytics(`${adConfig.title}-点击`);
  };

  const handleClose = () => {
    updateIsOpened(false);
  };

  return {
    isOpened,
    showCurtainAd,
    handleClick,
    handleClose,
  };
};

const useAdBound = (opt) => {
  const { selector, adDetail, onViewListen = () => {} } = opt;
  // res 0初始/1开始/2暂停/3继续
  const adBoundRef = useRef({ lock: true, windowHeight: 0, res: -1 });
  const [tabBarHeight, setTabBarHeight] = useState(48);
  const [tabStyles, setTabStyles] = useState('');

  useEffect(() => {
    const systemInfo = getSystemInfoSync();
    const { windowHeight } = systemInfo || {};
    if (windowHeight > 0) {
      adBoundRef.current.windowHeight = windowHeight;
    }
  }, []);

  // 获取目标元素信息
  const [selectorTrigger] = useBoundingClientRect(
    (resArr) => {
      const [{ height, top } = {}] = resArr;
      // console.log('目标元素resArr===>', resArr);
      const { windowHeight = 0 } = adBoundRef.current || {};
      if (windowHeight > 0 && top <= windowHeight / 2) {
        // console.log('达到展示广告要求===>YES', adBoundRef.current.res);
        if (adBoundRef.current.res === 1 || adBoundRef.current.res === 3) return;
        if (adBoundRef.current.res === 2) {
          adBoundRef.current.res = 3;
        } else {
          adBoundRef.current.res = 1;
        }
        onViewListen(adBoundRef.current.res);
      } else {
        // console.log('达到展示广告要求===>NO', adBoundRef.current.res);
        if (adBoundRef.current.res === 0 || adBoundRef.current.res === 2) return;
        if (adBoundRef.current.res === 1 || adBoundRef.current.res == 3) {
          adBoundRef.current.res = 2;
        } else {
          adBoundRef.current.res = 0;
        }
        onViewListen(adBoundRef.current.res);
      }
      // 为吸顶获取元素高度，获取一次即可
      if (height > 0 && !adBoundRef.current.lock) {
        adBoundRef.current.lock = true;
        setTabBarHeight(height);
      }
    },
    [selector],
    [adDetail],
  );

  // 处理目标元素吸顶
  useObserver(
    (res) => {
      const { intersectionRatio } = res || {};
      if (intersectionRatio > 0) {
        // 取消吸顶
        setTabStyles('');
      } else {
        // 吸顶
        setTabStyles({ position: 'fixed', top: 0, left: 0, right: 0, zIndex: 1 });
      }
    },
    {
      selector: selector,
      triggerOpts: {
        top: -tabBarHeight,
      },
    },
    [tabBarHeight, adDetail],
  );

  return {
    tabStyles,
    selectorTrigger,
  };
};

export const useLookAd = (props) => {
  const { actionRef } = props;
  const key = 'lookAdKey',
    idName = 'lookAd';
  const lookAdRef = useRef({ success: false, adDetail: {} });
  const [adDetail, setAdDetail] = useState();
  const [success, setSuccess] = useState();
  const triggerSuccess = () => {
    lookAdRef.current.success = true;
    setSuccess(true);
  };
  const runTriggerReportAnalytics = (tag) => {
    triggerReportAnalytics(`${lookAdRef.current.adDetail.title}-${tag}`);
  };
  const { isOpened, showCurtainAd, handleClick, handleClose } = useCurtainAd();
  const { count, init, start, continueStart, pause } = useCountDown({
    onEnd: () => {
      triggerSuccess();
      showCurtainAd();
      frequencyLimit('limit', key);
      runTriggerReportAnalytics('浏览完成');
    },
  });
  const { tabStyles, selectorTrigger } = useAdBound({
    selector: `#${idName}`,
    adDetail,
    onViewListen: (res) => {
      // console.log('res', res);
      if (lookAdRef.current.success) return;
      if (res === 1) {
        start();
        runTriggerReportAnalytics('浏览人数');
      } else if (res === 2) {
        pause();
      } else if (res == 3) {
        continueStart();
      }
    },
  });

  useEffect(() => {
    actionRef.current = {
      selectorTrigger: debounce(selectorTrigger, 100, { leading: false, trailing: true }),
    };
  }, []);

  useEffect(() => {
    getAdConfig({ position: '26' }, true).then((res) => {
      // console.log('浏览广告位', res);
      if (res && res.length > 0) {
        const ad = res[0];
        setAdDetail(ad);
        lookAdRef.current.adDetail = ad;
        const limit = frequencyLimit('check', key);
        if (limit) {
          triggerSuccess();
          return;
        }
        runTriggerReportAnalytics('拉取成功');
        const countTs = ad.title.match(/\d+[s|秒]/g);
        if (countTs && countTs.length > 0) {
          init(countTs[0].slice(0, -1));
        } else {
          init(15);
        }
      }
    });
  }, []);

  return {
    idName,
    count,
    success,
    adDetail,
    isOpened,
    tabStyles,
    handleClick,
    handleClose,
  };
};
