/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { createAd } from '@/components/_pages/ad-extension/sdk';
import KbActivityCard from '@/components/_pages/welfare/lotterys/card/index';
import KbPage from '@base/components/page';
import { reportAnalytics } from '@base/utils/utils';
import { Block, Button, Image, ScrollView, View } from '@tarojs/components';
import { Component } from '@tarojs/taro';
import { AtNoticebar } from 'taro-ui';
import methods from './methods/index';
import './style/index.scss';

export default class Index extends Component {
  config = {
    navigationBarTitleText: '每日抽奖',
    navigationBarBackgroundColor: '#fe2340',
  };
  constructor(props) {
    super(props);
    this.state = {
      currentTab: 'everyday',
      allTab: {
        everyday: {
          topName: '每日抽奖',
          page: 1,
          hasNextPage: true,
          activityList: [],
        },
        stay: {
          topName: '待开奖',
          page: 1,
          hasNextPage: true,
          activityList: [],
        },
        lottery: {
          topName: '已中奖',
          page: 1,
          hasNextPage: true,
          activityList: [],
        },
        end: {
          topName: '已结束',
          page: 1,
          hasNextPage: true,
          activityList: [],
        },
      },
      status: '',
    };
    for (var i in methods) {
      this[i] = methods[i].bind(this);
    }
  }

  componentDidMount() {
    // this.setLoadStatus();
    reportAnalytics({
      key: 'welfare.lotterys',
      title: '每日抽奖列表',
    });
    this.insertAd();
  }

  componentDidShow() {
    // this.setLoadStatus();
    if (this.state.currentTab === 'everyday') {
      this.getEveryDayList();
    } else {
      this.getMyList();
    }
  }

  // 插入广告
  insertAd = () => {
    const screenKey = `welfare.lotterys.screen`;
    createAd(`welfare.lotterys.full`, {
      filter: () => false,
      onClose: () => {
        createAd(screenKey);
      },
      onError: () => {
        createAd(screenKey);
      },
    });
  };

  onScrollToLower() {
    const { currentTab, allTab } = this.state;
    const { everyday } = allTab;
    if (currentTab === 'everyday') {
      everyday.hasNextPage ? this.getEveryDayList(everyday.hasNextPage) : '';
    } else {
      const hasNext = allTab[currentTab].hasNextPage;
      hasNext ? this.getMyList(hasNext) : '';
    }
  }

  render() {
    const { navigationBarTitleText } = this.config;
    const { currentTab, allTab, status } = this.state;
    const scrollStyle = {
      height: '100vh',
    };
    const currentList = allTab[currentTab].activityList;
    const scrollTop = 0;
    const Threshold = 20;

    return (
      <KbPage
        title={navigationBarTitleText}
        className='kb-lotterys'
        onUpdate={this.getEveryDayList}
        navigationStyle='custom'
        toast={{ text: '请稍等', status, hasMask: true }}
      >
        <View className='kb-lotterys__dailylotteryBox'>
          <ScrollView
            className='scrollview'
            scrollY
            scrollWithAnimation
            scrollTop={scrollTop}
            style={scrollStyle}
            lowerThreshold={Threshold}
            upperThreshold={Threshold}
            // onScrollToUpper={this.onScrollToUpper.bind(this)} // 使用箭头函数的时候 可以这样写 `onScrollToUpper={this.onScrollToUpper}`
            onScrollToLower={this.onScrollToLower}
          >
            <Image
              className='kb-lotterys__head'
              src='//osscdn-kbad.kuaidihelp.com/admin/ad/2022/04/28/626a272f0a4d8/WechatIMG462.png'
            />
            {process.env.PLATFORM_ENV === 'alipay' ? (
              <View className='kb-lotterys__noticebar'>
                <AtNoticebar marquee>参与抽奖提交保证金，保证金会在参与抽奖后立即返还</AtNoticebar>
              </View>
            ) : null}
            <View className='kb-lotterys__scrollBox'>
              <View className='kb-lotterys__ListParent'>
                {Object.keys(allTab).map((item, index) => {
                  return (
                    <Block key={item.key}>
                      <View className='kb-lotterys__ListTop'>
                        <View
                          className={`${
                            currentTab === item ? 'kb-lotterys__switchTab' : ''
                          } switch`}
                          onClick={this.switchTab.bind(this, item)}
                        >
                          <View className={`kb-lotterys__TopText kb-lotterys__TopText${index + 1}`}>
                            {allTab[item].topName}
                          </View>
                        </View>
                      </View>
                      {index + 1 == 4 ? '' : <View>|</View>}
                    </Block>
                  );
                })}
              </View>
              <View className='kb-lotterys__scroll'>
                {currentList && currentList.length > 0 ? (
                  currentList.map((item) => {
                    return (
                      <Block key={item.id}>
                        <View className='kb-lotterys__lottery' key={item.id}>
                          <KbActivityCard
                            Size={300}
                            params={{ ...item, currentTab }}
                            currentTab={currentTab}
                            handleCard={this.handleCard}
                            sourceType='list'
                          />
                        </View>
                        <View className='line' />
                      </Block>
                    );
                  })
                ) : (
                  <View className='kb-lotterys__emptyBox'>
                    {currentTab === 'stay' ? (
                      '暂无待开奖记录，快去参加吧～'
                    ) : currentTab === 'lottery' ? (
                      <Block>
                        <View>暂无中奖记录</View>
                        <View>参与越多抽奖，越有可能获奖哟～</View>
                      </Block>
                    ) : currentTab === 'end' ? (
                      '暂无记录，快去参加吧～'
                    ) : (
                      <View onClick={() => this.getEveryDayList()}>暂无数据，点我刷新</View>
                    )}
                    {currentTab === 'everyday' ? (
                      ''
                    ) : (
                      <Button className='kb-lotterys__ptpBtn' onClick={this.goEveryDay}>
                        立即参与
                      </Button>
                    )}
                  </View>
                )}
              </View>
            </View>
          </ScrollView>
        </View>
      </KbPage>
    );
  }
}
