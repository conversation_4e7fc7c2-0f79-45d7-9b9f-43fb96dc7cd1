/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */
import Taro from '@tarojs/taro';
import statisticsMap from '~/lib/config/config.subscribe/statisticsMap';
import { getIDsList } from '~base/components/subscribe/sdk';
import request from '~base/utils/request';

// 综合上报，包含阿拉丁与小程序自有上报
export const reportAnalytics = (report) => {
  try {
    let { skip = () => false, key, ...rest } = report || {};
    if (!key) return;
    // 配置skip且符合规则跳过统计
    if (skip(report)) return;
    // 统一上报事件
    if (['event_push', 'event-push'].includes(key)) {
      const { label = '', category = '', action = '' } = rest;
      rest = {
        label: [label, category, action].filter((item) => !!item).join('-'),
      };
    } else {
      const { title, options, status } = rest;
      const statusKeys = ['success', 'fail'];
      // 组合label，统计跳转成功与失败
      if (statusKeys.includes(status)) {
        if (title) {
          rest.title = `${title}-${status}`;
        } else if (options) {
          rest.options = `${options}-${status}`;
        }
      }
    }
    Taro.reportAnalytics(key, rest);
    // 微信新的数据分析系统---We分析
    if (process.env.PLATFORM_ENV === 'weapp') {
      if (wx && wx.reportEvent) {
        wx.reportEvent(key, rest);
      }
    }
    // 友盟上报
    Taro.uma && Taro.uma.trackEvent(key, rest);
    // 阿拉丁上报
    const app = Taro.getApp();
    if (!app.aldstat) return;
    const { title } = rest;
    if (!title) return;
    app.aldstat.sendEvent(title, { title, ...rest });
  } catch (_) {}
};

// 默认key，用于统一
export const reportAnalyticsUnifyKey = 'event_push_ad';
export const reportAnalyticsUnify = (report) => {
  reportAnalytics({
    key: reportAnalyticsUnifyKey,
    ...report,
  });
};

const subscribeStatisticsKey = 'subscribeStatistics';
export const handleSubscribeStatistics = (key = 'get', data) => {
  const baseData = Taro.kbGetGlobalDataOnce(subscribeStatisticsKey);
  if (key === 'get') {
    return baseData;
  } else {
    Taro.kbSetGlobalData(subscribeStatisticsKey, [...baseData, ...data]);
  }
};
export const reportSubscribe = ({ acceptedIds = [], action, position } = {}) => {
  if (!action) return;
  const ids = getIDsList(action);
  if (!ids.length) return;
  const stat_data = ids.map((id) => ({
    template_id: id,
    stat_type: Array.isArray(acceptedIds) && acceptedIds.includes(id) ? '2' : '1',
  }));
  request({
    url: '/v1/WeApp/templateSubscribeStat',
    toastLoading: false,
    data: {
      stat_data,
      event: statisticsMap[position].event,
      position,
    },
  });
};
