/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { Component } from '@tarojs/taro';
import KbPage from '@base/components/page';
import KbRetention from '@base/components/retention';

class Index extends Component {
  config = {
    navigationBarTitleText: '',
    navigationStyle: 'custom',
    transparentTitle: 'always',
    navigationBarBackgroundColor: '#fff',
  };
  constructor() {}

  render() {
    return (
      <KbPage
        navProps={{
          forceAlipayLeft: true,
          fixed: true,
          theme: 'ghost',
          exactHome: true,
        }}
      >
        <KbRetention keys='out.reward' params={this.$router.params} />
      </KbPage>
    );
  }
}

export default Index;
