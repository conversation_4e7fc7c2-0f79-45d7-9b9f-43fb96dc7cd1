/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb-extra-bars {
  position: relative;
  &__options {
    display: flex;
    align-items: center;
    height: 80px;
    padding: 0 $spacing-h-md 0 30px;
    &-dot {
      width: 10px;
      height: 10px;
      margin: 0 5px;
      background: #666;
      border-radius: 50%;
    }
  }
  &__popup {
    &-container {
      position: absolute;
      bottom: 70px;
      left: 20px;
      z-index: 5;
      box-sizing: border-box;
      width: 228px;
      background: #fff;
      border-radius: 16px;
      box-shadow: 0 0 8px rgba(0, 0, 0, 0.12);
      &::after {
        position: absolute;
        bottom: -16px;
        left: 23px;
        width: 0;
        height: 0;
        border-top: 16px solid #fff;
        border-right: 16px solid transparent;
        border-left: 16px solid transparent;
        filter: drop-shadow(0 0 8px rgba(0, 0, 0, 0.12));
        content: '';
      }
      &::before {
        position: absolute;
        bottom: -4px;
        left: 28px;
        z-index: 1;
        width: 25px;
        height: 12px;
        background: #fff;
        content: '';
      }
    }
    &-mask {
      position: fixed;
      top: 0;
      right: 0;
      bottom: 0px;
      left: 0;
      z-index: 4;
    }
    &-item {
      height: 80px;
      color: #333333;
      font-size: 30px;
      line-height: 80px;
      text-align: center;
    }
  }
}
