/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { Image, View } from '@tarojs/components';
import Taro, { Fragment } from '@tarojs/taro';
import classNames from 'classnames';
import { useNewCustomerCouponGuide } from './_utils';
import './guide.scss';

const BusinessCouponGuide = () => {
  const { isOpened, step, handleCancel, handleNext } = useNewCustomerCouponGuide({
    mode: '1',
    maxSteps: 3,
    key: 'alipay_activity_guide_newCustomer',
  });

  const maskCls = classNames('kb-businessCoupon-guide-mask', {
    'kb-businessCoupon-guide-mask__show': isOpened,
  });
  return (
    <Fragment>
      {isOpened ? (
        <View className='kb-businessCoupon-guide'>
          {step == 1 ? (
            <View className='kb-businessCoupon-guide-next01'>
              <Image
                className='img'
                mode='widthFix'
                src='https://cdn-img.kuaidihelp.com/wkd/miniApp/activity/alipay/new/guide01.png'
                onClick={handleNext.bind(null, 1)}
              />
              <View className='opt'>
                <View className='opt-btn opt-btn-cancel' onClick={handleCancel}>
                  跳过
                </View>
                <View className='opt-btn opt-btn-next' onClick={() => handleNext(1)}>
                  下一步(1/3)
                </View>
              </View>
            </View>
          ) : step == 2 ? (
            <View className='kb-businessCoupon-guide-next02'>
              <Image
                className='img'
                mode='widthFix'
                src='https://cdn-img.kuaidihelp.com/wkd/miniApp/activity/alipay/new/guide02.png'
                onClick={handleNext.bind(null, 2)}
              />
              <View className='opt'>
                <View className='opt-btn opt-btn-cancel' onClick={handleCancel}>
                  跳过
                </View>
                <View className='opt-btn opt-btn-next' onClick={() => handleNext(2)}>
                  下一步(2/3)
                </View>
              </View>
            </View>
          ) : step == 3 ? (
            <Image
              className='kb-businessCoupon-guide-next03'
              mode='widthFix'
              src='https://cdn-img.kuaidihelp.com/wkd/miniApp/activity/alipay/new/guide03.png'
              onClick={() => handleNext(3)}
            />
          ) : null}
        </View>
      ) : null}
      <View className={maskCls} />
    </Fragment>
  );
};

BusinessCouponGuide.options = {
  addGlobalClass: true,
};

export default BusinessCouponGuide;
