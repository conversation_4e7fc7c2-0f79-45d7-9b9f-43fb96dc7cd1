/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb-integralRecord {
  &-header {
    position: relative;
    box-sizing: border-box;
    width: 750px;
    height: 527px;
    background: 0 0
      url('https://cdn-img.kuaidihelp.com/wkd/miniApp/prize/integral-record/bg.png?v=1') no-repeat;
    background-size: 100% 100%;
  }
  &-total {
    position: absolute;
    right: $spacing-h-lg;
    bottom: 200px;
    left: $spacing-h-lg;
    color: $color-white;
    font-size: $font-size-base;
    .num {
      margin-top: $spacing-h-md;
      color: $color-white;
      font-weight: 800;
      font-size: 60px;
      line-height: 1;
    }
  }
  &-container {
    box-sizing: border-box;
    height: calc(100% + 150px);
    margin-top: -170px;
  }
  &-content {
    height: 100%;
    margin: 0 $spacing-h-md 0;
    background: $color-white;
    border-radius: $border-radius-base;
  }
  &-title {
    box-sizing: border-box;
    height: 108px;
    padding-top: 40px;
    color: $color-black-1;
    font-weight: 800;
    font-size: 32px;
    text-align: center;
  }
  &-list {
    box-sizing: border-box;
    height: calc(100% - 108px);
  }
  &-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 0 $spacing-h-md;
    padding: $spacing-h-md 0;
    color: $color-black-1;
    font-weight: bold;
    font-size: $font-size-lg;
    border-bottom: $border-lighter;
  }
}
