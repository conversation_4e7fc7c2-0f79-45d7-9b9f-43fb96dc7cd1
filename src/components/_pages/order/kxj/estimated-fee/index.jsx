/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import KbCardBar from '@/components/_pages/order/card-bar';
import KbCouponCardBar from '@/components/_pages/order/card-bar/coupon/index';
import apis from '@/utils/apis';
import request from '@base/utils/request';
import { debounce } from '@base/utils/utils';
import { Text, View } from '@tarojs/components';
import { Fragment, useEffect, useRef, useState } from '@tarojs/taro';
import isEmpty from 'lodash/isEmpty';
import numeral from 'numeral';
import { AtFloatLayout, AtIcon } from 'taro-ui';
import './index.scss';

const Index = (props) => {
  const {
    active,
    relationInfo,
    formData,
    extraInfo: { goods_weight, service, product_code, volume, brand: selectBrand },
    isOpenOnlinePay,
    total,
    data,
    onChange = () => {},
    onEquityChange = () => {},
    onCouponChange = () => {},
  } = props;
  const {
    type: relationInfoType,
    courier_id,
    account_phone,
    brand,
    platform,
    courier,
    customer,
    dak_id,
  } = relationInfo || {};
  const {
    send_province,
    send_city,
    send_district,
    send_address,
    send_name,
    send_mobile,
    receive_province,
    receive_city,
    receive_district,
    receive_address,
    receive_name,
    receive_mobile,
  } = formData || {};

  const couponCardBarRef = useRef();

  const [estimatedData, setEstimatedData] = useState({});
  const [feeList, setFeeList] = useState([]);
  const [estimatedFee, setEstimatedfee] = useState(0);
  const [loading, setLoading] = useState(false);
  const [isAddrReady, setIsAddrReady] = useState(false);
  const [isOpend, setIsOpend] = useState(false);
  const [cardInfo, setCardInfo] = useState({});
  const [coupon, setCoupon] = useState({});

  const isFullObject = (obj) => {
    const aKeys = ['width', 'length', 'height', 'volume'];
    for (let key in obj) {
      if (!obj[key] && !aKeys.includes(key)) {
        return false;
      }
    }
    return true;
  };

  useEffect(() => {
    getEstimatedFee();
  }, [active]);

  useEffect(() => {
    if (receive_province) {
      setIsAddrReady(true);
    }
  }, [receive_province]);

  useEffect(() => {
    if (!isEmpty(data) && data.clean) {
      reset();
      if (
        platform == 'yjkd_courier' &&
        couponCardBarRef &&
        couponCardBarRef.current.getYjDefaultCoupon
      ) {
        couponCardBarRef.current.getYjDefaultCoupon();
      }
    }
  }, [data]);

  useEffect(() => {
    countFee();
  }, [estimatedData, service, cardInfo, coupon]);

  const reset = () => {
    setEstimatedData({});
    triggerUpdate({
      estimatedFee: 0,
      feeList: [],
    });
  };

  const getEstimatedFee = debounce(
    () => {
      let reqData = {
        shipper_province: send_province,
        shipper_city: send_city,
        shipper_district: send_district,
        shipper_address: send_address,
        shipper_name: send_name,
        shipper_mobile: send_mobile,
        shipping_province: receive_province,
        shipping_city: receive_city,
        shipping_district: receive_district,
        shipping_address: receive_address,
        shipping_name: receive_name,
        shipping_mobile: receive_mobile,
        weight: goods_weight || 1,
      };
      if (relationInfoType == 'courier') {
        reqData.courier_id = courier_id;
        reqData.phone = account_phone;
        reqData.area = receive_province;
        reqData.courier_brand = selectBrand || brand;
      } else if (platform == 'yjkd_courier' && courier) {
        reqData.brand = 'yjkd';
        reqData.phone = courier.phone;
      } else if (relationInfoType == 'brand') {
        reqData.delivery_type = product_code;
        reqData.brand = brand == 'sf' || brand == 'sfky' ? 'sf' : brand;
        if (volume.checked) {
          reqData.width = volume.width;
          reqData.length = volume.length;
          reqData.height = volume.height;
          reqData.volume = volume.volume;
        }
      } else if (relationInfoType == 'dak') {
        reqData.dak_id = dak_id;
        reqData.area = receive_province;
      }
      if (total > 1) return;
      if (!isFullObject(reqData)) return;
      if (customer && customer.id) {
        reqData.customer_id = customer.id;
      }
      setLoading(true);
      request({
        url: apis['order.quotation'],
        data: reqData,
        toastLoading: false,
        onThen: ({ data, code }) => {
          setLoading(false);
          if (code == 0) {
            setEstimatedData(data);
          } else {
            reset();
          }
        },
      });
    },
    500,
    {
      trailing: true,
    },
  );

  const countFee = () => {
    const { price, f_fee, s_fee, range_one = {} } = estimatedData || {};
    let { cost_value } = service || {};
    //增值费用
    cost_value = cost_value * 1 || 0;
    //权益次卡抵扣费用
    let equityFee = relationInfoType == 'courier' && cardInfo.card_id ? range_one.f_weight * 1 : 0;
    equityFee = equityFee * 1 || 0;
    //优惠券费用
    let couponFee = platform == 'yjkd_courier' && coupon.cost > 0 ? coupon.cost * 1 : 0;
    couponFee = couponFee * 1 || 0;
    //计算后的总费用
    let estimatedFee = 0;
    if (price > 0) {
      estimatedFee = numeral(price * 1 + cost_value - equityFee - couponFee).format('0.00');
    }
    //费用列表
    let feeList = [
      {
        label: '首重费',
        fee: cardInfo && cardInfo.card_id ? cardInfo.desc : f_fee,
      },
      {
        label: '续重费',
        fee: s_fee,
        desc: !s_fee && '由快递员线下收取',
      },
    ];
    if (cost_value > 0) {
      feeList.push({
        label: '保价服务',
        fee: cost_value,
      });
    }
    if (coupon && coupon.id) {
      feeList.push({
        label: '优惠券',
        fee: coupon.cost,
        symbol: '-',
      });
    }
    triggerUpdate({
      estimatedFee,
      feeList,
    });
  };

  const triggerUpdate = ({ estimatedFee, feeList }) => {
    setFeeList(feeList);
    setEstimatedfee(estimatedFee);
    onChange({
      data: {
        estimatedFee,
      },
    });
  };

  const onSwitchFee = () => {
    setIsOpend(!isOpend);
  };

  const handleChange = (key, data) => {
    // console.log("key, data", key, data);
    switch (key) {
      case 'equity':
        setCardInfo(data);
        onEquityChange({ data });
        break;
      case 'coupon':
        setCoupon(data);
        onCouponChange({ data });
        break;
    }
  };

  const getDescText = () => {
    let text = '';
    if (relationInfoType == 'courier' && cardInfo.card_id) {
      text = cardInfo.desc;
    } else if (relationInfoType == 'courier' && !cardInfo.card_id && cardInfo.canUse) {
      text = '未使用权益次卡';
    } else if (!isAddrReady) {
      text = '填完寄件地址和重量可预估';
    } else if (estimatedFee > 0 && platform == 'yjkd_courier') {
      text = '重量超出快递员线下收取续重费';
    } else if (estimatedFee > 0 && !goods_weight) {
      text = '按1kg预估，填写重量后显示具体费用';
    }
    return text;
  };

  return (
    <Fragment>
      {total <= 1 && (
        <View className='at-row at-row__justify--between at-row__align--center kb-size__sm kb-color__grey'>
          <View className='at-col'>
            <View className='at-row at-row__align--center kb-margin-sm-b'>
              <View className='kb-spacing-md-r'>
                <Text className='kb-color__black'>预估运费：</Text>
                {loading ? (
                  <AtIcon
                    prefixClass='kb-icon'
                    value='loading'
                    className='kb-icon-size__base kb-color__grey'
                  />
                ) : (
                  <Text className='kb-color__red'>
                    {estimatedFee >= 0 ? `￥${estimatedFee}` : '--'}
                  </Text>
                )}
              </View>
              <View>
                {relationInfoType == 'courier' && (
                  <KbCardBar
                    data={relationInfo}
                    address={formData}
                    isOpenOnlinePay={isOpenOnlinePay}
                    onChange={(data) => {
                      handleChange('equity', data);
                    }}
                  />
                )}
                {platform == 'yjkd_courier' && (
                  <KbCouponCardBar
                    actionRef={couponCardBarRef}
                    phone={courier.phone}
                    onChange={(data) => {
                      handleChange('coupon', data);
                    }}
                  />
                )}
              </View>
            </View>
            <View className='kb-color__grey kb-size__sm'>{getDescText()}</View>
          </View>
          {estimatedFee >= 0 && (
            <View className='kb-spacing-xs' hoverClass='kb-hover-opacity' onClick={onSwitchFee}>
              明细
            </View>
          )}
        </View>
      )}
      <AtFloatLayout isOpened={isOpend} onClose={onSwitchFee} title='金额明细'>
        <View className='kb-fee-detail'>
          <View className='kb-fee-detail__content'>
            <View className='kb-fee-detail__list'>
              {feeList.map((item) => (
                <View className='kb-fee-detail__list--item' key={item.label}>
                  <View className='kb-color__grey'>{item.label}</View>
                  <View>
                    {item.fee ? (
                      <Text>
                        {item.symbol}￥{item.fee}
                      </Text>
                    ) : (
                      <Text className={`kb-color__${item.desc ? 'black' : 'grey'}`}>
                        {item.desc || '暂无'}
                      </Text>
                    )}
                  </View>
                </View>
              ))}
            </View>
            <View className='kb-fee-detail__tips'>
              <Text className='kb-color__orange'>注：</Text>
              <Text>预估快递费仅供参考,实际快递费以快递员确认为准</Text>
            </View>
            <View className='kb-fee-detail__total'>
              <View>合计</View>
              <View className='kb-color__red'>￥{estimatedFee}</View>
            </View>
          </View>
        </View>
      </AtFloatLayout>
    </Fragment>
  );
};

Index.options = {
  addGlobalClass: true,
};

Index.defaultProps = {
  extraInfo: {},
};

export default Index;
