/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { Image, View } from '@tarojs/components';
import Taro, { Fragment } from '@tarojs/taro';
import classNames from 'classnames';
import { useNewCustomerCouponGuide } from './_utils';
import './noUseGuide.scss';

const BusinessCouponNoUseGuide = () => {
  const { isOpened, step, handleCancel, handleNext } = useNewCustomerCouponGuide({
    mode: '2',
    maxSteps: 2,
    key: 'alipay_activity_guide_newCustomerUse',
  });

  const maskCls = classNames('kb-businessCoupon-guide-mask', {
    'kb-businessCoupon-guide-mask__show': isOpened,
  });

  return (
    <Fragment>
      {isOpened ? (
        <View className='kb-businessCoupon-guide'>
          {step == 1 ? (
            <View className='kb-businessCoupon-guide-next01'>
              <Image
                className='img'
                mode='widthFix'
                src='https://cdn-img.kuaidihelp.com/wkd/miniApp/activity/alipay/new/guide04.png?v=01'
                onClick={handleNext.bind(null, 1)}
              />
              <View className='opt'>
                <View className='opt-btn opt-btn-cancel' onClick={handleCancel}>
                  跳过
                </View>
                <View className='opt-btn opt-btn-next' onClick={() => handleNext(1)}>
                  下一步(1/2)
                </View>
              </View>
            </View>
          ) : step == 2 ? (
            <Image
              className='kb-businessCoupon-guide-next03'
              mode='widthFix'
              src='https://cdn-img.kuaidihelp.com/wkd/miniApp/activity/alipay/new/guide05.png?v=01'
              onClick={() => handleNext(2)}
            />
          ) : null}
        </View>
      ) : null}
      <View className={maskCls} />
    </Fragment>
  );
};

BusinessCouponNoUseGuide.options = {
  addGlobalClass: true,
};

export default BusinessCouponNoUseGuide;
