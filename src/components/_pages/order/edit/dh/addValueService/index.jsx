/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { Fragment } from '@tarojs/taro';
import { View, Text } from '@tarojs/components';
import { AtIcon } from 'taro-ui';
import KbCheckbox from '@base/components/checkbox';
import { useAddValueService } from './_utils';
import './index.scss';

const AddValueService = (props) => {
  const {
    brand,
    dynamicFloor,
    floor,
    formatVal,
    handleFloorChange,
    handleClickServiceLabel,
    handleClick,
  } = useAddValueService(props);

  return (
    <Fragment>
      {brand === 'htky' ? (
        dynamicFloor && dynamicFloor.isShow ? (
          <View className='kb-block'>
            <View className='kb-nav-item kb-service'>
              <View
                className='kb-nav-item-label'
                onClick={handleClickServiceLabel}
                hoverClass='kb-hover'
              >
                增值服务
                <AtIcon
                  prefixClass='kb-icon'
                  value='question'
                  className='kb-color__grey kb-icon-size__base kb-margin-sm-l'
                />
              </View>
              <View className='kb-nav-item-value' onClick={handleFloorChange} hoverClass='kb-hover'>
                <Text className='kb-margin-sm-r'>送货上门</Text>
                <KbCheckbox checked={floor == 1} onChange={handleFloorChange} />
              </View>
            </View>
          </View>
        ) : null
      ) : (
        <View className='kb-block' onClick={handleClick} hoverClass='kb-hover'>
          <View className='kb-nav-item kb-service'>
            <View className='kb-nav-item-label'>增值服务</View>
            <View className='kb-nav-item-value'>
              <View className='content'>
                {formatVal ? (
                  <Text>{formatVal}</Text>
                ) : (
                  <Text className='kb-color__grey'>请选择</Text>
                )}
              </View>
              <AtIcon
                prefixClass='kb-icon'
                value='arrow'
                className='kb-icon2 kb-color__grey kb-icon-size__base'
              />
            </View>
          </View>
        </View>
      )}
    </Fragment>
  );
};

AddValueService.options = {
  addGlobalClass: true,
};

export default AddValueService;
