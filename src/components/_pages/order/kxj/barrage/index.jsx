/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { useRef, useState } from '@tarojs/taro';
import { View } from '@tarojs/components';
import { useUpdate } from '@base/hooks/page';
import classNames from 'classnames';
import chunk from 'lodash/chunk';
import { getBarrageLists } from '../_utils';
import './index.scss';

const KbBarrage = (props) => {
  const { activity } = props || {};
  const [list, setList] = useState([]);
  const barrageRef = useRef({ timer: null, distance: 0, list: [] });
  const [distance, setDistance] = useState(500);
  const [hideTransition, setHideTransition] = useState(false);
  const step = 50;

  const start = (_distance, _list) => {
    const max = 255 * _list.length;
    let n = 0;
    if (barrageRef.current.timer) {
      clearInterval(barrageRef.current.timer);
    }
    barrageRef.current.distance = _distance;
    barrageRef.current.timer = setInterval(() => {
      // console.log('barrageRef.current.distance', barrageRef.current.distance)
      setDistance(barrageRef.current.distance);
      barrageRef.current.distance = barrageRef.current.distance - step;
      n++;
      if (n > 1) {
        setHideTransition(false);
      }
      if (max < Math.abs(barrageRef.current.distance)) {
        barrageRef.current.distance = _distance;
        setHideTransition(true);
        start(_distance, _list);
      }
    }, 1000);
  };

  useUpdate(() => {
    getBarrageLists().then((res) => {
      if (res && res.length > 0) {
        res = res.map((item) => {
          if (activity === 'merchant_return') {
            return item.replace('新客', '会员');
          }
          return item;
        });
        const _list = chunk(res, Math.ceil(res.length / 3));
        setList(_list);
        barrageRef.current.list = _list;
        start(400, barrageRef.current.list[0]);
      }
    });
  });

  return (
    <View className='barrage'>
      {list &&
        list.length > 0 &&
        list.map((listItem, index) => {
          const containerCls = classNames('barrage-container', {
            'barrage-container-transition': !hideTransition,
          });
          return (
            <View
              className={containerCls}
              style={{
                transform: `translateX(${distance}px)`,
                paddingLeft: `${index * 10}px`,
              }}
              key={listItem}
            >
              {listItem.map((item) => {
                return (
                  <View className='barrage-item' key={item}>
                    {item}
                  </View>
                );
              })}
            </View>
          );
        })}
    </View>
  );
};

KbBarrage.options = {
  addGlobalClass: true,
};

export default KbBarrage;
