/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import KbAddressEditAi from '@/components/_pages/order/address-edit/edit-ai';
import KbAddressEditManual from '@/components/_pages/order/address-edit/edit-manual';
import { checkAddress, supportAi } from '@/components/_pages/order/address-edit/utils';
import { noop } from '@base/utils/utils';
import { View } from '@tarojs/components';
import { Component } from '@tarojs/taro';
import isArray from 'lodash/isArray';
import isEmpty from 'lodash/isEmpty';
import './index.scss';

class Index extends Component {
  constructor() {
    this.state = {
      addressData: {},
    };
    // this.triggerFormDataChange = debounce(this.triggerFormDataChange);
  }

  componentDidMount() {
    const { data } = this.props;
    this.triggerFormDataChange(data);
  }

  triggerFormDataChange = (data) => {
    if (isEmpty(data)) return;
    const { addressData } = this.state;
    this.setState({
      addressData: { ...addressData, ...data },
    });
  };

  handleAddressChange(key, data) {
    // console.log("地址聚合组件", key, data);
    const { onChange } = this.props;
    switch (key) {
      case 'ai':
        // console.log("智能录入", data);
        data = isArray(data) ? data[0] : data;
        this.triggerFormDataChange(data);
        break;
      case 'manual':
        // console.log("手动录入", data);
        break;
    }
    onChange({ ...data });
  }

  render() {
    const { addressData } = this.state;
    const {
      data,
      allowUpload,
      locked,
      allowAi,
      org,
      source,
      action,
      allowSetDefault,
      onParseImg,
      hideBook,
      isPickupAddress = false,
      isEdit,
    } = this.props;
    const aiArray = supportAi.filter((key) => {
      return (source === 'list' || hideBook) && key == 'book' ? false : true;
    });

    return (
      <View className='kb-margin-lg'>
        {!locked && allowAi && (
          <KbAddressEditAi
            supportAi={aiArray}
            type={org}
            className='kb-background__white kb-margin-lg-tb kb-spacing-lg kb-border-radius-lg'
            onFormDataUpdate={this.handleAddressChange.bind(this, 'ai')}
            onParseImg={onParseImg}
            hasManualData={checkAddress(data) !== 'empty'}
            action={action}
            isEdit={isEdit}
          />
        )}
        <KbAddressEditManual
          ref={this.addressEditRef}
          className='kb-border-radius-lg'
          org={org}
          data={addressData}
          locked={locked}
          action={action}
          showSaveBtn={allowUpload}
          showSetDefaultBtn={allowSetDefault}
          isPickupAddress={isPickupAddress}
          onChange={this.handleAddressChange.bind(this, 'manual')}
        />
      </View>
    );
  }
}

Index.options = {
  addGlobalClass: true,
};

Index.defaultProps = {
  org: 'send', // 地址类型
  data: {}, //地址信息
  allowUpload: false,
  allowAi: true,
  hideBook: false,
  isPickupAddress: false,
  onChange: noop,
};

export default Index;
