/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { get } from '@/actions/brands';
import { requestPayment } from '@/utils/qy';
import KbPage from '@base/components/page';
import request from '@base/utils/request';
import { View } from '@tarojs/components';
import { connect } from '@tarojs/redux';
import Taro, { Component } from '@tarojs/taro';
import { AtButton, AtIcon } from 'taro-ui';
import './index.scss';

@connect(
  ({ global }) => ({
    loginData: global.loginData,
    brands: global.brands,
  }),
  { get },
)
class Index extends Component {
  config = {
    navigationBarTitleText: '支付',
  };
  constructor() {
    this.state = {
      disabledPay: false,
    };
  }

  componentDidMount() {
    this.props.get();
  }

  backHome = () => {
    Taro.navigator({
      url: 'query',
      target: 'tab',
    });
  };

  paySuccess = () => {
    if (process.env.PLATFORM_ENV === 'alipay') {
      Taro.kbModal({
        title: '支付成功',
        top: false,
        closable: false,
        rootClass: 'kb-pay__successModal',
        confirmText: '我知道了',
        onConfirm: () => {
          this.backHome();
        },
      });
    } else {
      Taro.kbModal({
        title: '支付成功',
        top: false,
        closable: false,
        rootClass: 'kb-pay__successModal',
        cancelText: '返回小程序首页',
        confirmText: '返回快递员App',
        confirmButtonProps: {
          openType: 'launchApp',
        },
        onCancel: (ev) => {
          console.log('onCancel.ev', ev);
          if (ev === 'button') {
            this.backHome();
          }
        },
      });
    }
  };

  handleNotPay = () => {
    this.backHome();
  };

  handlePay = () => {
    const { token, money, kb_id } = this.$router.params;
    if (!token) return;
    this.setState({ disabledPay: true });
    let url = '/g_order_core/v2/mina/Payment/wxPayTimely';
    const params = {};
    const isAli = process.env.PLATFORM_ENV === 'alipay';
    if (isAli) {
      url = '/g_order_core/v2/mina/Payment/aliPayMoney';
      params.money = money;
      params.kb_id = kb_id;
    }
    request({
      url,
      data: {
        token,
        ...params,
      },
      toastError: true,
      onThen: ({ code, data }) => {
        this.setState({ disabledPay: false });
        if (code == 0 && data) {
          const _data = isAli ? { ...data, isWallet: true } : data;
          requestPayment(_data)
            .then(() => {
              this.paySuccess();
            })
            .catch((error) => {
              Taro.kbToast({
                text: error,
              });
            });
        }
      },
    });
  };

  render() {
    const { courier_name, courier_phone, price, channel, money } = this.$router.params;
    const { disabledPay, ...rest } = this.state;
    const isAli = process.env.PLATFORM_ENV == 'alipay';

    return (
      <KbPage
        {...rest}
        renderFooter={
          <View className='kb-spacing-md'>
            <View className='at-row at-row__justify--around'>
              <View className='at-col kb-margin-sm-lr'>
                <AtButton
                  type='secondary'
                  className='kb-button__middle kb-pay__notPay'
                  onClick={this.handleNotPay}
                  circle
                >
                  暂不支付
                </AtButton>
              </View>
              <View className='at-col kb-margin-sm-lr'>
                <AtButton
                  type='primary'
                  className='kb-button__middle '
                  onClick={this.handlePay}
                  disabled={disabledPay}
                  circle
                >
                  确认支付
                </AtButton>
              </View>
            </View>
          </View>
        }
      >
        <View className='kb-order-wrap kb-spacing-lg-tb'>
          <View className='at-row at-row__align--center kb-color__white at-row__justify--center'>
            <AtIcon
              prefixClass='kb-icon'
              value='unpaid'
              className='kb-color__white kb-icon-size__lg'
            />
            待支付
          </View>
        </View>
        <View className='kb-spacing-md kb-order-content'>
          <View className='kb-box'>
            <View className='kb-pay__title'>费用详情</View>
            {!isAli && (
              <View className='at-row at-row__align--center at-row__justify--between kb-pay__item'>
                <View className='kb-color__greyer'>收款人:</View>
                <View className='kb-pay__item-content'>
                  {decodeURIComponent(courier_name)}
                  {courier_phone}
                </View>
              </View>
            )}
            <View className='at-row at-row__align--center at-row__justify--between kb-pay__item'>
              <View className='kb-color__greyer'>{isAli ? '充值费用' : '预付费用'}:</View>
              <View className='kb-pay__item-content'>{(isAli ? money : price) || '--'}元</View>
            </View>
            <View className='at-row at-row__align--center at-row__justify--between kb-pay__item'>
              <View className='kb-color__greyer'>使用渠道:</View>
              <View className='kb-pay__item-content'>
                {isAli ? '快递员专用金钱包充值' : decodeURIComponent(channel) || '--'}
              </View>
            </View>
          </View>
        </View>
      </KbPage>
    );
  }
}

export default Index;
