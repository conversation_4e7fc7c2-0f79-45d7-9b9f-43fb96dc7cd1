import { View, Image, Text } from '@tarojs/components';
import { useMemo } from '@tarojs/taro';
import dayjs from 'dayjs';
import KbSubscribe from '@base/components/subscribe';

import './index.scss';

const WelfareSign = (props) => {
  const { sign_day = [], signDetails, signBtn, day } = props;

  const list = useMemo(() => {
    const { consecutive_days, cret } = signDetails || {};
    const format = sign_day.map((item, index) => {
      if (consecutive_days && cret) {
        const days = index + 1;
        const isTodaySign = dayjs().isSame(cret, 'day');
        const isToday = isTodaySign ? consecutive_days == days : consecutive_days == index;
        const isTor = isTodaySign ? consecutive_days == days - 1 : consecutive_days == index - 1;
        return {
          ...item,
          sign_type: index < consecutive_days % 7,
          daysText: isToday ? '今天' : isTor ? '明天' : item.day,
        };
      }
      return {
        ...item,
        daysText: index == 0 ? '今天' : index == 1 ? '明天' : item.day,
      };
    });
    return format;
  }, [sign_day, signDetails]);

  return (
    <View className='kb-sign'>
      <View className='kb-sign__package'>
        {list.map((item) => (
          <View className='kb-sign__package__item' key={item.id}>
            <View
              className={`kb-sign__package__item__imageContainer ${
                !item.sign_type && item.daysText == '今天' ? 'package_rotate' : ''
              } `}
            >
              {item.id == day ? (
                <KbSubscribe action='continuity' onSubscribe={signBtn} className='wkd_continuity'>
                  <Image
                    src={`//cdn-img.kuaidihelp.com/wkd/welfare/${
                      item.sign_type ? (item.daysText == '今天' ? 'today' : 'sign') : 'unsign'
                    }.png`}
                    className={`kb-sign__package__item__image${
                      item.sign_type ? (item.daysText == '今天' ? '__today' : '__sign') : ''
                    } `}
                  />
                </KbSubscribe>
              ) : (
                <Image
                  src={`//cdn-img.kuaidihelp.com/wkd/welfare/${
                    item.sign_type ? (item.daysText == '今天' ? 'today' : 'sign') : 'unsign'
                  }.png`}
                  className={`kb-sign__package__item__image${
                    item.sign_type ? (item.daysText == '今天' ? '__today' : '__sign') : ''
                  }`}
                />
              )}
            </View>
            <Text className='kb-sign__package__item__desc'>{item.daysText}</Text>
            {item.urlType == '1' && <Text className='kb-sign__package__item__badge'>惊喜红包</Text>}
            {item.urlType == '2' && <Text className='kb-sign__package__item__badge'>超大红包</Text>}
          </View>
        ))}
      </View>
    </View>
  );
};

WelfareSign.addGlobalClass = true;

export default WelfareSign;
