import isFunction from 'lodash/isFunction';
import { getStorageSync, now, removeStorage, setStorage } from './utils';
import dayjs from 'dayjs';

const dayFormatter = 'YYYY-MM-DD';
/**
 *
 * @description 用于实现访问限制，例如，当日访问多少次，或某时间段内可访问若干次，默认按照当日访若干次
 */
export class StorageVisits {
  constructor(opts) {
    const { key: storageKey, filter, max = 1, type = '0', time = 0 } = opts || {};
    this.storageKey = storageKey; // 缓存key
    this.filter = filter; // 自定义拦截过滤
    this.max = max; // 某时间段内最大多少次；默认为每日最大为1
    this.type = type; // 0 按照天，当天max次；1 按照time配置的时长计算
    this.time = time; // type 为1时有效
  }
  _getStorage() {
    const { storageKey } = this;
    if (!storageKey) return null;
    const storageData = getStorageSync(this.storageKey);
    return storageData;
  }
  _setStorage(data) {
    const { storageKey } = this;
    if (!storageKey) return null;
    setStorage({
      key: storageKey,
      data,
    });
  }
  // 检查是否可用
  check() {
    const storageData = this._getStorage();
    if (!storageData) return true;
    if (isFunction(this.filter)) {
      return this.filter(storageData);
    }
    const { count = 0 } = (storageData && storageData.data) || {};
    // 没有超过最大次数
    if (count < this.max) return true;
    // 是否在时间段内
    const { ts } = storageData;
    const isDay = this.type === '0';
    let canVisit = false;
    if (isDay) {
      const today = dayjs().format(dayFormatter);
      // 已经非当天
      canVisit = today !== dayjs(ts).format(dayFormatter);
    } else {
      const timeSpace = now() - ts;
      // 时间间隔超过设置时长
      canVisit = timeSpace > this.time;
    }
    if (canVisit) {
      // 清空缓存
      removeStorage({ key: this.storageKey });
    }
    return canVisit;
  }
  // 记录标记，用于下次检查
  record() {
    const storageData = this._getStorage();
    const { count = 0 } = (storageData && storageData.data) || {};
    this._setStorage({
      count: 1 + count,
    });
  }
}
