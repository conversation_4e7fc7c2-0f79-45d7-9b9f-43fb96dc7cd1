/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import KbInputNumber from '@base/components/input-number';
import KbDHEstimatedFee from '@/components/_pages/order/estimated-fee/dh';
import KbPage from '@base/components/page';
import { View } from '@tarojs/components';
import { connect } from '@tarojs/redux';
import Taro, { Component } from '@tarojs/taro';
import { getCurQuotationItem } from '@/components/_pages/order/_utils/order.edit';
import { AtButton } from 'taro-ui';
import {
  back_sign_bill_options,
  createDhServiceForm,
  pickup_way_options,
  package_service_options,
} from '@/components/_pages/order/edit/dh/service/_utils';
import SelectBox from '@/components/_pages/order/edit/dh/service/select-box';
import { requestYjQuotation } from '@/components/_pages/order/edit/dh/brand-info/_utils';
import { transferWkdAddress } from '@/components/_pages/order/_utils';
import { debounce } from '@base/utils/utils';
import Form from '@base/utils/form';
import './index.scss';

@connect(({ global }) => ({ loginData: global.loginData }))
class Index extends Component {
  static options = {
    addGlobalClass: true,
  };
  static config = {
    navigationBarTitleText: '增值服务',
  };
  constructor(props) {
    super(props);
    this.state = {};
    this.requestYjQuotationDebounce = debounce(this.requestYjQuotationDebounce, 500, {
      leading: false,
      trailing: true,
    });
  }

  // 数据传入
  onPostMessage = (key, e) => {
    const { params } = e;
    const { relationInfo, address, extraInfo, weight, volume } = params || {};
    const { service = {} } = extraInfo || {};
    switch (key) {
      case 'routerParamsChange':
        this.createForm().then(() => {
          this.setState({
            relationInfo,
            address,
            extraInfo,
            weight,
            volume,
          });
          const _service = { ...service };
          if (!_service.floor) {
            _service.floor = 1;
          }
          this.formIns.update(_service);
        });
        break;
    }
  };

  // 表单输入变化
  onChange_form = () => {};
  onSubmit_form = () => {};
  createForm = () => {
    return new Promise((resolve) => {
      const form = createDhServiceForm();
      this.formIns = new Form(
        {
          form,
          enableEmpty: false,
          onSubmit: (formData) => {
            console.log('onSubmit==>formData', formData);
            if (!formData.pickup_way) {
              formData.floor = '';
            }
            Taro.navigator({
              post: {
                type: 'dhService',
                data: formData,
              },
            });
          },
          onUpdate: () => {
            this.requestYjQuotationDebounce();
          },
          onReady: () => {
            resolve();
          },
        },
        this,
      );
    });
  };

  getFormData = () => {
    const { form } = this.state;
    const { data: formData = {} } = form || {};
    return {
      formData,
    };
  };

  handleSelect = (key, item) => {
    const { volume, relationInfo } = this.state;
    const { brand } = relationInfo || {};
    if (
      key === 'package_service' &&
      brand === 'ky' &&
      (!volume || !volume.volume || !volume.checked)
    ) {
      Taro.kbToast({
        text: '请先填写物品信息-补充体积；',
      });
      return;
    }
    const { formData } = this.getFormData();
    const formVal = formData[key] || '';
    if (!formVal || formVal != item.value) {
      this.formIns.update({
        [key]: item.value,
      });
    } else if (formVal == item.value) {
      this.formIns.update({
        [key]: '',
      });
    }
  };

  requestYjQuotationDebounce = () => {
    const { formData = {} } = this.getFormData();
    const { address, extraInfo } = this.state;
    const addrData = transferWkdAddress(address);
    const { goods_weight, volume, service } = extraInfo || {};
    const { back_sign_bill, package_service, pickup_way, floor } =
      { ...service, ...formData } || {};
    requestYjQuotation({
      addrData,
      weight: goods_weight,
      volume,
      back_sign_bill,
      package_service,
      pickup_way,
      floor,
    }).then(({ data }) => {
      this.setState({
        quotation: data.quotation || [],
      });
    });
  };

  render() {
    const { relationInfo, address, extraInfo, quotation = [], ...rest } = this.state;
    const { formData } = this.getFormData();
    const { back_sign_bill, package_service, pickup_way, floor } = formData || {};
    const { brand = '' } = relationInfo || {};
    const quotationData = getCurQuotationItem(brand, quotation) || '';
    return (
      <KbPage
        {...rest}
        renderFooter={
          <View className='kb-box at-row at-row__align--center'>
            <View className='at-col kb-spacing-md-l'>
              <KbDHEstimatedFee
                relationInfo={relationInfo}
                address={address}
                extraInfo={extraInfo}
                quotationData={quotationData}
              />
            </View>
            <View className='kb-spacing-md'>
              <AtButton
                className='kb-addValueService-submit'
                type='primary'
                circle
                onClick={this.onSubmit_form.bind(this)}
              >
                确认
              </AtButton>
            </View>
          </View>
        }
      >
        <View className='kb-addValueService-container'>
          {/* 回单费 */}
          <SelectBox
            brand={brand}
            type='back_sign_bill'
            title='回单费'
            extra='默认1张回单，如需更多回单咨询快递员'
            value={back_sign_bill}
            options={back_sign_bill_options({ brand })}
            onSelect={this.handleSelect.bind(this, 'back_sign_bill')}
          />
          {/* 包装服务 */}
          {brand === 'bt' || brand === 'ky' ? (
            <SelectBox
              brand={brand}
              type='package_service'
              title='包装服务'
              description='选择包装服务后，我司将重新称重并收取一定的包装费'
              value={package_service}
              options={package_service_options({ brand })}
              onSelect={this.handleSelect.bind(this, 'package_service')}
            />
          ) : null}
          {/* 上楼费 */}
          <SelectBox
            className={`pickup_way-${brand}`}
            brand={brand}
            type='pickup_way'
            title={brand === 'ky' ? '爬楼费' : '上楼费'}
            value={pickup_way}
            options={pickup_way_options({ brand })}
            onSelect={this.handleSelect.bind(this, 'pickup_way')}
          />
          <View className='kb-nav-item kb-addValueService-floorNum'>
            <View className='kb-nav-item-label'>选择楼层</View>
            <View className='kb-nav-item-value'>
              <KbInputNumber
                className='kb-input-center'
                width={120}
                step={1}
                max={999}
                min={1}
                placeholder='层数'
                alwaysEmbed
                value={floor}
                onChange={this.onChange_form.bind(this, 'floor')}
              />
            </View>
          </View>
        </View>
      </KbPage>
    );
  }
}

export default Index;
