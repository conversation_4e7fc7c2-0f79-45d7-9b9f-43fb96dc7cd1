import { getStorage, now, setStorage } from '@base/utils/utils';

/**
 *
 * @description 广告检查是否展示缓存 - 可逐渐统一使用该方案
 * @param {*} data
 */
const adCSStorageKey = 'ad-common';
export class AdCheckShowStorage {
  constructor() {}

  _formatStorageKey(p) {
    return `p-${p}`;
  }
  async set(data) {
    const { position, ...restData } = data || {};
    const storageData = await this.get();
    const { count = 0, ...restStorageData } = storageData[`${position}`] || {};
    await setStorage({
      key: adCSStorageKey,
      data: {
        ...storageData,
        [`${position}`]: {
          ...restStorageData,
          ...restData,
          ts: now(),
          count: 1 + count,
        },
      },
    });
  }

  async get(position) {
    const { data: res } = await getStorage({
      key: adCSStorageKey,
    });
    const { data: storageData } = res || {};
    return {
      ...(position && storageData ? storageData[`${position}`] : storageData),
    };
  }
}
