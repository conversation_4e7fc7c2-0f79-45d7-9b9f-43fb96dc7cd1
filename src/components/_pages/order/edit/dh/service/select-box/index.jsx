/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro from '@tarojs/taro';
import { View, Text, Image } from '@tarojs/components';
import { AtIcon } from 'taro-ui';
import classNames from 'classnames';
import './index.scss';

const SelectBox = (props) => {
  const { className, brand, type, title, description, extra, value, options, onSelect } = props;

  const handleClickLabel = () => {
    Taro.navigator({
      url: 'order/edit/service/dh/desc',
      options: {
        brand,
        type: type,
      },
    });
  };

  const handleSelect = (item) => {
    if (onSelect) {
      onSelect(item);
    }
  };

  return (
    <View className={classNames('kb-selectBox-item', className)}>
      <View className='title'>
        <View className='title-left'>
          <View className='title-text' onClick={handleClickLabel}>
            <Text>{title}</Text>
            <AtIcon
              prefixClass='kb-icon'
              value='question'
              className='icon kb-icon2'
              customStyle={{
                fontSize: '28rpx',
                color: '#c9c9c9',
                paddingLeft: '10rpx',
              }}
            />
          </View>
          <View className='title-description'>{description}</View>
        </View>
        <View className='title-extra'>{extra}</View>
      </View>
      <View className='list'>
        {options.map((item) => {
          const itemCls = classNames('list-item', {
            'list-item--active': value == item.value,
          });
          const label = item.label.split('（');
          return (
            <View key={item.value} className={itemCls} onClick={() => handleSelect(item)}>
              {item.img && <Image className='img' mode='widthFix' src={item.img} />}
              {label.map((i, ii) => {
                return <View key={i}>{ii > 0 ? '（' + i : i}</View>;
              })}
            </View>
          );
        })}
      </View>
    </View>
  );
};

SelectBox.options = {
  addGlobalClass: true,
};

export default SelectBox;
