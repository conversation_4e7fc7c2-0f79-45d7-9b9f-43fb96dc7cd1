/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { View } from '@tarojs/components';
import { useState } from '@tarojs/taro';
import './index.scss';

const KbExtraBars = (props) => {
  const { options = [], onClick = () => {} } = props;

  const [isOpened, setIsOpened] = useState(false);

  const handleClick = (...arg) => {
    setIsOpened(false);
    onClick(...arg);
  };

  const handleOpen = () => {
    setIsOpened(true);
  };

  const handleMask = () => {
    setIsOpened(false);
  };

  return (
    <View className='kb-extra-bars'>
      <View className='kb-extra-bars__options' onClick={handleOpen} hoverClass='kb-hover-opacity'>
        <View className='kb-extra-bars__options-dot' />
        <View className='kb-extra-bars__options-dot' />
        <View className='kb-extra-bars__options-dot' />
      </View>
      {isOpened && (
        <View className='kb-extra-bars__popup'>
          <View className='kb-extra-bars__popup-mask' onClick={handleMask} />
          <View className='kb-extra-bars__popup-container'>
            <View>
              {options &&
                options.length > 0 &&
                options.map((item) => {
                  return (
                    <View
                      className='kb-extra-bars__popup-item'
                      key={item.key}
                      onClick={() => handleClick(item.key)}
                      hoverClass='kb-hover-opacity'
                    >
                      {item.label}
                    </View>
                  );
                })}
            </View>
          </View>
        </View>
      )}
    </View>
  );
};

KbExtraBars.options = {
  addGlobalClass: true,
};

export default KbExtraBars;
