/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb-guideReceiveCouponModal {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  &-tips {
    position: relative;
    width: 440px;
    height: 57px;
    margin-bottom: 40px;
    color: #ff4c4c;
    font-size: 26px;
    line-height: 57px;
    text-align: center;
    background: #fff4f4;
    border: 1px solid #ff4c4c;
    border-radius: 50px;
    &::after {
      position: absolute;
      bottom: -12px;
      left: 50%;
      width: 20px;
      height: 20px;
      background: red;
      background: #fff4f4;
      border: 1px solid #ff4c4c;
      border-top-color: #fff4f4;
      border-left-color: #fff4f4;
      transform: translateX(-50%) rotate(45deg);
      content: '';
    }
  }
  &-coupon {
    position: relative;
    box-sizing: border-box;
    width: 440px;
    height: 340px;
    &--img {
      width: 100%;
      height: 100%;
    }
  }
  &-footer {
    margin-top: 20px;
    .confirm {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 400px;
      height: 66px;
      margin: 0 auto;
      color: #ffffff;
      font-weight: 500;
      font-size: 30px;
      background: $color-brand;
      border-radius: 33px;
    }
    .cancel {
      margin-top: 30px;
      color: #999999;
      font-weight: 500;
      font-size: 26px;
      text-align: center;
    }
  }
}
