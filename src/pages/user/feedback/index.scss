/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb-feedback-scrollWrap {
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  padding: $spacing-h-md;
  .kb-card {
    margin-bottom: $spacing-h-md;
    padding: $spacing-h-md $spacing-h-xl;
    background-color: $color-white;
    border-radius: $border-radius-base;
    .at-button {
      color: $color-grey-1;
    }
    .at-button--primary {
      color: #099fff;
      background: rgba(66, 159, 248, 0.1);
    }
    &__top {
      display: flex;
      align-items: center;
      color: $color-black-1;
      .kb-icon {
        color: $color-grey-2;
      }
      .kb-required {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 16px;
        height: 16px;
        margin-right: $spacing-h-sm;
        color: $color-white;
        background-color: $color-brand;
        border-radius: $border-radius-circle;
        &::after {
          position: absolute;
          top: 50%;
          left: 50%;
          width: 10px;
          height: 10px;
          background-color: $color-white;
          border-radius: $border-radius-circle;
          transform: translate(-50%, -50%);
          content: '';
        }
      }
    }
    &__bottom {
      display: flex;
      align-items: center;
      margin-top: $spacing-h-md;
      .at-button {
        margin-right: $spacing-h-md;
        margin-left: 0px;
      }
    }
    .kb-bars {
      flex-wrap: wrap;
      margin-top: 0;
      .at-button {
        margin-top: $spacing-h-md;
      }
    }
    .kb-flex-1 {
      flex: 1;
    }
  }
  .kb-color__grey-placeholder {
    color: #c5c5c5;
  }
  .kb-textarea,
  .at-input {
    width: calc(100vw - 2 * #{$spacing-h-md} - 2 * #{$spacing-h-xl});
    margin: 0;
    padding: 5px 0;
  }
  .kb-textarea {
    padding: $spacing-h-md;
  }
  .at-input {
    width: calc(100vw - 2 * #{$spacing-h-md} - 2 * #{$spacing-h-xl} - 30px);
    margin-left: $spacing-h-md;
    &::after {
      display: none;
    }
  }
  .at-textarea__textarea {
    background-color: transparent;
  }
}

.kb-feedback-bottom {
  padding: $spacing-h-md;
  background-color: $color-white;
}
.kb-text__sm {
  color: $color-grey-2;
  font-size: $font-size-sm;
}
.at-textarea {
  background-color: #f5f5f5;
  .at-textarea__counter {
    color: $color-grey-2;
  }
}
.picker-bar__default {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 120px;
  height: 120px;
  color: $color-grey-2;
  line-height: 1.5;
  background-color: $color-white !important;
  border: $width-base solid #eaeaea;
  border-radius: $border-radius-md;

  &--text {
    font-size: $font-size-xs;
  }
  .kb-rotate {
    margin-bottom: $spacing-h-sm;
    font-size: $font-size-sm !important;
    transform: rotate(45deg);
  }
}

.kb-qrCode {
  padding: 60px 55px !important;
  text-align: center;
  &__wrap {
    height: 800px;
    overflow: hidden;
    border: $width-base solid $color-brand;
    border-radius: 20px;
  }
  &__title {
    padding: 30px 0;
    font-weight: bold;
    font-size: 36px;
  }
  &__content {
    flex: 1;
    padding: 80px 120px;
    padding-bottom: 60px;
    color: $color-white;
    background-color: $color-brand;
  }
  &__img {
    width: 360px;
    height: 360px;
    margin-bottom: 30px;
  }
  &__text {
    margin-bottom: 40px;
  }
  .kb-underline {
    text-decoration: underline;
  }
}
