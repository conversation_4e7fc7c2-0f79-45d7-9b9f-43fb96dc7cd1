$start-icon-size: 40px;

.kb-guide-favorites {
  position: fixed;
  top: $spacing-v-md;
  right: $spacing-h-md;
  z-index: 9999;
  padding: $spacing-v-md $spacing-h-md;
  color: $color-white;
  font-size: $font-size-base;
  background-color: rgba($color: #000000, $alpha: 0.8);
  border-radius: $border-radius-xl;

  .kb-icon {
    width: $start-icon-size;
    height: $start-icon-size;
    margin: 0 $spacing-h-xs;
    color: $color-black-1;
    font-size: 26px;
    line-height: $start-icon-size;
    text-align: center;
    background-color: $color-white;
    border-radius: $border-radius-arc;
  }

  &::before {
    position: absolute;
    top: -20px;
    left: 50%;
    border: 15px solid rgba($color: #000000, $alpha: 0.8);
    border-top-width: 0;
    border-right-color: transparent;
    border-bottom-width: 20px;
    border-left-color: transparent;
    transform: translateX(-50%);
    content: '';
  }
}
