/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb-send {
  padding: $spacing-v-md $spacing-h-md;

  &__item {
    margin-bottom: $spacing-v-md;

    &:last-child {
      margin-bottom: 0;
    }
  }

  &__fee {
    box-sizing: border-box;
    background-color: $color-white;
    padding-left: $spacing-h-md;
    align-self: stretch;
    display: flex;
    flex-direction: column;
    justify-content: center;

    &--row {
      background-color: $color-white;
      @include bars-top();
    }

    &--detail {
      position: relative;
      padding-right: 20px;
      width: 65px;
      &::after {
        content: "\e620";
        font-family: "kb-icon";
        display: block;
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%) rotate(-90deg);
        color: $color-grey-2;
      }
    }
  }
}

.kb-login__auth--box {
  background-color: $color-white;
  padding: $spacing-v-md $spacing-h-md;
}

.kb-back {
  position: fixed;
  width: 80px;
  height: 80px;
  line-height: 80px;
  text-align: center;
  font-size: $font-size-sm;
  bottom: 440px;
  right: $spacing-h-md;
  border-radius: $border-radius-arc;
  color: $color-brand;
  border: $width-base solid $color-brand;
}

.kb-align-bottom {
  vertical-align: text-bottom !important;
}
.kb-card-wrap {
  background-color: $color-white;
  border-radius: $border-lightest;
  overflow: hidden;
}

.kb-scan-coupon {
  position: relative;
  color: rgb(241, 16, 46);
  width: 544px;
  height: 604px;
  margin: 0 auto;
  background: url(https://cdn-img.kuaidihelp.com/miniapp/miniapp_yz/scan-coupon.png?v=2)
    no-repeat center top / 100% auto;
  &--fee {
    &::before {
      content: "¥";
      font-size: $font-size-base;
    }
    position: absolute;
    top: 240px;
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
    font-size: $font-size-xxl * 1.5;
  }
  &--btn {
    position: absolute;
    white-space: nowrap;
    padding: 20px 100px;
    border-radius: $border-radius-arc;
    bottom: 60px;
    left: 50%;
    transform: translateX(-50%);
    display: inline-block;
    border: none;
    z-index: 999;
    letter-spacing: 6px;
    background-image: linear-gradient(
      to right,
      rgb(255, 245, 198),
      rgb(255, 223, 107)
    );
  }
}
