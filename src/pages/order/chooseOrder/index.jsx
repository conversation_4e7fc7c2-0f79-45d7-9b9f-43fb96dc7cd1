/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { Component, Fragment } from '@tarojs/taro';
import KbPage from '~base/components/page';
import KbListSearch from '~/components/_pages/order/list-search';
import KbList from '~/components/_pages/order/list';
import { getShareAppMessage } from '~/utils/share';
import { connect } from '@tarojs/redux';
import { get } from '~/actions/brands';
import classNames from 'classnames';
import './index.scss';

@connect(
  ({ global }) => ({
    brands: global.brands,
  }),
  {
    dispatchGet: get,
  },
)
class Index extends Component {
  config = {
    navigationBarTitleText: '选择运单号',
  };
  constructor() {
    this.state = {
      searchData: {},
      active: true,
      selected: null,
      list: [],
    };
  }

  componentDidMount() {
    this.props.dispatchGet();
  }

  // 搜索信息变更
  handleSearch = (val) => {
    const { searchData } = this.state;
    this.setState({
      searchData: {
        ...searchData,
        [process.env.MODE_ENV === 'wkd' ? 'search' : 'search_value']: val,
      },
    });
  };

  // 获取列表
  onGetted = (list) => {
    this.hasList = list.length > 0;
    this.setState({
      list,
    });
  };

  onShareAppMessage = getShareAppMessage;

  updateSearchData = (val, cb) => {
    const { searchData } = val;
    this.setState(
      {
        searchData: {
          ...searchData,
          ...val,
        },
      },
      cb,
    );
  };

  onReady = (ins) => {
    this.listIns = ins;
  };

  onClickItem = (e, item) => {
    this.setState({
      selected: [item],
    });
    setTimeout(() => {
      Taro.navigator({
        post: {
          type: 'chooseOrder',
          data: {
            order_id: item.order_id,
          },
        },
      });
    }, 300);
  };

  render() {
    const { active, searchData, customer_id, selected, ...rest } = this.state;

    return (
      <KbPage
        className={classNames({
          'kb-page__footer_white': !!selected,
        })}
        {...rest}
        renderHeader={
          <Fragment>
            <KbListSearch onSearch={this.handleSearch} />
          </Fragment>
        }
      >
        <KbList
          tabKey='send'
          type='chooseOrder'
          active={active}
          searchData={searchData}
          onGetted={this.onGetted}
          onSwitchTab={this.handleSwitchTab}
          updateSearchData={this.updateSearchData}
          onClickItem={this.onClickItem}
          selected={selected}
          onReady={this.onReady}
        />
      </KbPage>
    );
  }
}

export default Index;
