/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { Image, View } from '@tarojs/components';
import Taro, { useMemo } from '@tarojs/taro';
import { runSign } from '~/services/out-reward/sign';
import { debounce } from '@base/utils/utils';
import { navigateToMiniProgramScheme } from '~base/utils/navigator-utils';
import classNames from 'classnames';
import './index.scss';

const KbSignTask = (props) => {
  const { signDetail = {}, updateSignDetails = () => {} } = props;
  const { list = [] } = signDetail || {};

  const formatList = useMemo(() => {
    if (list && list.length > 0) {
      // 最多显示4个，且包含今天
      const MAX = 4;
      const index = list.findIndex((item) => item.isToday);
      const n = index + 1;
      const len = list.length;
      if (len - n >= MAX - 1) {
        return list.slice(index, index + MAX);
      } else {
        return list.slice(len - MAX);
      }
    }
    return list;
  }, [list]);

  const handleJumpCoupon = (item) => {
    if (item.coupon && item.isSign && item.isToday) {
      navigateToMiniProgramScheme({
        scheme: item.coupon,
      });
    }
  };

  // 签到
  const handleRewardSign = debounce((item) => {
    if (item.isSign || !item.isToday) return; // 已签到
    runSign().then((res) => {
      if (`${res.code}` === '0') {
        handleJumpCoupon(item);
        updateSignDetails();
      }
    });
  });

  const todaySignDetail = useMemo(() => {
    return list && list.length > 0 ? list.find((item) => item.isToday) || {} : {};
  }, [list]);

  const isTodaySign = !!(todaySignDetail && todaySignDetail.isSign);

  const icon_gou = 'https://cdn-img.kuaidihelp.com/wkd/miniApp/prize/gou.png?v=1';
  const icon_gou_active = 'https://cdn-img.kuaidihelp.com/wkd/miniApp/prize/gou_active.png?v=1';
  const icon_coupon = 'https://cdn-img.kuaidihelp.com/wkd/miniApp/prize/coupon.png?v=1';
  const signCls = classNames('kb-signTask-sign', {
    'kb-signTask-sign--active': isTodaySign,
  });

  return (
    <View className='kb-signTask'>
      <View className='kb-signTask-list'>
        {formatList.map((item) => {
          const itemCls = classNames('kb-signTask-item', {
            'kb-signTask-item--active': item.isSign,
          });
          return (
            <View className={itemCls} key={item.key}>
              <View className='kb-signTask-item__icon' onClick={() => handleJumpCoupon(item)}>
                <Image
                  className={!!item.coupon ? 'img_coupon' : 'img'}
                  src={!!item.coupon ? icon_coupon : item.isSign ? icon_gou_active : icon_gou}
                  mode='widthFix'
                />
              </View>
              {!item.coupon ? <View className='kb-signTask-item__text'>第{item.day}天</View> : null}
            </View>
          );
        })}
      </View>
      <View
        className={signCls}
        onClick={() => handleRewardSign(todaySignDetail)}
        hoverClass={isTodaySign ? 'none' : 'kb-hover-opacity'}
      >
        {isTodaySign ? '今日已签到' : '点击签到'}
      </View>
    </View>
  );
};

KbSignTask.options = {
  addGlobalClass: true,
};

export default KbSignTask;
