/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

// 激励视频/插屏广告
// idsMap
export const adIdsMap = {
  0: {
    id: 'adunit-e8c9b02e1e3ccfc4',
    label: '我的更多推荐',
    type: 'rewardedVideo',
  },
  1: {
    id: 'adunit-0b6279a459673f34',
    label: '下单结果页',
    type: 'interstitial',
  },
  welfare: {
    id: 'adunit-dc28a833c9cab0f2',
    label: '福利中心',
    type: 'interstitial',
  },
  lotteryList: {
    id: 'adunit-564efa7d10aa40a7',
    label: '抽奖列表',
    type: 'interstitial',
  },
  lotteryDetails: {
    id: 'adunit-6ccd7d30cde9dca8',
    label: '抽奖详情',
    type: 'interstitial',
  },
  'query.detail': {
    id: 'adunit-8b89abbd1fdec509',
    label: '查件详情',
    type: 'interstitial',
  },
  lotteryTask: {
    id: 'adunit-26ef0e1c1b8c2498',
    label: '抽奖详情任务',
    type: 'rewardedVideo',
  },
  'out.reward.full': {
    id: '50_2024080825000181009',
    label: '天天领现金-全屏',
    type: 'interstitial',
  },
  'out.reward.screen': {
    id: '50_2024080825000181008',
    label: '天天领现金-插屏',
    type: 'interstitial',
  },
  'out.reward.full1': {
    id: '50_2024100925000197049',
    label: '天天领现金-全屏',
    type: 'interstitial',
  },
  'out.reward.screen1': {
    id: '50_2024100925000197007',
    label: '天天领现金-插屏',
    type: 'interstitial',
  },
  'out.red_envelope.full': {
    id: '50_2024121225000210732',
    label: '天天领现金-全屏',
    type: 'interstitial',
  },
  'out.red_envelope.screen': {
    id: '50_2024121225000210641',
    label: '天天领现金-插屏',
    type: 'interstitial',
  },
  'out.integral.record.full': {
    id: '50_2025052025002046964',
    label: '积分明细-全屏',
    type: 'interstitial',
  },
  'out.integral.record.screen': {
    id: '50_2025052025002046916',
    label: '积分明细-插屏',
    type: 'interstitial',
  },
  'query.index.full':
    process.env.PLATFORM_ENV === 'alipay'
      ? {
          id: '50_2024012525000080343',
          label: '首页弹窗-全屏',
          type: 'interstitial',
        }
      : null,
  'query.index.screen':
    process.env.PLATFORM_ENV === 'alipay'
      ? {
          id: '50_2024040125000087951',
          label: '首页弹窗-插屏',
          type: 'interstitial',
        }
      : null,
  'bill.detail.full':
    process.env.PLATFORM_ENV === 'alipay'
      ? {
          id: '50_2024071525000126742',
          label: '物流详情页-全屏',
          type: 'interstitial',
        }
      : null,
  'bill.detail.screen':
    process.env.PLATFORM_ENV === 'alipay'
      ? {
          id: '50_2024072225000173621',
          label: '物流详情页-插屏',
          type: 'interstitial',
        }
      : null,
  'welfare.lotterys.full':
    process.env.PLATFORM_ENV === 'alipay'
      ? {
          id: '50_2025060625002050084',
          label: '每日抽奖-全屏',
          type: 'interstitial',
        }
      : null,
  'welfare.lotterys.screen':
    process.env.PLATFORM_ENV === 'alipay'
      ? {
          id: '50_2025060625002050098',
          label: '每日抽奖-插屏',
          type: 'interstitial',
        }
      : null,
};

export default process.env.PLATFORM_ENV === 'alipay'
  ? {
      'query.bill': 'ad_tiny_2017062807585646_202401192200079688', // 我的快递-物流详情页
      'out.reward': 'ad_tiny_2017062807585646_202408202200187420', // Tower 任务: 支付宝微快递小程序-新增营销页面 ( https://tower.im/teams/258300/todos/102006 )
      'out.reward1': 'ad_tiny_2017062807585646_202410092200196949',
    }
  : {
      0: 'adunit-0af5ecc84d40c4fd.custom', // 预约取件位置：
      1: 'adunit-c2b4027267c614a0.custom', //物流详情位置：58be93eea119ad4d
      2: 'adunit-531bf0302fdadc90.custom', //首页取件位置：
      'courier.detail': 'adunit-0140ea84811d06c2', // 快递员详情页
      'order.result': 'adunit-99e86c8079afc0f0.custom', // 下单结果页
      'order.detail': 'adunit-67188e7a2bcf2e9e.custom', // 订单详情页 374f6702634c873b
      'order.pay': 'adunit-3ef843737937b18b.custom', // 订单待支付页
      query: 'adunit-531bf0302fdadc90.custom', // 物流详情页
      'query.detail': 'adunit-c2b4027267c614a0.custom', // 物流详情页
      yiqing: 'adunit-cfba8b1296abab2c.custom', // 疫情查询页
      query_list: [
        'adunit-531bf0302fdadc90.custom',
        'adunit-2aec7a57fe4e9177.custom',
        'adunit-f2d2150d2be32a10.custom',
      ], // 首页
    };

export const cityAdMap = {
  query_list: {
    city: ['北京', '上海', '广州', '深圳', '成都'],
    ids: 'adunit-c8db8c10fba17801',
  },
};
