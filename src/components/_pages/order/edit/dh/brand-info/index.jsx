/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { Fragment } from '@tarojs/taro';
import { Text, View, Image, ScrollView } from '@tarojs/components';
import { AtIcon } from 'taro-ui';
import KbModal from '@base/components/modal';
import isArray from 'lodash/isArray';
import classNames from 'classnames';
import { useDHBrandInfo } from './_utils';
import './index.scss';

const BrandInfo = (props) => {
  const {
    brands = {},
    loading = false,
    quotationList = [],
    brand = '',
    delivery_type = '',
    scrollViewId,
    openBrandChooseTips,
    handleChoose,
    handleProductDesc,
    handleBrandChooseTips,
  } = useDHBrandInfo(props);

  const hasQuotation = !!(isArray(quotationList) && quotationList.length > 0);
  const rootCls = classNames('kb-brandInfo', {
    'kb-brandInfo-row': !hasQuotation,
  });
  return (
    <Fragment>
      <View className={rootCls}>
        <View className='kb-brandInfo-label'>快递品牌</View>
        <View className='kb-brandInfo-value'>
          {hasQuotation ? (
            <ScrollView scrollX scrollIntoView={scrollViewId}>
              <View className='kb-brandInfo-list'>
                {quotationList.map((item) => {
                  const oBrandItem = (brands && brands[item.brand]) || {};
                  item.brandName = oBrandItem && oBrandItem.name;
                  const {
                    discount_price,
                    s_fee,
                    message = '',
                    id,
                    delivery_type: delivery_type2 = '',
                    delivery_type_name,
                  } = item || {};
                  const activeBrand = brand === item.brand && delivery_type == delivery_type2;
                  const itemCls = classNames('kb-brandInfo-item', {
                    'kb-brandInfo-item--notActive': !activeBrand,
                  });
                  return (
                    <View
                      className={itemCls}
                      key={id}
                      id={id}
                      onClick={() => handleChoose(item)}
                      hoverClass='kb-hover'
                    >
                      <View className='kb-brandInfo-item-name'>
                        <View
                          className='brand'
                          onClick={(ev) => {
                            if (item.delivery_type_name) {
                              ev.stopPropagation();
                              handleProductDesc(item);
                            }
                          }}
                        >
                          <Image
                            className='img-bg'
                            mode='widthFix'
                            src={`https://cdn-img.kuaidihelp.com/wkd/miniApp/dh/brand-bg${
                              !activeBrand ? '' : '-active'
                            }.png?v=2`}
                          />
                          <View className='brand-name'>
                            <Text className='kb-text__bold'>{item.brandName}</Text>
                            {delivery_type_name && (
                              <AtIcon
                                prefixClass='kb-icon'
                                value='question'
                                className='icon kb-icon2'
                                customStyle={{
                                  fontSize: '24rpx',
                                  marginLeft: '10rpx',
                                }}
                              />
                            )}
                          </View>
                        </View>
                        {delivery_type_name && (
                          <View className='product'>{delivery_type_name}</View>
                        )}
                      </View>
                      <View className='kb-brandInfo-item-price'>
                        {loading ? (
                          <AtIcon
                            prefixClass='kb-icon'
                            value='loading'
                            className='kb-icon-size__base kb-color__grey'
                          />
                        ) : (
                          <Fragment>
                            <View className='price'>
                              <Text className='unit'>￥</Text>
                              {discount_price > 0 ? discount_price : '-'}
                            </View>
                            {s_fee && s_fee * 1 > 0 && (
                              <View className='price2'>
                                <View className='continuation-weight'>续重</View>
                                <View>
                                  <Text className='kb-color__black kb-size__bold'>￥{s_fee}</Text>
                                  /kg
                                </View>
                              </View>
                            )}
                          </Fragment>
                        )}
                      </View>
                      {message && <View className='kb-brandInfo-item-desc'>{message}</View>}
                    </View>
                  );
                })}
              </View>
            </ScrollView>
          ) : loading ? (
            <View className='kb-brandInfo-loading'>
              <AtIcon
                prefixClass='kb-icon'
                value='loading'
                className='kb-icon-size__base kb-color__grey'
              />
            </View>
          ) : (
            '暂无可选品牌'
          )}
        </View>
      </View>
      <KbModal
        isOpened={openBrandChooseTips}
        top={false}
        title='温馨提示'
        hideFooter
        onCancel={() => handleBrandChooseTips('close')}
        onClose={() => handleBrandChooseTips('close')}
      >
        <View className='kb-brandInfo-chooseBrandModal'>
          <View className='content'>
            因各品牌【增值服务】产品提供服务不同。 切换其他物流品牌时，
            <Text className='kb-color__red'>当前选择【增值服务】项将被清空</Text>，请知晓！
          </View>
          <View className='footer'>
            <View
              className='footer-btn footer-btn-confirm'
              onClick={() => handleBrandChooseTips('confirm')}
              hoverClass='kb-hover-opacity'
            >
              确定
            </View>
            <View
              className='footer-btn footer-btn-cancel'
              onClick={() => handleBrandChooseTips('close')}
              hoverClass='kb-hover-opacity'
            >
              我再想想
            </View>
          </View>
        </View>
      </KbModal>
    </Fragment>
  );
};

BrandInfo.options = {
  addGlobalClass: true,
};

export default BrandInfo;
