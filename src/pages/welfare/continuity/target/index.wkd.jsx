/* eslint-disable react/no-array-index-key */
/* eslint-disable no-unused-vars */
/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { Component, Fragment } from '@tarojs/taro';
import KbPage from '@base/components/page';
import KbCheckbox from '@base/components/checkbox';
import KbAgreement from '@/components/_pages/agreement';
import { View, Image, Text, Swiper, SwiperItem } from '@tarojs/components';
import { AtButton, AtCurtain } from 'taro-ui';
import KbSwitch from '@base/components/switch';
import { tabItemTapCall } from '@/components/_pages/_utils';
import { getShareAppMessage } from '@/utils/share';
import { getUserStateByComplete, noop, debounce, reportAnalytics } from '@base/utils/utils';
import { adNavigator, createAd } from '@/components/_pages/ad-extension/sdk';
import { triggerClearTabBadge } from '@/components/_pages/_utils/tab-badge';
import methods from './methods';
import './index.wkd.scss';
import WelfareSign from './components/sign';
import WelfareEveryday from './components/everyday';
import classNames from 'classnames';

class Index extends Component {
  config = {
    backgroundColorTop: '#fff',
    backgroundColorBottom: '#fff',
    navigationBarTitleText: '福利',
    navigationBarBackgroundColor: '#fff',
    navigationBarTextStyle: 'black',
  };
  constructor() {
    this.edition = process.env.PLATFORM_ENV + '_' + process.env.MODE_ENV;
    this.state = {
      popupMoney: 0.01,
      day: 0,
      sign_day: [],
      isOpened: false,
      modalName: '',
      disabled: false,
      isOpenedSaveModal: false,
      agreementStatus: false,
      currentTab: 'everyday',
      allTab: {
        everyday: {
          topName: '每日抽奖',
          page: 1,
          hasNextPage: true,
          activityList: [],
        },
        stay: {
          topName: '待开奖',
          page: 1,
          hasNextPage: true,
          activityList: [],
        },
        lottery: {
          topName: '已中奖',
          page: 1,
          hasNextPage: true,
          activityList: [],
        },
        end: {
          topName: '已结束',
          page: 1,
          hasNextPage: true,
          activityList: [],
        },
      },
    };
    for (var i in methods) {
      this[i] = methods[i].bind(this);
    }
    this.getEveryDayLists = debounce(this.getEveryDayList, 300);
    this.getInitDatas = debounce(this.getInitData, 300);
    this.getInitDatas = debounce(this.getInitData, 300);
    this.getEveryDayLists = debounce(this.getEveryDayList, 300);
    this.getVipInfos = debounce(this.getVipInfo, 300);
    this.getBannerList = debounce(this.getBannerList, 300);
    this.getIconAreaList = debounce(this.getIconAreaList, 300);
    this.getHotTopicList = debounce(this.getHotTopicList, 300);
  }
  onTabItemTap = tabItemTapCall;
  componentDidMount() {
    triggerClearTabBadge('FLNew');
    this.dataInit();
  }
  componentDidShow() {
    this.getVipInfos();
    this.getInitDatas();
    createAd('welfare').catch(noop);
    if (this.state.currentTab === 'everyday') {
      this.getEveryDayLists();
    } else {
      this.getMyList();
    }
    this.reportAnalytics();
  }

  reportAnalytics = () => {
    ['banner', 'icon', 'hotTopic'].forEach((item) => {
      reportAnalytics({
        key: 'm_welfare',
        options: `福利页-${item}区域-展示`,
      });
    });
  };

  onUpdate = ({ userInfo }) => {
    if (getUserStateByComplete()) {
      this.getInitDatas();
      this.getEveryDayLists();
      this.getVipInfos();
      this.getBannerList();
      this.getIconAreaList();
      this.getHotTopicList();
    }
  };

  onShareAppMessage = (e) =>
    getShareAppMessage(e, {
      info: {
        action: 'continuity',
      },
      page: 'welfare.center',
    });

  onScrollToLower() {
    const { currentTab, allTab } = this.state;
    const { everyday } = allTab;
    if (currentTab === 'everyday') {
      everyday.hasNextPage ? this.getEveryDayLists(everyday.hasNextPage) : '';
    } else {
      const hasNext = allTab[currentTab].hasNextPage;
      hasNext ? this.getMyList(hasNext) : '';
    }
  }

  renderModal = () => {
    const { isOpened, popupMoney } = this.state;
    return (
      <AtCurtain className='kb-continuity-popup' isOpened={isOpened} onClose={this.onCloseCurtain}>
        <Image
          style={{ width: '140%', marginLeft: '-20%' }}
          mode='widthFix'
          src='https://cdn-img.kuaidihelp.com/wkd/miniApp/vip/modal_bg_member.png'
        />
        <View className='popupMoney'>
          <Text>已领取</Text>
          <Text className='count'>{popupMoney}元</Text>
          <Text>红包</Text>
        </View>
        <View className='describe'>已享受会员奖励翻倍</View>
        <Image
          style={{ width: '65%' }}
          className='button'
          mode='widthFix'
          src='https://cdn-img.kuaidihelp.com/wkd/miniApp/vip/modal_bg_member_button.png'
          onClick={this.onJump}
        />
        <View className='reject'>红包记录可至“我的-我的钱包”中查看</View>
      </AtCurtain>
    );
  };

  renderSaveModal = () => {
    const {
      isOpenedSaveModal,
      agreementStatus,
      popupMoney,
      vipInfo: { first_price = '' } = {},
    } = this.state;
    return (
      <AtCurtain
        className='kb-continuity-popup'
        isOpened={isOpenedSaveModal}
        onClose={this.onCloseSaveModal}
      >
        <Image
          style={{ width: '140%', minHeight: 480, marginLeft: '-20%' }}
          mode='widthFix'
          src='https://osscdn-kbad.kuaidihelp.com/admin/ad/2022/09/09/631af2fc854c7/202209091602049033040.png'
        />
        <View className='popupMoney vipCount'>
          <Text className='count'>{popupMoney}元</Text>
        </View>
        <View className='agreement'>
          <KbCheckbox
            label='我已阅读并同意'
            checked={agreementStatus}
            onChange={this.onSwitchAgree}
            className='kb-color__white'
            isWelfare={agreementStatus}
          />
          <KbAgreement agreeType='serviceAgreement' style={{ color: '#fff' }} />
        </View>
        <AtButton className='save_button' onClick={this.onPay}>
          {first_price}元开通
        </AtButton>
      </AtCurtain>
    );
  };

  onNavigatorMore = () => {
    Taro.navigateTo({
      url: '/pages-0/pages/welfare/lotterys/index',
    });
  };

  adHandle = ({ type, ...rest }) => {
    adNavigator({
      ...rest,
      report: {
        key: 'm_welfare',
        options: `福利页-${type}区域-${rest.title}`,
      },
    });
  };

  render() {
    const {
      allTab,
      currentTab,
      isOpened,
      popupMoney,
      sign_day,
      modalName,
      day,
      disabled,
      isOpenedSaveModal,
      subscribe_status,
      signDetails,
      bannerList = [],
      iconArea = [],
      hotTopicList = [],
      ...rest
    } = this.state;
    const currentList = allTab[currentTab].activityList;
    const hasAdBanner = !!bannerList.length;
    const containerCls = classNames('kb-continuity__container', {
      'kb-continuity__container--static': !hasAdBanner,
    });

    return (
      <Fragment>
        <KbPage
          {...rest}
          className='kb-continuity'
          onUpdate={this.onUpdate}
          renderFooter={
            <View>
              {isOpened && this.renderModal()}
              {isOpenedSaveModal && this.renderSaveModal()}
            </View>
          }
        >
          <View className='kb-continuity'>
            <View className='kb-continuity__top' />
            {/* banner */}
            {hasAdBanner && (
              <View className='kb-continuity__swiperContainer'>
                <Swiper
                  className='kb-continuity__swiper'
                  indicatorColor='#999'
                  indicatorActiveColor='#333'
                  circular
                  autoplay
                >
                  {bannerList.map((item) => (
                    <SwiperItem key={item.id} className='kb-continuity__swiper__item'>
                      <Image
                        className='kb-continuity__swiper__item__image'
                        src={item.imgUrl}
                        onClick={this.adHandle.bind(this, item)}
                      />
                    </SwiperItem>
                  ))}
                </Swiper>
              </View>
            )}

            <View className={containerCls}>
              {/* icon区域 */}
              {!!iconArea.length && (
                <View className='kb-continuity__iconArea'>
                  <View className='at-row at-row--wrap'>
                    {iconArea.map((item) => (
                      <View
                        key={item.id}
                        className='at-col at-col-3 kb-continuity__iconArea__item '
                        onClick={this.adHandle.bind(this, item)}
                      >
                        <Image src={item.imgUrl} className='kb-continuity__iconArea__item__image' />
                        <View className='kb-continuity__iconArea__item__desc'>{item.title}</View>
                      </View>
                    ))}
                  </View>
                </View>
              )}

              {/* 红包 */}
              <View className='kb-continuity__sign'>
                <View className='kb-continuity__sign__signSwitch'>
                  <KbSwitch
                    title='福利提醒'
                    checked={subscribe_status == '1'}
                    onChange={this.handleLottery}
                    color='#eb673d'
                  />
                </View>
                <WelfareSign
                  day={day}
                  signDetails={signDetails}
                  sign_day={sign_day || []}
                  signBtn={this.SignBtn.bind(this)}
                />
              </View>

              {/* 天天抽奖 */}
              <View className='kb-everyday'>
                <View className='kb-everyday__title'>
                  <AtButton
                    className='kb-everyday__title__button'
                    onClick={() => this.onNavigatorMore()}
                  >
                    <View className='kb-everyday__title__text'>更多抽奖</View>
                  </AtButton>
                </View>
                <WelfareEveryday
                  list={currentList}
                  getEveryDayLists={this.getEveryDayLists.bind(this)}
                />
              </View>

              {/* 热门 */}
              {!!hotTopicList.length && (
                <View className='kb-continuity__hotTopic'>
                  <View className='kb-continuity__hotTopic__header'>
                    <Image
                      className='kb-continuity__hotTopic__header__image'
                      src='//cdn-img.kuaidihelp.com/wkd/welfare/renqi.png'
                    />
                  </View>

                  <View className='kb-continuity__hotTopic__item'>
                    {hotTopicList.map((item) => (
                      <Image
                        key={item.id}
                        src={item.imgUrl}
                        className='kb-continuity__hotTopic__item__image'
                        onClick={this.adHandle.bind(this, item)}
                      />
                    ))}
                  </View>
                </View>
              )}
            </View>
          </View>
        </KbPage>
      </Fragment>
    );
  }
}
export default Index;
