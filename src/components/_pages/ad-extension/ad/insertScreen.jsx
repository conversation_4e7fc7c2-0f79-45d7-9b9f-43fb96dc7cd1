/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { useEffect, useState, useRef } from '@tarojs/taro';
import KbCountdown from '@/components/_pages/user/activity/countdown';
import { Image, View } from '@tarojs/components';
import { AtCurtain } from 'taro-ui';
import { useDidShowCom } from '@base/hooks/page';
import { reportAnalytics } from '@base/utils/utils';
import { useSelector } from '@tarojs/redux';
import isArray from 'lodash/isArray';
import { adNavigator, createInterstitialAdWrap, loadAdminAd } from '../sdk';
import { getAdTypeReq, getAdStorageKey } from '../_utils';
import './insertScreen.scss';

/**
 * 微信官方广告组件位 - 插屏广告;
 */

const Index = (props) => {
  const { isOpen, adType = 'order.result' } = props;
  const queryData = getAdTypeReq(adType);
  const adStorageKey = getAdStorageKey(adType);
  const { isVip = false } = useSelector((state) => state.global);
  const [adData, setAdData] = useState(); //当前需要展示的广告;
  const [isOpened, setIsOpened] = useState(false);
  const WXInsertAdRef = useRef(); // 插屏广告
  const AdListRef = useRef(); //全部列表数据
  const refreshRef = useRef(false); //刷新标志
  const reportKey = 'exp_ad';

  useEffect(() => {
    isOpen && !isVip && loaderAd();
    return () => {
      //销毁微信插屏广告
      if (WXInsertAdRef.current && WXInsertAdRef.current.destroy) {
        WXInsertAdRef.current.destroy();
      }
    };
  }, [isOpen, isVip]);

  useDidShowCom(() => {
    if (refreshRef.current) {
      showOwnInsertAd('switch');
      refreshRef.current = false;
    }
  });

  const loaderAd = () => {
    loadAdminAd(queryData, {
      type: '1',
      limit: {
        key: adStorageKey,
      },
    }).then((data) => {
      const { list } = data || {};
      if (isArray(list) && list.length > 0) {
        AdListRef.current = data;
        AdListRef.current.oldList = [...list];
        showOwnInsertAd();
      } else {
        showWxInsertAd();
      }
    });
  };

  const showOwnInsertAd = (action = 'init') => {
    //自有插屏广告
    /**
     * balance 均匀分配模式 / sequence 主次分配模式
     */
    let { list, oldList, type } = AdListRef.current || {};
    const { id } = adData || {};
    let cIndex = 0,
      len = list.length,
      current;
    switch (type) {
      case 'balance':
        cIndex = Math.floor(Math.random() * len);
        current = list[cIndex];
        AdListRef.current.list.splice(cIndex, 1);
        // 每轮后重置
        if (action == 'switch' && len <= 1) {
          AdListRef.current.list = [...oldList];
        }
        break;
      case 'sequence':
        cIndex = list.findIndex((item) => item.id == id) || 0;
        cIndex = action == 'init' ? 0 : cIndex * 1 + 1;
        if (cIndex > len - 1) cIndex = 0;
        current = list[cIndex];
        break;
    }
    if (!current) return;
    setAdData(current);
    setIsOpened(true);
    reportAnalytics({
      key: reportKey,
      options: `展示广告-${current.title}`,
    });
  };

  const handleAdClick = () => {
    refreshRef.current = true;
    adNavigator({
      ...adData,
      report: {
        key: reportKey,
        options: `点击广告-${adData.title}`,
      },
    });
  };

  const handleCloseOwnAd = () => {
    setIsOpened(false);
  };

  const showWxInsertAd = () => {
    //微信插屏广告
    createInterstitialAdWrap(() => {
      WXInsertAdRef.current = Taro.createInterstitialAd({
        adUnitId: 'adunit-0b6279a459673f34',
      });
      WXInsertAdRef.current.onLoad(() => {
        reportAnalytics({
          key: 'cpad_onload',
        });
        WXInsertAdRef.current.show().catch((err) => {
          console.log('cpad_err', err);
          reportAnalytics({
            key: 'cpad_errormsg',
            errormsg: err.errMsg,
          });
        });
      });
    });
  };

  return (
    <AtCurtain isOpened={isOpened} onClose={handleCloseOwnAd} className='kb-curtain__large'>
      {isOpened && <KbCountdown onEnd={handleAdClick} record={adData} reportKey={reportKey} />}
      <View className='kb-curtain__inner'>
        <Image
          className='kb-curtain__inner--image'
          showMenuByLongpress
          mode='widthFix'
          src={adData.imgUrl}
          onClick={handleAdClick}
        />
      </View>
    </AtCurtain>
  );
};
Index.defaultProps = {
  isOpen: false,
  adUnitIdIndex: '',
};
Index.options = {
  addGlobalClass: true,
};
export default Index;
