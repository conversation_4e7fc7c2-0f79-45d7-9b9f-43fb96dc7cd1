/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { Fragment, useEffect, useState } from '@tarojs/taro';
import KbModal from '@base/components/modal';
import { Image, Text, View } from '@tarojs/components';
import './index.scss';

const GuideUseCoupon = (props) => {
  const { open, onCancel, onConfirm, quotation } = props;
  const { discount_type, discount_fee } = quotation || {};
  const [isOpened, setIsOpened] = useState(false);

  useEffect(() => {
    setIsOpened(!!open);
  }, [open]);

  const handelGuideCoupon = (key, ev) => {
    console.log('key,ev', key, ev);
    const close = () => {
      setIsOpened(false);
    };
    switch (key) {
      case 'close':
        close();
        break;
      case 'open':
        setIsOpened(true);
        break;
      case 'cancel':
        if (ev === 'button') {
          if (onCancel) {
            onCancel();
          }
        }
        close();
        break;
      case 'confirm':
        if (onConfirm) {
          onConfirm();
        }
        close();
        break;
    }
  };

  return (
    <Fragment>
      <KbModal
        isOpened={isOpened}
        top={false}
        title='确定要原价寄件吗？'
        onClose={handelGuideCoupon.bind(null, 'close')}
        onCancel={handelGuideCoupon.bind(null, 'cancel')}
        onConfirm={handelGuideCoupon.bind(null, 'confirm')}
        confirmText='尝试优惠寄件'
        cancelText='继续提交订单'
        footerLayout='column'
      >
        <View className='kb-guideCouponModal'>
          <View className='kb-guideCouponModal-tips'>原价寄件，将不能享受平台折扣</View>
          <View className='kb-guideCouponModal-coupon'>
            <Image
              className='kb-guideCouponModal-coupon--img'
              mode='widthFix'
              src='https://cdn-img.kuaidihelp.com/wkd/miniApp/activity/coupon/coupon.png?v=1'
            />
            <View className='kb-guideCouponModal-coupon--fee'>
              {discount_type === 'discount' ? (
                <Fragment>
                  <Text className='num'>{discount_fee}</Text>
                  <Text>折</Text>
                </Fragment>
              ) : (
                <Fragment>
                  <Text>￥</Text>
                  <Text className='num'>{discount_fee}</Text>
                </Fragment>
              )}
            </View>
            <View className='kb-guideCouponModal-coupon--desc'>
              {discount_type === 'discount' ? '品牌折扣券' : '无门槛直减券'}
            </View>
          </View>
        </View>
      </KbModal>
    </Fragment>
  );
};

GuideUseCoupon.options = {
  addGlobalClass: true,
};

export default GuideUseCoupon;
