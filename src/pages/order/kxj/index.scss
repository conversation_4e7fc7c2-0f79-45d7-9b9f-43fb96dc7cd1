/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb-send {
  padding: $spacing-v-md $spacing-h-md;

  &__item {
    margin-bottom: $spacing-v-md;

    &:first-child,
    &:last-child {
      margin-bottom: 0;
    }
  }
}

.kb-login__auth--box {
  padding: $spacing-v-md $spacing-h-md;
  background-color: $color-white;
}

.yj-container {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 2;
  overflow: visible;
  background: #fff;
  &-list {
    padding: 0 20px;
  }
  &-msg {
    position: relative;
    z-index: 2;
    padding-bottom: 0;
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
    background: #fff;
    box-shadow: 0 10px 15px #000;
  }
}

.kb-scrollview-wrapper .wrapper-footer {
  z-index: 4 !important;
}

// 春节快递品牌通告
.kb-spring {
  position: relative;
  width: 632px;
  height: 850px;
  margin: 0 auto;
  color: #fff;
  font-size: $font-size-sm;
  background: url('https://cdn-img.kuaidihelp.com/wkd/miniApp/spring/bg.png') 0 0 no-repeat;
  background-size: contain;
  &__body {
    width: 425px;
    margin: 0 auto;
    padding-top: 210px;
    line-height: 1.5;
  }
  &__title {
    padding-bottom: $spacing-h-md;
    color: #ffeed9;
    font-size: $font-size-lg;
    text-align: center;
  }
  &__btn {
    width: 260px;
    height: 60px;
    margin: 20px auto;
    color: #ec222c;
    font-size: $font-size-lg;
    line-height: 60px;
    text-align: center;
    background: linear-gradient(to bottom, #ffeed9, #ffc487);
    border-radius: 50px;
    box-shadow: 0 4px 4px rgba(0, 0, 0, 0.2);
  }
  &__desc {
    position: absolute;
    bottom: -40px;
    left: 50%;
    transform: translateX(-50%);
  }
}

// 不活跃快递员弹窗
.kb-notActive {
  .at-modal__content {
    padding: 40px;
  }
  .kb-modal__footer-column {
    padding-bottom: 50px;
    .at-button.kb-button__middle.at-button {
      width: 510px;
      margin-bottom: 10px;
    }
  }
}

.kb-layout {
  &--img {
    width: 100%;
  }
  &--text {
    color: $color-grey-2;
    text-align: center;
  }
  &--dot {
    position: relative;
    padding-left: 30px;
    &::before {
      position: absolute;
      top: 15px;
      left: 0;
      width: 10px;
      height: 10px;
      background: $color-grey-2;
      border-radius: 50%;
      content: '';
    }
  }
}

.kb-custom-float {
  .at-float-layout__container {
    left: 3%;
    width: 94%;
  }
  .kb-layout {
    &--img {
      width: 100%;
    }
    &--text {
      color: $color-grey-2;
      text-align: center;
    }
    &--dot {
      position: relative;
      padding-left: 30px;
      &::before {
        position: absolute;
        top: 15px;
        left: 0;
        width: 10px;
        height: 10px;
        background: $color-grey-2;
        border-radius: 50%;
        content: '';
      }
    }
  }
}

// 德邦重量提示弹窗
.kb-dpWightTips {
  color: $color-grey-1;
  font-size: $font-size-lg;
  &-content {
    margin-bottom: $spacing-v-md;
    line-height: 1.5;
  }
  &-dot {
    position: relative;
    padding-left: 30px;
    &::before {
      position: absolute;
      top: 15px;
      left: 0;
      width: 10px;
      height: 10px;
      background: $color-brand;
      border-radius: 50%;
      content: '';
    }
  }
  &-tips {
    margin-bottom: $spacing-h-xxl;
    padding: $spacing-h-md;
    font-size: $font-size-base;
    background: #f1f4f9;
    border-radius: $border-radius-lg;
  }
  .kb-checkbox__label {
    font-size: 28px;
  }
}
/*  #ifdef swan  */
.yjkd__weight {
  &_input {
    .at-input {
      width: 180px !important;
      margin-left: 0 !important;
      padding: 0 !important;
    }
  }
}
/*  #endif  */

.kb-batchOrderTips {
  text-align: center;
  &__fail {
    &-num {
      margin: 0 $spacing-h-sm;
      font-size: 48px;
    }
    &-copy {
      height: 80px;
      font-size: 32px;
      line-height: 80px;
    }
  }
}
