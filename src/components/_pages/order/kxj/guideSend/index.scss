/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.guideSend {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 10000;
  background: rgba(0, 0, 0, 0.7);
  &-img01 {
    position: absolute;
    right: 30px;
    bottom: env(safe-area-inset-bottom);
    width: 641px;
    height: 430px;
    .img {
      width: 100%;
      height: 100%;
    }
    .opt {
      position: absolute;
      bottom: 120px;
      left: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      &-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 64px;
        font-size: 28px;
        border-radius: 32px;
        &-cancel {
          width: 168px;
          color: #ffffff;
          border: 2px solid #ffffff;
        }
        &-next {
          width: 248px;
          margin-left: 30px;
          color: #157eff;
          background: #ffffff;
        }
      }
    }
  }
  &-img02 {
    position: absolute;
    top: 500px;
    left: 50px;
    width: 643px;
    height: 476px;
  }
}
