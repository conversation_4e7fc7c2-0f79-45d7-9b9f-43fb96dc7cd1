/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { Image, View, Text } from '@tarojs/components';
import Taro from '@tarojs/taro';
import numeral from 'numeral';
import { triggerRewardReportAnalytics } from '../_utils/reportAnalytics';
import './index.scss';

const KbRewardTotal = (props) => {
  const { signDetail = {}, position } = props;

  // 跳转我的钱包
  const handleToWallet = () => {
    // 统计-点击提现
    triggerRewardReportAnalytics({ action: 'to_wallet' }, position);
    Taro.navigator({
      url: 'user/wallet',
    });
  };

  const handleToIntegral = () => {
    // 统计-点击积分
    triggerRewardReportAnalytics({ action: 'to_integral' }, position);
    Taro.navigator({
      url: 'out/reward/integral-record',
    });
  };

  return (
    <View className='kb-rewardTotal'>
      <View
        className='kb-rewardTotal-item'
        hoverClass='kb-hover-opacity'
        onClick={handleToIntegral}
      >
        <Image
          className='img'
          mode='widthFix'
          src='https://cdn-img.kuaidihelp.com/wkd/miniApp/prize/icon_jf.png?v=1'
        />
        <View className='num'>{numeral(signDetail.integral).format('0')}</View>
      </View>
      <View className='kb-rewardTotal-item' hoverClass='kb-hover-opacity' onClick={handleToWallet}>
        <Image
          className='img'
          mode='widthFix'
          src='https://cdn-img.kuaidihelp.com/wkd/miniApp/prize/icon_xj.png?v=1'
        />
        <View className='num'>{signDetail.money}</View>
        <View>提现</View>
        <Text className='kb-icon kb-icon-arrow_copy' />
      </View>
    </View>
  );
};

KbRewardTotal.options = {
  addGlobalClass: true,
};

export default KbRewardTotal;
