const filterIPPositions = process.env.PLATFORM_ENV === 'alipay' ? ['3'] : [];
// Tower 任务: 支付宝微快递小程序-我的快递广告调整（1.14） ( https://tower.im/teams/258300/todos/109188 )
export async function filterAdsBase(list, data) {
  const { isShieldIp, position } = data || {};
  if (!filterIPPositions.includes(`${position}`)) {
    return list;
  }

  // IP屏蔽区域：仅展示含有 屏蔽城市- 的广告；
  // 非IP屏蔽区域：仅展示不含有  屏蔽城市- 的广告；
  return list.filter((item) => {
    const has = item.title.startsWith('屏蔽城市-');
    return isShieldIp ? has : !has;
  });
}
