/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { Component } from '@tarojs/taro';
import { connect } from '@tarojs/redux';
import { AtButton, AtCurtain } from 'taro-ui';
import KbPage from '@base/components/page';
import KbModal from '@base/components/modal';
import { Image, Text, View } from '@tarojs/components';
import {
  checkIsFromDesktop,
  desktopDescList,
  startPrize,
} from '@/components/_pages/desktop/_utils';
import { debounce } from '@base/utils/utils';
import './index.scss';

@connect(({ global }) => ({
  loginData: global.loginData,
}))
class Index extends Component {
  config = {
    navigationBarTitleText: '桌面权益频道',
    navigationStyle: 'custom',
  };

  constructor(props) {
    super(props);
    this.state = {};
    this.handleStartPrize = debounce(this.handleStartPrize, 500, {
      leading: true,
      trailing: false,
    });
  }

  componentDidMount() {
    this.getEntryScene();
    if (!this.isFromDesktop) {
      this.handleGuide('open');
    }
  }

  componentDidShow() {
    this.getEntryScene();
  }

  handleUpdate(data) {
    if (!data.logined) return;
  }

  handleReady = (ev) => {
    this.setState({
      ...ev,
    });
  };

  getEntryScene = () => {
    this.isFromDesktop = checkIsFromDesktop();
    this.setState({
      isFromDesktop: this.isFromDesktop,
    });
    console.log('是否从桌面进入', this.isFromDesktop);
  };

  handleStartPrize = () => {
    if (!this.isFromDesktop) {
      Taro.kbToast({
        text: '领取失败，抽奖请从桌面访问哦~',
      });
      this.handleFailGuide('open');
      return;
    }
    if (this.lock) {
      return;
    }
    this.lock = true;
    startPrize()
      .then((res) => {
        console.log('res', res);
        if (res) {
          const angle = 360 * 6 - (res.code == 'P1' ? 0 : res.code == 'P2' ? 45 : 0);
          this.setState(
            {
              transform: `rotate(${angle}deg)`,
            },
            () => {
              this.timer && clearTimeout(this.timer);
              this.timer = setTimeout(() => {
                this.lock = false;
                this.handlePrizeModal('open', res);
              }, 5500);
            },
          );
        } else {
          this.lock = false;
        }
      })
      .catch(() => {
        this.lock = false;
      });
  };

  handleClickBar = (key) => {
    switch (key) {
      case 'prize':
        Taro.navigator({
          url: 'desktop/prize',
        });
        break;
      case 'rule':
        this.handleRuleModal('open');
        break;
    }
  };

  handleGuide = (key) => {
    switch (key) {
      case 'open':
        this.setState({
          openGuide: true,
        });
        break;
      case 'close':
        this.setState({
          openGuide: false,
        });
        break;
    }
  };

  handleFailGuide = (key) => {
    switch (key) {
      case 'open':
        this.setState({
          openFailGuide: true,
        });
        break;
      case 'close':
        this.setState({
          openFailGuide: false,
        });
        break;
      case 'openDesktopAuth':
        wx.openAppAuthorizeSetting();
        break;
      case 'addDesktop':
        this.setState({
          openGuide: true,
          openFailGuide: false,
        });
        break;
    }
  };

  handlePrizeModal = (key, data) => {
    switch (key) {
      case 'open':
        this.setState({
          openPrizeModal: data,
        });
        break;
      case 'close':
        this.setState({
          openPrizeModal: false,
        });
        break;
      case 'jump':
        if (data.code == 'P1') {
          Taro.navigator({
            url: 'user/wallet',
          });
        } else if (data.code == 'P2') {
          Taro.navigator({
            url: 'user/member',
          });
        }
        break;
    }
  };

  handleRuleModal = (key) => {
    switch (key) {
      case 'open':
        this.setState({
          openRule: true,
        });
        break;
      case 'close':
        this.setState({
          openRule: false,
        });
        break;
    }
  };

  render() {
    const {
      openRule,
      openGuide,
      openFailGuide,
      openPrizeModal,
      isFromDesktop,
      transform,
      statusBarHeight = 0,
      titleBarHeight = 0,
      ...rest
    } = this.state;
    const navHeight = statusBarHeight * 1 + titleBarHeight * 1 || 0;
    return (
      <KbPage
        {...rest}
        onUpdate={this.handleUpdate.bind(this)}
        navProps={{
          onReady: this.handleReady,
        }}
      >
        <View className='kb-desktop'>
          <View className='kb-desktop-bg'>
            <Image
              className='kb-desktop-bg--img'
              mode='widthFix'
              src='https://cdn-img.kuaidihelp.com/wkd/miniApp/desktop/bg.png?v=01'
            />
          </View>
          <View
            className='kb-desktop-container'
            style={{ top: `${navHeight + (isFromDesktop ? 10 : 30)}px` }}
          >
            <Image
              className='kb-desktop-text'
              mode='widthFix'
              src='https://cdn-img.kuaidihelp.com/wkd/miniApp/desktop/bg-text.png?v=01'
            />
            <View className='kb-desktop-pan' onClick={this.handleStartPrize}>
              <Image
                style={{ width: '100%', height: '100%' }}
                mode='widthFix'
                src='https://cdn-img.kuaidihelp.com/wkd/miniApp/desktop/pan-out.png'
              />
              <Image
                className='kb-desktop-pan-inner'
                // className='kb-desktop-pan-inner kb-desktop-pan-inner--active'
                style={{ transform: transform }}
                mode='widthFix'
                src='https://cdn-img.kuaidihelp.com/wkd/miniApp/desktop/pan-inner.png?v=01'
              />
              <Image
                className='kb-desktop-pan-z'
                mode='widthFix'
                src='https://cdn-img.kuaidihelp.com/wkd/miniApp/desktop/pan-z.png'
              />
            </View>
            <Image
              className='kb-desktop-img'
              mode='widthFix'
              src='https://cdn-img.kuaidihelp.com/wkd/miniApp/desktop/img01.png'
            />
            <Image
              className='kb-desktop-img'
              style={{ marginTop: '20px' }}
              mode='widthFix'
              src='https://cdn-img.kuaidihelp.com/wkd/miniApp/desktop/img02.png'
            />
            <Image
              className='kb-desktop-img'
              style={{ marginTop: '20px' }}
              mode='widthFix'
              src='https://cdn-img.kuaidihelp.com/wkd/miniApp/desktop/img03.png'
            />
            {!isFromDesktop && (
              <View className='kb-desktop-btn' onClick={() => this.handleFailGuide('open')}>
                <Image
                  className='kb-desktop-btn--img'
                  mode='widthFix'
                  src='https://cdn-img.kuaidihelp.com/wkd/miniApp/desktop/btn-01.png?v=01'
                />
              </View>
            )}
            <View className='kb-desktop-door'>
              <View
                className='kb-desktop-door--box'
                onClick={() => this.handleClickBar('rule')}
                hoverClass='kb-hover'
              >
                活动规则
              </View>
              <View
                className='kb-desktop-door--box'
                onClick={() => this.handleClickBar('prize')}
                hoverClass='kb-hover'
              >
                我的奖励
              </View>
            </View>
            {!isFromDesktop && (
              <View className='kb-desktop-notice' onClick={() => this.handleGuide('open')}>
                <View>您未从桌面访问，部分桌面特权无法享受</View>
                <View className='kb-desktop-notice--btn'>
                  去桌面
                  <Text className='kb-icon kb-icon-arrow kb-size__xs' />
                </View>
              </View>
            )}
          </View>
        </View>
        {openGuide && (
          <View className='at-curtain kb-desktop-guide'>
            <View className='at-curtain__container' onClick={() => this.handleGuide('close')}>
              <View className='at-curtain__body' onClick={(ev) => ev.stopPropagation()}>
                <Image
                  className='kb-desktop-guide--img'
                  src='https://cdn-img.kuaidihelp.com/wkd/miniApp/desktop/guide.png'
                />
              </View>
            </View>
          </View>
        )}
        <KbModal
          isOpened={openFailGuide}
          className='kb-desktop-failGuide'
          top={false}
          closable={false}
          onClose={() => this.handleFailGuide('close')}
          onCancel={() => this.handleFailGuide('close')}
          confirmText=''
          full
        >
          <View>
            <View className='kb-desktop-failGuide--head'>抽奖失败</View>
            <View className='kb-desktop-failGuide--content'>
              <View className='title'>一、未添加小程序到桌面</View>
              <View className='desc'>
                a、未打开<Text className='kb-color__red'>桌面编辑权限</Text>
              </View>
              <View className='desc'>b、若打开桌面编辑权限后，点击右上角</View>
              <View className='desc desc2'>
                <Image
                  className='img_setting'
                  mode='widthFix'
                  src='https://cdn-img.kuaidihelp.com/wkd/miniApp/desktop/icon_setting.png'
                />
                <Text className='kb-color__red'>添加到桌面</Text>
              </View>
              <View className='title'>二、未从手机桌面进入领奖</View>
              <View className='desc'>
                需要从<Text className='kb-color__red'>手机桌面</Text>添加的入口
                <Text className='kb-color__red'>进入</Text>即可领取奖品
              </View>
              <Image
                className='img_phone'
                mode='widthFix'
                src='https://cdn-img.kuaidihelp.com/wkd/miniApp/desktop/img_phone.png'
              />
            </View>
            <View className='kb-desktop-failGuide--options'>
              <View className='btn'>
                <AtButton
                  type='secondary'
                  circle
                  size='small'
                  onClick={() => this.handleFailGuide('openDesktopAuth')}
                >
                  打开桌面权限
                </AtButton>
              </View>
              <View className='btn'>
                <AtButton
                  type='primary'
                  circle
                  size='small'
                  onClick={() => this.handleFailGuide('addDesktop')}
                >
                  添加到桌面
                </AtButton>
              </View>
            </View>
          </View>
        </KbModal>
        <AtCurtain
          className='kb-desktop-prizeModal'
          isOpened={openPrizeModal}
          onClose={() => this.handlePrizeModal('close')}
        >
          <View
            className='kb-desktop-prizeModal--content'
            onClick={() => this.handlePrizeModal('jump', openPrizeModal)}
          >
            <Image
              className='kb-desktop-prizeModal--img'
              src='https://cdn-img.kuaidihelp.com/wkd/miniApp/desktop/prize.png'
            />
            <View className='kb-desktop-prizeModal--prize'>
              <View className='left'>
                <Text className='kb-size__sm'>￥</Text>
                <Text className='money'>{openPrizeModal && openPrizeModal.price}</Text>
              </View>
              <View className='right'>
                <View className='right-title'>
                  {openPrizeModal && openPrizeModal.code == 'P1'
                    ? '微快递现金红包'
                    : '微快递VIP会员'}
                </View>
                <View className='kb-size__xs'>桌面特权频道-专享福利</View>
              </View>
            </View>
            <View className='kb-desktop-prizeModal--btn'>去看看&gt;</View>
            <View className='kb-desktop-prizeModal--desc'>
              {openPrizeModal && openPrizeModal.code == 'P1'
                ? '中奖奖品已放入小程序“我的钱包”'
                : '中奖奖品已放入“我的会员”'}
            </View>
          </View>
        </AtCurtain>
        <KbModal
          title='活动规则'
          top={false}
          closable={false}
          confirmText='我知道了'
          isOpened={openRule}
          onClose={() => this.handleRuleModal('close')}
          onCancel={() => this.handleRuleModal('close')}
          onConfirm={() => this.handleRuleModal('close')}
        >
          <View className='kb-desktop-rule--list'>
            {desktopDescList.map((item, index) => {
              return (
                <View className='kb-desktop-rule--list-item' key={item}>
                  <View className='kb-desktop-rule--list-index'>{index + 1}、</View>
                  <View className='kb-desktop-rule--list-content'>{item}</View>
                </View>
              );
            })}
          </View>
        </KbModal>
      </KbPage>
    );
  }
}

export default Index;
