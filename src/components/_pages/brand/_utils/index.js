/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { useMemo } from '@tarojs/taro';
import { useSelector } from '@tarojs/redux';

export const getBrandInfo = (brand, brands = Taro.brands) => {
  return brand && brands ? brands[brand] || {} : {};
};

export const useBrandInfo = (opt = {}) => {
  const { brand } = opt || {};
  const { brands } = useSelector((state) => state.global);

  const brandInfo = useMemo(() => {
    return getBrandInfo(brand, brands);
  }, [brand, brands]);

  return {
    brandInfo,
  };
};
