/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro from '@tarojs/taro';
import { pagePaths } from '@base/config';

export const dealPageNavBack = (opts) => {
  const { delta = 1 } = opts || {};
  const total = Taro.getCurrentPages().length;
  if (total > delta) {
    // 总页数大于返回页数
    return Taro.navigateBack({
      delta,
    });
  }
  Taro.navigator({
    url: pagePaths.homePage,
    target: 'tab',
  });
  return Promise.resolve();
};
