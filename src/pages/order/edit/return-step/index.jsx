/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import KbPage from '@base/components/page';
import { View, Image, Text } from '@tarojs/components';
import Taro, { Component } from '@tarojs/taro';
import { AtButton } from 'taro-ui';
import KbScrollView from '@base/components/scroll-view';
import { orderAction } from '@/components/_pages/order/_utils';
import KbBarrage from '@/components/_pages/order/kxj/barrage';
import './index.scss';

class ReturnStep extends Component {
  config = {
    navigationBarTitleText: '',
    backgroundColorTop: '#f2f2f2',
    navigationStyle: 'custom',
  };

  onShareAppMessage = () => {
    return {
      title: '网购退货低至4.9元起…',
      path: '/pages-1/pages/order/edit/return-step/index',
      imageUrl: 'https://cdn-img.kuaidihelp.com/miniapp/wkd/v2/card.png',
    };
  };

  onShareTimeline = () => {
    return {
      title: '网购退货低至4.9元起…',
      path: '/pages-1/pages/order/edit/return-step/index',
      imageUrl: 'https://cdn-img.kuaidihelp.com/miniapp/wkd/v2/card.png',
    };
  };

  pageToReturn = () => {
    orderAction({
      action: 'edit',
      data: {
        editOrderType: 'th',
      },
    });
  };

  pageToVipDetail = () => {
    Taro.navigator({
      url: '/pages-3/pages/user/member/right/index',
    });
  };

  render() {
    return (
      <KbPage>
        <KbScrollView
          renderFooter={
            <View className='kb-returnStep-footer'>
              <Image
                style={{ width: '680rpx' }}
                mode='widthFix'
                src='https://cdn-img.kuaidihelp.com/miniapp/wkd/return-btn.png'
                onClick={this.pageToReturn}
              />
              <Image
                mode='widthFix'
                src='https://cdn-img.kuaidihelp.com/miniapp/wkd/vip-tag.png'
                className='vip-tag'
              />
            </View>
          }
        >
          <View className='kb-returnStep'>
            <View className='img-box img-box-01'>
              <Image
                className='img'
                mode='widthFix'
                src='https://cdn-img.kuaidihelp.com/miniapp/wkd/v2/banner.png?v=2'
              />
              <KbBarrage activity='merchant_return' />
            </View>
            <View className='img-box'>
              <Image
                className='img'
                mode='widthFix'
                src='https://cdn-img.kuaidihelp.com/miniapp/wkd/v2/steps.png'
              />
            </View>
            <View className='img-box'>
              <Image
                className='img'
                mode='widthFix'
                src='https://cdn-img.kuaidihelp.com/miniapp/wkd/v2/steps2.png'
              />
            </View>
            <View className='img-box'>
              <Image
                className='img'
                mode='widthFix'
                src='https://cdn-img.kuaidihelp.com/miniapp/wkd/v2/steps3.png'
              />
              <View
                className='img-vip'
                onClick={this.pageToVipDetail.bind(this)}
                hoverClass='kb-hover-opacity'
              >
                优享寄VIP会员专属权益，<Text className='kb-color__red'>前往查看特权详情&gt;</Text>
              </View>
            </View>
            <AtButton openType='share' size='small' className='share'>
              <Image
                style={{ width: '32rpx' }}
                src='https://cdn-img.kuaidihelp.com/miniapp/wkd/<EMAIL>'
                mode='widthFix'
              />
            </AtButton>
          </View>
        </KbScrollView>
      </KbPage>
    );
  }
}

export default ReturnStep;
