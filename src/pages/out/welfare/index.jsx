/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */
/* eslint-disable react/no-unknown-property */

import Taro, { Component } from '@tarojs/taro';
import KbPage from '@base/components/page';
import { Image, View } from '@tarojs/components';
import { adNavigator, loadAdminAd } from '@/components/_pages/ad-extension/sdk';
import { createInterstitialAd } from '@/components/_pages/ad-extension/ad/intersitialAd';
import './index.scss';

class WelfarePage extends Component {
  config = {
    navigationBarTitleText: '',
    navigationBarBackgroundColor: '#fff',
    usingComponents: {
      'ad-component': 'plugin://xlightPlugin/ad-component',
      feeds: 'plugin://xlightPlugin/feeds',
    },
  };
  constructor() {
    this.state = {
      ad: null,
    };
  }

  componentDidMount() {
    loadAdminAd({ position: '43' }).then((data) => {
      this.setState({
        ad: Array.isArray(data) ? data[0] : null,
      });
    });
    createInterstitialAd({
      adUnitId: '50_2024072325000173793',
      onClose: () => {
        createInterstitialAd({
          adUnitId: '50_2024072325000173810',
        }).then((interstitialAd) => {
          if (interstitialAd && interstitialAd.openAd) {
            interstitialAd.openAd();
          }
        });
      },
    }).then((interstitialAd) => {
      if (interstitialAd && interstitialAd.openAd) {
        interstitialAd.openAd();
      }
    });
  }

  handleNavigator = (item) => {
    adNavigator(item);
  };

  render() {
    const { ad, ...rest } = this.state;

    return (
      <KbPage {...rest} className='kb-welfarePage'>
        <View>
          <Image
            className='kb-welfare_img'
            mode='widthFix'
            src={ad.imgUrl}
            onClick={() => this.handleNavigator(ad)}
          />
        </View>
        <View className='kb-margin-md-lr kb-margin-md-b'>
          <ad unit-id='ad_tiny_2017062807585646_202407232200173824' />
        </View>
        <View>
          <feeds spaceCode='27_2024072322700173809' />
        </View>
      </KbPage>
    );
  }
}

export default WelfarePage;
