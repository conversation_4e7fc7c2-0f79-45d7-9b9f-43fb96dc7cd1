/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */
/* eslint-disable react/no-unknown-property */

import Taro, { Component } from '@tarojs/taro';
import { View } from '@tarojs/components';
import KbLongList from '@base/components/long-list';
import KbPage from '@base/components/page';
import { getSignDetails } from '@/services/out-reward/sign';
import KbAdCurtain from '@/components/_pages/ad-extension/ad/curtain';
import { createAd } from '~/components/_pages/ad-extension/sdk';
import KbAutoReward from '~/components/_pages/out/reward/auto-reward';
import numeral from 'numeral';
import './index.scss';

class Index extends Component {
  config = {
    navigationBarTitleText: '积分明细',
    navigationStyle: 'custom',
    transparentTitle: 'always',
    navigationBarBackgroundColor: '#fff',
  };
  constructor() {
    this.state = {
      showBackAd: false,
    };
    this.listData = {
      api: {
        url: '/g_wkd/v2/marketingActivity/ZfbSignReward/getSignRecord',
        data: {
          page_size: 20,
        },
        onThen: (list = []) => {
          this.setState({
            list,
          });
        },
      },
    };
  }

  componentDidMount() {
    Taro.isFromIntegralRecord = true;
  }
  // 登录状态更新
  onUpdate = (data) => {
    const { logined } = data;
    if (logined) {
      this.updateSignDetails();
    }
  };

  // 插入广告
  insertAd = () => {
    const screenKey = `out.integral.record.screen`;
    createAd(`out.integral.record.full`, {
      filter: () => false,
      onClose: () => {
        createAd(screenKey);
      },
      onError: () => {
        createAd(screenKey);
      },
    });
    this.insertOwnAd();
  };

  insertOwnAd = () => {
    this.setState({
      loadAd: Math.random() + 1,
    });
  };

  handleLongListReady = (ins) => {
    this.listIns = ins;
  };

  // 获取签到详情：积分、现金、签到列表
  updateSignDetails = () => {
    getSignDetails().then((signDetail) => {
      this.setState({
        signDetail,
      });
    });
  };

  // 弹窗广告加载
  handleAdCurtainLoad = (has) => {
    if (!has) {
      // 无弹窗广告时
    }
  };

  handleShowInsertAd = () => {
    // Tower 任务: 支付宝微快递-灯火插屏处理 ( https://tower.im/teams/258300/todos/115149 )
    // 无积分弹窗，或者关闭积分获取弹窗后，展示插屏广告
    this.insertAd();
  };

  handleToBackAd = () => {
    return new Promise((resolve) => {
      const { showBackAd } = this.state;
      this.setState({
        showBackAd: true,
      });
      resolve(showBackAd);
    });
  };

  render() {
    const { signDetail = {}, list = [], loadAd, showBackAd, ...rest } = this.state;

    return (
      <KbPage
        navProps={{
          forceAlipayLeft: true,
          fixed: true,
          theme: 'ghost-white',
        }}
        closeIosSafeArea
        {...rest}
        onUpdate={this.onUpdate}
        renderHeader={
          <View className='kb-integralRecord-header'>
            <View className='kb-integralRecord-total'>
              <View>我的明细</View>
              <View className='num'>{numeral(signDetail.integral).format('0')}</View>
            </View>
          </View>
        }
        intercept={{
          tips: this.handleToBackAd,
          min: -1,
        }}
      >
        <View className='kb-integralRecord-container'>
          <View className='kb-integralRecord-content'>
            <View className='kb-integralRecord-title'>全部明细</View>
            <View className='kb-integralRecord-list'>
              <KbLongList data={this.listData} noDataText='您还没有明细信息哦~' enableMore>
                {list.map((item) => {
                  return (
                    <View className='kb-integralRecord-item' key={item.id}>
                      <View>
                        <View>{item.type_name}</View>
                        <View className='kb-color__grey kb-size__sm kb-margin-xs-t'>
                          {item.create_time}
                        </View>
                      </View>
                      <View className={item.type == 3 ? '' : 'kb-color__red'}>
                        {item.type == 3 ? '-' : '+'}
                        {item.money}
                      </View>
                    </View>
                  );
                })}
              </KbLongList>
            </View>
          </View>
        </View>
        <KbAdCurtain loadAd={loadAd} position='62' allowFull onLoad={this.handleAdCurtainLoad} />
        <KbAutoReward onFresh={this.updateSignDetails} onShowInsertAd={this.handleShowInsertAd} />
        <KbAdCurtain
          loadAd={showBackAd}
          position='63'
          allowFull
          onLoad={this.handleAdCurtainLoad}
        />
      </KbPage>
    );
  }
}

export default Index;
