/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro from '@tarojs/taro';
import { getPage, frequencyLimitByMinute } from '@base/utils/utils';
import { createInterstitialAd } from '@/components/_pages/ad-extension/ad/intersitialAd';
import isString from 'lodash/isString';
import { getAdConfig } from '../../ad-extension/_utils';

const formatAdResponse = (data, opt = {}) => {
  const { key } = opt || {};
  const [{ adUrl: url, ...rest } = {}] = data || [];
  // 处理广告数据
  const [resetTitle, count = 0] = `${rest.title}`.split('#');
  const barsAdItem = {
    ...rest,
    key: key || '4',
    url,
    count: count,
    title: resetTitle,
  };
  return barsAdItem && barsAdItem.id ? barsAdItem : null;
};

export const getIconAd = (position) => {
  return new Promise((resolve) => {
    getAdConfig({ position }, true).then((res) => {
      const _res = formatAdResponse(res);
      // 闪动特效开关
      const flashRegex = /-闪动/;
      if (_res && isString(_res.count) && flashRegex.test(_res.count)) {
        _res.flash = 1;
        _res.count = _res.count.replace(flashRegex, '');
      }
      resolve(_res);
    });
  });
};

export const getAdBar = (arr = []) => {
  const _this = getPage();
  return new Promise((resolve) => {
    Promise.all(['38', '39', '40'].map((i) => getIconAd(i))).then((res) => {
      const [iconAd1, iconAd2, iconAd3] = res;
      const bars = arr && arr.length > 0 ? arr : [];
      const pos1Index = bars.findIndex((i) => i.position == 1);
      // 优先显示广告icon
      if (iconAd3) {
        const linkIndex = bars.findIndex((i) => i.position == 3);
        if (linkIndex > -1) {
          bars.splice(linkIndex, 1, iconAd3);
        } else {
          bars.push(iconAd3);
        }
      }
      if (iconAd2) {
        bars.splice(bars.length / 2, 0, iconAd2);
      }
      // 优先显示业务icon
      if (pos1Index <= -1 && iconAd1) {
        bars.unshift(iconAd1);
      }
      _this.setState({
        bars,
      });
      resolve(bars);
    });
  });
};

export const handleTaskFinished = () => {
  Taro.navigator({
    url: 'order/kxj/center',
  });
};

export const getTopBgAd = () => {
  const _this = getPage();
  return new Promise((resolve) => {
    getAdConfig({ position: '41' }, true).then((res) => {
      res = formatAdResponse(res, { key: 'topBgAd' });
      _this.setState(
        {
          topBgAd: res,
        },
        () => {
          // 增加缓入动效
          _this.animation_topAd.height(281).step();
          _this.setState({
            topBgAdAnimationData: _this.animation_topAd.export(),
          });
          let n = res && res.count && res.count > 0 ? res.count : 0;
          _this.setState({
            topBgAdCount: n,
            topBgAdIsOpen: true,
          });
          if (n <= 1) return;
          _this.topBgAdTimer && clearInterval(_this.topBgAdTimer);
          _this.topBgAdTimer = setInterval(() => {
            n--;
            _this.setState({
              topBgAdCount: n,
            });
            if (n <= 0) {
              clearInterval(_this.topBgAdTimer);
              _this.animation_topAd.height(0).step();
              _this.setState({
                topBgAdAnimationData: _this.animation_topAd.export(),
                topBgAdIsOpen: false,
              });
            }
          }, 1000);
        },
      );
      resolve(res);
    });
  });
};

export const showXLightAdFull = async () => {
  const key = 'bill_xLight_cp_full';
  // 全屏广告
  const limit = await frequencyLimitByMinute('check', key, 'full');
  console.log('展示全屏广告-limit', limit);
  if (limit) return;
  createInterstitialAd({
    adUnitId: '50_2024071525000126742',
  }).then((interstitialAd) => {
    if (interstitialAd && interstitialAd.openAd) {
      interstitialAd.openAd().then(() => {
        frequencyLimitByMinute('limit', key);
      });
    }
  });
};

export const showXLightAd = async () => {
  const key_full = 'billDetail_xLight_cp_full';
  const key = 'billDetail_xLight_cp2';
  // 全屏广告
  const limit_full = await frequencyLimitByMinute('check', key_full, 'full');
  if (limit_full) return;
  createInterstitialAd({
    adUnitId: '50_2024080825000181000',
    onClose: async () => {
      // 插屏广告
      const limit = await frequencyLimitByMinute('check', key, 'insert');
      if (limit) return;
      createInterstitialAd({
        adUnitId: '50_2024080825000180882',
      }).then((interstitialAd) => {
        if (interstitialAd && interstitialAd.openAd) {
          interstitialAd.openAd().then(() => {
            frequencyLimitByMinute('limit', key);
          });
        }
      });
    },
  }).then((interstitialAd) => {
    if (interstitialAd && interstitialAd.openAd) {
      interstitialAd.openAd().then(() => {
        frequencyLimitByMinute('limit', key_full);
      });
    }
  });
};
