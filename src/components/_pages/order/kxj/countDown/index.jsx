/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { View, Fragment } from '@tarojs/components';
import { activityEndTime } from '../_utils';
import { useCountDown } from './_uitls';
import './index.scss';

const CountDown = () => {
  const { count } = useCountDown({ endTime: activityEndTime });

  const { day, hour, mint, seconds } = count || {};
  return (
    <Fragment>
      <View className='countDown'>
        距活动结束<View className='countDown-block'>{day || '00'}</View>天
        <View className='countDown-block'>{hour || '00'}</View>时
        <View className='countDown-block'>{mint || '00'}</View>分
        <View className='countDown-block'>{seconds || '00'}</View>秒
      </View>
    </Fragment>
  );
};

CountDown.options = {
  addGlobalClass: true,
};

export default CountDown;
