{"useApiMock": true, "apiMockConfig": {"globalOpen": true, "rules": [{"ruleId": "2329f495-9277-4382-bb06-04002e03f6f2", "ruleName": "大货物流-报价单", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/g_wkd/v2/Yj/Order/quotation", "filterId": "7fc83172-42c8-4991-9ee7-e794ae033247", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\": {\n    \"code\":0,\n    \"msg\":\"成功\",\n    \"data\":{\n        \"quotation\":[\n            {\n                \"price\":\"49.96\",\n                \"discount_price\":\"49.96\",\n                \"discount_total_amount\":\"0\",\n                \"f_kg\":30,\n                \"s_kg\":1,\n                \"f_fee\":\"49\",\n                \"s_fee\":\"0.96\",\n                \"s_total_fee\":\"0.96\",\n                \"discount_list\":[\n\n                ],\n                \"original_f_fee\":\"0\",\n                \"original_s_fee\":\"0\",\n                \"original_price\":\"0\",\n                \"original_s_total_fee\":\"0\",\n                \"service_price\":[\n                    {\n                        \"service_code\":\"pickup_way\",\n                        \"service_name\":\"上楼费\",\n                        \"amount\":\"50.00\"\n                    },\n                    {\n                        \"service_code\":\"warehousing\",\n                        \"service_name\":\"入仓费\",\n                        \"amount\":\"10.00\"\n                    },\n                    {\n                        \"service_code\":\"warehousingServiceFee\",\n                        \"service_name\":\"仓储费\",\n                        \"amount\":\"20.00\"\n                    },\n                    {\n                        \"service_code\":\"teshuquyu_fee\",\n                        \"service_name\":\"特殊区域费用\",\n                        \"amount\":\"15.00\"\n                    },\n                    {\n                        \"service_code\":\"lanshouchaoqu\",\n                        \"service_name\":\"揽收超区费\",\n                        \"amount\":\"10.00\"\n                    },\n                    {\n                        \"service_code\":\"cqf_fee\",\n                        \"service_name\":\"派送超区费\",\n                        \"amount\":\"25.00\"\n                    }\n                ],\n                \"brand\":\"ht\",\n                \"available\":1,\n                \"unavailable_msg\":\"\"\n            }\n        ],\n        \"is_jd_sort\":0\n    }\n},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "6a67a8b7-355b-4e0f-9746-a0d57795c716", "ruleName": "大货物流-品牌配置", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/g_wkd/v2/Yj/Order/channelDecisionCondition", "filterId": "97583ecc-ca1e-4bd4-8eec-ab566b2dbd74", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\": {\n    \"code\":0,\n    \"msg\":\"成功\",\n    \"data\":[\n        {\n            \"need_volume\":\"false\",\n            \"package_num_limit\":\"99\",\n            \"min_weight\":\"0.000\",\n            \"max_weight\":0,\n            \"protect_price_text\":\"保价金额2000以内免保价费，超过2000的按0.2%费率计保价费，上限3万，超过3万按3万保价\",\n            \"service_info_List\":[\n                {\n                    \"is_show\":\"false\",\n                    \"service_code\":\"specialAreaFee\",\n                    \"service_name\":\"特殊区域费\"\n                },\n                {\n                    \"is_show\":\"true\",\n                    \"service_code\":\"pickup_way\",\n                    \"service_name\":\"上楼费\"\n                },\n                {\n                    \"is_show\":\"false\",\n                    \"service_code\":\"warehousingServiceFee\",\n                    \"service_name\":\"入仓服务费\"\n                },\n                {\n                    \"is_show\":\"false\",\n                    \"service_code\":\"backSignBillValueFee\",\n                    \"service_name\":\"回单费\"\n                }\n            ],\n            \"channel\":\"htzfb\"\n        }\n    ]\n},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "c8c4e1aa-2cc4-43a2-9f6a-eebb1c9c4180", "ruleName": "快递员是否vip", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/v1/WeApp/courierIsVip", "filterId": "049295d4-48d2-4988-9978-43a9dff84019", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\": {\n    \"code\":0,\n    \"msg\":\"成功\",\n    \"data\":{\n        \"isVip\":0\n    }\n},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "bf3f6e8c-c656-452d-85ee-b7ed5069e1d8", "ruleName": "寄快递-是否开通支付分", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/g_order_core/v2/mina/Payment/getWxPayServiceStatus", "filterId": "e0a1597b-d361-4d7f-b82f-7899e7dead52", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\": {\n    \"code\":0,\n    \"msg\":\"成功\",\n    \"data\":{\n        \"serviceStatus\":true\n    }\n},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "afea7e2c-7289-46d0-aff1-0befe5dc5b10", "ruleName": "领取优惠券", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/g_wkd/v1/WeApp/createCustomCoupon", "filterId": "634f8884-8d93-4111-9685-1d76714e4daa", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\": {\n    \"code\":0,\n    \"msg\":\"成功\",\n    \"data\":{\n        \"id\":\"5929226\",\n        \"cost\":\"1.00\",\n        \"received_num\":1\n    }\n},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "7bad8c26-20e9-4028-850c-f2eaea7b34d2", "ruleName": "寄快递-提交优寄订单", "apiName": "request", "enable": true, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/g_wkd/v2/Yj/Order/submit", "filterId": "5046d466-2b3e-4857-8f0a-396ee55207b3", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\": {\n    \"code\":0,\n    \"msg\":\"成功\",\n    \"data\":{\n        \"grab_id\":0,\n        \"order_id\":\"706153671709649\",\n        \"wx_after_pay\":1,\n        \"wx_after_pay_bind\":1\n    }\n},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "5da5d21d-fe67-403c-ae10-37b383247fe3", "ruleName": "寄快递-报价单", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/g_wkd/v2/Yj/Order/quotation", "filterId": "00833193-3f4b-4215-bc29-8bbb37c6f3d3", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\": {\n    \"code\":0,\n    \"msg\":\"成功\",\n    \"data\":{\n        \"quotation\":[\n            {\n                \"price\":\"9.5\",\n                \"discount_price\":\"9.5\",\n                \"discount_total_amount\":\"0\",\n                \"f_kg\":1,\n                \"s_kg\":0,\n                \"f_fee\":\"9.5\",\n                \"s_fee\":\"3.5\",\n                \"s_total_fee\":\"0\",\n                \"discount_list\":[\n\n                ],\n                \"original_f_fee\":\"9.5\",\n                \"original_s_fee\":\"3.5\",\n                \"original_price\":\"9.5\",\n                \"original_s_total_fee\":\"0\",\n                \"order_type\":\"zfb\",\n                \"brand\":\"yt\",\n                \"available\":1,\n                \"unavailable_msg\":\"\"\n            },\n            {\n                \"price\":\"10.79\",\n                \"discount_price\":\"10.79\",\n                \"discount_total_amount\":\"0\",\n                \"f_kg\":1,\n                \"s_kg\":0,\n                \"f_fee\":\"10.79\",\n                \"s_fee\":\"4.15\",\n                \"s_total_fee\":\"0\",\n                \"discount_list\":[\n\n                ],\n                \"original_f_fee\":\"0\",\n                \"original_s_fee\":\"0\",\n                \"original_price\":\"0\",\n                \"original_s_total_fee\":\"0\",\n                \"brand\":\"dp\",\n                \"available\":1,\n                \"unavailable_msg\":\"\"\n            },\n            {\n                \"price\":\"11\",\n                \"discount_price\":\"11\",\n                \"discount_total_amount\":\"0\",\n                \"f_kg\":1,\n                \"s_kg\":0,\n                \"f_fee\":\"11\",\n                \"s_fee\":\"5\",\n                \"s_total_fee\":\"0\",\n                \"discount_list\":[\n\n                ],\n                \"original_f_fee\":\"12\",\n                \"original_s_fee\":\"8\",\n                \"original_price\":\"12\",\n                \"original_s_total_fee\":\"0\",\n                \"brand\":\"sto\",\n                \"available\":1,\n                \"unavailable_msg\":\"\"\n            },\n            {\n                \"price\":\"11\",\n                \"discount_price\":\"11\",\n                \"discount_total_amount\":\"0\",\n                \"f_kg\":1,\n                \"s_kg\":0,\n                \"f_fee\":\"11\",\n                \"s_fee\":\"3.7\",\n                \"s_total_fee\":\"0\",\n                \"discount_list\":[\n\n                ],\n                \"original_f_fee\":\"0\",\n                \"original_s_fee\":\"0\",\n                \"original_price\":\"0\",\n                \"original_s_total_fee\":\"0\",\n                \"brand\":\"jt\",\n                \"available\":1,\n                \"unavailable_msg\":\"\"\n            },\n            {\n                \"price\":\"12\",\n                \"discount_price\":\"12\",\n                \"discount_total_amount\":\"0\",\n                \"f_kg\":1,\n                \"s_kg\":0,\n                \"f_fee\":\"12\",\n                \"s_fee\":\"6\",\n                \"s_total_fee\":\"0\",\n                \"discount_list\":[\n\n                ],\n                \"original_f_fee\":\"0\",\n                \"original_s_fee\":\"0\",\n                \"original_price\":\"0\",\n                \"original_s_total_fee\":\"0\",\n                \"brand\":\"zt\",\n                \"available\":1,\n                \"unavailable_msg\":\"\"\n            },\n            {\n                \"price\":\"12\",\n                \"discount_price\":\"12\",\n                \"discount_total_amount\":\"0\",\n                \"f_kg\":1,\n                \"s_kg\":0,\n                \"f_fee\":\"12\",\n                \"s_fee\":\"4.5\",\n                \"s_total_fee\":\"0\",\n                \"discount_list\":[\n\n                ],\n                \"original_f_fee\":\"16\",\n                \"original_s_fee\":\"6\",\n                \"original_price\":\"16\",\n                \"original_s_total_fee\":\"0\",\n                \"brand\":\"jd\",\n                \"available\":1,\n                \"unavailable_msg\":\"\"\n            },\n            {\n                \"price\":\"12\",\n                \"discount_price\":\"12\",\n                \"discount_total_amount\":\"0\",\n                \"f_kg\":1,\n                \"s_kg\":0,\n                \"f_fee\":\"12\",\n                \"s_fee\":\"8\",\n                \"s_total_fee\":\"0\",\n                \"discount_list\":[\n\n                ],\n                \"original_f_fee\":\"12\",\n                \"original_s_fee\":\"8\",\n                \"original_price\":\"12\",\n                \"original_s_total_fee\":\"0\",\n                \"brand\":\"yd\",\n                \"available\":1,\n                \"unavailable_msg\":\"\"\n            },\n            {\n                \"price\":\"17.85\",\n                \"discount_price\":\"17.85\",\n                \"discount_total_amount\":\"0\",\n                \"f_kg\":1,\n                \"s_kg\":0,\n                \"f_fee\":\"17.85\",\n                \"s_fee\":\"8.5\",\n                \"s_total_fee\":\"0\",\n                \"discount_list\":[\n\n                ],\n                \"original_f_fee\":\"21\",\n                \"original_s_fee\":\"10\",\n                \"original_price\":\"21\",\n                \"original_s_total_fee\":\"0\",\n                \"brand\":\"ems\",\n                \"available\":1,\n                \"unavailable_msg\":\"\"\n            },\n            {\n                \"price\":\"18\",\n                \"discount_price\":\"18\",\n                \"discount_total_amount\":\"0\",\n                \"f_kg\":1,\n                \"s_kg\":0,\n                \"f_fee\":\"18\",\n                \"s_fee\":\"6\",\n                \"s_total_fee\":\"0\",\n                \"discount_list\":[\n\n                ],\n                \"original_f_fee\":\"0\",\n                \"original_s_fee\":\"0\",\n                \"original_price\":\"0\",\n                \"original_s_total_fee\":\"0\",\n                \"brand\":\"sf\"\n            },\n            {\n                \"brand\":\"cnsd\",\n                \"available\":0,\n                \"unavailable_msg\":\"暂不支持该地址的收寄服务，请选择其它快递品牌下单，谢谢支持\"\n            }\n        ],\n        \"is_jd_sort\":0\n    }\n},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "04c3d1d1-073c-4563-85d8-3a8af04f6fb8", "ruleName": "双节活动-已获得券列表", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/g_order_core/v2/WkdCoupon/getReceiveCouponList", "filterId": "e766ddae-e3de-4cf5-99aa-8437cc8f427d", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "template", "manual": {"succ": {"resStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    \"code\":0,\n    \"msg\":\"success\",\n    \"data\":[\n        {\n            \"id\":\"44\",\n            \"coupon_id\":\"5\",\n            \"user_id\":\"1950941\",\n            \"coupon_num\":\"1\",\n            \"expiration_time\":\"2023-10-20 23:59:59\",\n            \"create_at\":\"2023-09-26 17:44:25\",\n            \"update_at\":\"2023-09-26 17:44:25\",\n            \"coupon_info\":{\n                \"coupon_id\":\"5\",\n                \"activity\":\"new_customer\",\n                \"brand\":\"\",\n                \"brand_name\":\"\",\n                \"rate\":\"3.00\",\n                \"rate_type\":\"subtract\",\n                \"max_discount_money\":\"0.00\",\n                \"rebate\":\"0.00\",\n                \"rebate_type\":\"\",\n                \"pay_method\":\"2\",\n                \"min_freight\":\"0.00\",\n                \"max_freight\":\"0.00\",\n                \"receive_num\":\"1\",\n                \"expiration_time\":\"2023-10-20 23:59:59\",\n                \"expiration_type\":\"time\",\n                \"status\":\"1\",\n                \"create_at\":\"2023-09-21 15:42:51\",\n                \"update_at\":\"2023-09-26 14:51:54\",\n                \"coupon_end_time\":\"2023-10-20 23:59:59\",\n                \"coupon_start_time\":\"2023-09-28 00:00:00\",\n                \"note\":\"微快递小程序 新用户专享\",\n                \"card_name\":\"无门槛直减券\",\n                \"desc\":[\n                  \"无门槛直减券\"\n                ],\n            }\n        }\n    ]\n},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "ba3a99d9-e007-448f-b1b1-fc5c8e327c92", "ruleName": "双节活动-详情", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/g_order_core/v2/activity/Activity/checkIsParticipateActivity", "filterId": "161e890c-00ea-4947-a3e7-7c39fe995be5", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\": {\n    \"code\": 20230921,\n    \"msg\": \"活动还未开始！\",\n    \"data\": {}\n  },\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": {\n    \"code\":0,\n    \"msg\":\"成功\",\n    \"data\":{\n        \"is_new\":1,\n        \"is_receive\":0,\n        \"is_vip\":0,\n        \"is_use\":0,\n        \"coupon_nums\":1800,\n        \"order_nums\":0,\n    }\n},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}, {"ruleId": "79ea1671-4331-48ce-85b2-21d648f9e5f9", "ruleName": "扫码领券-支付券", "apiName": "request", "enable": false, "verifyFail": false, "filterList": [{"propName": "url", "propRegString": "/g_wkd/v1/WeApp/getCustomCouponInfo", "filterId": "c0d9e997-690e-49b0-9b32-7b425872ec11", "matchType": "regExp"}], "returnConfig": {"returnType": "succ", "generateType": "manual", "manual": {"succ": {"resStr": "{\n  \"data\": {\n    \"code\":0,\n    \"msg\":\"成功\",\n    \"data\":{\n        \"id\":\"1290\",\n        \"kb_id\":\"2997\",\n        \"status\":\"1\",\n        \"coupon_min\":\"0.98\",\n        \"coupon_max\":\"0.99\",\n        \"min_time\":\"2023-6-5\",\n        \"max_time\":\"2023-12-31\",\n        \"total\":\"95\",\n        \"name\":\"嘻嘻嘻\",\n        \"qc_path\":\"http:\\/\\/upload.kuaidihelp.com\\/qrcode\\/quadrate\\/wkd\\/customcoupon\\/2023\\/06\\/05\\/wkd9880731290.png\",\n        \"qrcode_content\":\"https:\\/\\/kbydy.cn\\/ii?coupon_id=1290&phone=13511111111&index_shop_id=889\",\n        \"brand\":\"postx,sf\",\n        \"times_page\":\"1\",\n        \"coupon_price\":\"0.01\",\n        \"coupon_random_price\":\"0.98\"\n    }\n},\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"resStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}, "template": {"succ": {"templateStr": "{\n  \"data\": \"\",\n  \"statusCode\": \"\",\n  \"header\": \"\"\n}"}, "fail": {"templateStr": "{\n  \"errMsg\": \"request:fail 填写错误信息\"\n}"}}}}]}}