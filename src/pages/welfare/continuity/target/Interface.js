/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

const Interface = {
  getInitData: {
    wkd: '/g_wkd/v2/activity/Sign/signDetails',
    yz: '/api/weixin/mini/user/SignIn/signDetails',
  },
  SignBtn: {
    wkd: '/g_wkd/v2/activity/Sign/signIn',
    yz: '/api/weixin/mini/user/SignIn/signRedAward',
  },
  Mail: '/pages/order/edit/index',
  wellet: '/pages-3/pages/user/wallet/index',
};

export default Interface;
