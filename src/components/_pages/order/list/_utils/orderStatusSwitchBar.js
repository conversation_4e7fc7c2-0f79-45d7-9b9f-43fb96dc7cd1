import { useMemo, useState } from '@tarojs/taro';

// 默认订单额外筛选参数
export const orderExtraInfoDefaultType = 'all';

/**
 *
 * @description 根据传入的type确定订单额外筛选参数
 */
export function getOrderExtraInfo(itemType = orderExtraInfoDefaultType) {
  return {
    wait_pay: itemType === 'needpay' ? 1 : 0,
    inprogress: itemType === 'inprogress' ? 1 : 0,
  };
}

/**
 *
 * @description 是否允许展示订单额外参数切换组件
 * @param {*} type
 * @param {*} itemType
 * @returns
 */
export function checkOrderTabsItemsShow(type, itemType) {
  if (process.env.MODE_ENV === 'wkd') {
    const enableTypes = ['send', 'receive'];
    if (type === 'all' && enableTypes.includes(itemType)) return true;
  }
  return false;
}

/**
 *
 * @description 创建订单额外筛选参数切换组件选项
 * @param {*} type
 * @param {*} itemType
 * @returns
 */
export const createOrderTabsItems = (type, itemType) => {
  if (process.env.MODE_ENV === 'wkd') {
    const isShow = checkOrderTabsItemsShow(type, itemType);
    if (!isShow) return null;
    const items = [
      {
        label: '全部',
        key: 'all',
      },
      {
        label: '进行中',
        key: 'inprogress',
      },
      {
        label: '待支付',
        key: 'needpay',
        unable: 'receive',
      },
    ];
    return items.filter(({ unable }) => !unable || unable !== itemType);
  }
  return null;
};

/**
 *
 * @description 合并订单额外筛选默认参数
 */
export function useOrderStatusSwitch(type, itemType, searchData) {
  // 切换状态tab
  const [statusSearchData, setStatusSearchData] = useState(null);
  const [value, setValue] = useState(orderExtraInfoDefaultType);
  const onChange = (v) => {
    setValue(v);
    setStatusSearchData({
      extra_info: getOrderExtraInfo(v),
      courier_id: '', // 清空从下单拦截弹窗带来的数据
    });
  };

  const data = useMemo(() => {
    const isShow = checkOrderTabsItemsShow(type, itemType);
    if (!isShow) return searchData;

    const { extra_info: newExtraInfo, ...restStatusSearchData } = statusSearchData || {};
    const { extra_info = getOrderExtraInfo(orderExtraInfoDefaultType), ...restSearchData } =
      searchData || {};

    return {
      extra_info: {
        ...extra_info,
        ...newExtraInfo,
      },
      ...restSearchData,
      ...restStatusSearchData,
    };
  }, [type, itemType, searchData, statusSearchData]);

  return {
    onChange,
    data,
    value,
  };
}
