/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import KbModal from '@base/components/modal';
import { Image, Text, View } from '@tarojs/components';
import { useEffect, useMemo, useState } from '@tarojs/taro';
import { AtButton } from 'taro-ui';
import { useOpenVipService } from '../_uitls';
import './index.scss';

const RenewManageModal = (props) => {
  const { open = false, userVipData = {}, card, onOpen, onFail } = props;
  const { expires_time, expires_time_format } = userVipData || {};
  const [isOpened, setIsOpened] = useState(false);

  const { open: handleOpen } = useOpenVipService({
    onSuccess: () => {
      setIsOpened(false);
      if (onOpen) {
        onOpen();
      }
    },
    onFail: (...arg) => {
      if (onFail) {
        onFail(...arg);
      }
    },
  });

  useEffect(() => {
    setIsOpened(open);
  }, [open]);

  const handleClose = () => {
    setIsOpened(false);
  };

  const residueDays = useMemo(() => {
    if (!expires_time) return 0;
    const nowTs = new Date().valueOf();
    const expiresTs = new Date(expires_time).valueOf();
    return Math.floor((expiresTs - nowTs) / (1000 * 60 * 60 * 24));
  }, [expires_time]);

  return (
    <KbModal
      isOpened={isOpened}
      top={false}
      onClose={handleClose}
      onCancel={handleClose}
      confirmText=''
      full
    >
      <View className='kb-renewVipModal-content'>
        <View className='kb-renewVipModal-head'>
          <View className='kb-renewVipModal-head--bg' />
          <View className='card'>
            <View className='card-time'>有效期至{expires_time_format}</View>
            <View className='card-desc'>
              {residueDays || 0}天后到期<Text className='card-tips'>（暂未开通自动续费）</Text>
            </View>
          </View>
        </View>
        <View className='kb-renewVipModal-desc'>
          尊敬的用户，<Text className='kb-color__brand'>开通</Text>自动续费功能将为您
          <Text className='kb-color__brand'>自动续费当前套餐</Text>
          ，如需单独购买或升级当前套餐，请点 击查看更多套餐
        </View>
        <View className='kb-vipManage-card--list'>
          <View className='kb-vipManage-card--item'>
            <Image
              className='img'
              mode='widthFix'
              src='https://cdn-img.kuaidihelp.com/wkd/miniApp/vip2/icon_exyh.png'
            />
            <View className='title'>特享优惠</View>
            <View className='desc'>持续更划算</View>
          </View>
          <View className='kb-vipManage-card--item'>
            <Image
              className='img'
              mode='widthFix'
              src='https://cdn-img.kuaidihelp.com/wkd/miniApp/vip2/icon_ssqx.png'
            />
            <View className='title'>随时取消</View>
            <View className='desc'>便捷管理</View>
          </View>
          <View className='kb-vipManage-card--item'>
            <Image
              className='img'
              mode='widthFix'
              src='https://cdn-img.kuaidihelp.com/wkd/miniApp/vip2/icon_ssgx.png'
            />
            <View className='title'>省时高效</View>
            <View className='desc'>到期自动续费</View>
          </View>
        </View>
        <View className='kb-renewVipModal-options'>
          <View className='btn'>
            <AtButton type='secondary' circle size='small' onClick={handleClose}>
              查看更多套餐
            </AtButton>
          </View>
          <View className='btn'>
            <AtButton type='primary' circle size='small' onClick={() => handleOpen({ card })}>
              开通自动续费
            </AtButton>
          </View>
        </View>
      </View>
    </KbModal>
  );
};

RenewManageModal.options = {
  addGlobalClass: true,
};

export default RenewManageModal;
