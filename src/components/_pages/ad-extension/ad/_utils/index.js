/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { useState, useEffect } from '@tarojs/taro';
import gpsSDK from '@base/utils/gps';
import { setGpsInfo } from '@/actions/gps';
import { useSelector, useDispatch } from '@tarojs/redux';
import idsMap, { cityAdMap } from '../../mapAdId';
import { useDidShowCom } from '@base/hooks/page';

let cityUnitIdLock = false;
const useCityUnitId = (opt = {}) => {
  const { adUnitIdIndex } = opt;
  const { city: citys, ids } = cityAdMap[adUnitIdIndex] || {};
  const [cityUnitIds, setCityUnitIds] = useState('');
  const [isReportGps, setIsReportGps] = useState(false);
  const dispatch = useDispatch();
  const { gpsInfo } = useSelector((state) => state.global);

  useDidShowCom(() => {
    console.log('useDidShowCom===>');
    setIsReportGps(!!(Taro.wxAdSdk && Taro.wxAdSdk.reportLocationLock));
  });

  useEffect(() => {
    if (citys && citys.length > 0 && ids && !cityUnitIdLock) {
      if (Taro.wxAdSdk && Taro.wxAdSdk.reportLocationLock) return;
      cityUnitIdLock = true;
      // console.log('进行定位===>');
      gpsSDK({
        toastLoading: false,
        toastSuccess: false,
        toastError: false,
      })
        .then((res) => {
          // console.log('定位结果===>', res);
          cityUnitIdLock = false;
          dispatch(setGpsInfo(res));
        })
        .catch(() => {
          cityUnitIdLock = false;
        });
    }
  }, [citys, ids]);

  useEffect(() => {
    if (citys && citys.length > 0 && ids) {
      if (Taro.wxAdSdk && Taro.wxAdSdk.reportLocationLock) {
        setCityUnitIds(ids);
      } else if (gpsInfo && gpsInfo.city) {
        const { city = '' } = gpsInfo || {};
        // console.log('定位的城市==>', city, gpsInfo);
        const index = citys.findIndex((i) => city.includes(i) || i.includes(city));
        if (city && index > -1) {
          // console.log('广告筛选符合定位城市==>', city, index);
          setCityUnitIds(ids);
        }
      }
    }
  }, [citys, ids, gpsInfo, isReportGps]);

  return {
    cityUnitIds,
    setCityUnitIds,
  };
};

// 获取广告位id
export const useKbAdExtensionAdId = (opt = {}) => {
  const { adUnitIdIndex } = opt;
  const { cityUnitIds } = useCityUnitId(opt);
  const unitIdAndType = idsMap[adUnitIdIndex] || '';

  const [unitIds, setUnitIds] = useState('');

  useEffect(() => {
    // 未获取到位置信息前也要先展示原本广告
    const _unitIds = cityUnitIds || unitIdAndType;
    if (_unitIds) {
      setUnitIds(_unitIds);
    }
  }, [unitIdAndType, cityUnitIds]);

  return {
    unitIds,
    setUnitIds,
  };
};
