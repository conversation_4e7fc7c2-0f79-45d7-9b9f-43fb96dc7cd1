/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import KbPage from '@base/components/page';
import { View, Image } from '@tarojs/components';
import { Component } from '@tarojs/taro';
import './index.scss';

class Index extends Component {
  static config = {
    navigationBarTitleText: '超值精选',
    navigationBarBackgroundColor: '#ffffff',
    usingComponents: {
      feeds: 'plugin://xlightPlugin/feeds',
    },
  };
  constructor() {}

  render() {
    return (
      <KbPage>
        <View>
          <View>
            <Image
              mode='widthFix'
              className='kb-boutique__image'
              src='https://cdn-img.kuaidihelp.com/miniapp/miniapp_yz/welfare/banner.png'
            />
          </View>
          {/* eslint-disable-next-line */}
          <feeds spaceCode='27_2023091822700063984' />
        </View>
      </KbPage>
    );
  }
}
Index.options = {
  addGlobalClass: true,
};
export default Index;
