/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { keepTwo } from '@/components/_pages/welfare/_utils/';
import { getVipDataInfo } from '@/components/_pages/welfare/_utils/index';
import { requestPayment } from '@/utils/qy';
import { previewImage } from '@base/utils/patch-native';
import request from '@base/utils/request';
import { compareVersion, reportAnalytics } from '@base/utils/utils';
import Taro from '@tarojs/taro';
import { requestAd } from '@/components/_pages/ad-extension/sdk';
import Interface from './Interface';

const { getInitData, SignBtn, Mail, wellet } = Interface;
const evn = process.env.MODE_ENV;
const days = ['一', '二', '三', '四', '五', '六', '七'];
const methods = {
  dataInit() {
    const rowsData = Array.from({ length: 7 }).map((iitem, iindex) => {
      return {
        id: iindex,
        day: `第${iindex + 1}天`,
        bDay: `第${days[iindex]}天`,
        type: 1,
        sign_type: false,
        urlType: (iindex + 1) % 7 == 4 ? 1 : (iindex + 1) % 7 == 0 ? 2 : 0,
      };
    });
    this.setState({
      sign_day: rowsData,
    });
  },

  handleLottery() {
    const { subscribe_status, gzh_subscriber } = this.state;
    const type = {
      0: 'subscribe',
      1: 'unSubscribe',
    };
    reportAnalytics({
      key: 'welfare.lotterys',
      title: '福利提醒开关',
    });
    if (subscribe_status == '0') {
      gzh_subscriber === '0'
        ? previewImage({
            urls: ['https://cdn-img.kuaidihelp.com/wkd/gzh_wkd_continuity.png'],
            success: () => this.setSubscribeOperate(type[subscribe_status]),
          })
        : this.setSubscribeOperate(type[subscribe_status]);
    } else {
      Taro.showModal({
        cancelText: '确认关闭',
        cancelColor: '#8e8e8e',
        confirmText: '暂不关闭',
        confirmColor: '#fd7a54',
        title: '关闭提醒',
        content: '可能会错过惊喜红包哦~',
        success: (res) => {
          if (res.cancel) this.setSubscribeOperate(type[subscribe_status]);
        },
      });
    }
  },

  setSubscribeOperate(type) {
    request({
      url: '/g_wkd/v2/activity/VxLottery/subscribeOperate',
      data: { type },
    }).then(() => this.getInitData());
  },

  getInitData() {
    request({
      url: getInitData[evn],
      toastLoading: false,
      mastHasMobile: false,
    }).then((res) => {
      this.signType(res);
    });
  },

  getVipInfo() {
    getVipDataInfo().then((data) => {
      this.setState({
        vipInfo: data,
      });
    });
  },

  signType(res) {
    // "cret":"2021-04-20", // 今天或者昨天,不等于今天就是可签到
    //   "create_time":"2021-04-20 17:25:58",
    //   "consecutive_days":"1", //连续签到时间
    //   "money":"0.00"
    const { sign_day } = this.state;
    const { data, code } = res.data.data ? res.data : res;
    const cret = data && data.cret ? data.cret : '2021-04-20',
      consecutive_days = data && data.consecutive_days ? data.consecutive_days : '0';
    const newDate = new Date();
    let newTime =
      newDate.getFullYear() +
      '-' +
      keepTwo(newDate.getMonth() + 1) +
      '-' +
      keepTwo(newDate.getDate());
    if (code == 0) {
      this.setState({
        day: consecutive_days % 7,
        subscribe_status: data.subscribe_status || '0',
        gzh_subscriber: data.gzh_subscriber.toString() || '0',
        signDetails: data,
      });
      if (consecutive_days) {
        for (let i = 0; i < 7; i++) {
          let consecutiveDay = 0;
          consecutiveDay =
            consecutive_days >= 7 ? consecutive_days * 1 + 1 - (consecutive_days - i) : 7 - (6 - i);
          sign_day[i] = {
            id: i,
            day: '第' + consecutiveDay + '天',
            bDay: `第${days[i]}天`,
            sign_type: consecutiveDay - 1 < consecutive_days % 7,
            urlType: consecutiveDay % 7 == 4 ? 1 : consecutiveDay % 7 == 0 ? 2 : 0,
          };
          this.setState({
            disabled: cret == newTime,
            sign_day,
          });
        }
      }
    }
  },

  SignBtn() {
    // if (show) {
    //   this.setState({
    //     isOpened: true,
    //     modalName: "关注"
    //   });
    //   return;
    // }
    request({
      url: SignBtn[evn],
      toastLoading: false,
      mastHasMobile: false,
    }).then((res) => {
      const { code, data } = res.data.data ? res.data : res;
      const { day } = this.state;
      const days = day * 1 + 1;
      if (code == 0) {
        //根据签到日期获取签到红包数额
        let popupMoney = data.money
          ? data.money
          : days % 7 == 4
          ? 0.04
          : days % 7 == 0
          ? 0.07
          : 0.01;
        const { vipInfo: { status: vipStatus = 0 } = {} } = this.state;
        if (this.edition === 'weapp_wkd' && vipStatus != 1) {
          this.setState({
            popupMoney,
            isOpenedSaveModal: true,
          });
        } else {
          this.setState({
            popupMoney,
            modalName: '签到成功',
            isOpened: true,
          });
        }

        reportAnalytics({
          key: 'welfare.lotterys',
          title: `${vipStatus != 1 ? '非' : ''}会员签到弹窗-展示`,
        });
        this.getInitData();
      } else {
        Taro.kbToast({
          text: res.msg || res.data.msg,
        });
      }
    });
  },

  onCloseCurtain() {
    this.setState({
      isOpened: false,
      modalName: '',
    });
  },

  onSwitchAgree() {
    const { agreementStatus } = this.state;
    this.setState({
      agreementStatus: !agreementStatus,
    });
  },

  onCloseSaveModal() {
    this.setState({
      isOpenedSaveModal: false,
      modalName: '',
    });
  },

  onPay() {
    const { agreementStatus, vipInfo: { vipInfoId } = {} } = this.state;
    if (!agreementStatus) {
      Taro.kbToast({
        text: '请先阅读并同意会员服务协议',
      });
      return;
    }
    const text = '非会员签到弹窗';
    reportAnalytics({
      key: 'welfare.lotterys',
      title: `${text}-点击立即开通`,
    });

    request({
      url: '/g_wkd/v2/memberCard/MemberCard/memberCardSign',
      data: {
        card_id: vipInfoId,
        open_channel: 'sign',
      },
      toastError: true,
      onThen: ({ code, data }) => {
        if (code == 0) {
          requestPayment(data)
            .then(() => {
              this.setState({
                isOpened: false,
                isOpenedSaveModal: false,
              });
              reportAnalytics({
                key: 'welfare.lotterys',
                title: `${text}-付款成功`,
              });
              reportAnalytics({
                key: 'welfare.lotterys',
                title: `会员日总开通数`,
              });
              Taro.navigateTo({
                url: wellet,
              });
            })
            .catch(() => {
              // Taro.kbToast({
              //   text: err.message
              // });
            });
        }
      },
    });
  },

  onJump() {
    if (this.edition === 'weapp_wkd') {
      Taro.navigateTo({
        url: wellet,
      });
    } else {
      Taro.switchTab({
        url: Mail,
        success: () => {
          this.onCloseCurtain();
          Taro.kbUpdateRelationInfo({
            brandId: 'sto',
          });
        },
      });
    }
  },

  onReachBottom() {
    this.setState({
      loadMoreTime: (this.state.loadMoreTime || 0) + 1,
    });
  },

  checkFollowCb(e) {
    // e.detail对象里会有followed字段，可以用来判断关注状态
    // console.log("call back data: ", e);
    const { followed } = e.detail;

    // 把checkFollow、show都置为false
    this.setState({
      checkFollow: false,
      show: false,
    });

    // 如果没有关注的话，展示组件
    if (!followed) {
      setTimeout(() => {
        this.setState({
          show: true,
        });
      });
    }
  },
  closeCb(e) {
    // console.log("关闭关注浮层时 触发回调: ", e);
    const { followed } = e.detail;
    this.setState({
      show: true,
      isOpened: false,
      modalName: '',
    });
    if (followed) {
      setTimeout(() => {
        this.setState(
          {
            show: false,
          },
          () => {
            methods.SignBtn.call(this);
          },
        );
      });
    }
  },
  onCallBackActivity(e) {
    const { finish, money, message, taskLoading } = e || {};
    if (!taskLoading) {
      this.setState({
        taskLoading,
      });
    }
    if (finish) {
      this.setState({
        popupMoney: money,
        isOpened: true,
        modalName: '集集星球',
      });
    } else if (message) {
      Taro.kbToast({
        text: message,
      });
    }
  },
  loadWelfareAd() {
    const version = wx.getSystemInfoSync().SDKVersion;
    if (compareVersion(version, '2.19.6') >= 0) {
      this.setState({
        welfareAdShow: true,
      });
    }
  },

  //微快递每日抽奖模块⬇
  getEveryDayList(nextPage) {
    const { allTab } = this.state;
    const { everyday } = allTab;
    request({
      url: '/g_wkd/v2/activity/VxLottery/mailList',
      toastLoading: true,
      data: {
        page: 1,
        page_size: 7,
        show_num: 1,
        order_by: 'lottery_time_asc',
      },
    }).then((res) => {
      const { data = {}, code } = res;
      if (code == 0) {
        const { list = [] } = data;
        const hasNextPage = !!list.length;
        const activityList = everyday.page === 1 ? list : [...everyday.activityList, ...list];
        const page = hasNextPage && nextPage ? everyday.page + 1 : everyday.page;
        this.setState({
          allTab: {
            ...allTab,
            everyday: {
              ...everyday,
              page,
              activityList: this.customList(activityList),
              hasNextPage,
            },
          },
        });
      }
    });
  },

  getMyList(nextPage) {
    const { allTab, currentTab } = this.state;
    const { page: triggerPage, activityList: currentList } = allTab[currentTab];
    request({
      url: '/g_wkd/v2/activity/VxLottery/myList',
      toastLoading: true,
      data: {
        lottery_status: currentTab,
        page: triggerPage,
        page_zise: '7',
      },
    }).then((res) => {
      const { data, code } = res;
      if (code == 0) {
        const hasNextPage = !!data.lList.length;
        const activityList = triggerPage === 1 ? data.lList : [...currentList, ...data.lList];
        const page = hasNextPage && nextPage ? triggerPage + 1 : triggerPage;
        const currentData = {
          ...allTab,
          [currentTab]: {
            ...allTab[currentTab],
            page,
            activityList: this.customList(activityList),
            hasNextPage,
          },
        };
        this.setState({
          allTab: {
            ...currentData,
          },
        });
      }
    });
  },

  handleCard(activityId, isLottery) {
    if (activityId) {
      const { allTab, currentTab } = this.state;
      this.setState({
        allTab: {
          ...allTab,
          [currentTab]: {
            ...allTab[currentTab],
            activityList: [],
            page: 1,
          },
        },
      });
      Taro.navigateTo({
        url: `/pages-0/pages/welfare/lotterys/details?activityId=${activityId}&isLottery=${isLottery}`,
      });
    }
  },

  switchTab(triggerTab) {
    const { currentTab, allTab } = this.state;
    if (currentTab !== triggerTab) {
      this.setState(
        {
          currentTab: triggerTab,
          allTab: {
            ...allTab,
            [triggerTab]: {
              ...allTab[triggerTab],
              activityList: [],
              page: 1,
            },
          },
        },
        () => {
          if (triggerTab !== 'everyday') {
            this.getMyList(triggerTab);
          } else {
            this.getEveryDayList();
          }
        },
      );
    }
  },

  goEveryDay() {
    this.setState({
      currentTab: 'everyday',
      allList: this.state.everyDayList,
    });
  },

  customList(data) {
    return data.map((item) => ({
      ...item,
      img: item.img || '',
      custom: item.img,
    }));
  },

  getBannerList() {
    requestAd({ position: '23' }, true).then((data) => {
      this.setState({
        bannerList: (Array.isArray(data) ? data : []).map((item) => ({
          ...item,
          type: 'banner',
        })),
      });
    });
  },
  getIconAreaList() {
    requestAd({ position: '24' }, true).then((data) => {
      this.setState({
        iconArea: (Array.isArray(data) ? data : []).map((item) => ({
          ...item,
          type: 'icon',
        })),
      });
    });
  },
  getHotTopicList() {
    requestAd({ position: '25' }, true).then((data) => {
      this.setState({
        hotTopicList: (Array.isArray(data) ? data : []).map((item) => ({
          ...item,
          type: 'hotTopic',
        })),
      });
    });
  },
};

export default methods;
