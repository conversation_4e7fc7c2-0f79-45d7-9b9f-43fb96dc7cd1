/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro from '@tarojs/taro';
import { calculate } from '@/components/_pages/order/extra-info/_utils';
import { getPage } from '@base/utils/utils';
import isUndefined from 'lodash/isUndefined';
import { getProPrice } from '@/components/_pages/order/_utils/order.edit.dh';
import { transferWkdAddress } from '../../../_utils';

// 计算达达保价费
export const getDadaMoney = (InputMoney, cost) => {
  let money = InputMoney;
  if (money < 100) {
    money = 100;
  } else {
    money = money % 100 > 0 ? Math.floor(money / 100) * 100 + 100 : money;
  }
  let costMoney = Math.ceil(calculate(money, cost));
  return costMoney;
};

// 计算德邦保价费
export const getDpProPriceMoney = (InputMoney, cost) => {
  if (InputMoney > 0 && InputMoney <= 300) {
    return 1;
  }
  let costMoney = calculate(InputMoney, cost);
  return Math.ceil(costMoney);
};

// 计算保价费
export const getProPriceMoney = (InputMoney, cost) => {
  if (InputMoney > 0 && InputMoney <= 500) {
    return 1;
  } else if (InputMoney > 500 && InputMoney <= 1000) {
    return 2;
  }
  let costMoney = calculate(InputMoney, cost);
  return Math.ceil(costMoney);
};

// 综合分类计算保价费用
export const calculateProPrice = (opt = {}) => {
  const { value, cost, relationType, brand, product_code } = opt;
  let costMoney = 0,
    InputMoney = value * 1;
  let brandArr = ['sfky'];
  if (relationType == 'brand') {
    costMoney =
      brand === 'dp' ? getDpProPriceMoney(InputMoney, cost) : getProPriceMoney(InputMoney, cost);
    if (brandArr.includes(brand)) {
      if (brand == 'sfky' && product_code == 'SE0100' && costMoney < 1) {
        //顺丰-重货包裹
        costMoney = 1;
      } else if (brand == 'sfky' && product_code == 'SE0101' && costMoney < 10) {
        //顺丰-小票零担
        costMoney = 10;
      }
    }
  } else if (relationType == 'tcjs' && brand == 'dada') {
    costMoney = getDadaMoney(InputMoney, cost);
  } else {
    costMoney = calculate(InputMoney, cost);
    if (costMoney > 0 && costMoney < 1) {
      //保价费用小于1元，按1元处理;
      costMoney = 1;
    }
  }
  return costMoney;
};

/**
 * 用户更新物品价值后，计算保价费
 * @param {*} value 声明的物品价值
 */
export const _calculateProPriceAfterUpdate = (opt = {}) => {
  const _this = getPage();
  const {
    value,
    checked,
    cost,
    pageSource = '',
    address,
    brand,
    relationType,
    extraInfo = {},
    delivery_type,
  } = opt || {};
  const { goods_weight, product_code, volume, service = {} } = extraInfo;
  const { back_sign_bill, package_service, pickup_way, floor } = service;
  return new Promise(async (resolve) => {
    let cost_value;
    if (value && value * 1 > 0 && _this.curValue != value) {
      _this.curValue = value;
      if (checked) {
        if (process.env.MODE_ENV == 'wkd') {
          if (pageSource == 'dh') {
            const addrData = transferWkdAddress(address);
            cost_value = await getProPrice({
              addrData,
              weight: goods_weight,
              volume,
              brand,
              decVal: value,
              back_sign_bill,
              package_service,
              pickup_way,
              floor,
              delivery_type,
            });
          } else {
            cost_value = calculateProPrice({
              value,
              cost,
              relationType,
              brand,
              product_code,
            });
          }
        } else {
          cost_value = calculate(value, cost);
        }
      } else {
        cost_value = 0;
      }
    }
    resolve(cost_value);
  });
};

/**
 * 更新页面保价费、保价开启开关等显示UI
 * @param {*} opt
 */
export const _updateProPriceUI = (opt) => {
  const { value, cost_value, checked, updateData } = opt || {};
  const _this = getPage();
  if (value && value * 1 > 0) {
    _this.curValue2 = value;
  }
  if (!isUndefined(cost_value) && updateData.cost_value !== cost_value) {
    _this.formIns.update({
      cost_value,
      rate_checked: value === 0 ? false : checked,
    });
  } else if (isUndefined(cost_value) && value === 0 && _this.curValue2 != value) {
    _this.curValue2 = value;
    _this.formIns.update({
      cost_value: 0,
      rate_checked: false,
    });
  }
};

/**
 * 处理输入的物品价值，上限和下限边界问题
 * @param {*} opt
 */
export const _dealInputKeepValueLimit = (opt = {}) => {
  let { value, proPriceStart, proPriceEnd, checked } = opt;
  const _this = getPage();
  if (value > parseFloat(proPriceStart || 0) && value <= parseFloat(proPriceEnd)) {
    checked = true;
  } else {
    if (value >= proPriceEnd) {
      checked = true;
      value = proPriceEnd;
      // 同步更新输入框中的值，防止当超出范围后，没有显示最大值
      _this.formIns.update({
        keep_account: proPriceEnd * 1,
      });
      if (value > proPriceEnd) {
        Taro.kbToast({
          text: `物品价值超过最大限制${proPriceEnd}元，请重新录入`,
        });
      }
    }
  }
  return {
    value,
    checked,
  };
};

export const getDeclarationList = (opt = {}) => {
  const { showProPriceDec, pageSource, proPriceStart, proPriceEnd, isDeclared, declaration } = opt;
  if (pageSource === 'dh') {
    return [];
  }
  let list = showProPriceDec
    ? [
        `根据驿站/快递员/网点声明每笔订单交寄物品价值在 ${parseFloat(
          proPriceStart || 0,
        )}元~${parseFloat(proPriceEnd || 0)}元必须保价，低于 ${parseFloat(
          proPriceStart || 0,
        )}元非必须保价（价值区间是或字段，根据快递员/驿站/网点设置的）`,
        '请按物品实际价值来填写物品价值，我方页面只做下单参考，具体业务请咨询驿站/快递员/驿站/网点',
        '物品是否属于可保价范围请咨询上门驿站/快递员/网点服务人员，我方与驿站/快递员/网点没有从属关系，不对其服务做任何担保和背书',
        '请仔细与驿站/快递员/网点明确服务内容，并当面与驿站/快递员/网点签订增值服务协议且索要服务依据',
      ]
    : [];
  if (isDeclared && declaration) {
    list = [declaration, ...list];
  }
  return list;
};
