/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro from '@tarojs/taro';
import { jsonParse2, makePhoneCall } from '@base/utils/utils';
import isArray from 'lodash/isArray';

/**
 *
 * @description 格式化子列数据
 * @param {*} arr
 * @returns
 */
function formatChildren(arr) {
  const list = [];
  arr.map((item) => {
    const { row, col } = item;
    const rI = row - 1;
    const cI = col - 1;
    list[rI] = list[rI] || [];
    list[rI][cI] = item;
  });
  return list;
}

/**
 *
 * @description 格式化快递柜数据
 * @param {*} data
 * @returns
 */
export const formatResponseKdg = (data) => {
  const { config, ...rest } = data || {};
  let list = jsonParse2(config) || [];

  if (isArray(list) && list.length > 0) {
    const { standard, row: totalRow, col: totalCol } = rest;
    const n = totalCol / standard; //等分列数
    const singleBodyNumber = n * totalRow;
    const groups = [];
    let groupsBy = [];
    switch (`${standard}`) {
      case '3': // 一拖二
        groupsBy = [
          { slice: [0, singleBodyNumber] },
          {
            slice: [singleBodyNumber, -1 * singleBodyNumber],
            type: 'primary',
          },
          { slice: [-1 * singleBodyNumber] },
        ];
        break;
      case '2': // 一拖一
        groupsBy = [
          { slice: [singleBodyNumber], type: 'primary' },
          { slice: [-1 * singleBodyNumber] },
        ];
        break;

      default:
        // 单柜
        groupsBy = [{ slice: [0], type: 'primary' }];
        break;
    }
    groupsBy.map(({ slice, type = 'follow' }) => {
      groups.push({
        type,
        children: formatChildren(list.slice(...slice)),
      });
    });
    return {
      code: 0,
      data: {
        ...rest,
        list: groups,
      },
    };
  }

  return {
    data: void 0,
  };
};

/**
 *
 * @description 联系管理员
 * @param {*} concat_phone
 */
export const contactManager = (concat_phone) => {
  Taro.kbModal({
    content: ['请联系站点代理点客服', concat_phone],
    confirmText: '好的',
    centered: true,
    onConfirm: () => makePhoneCall(concat_phone),
  });
};
