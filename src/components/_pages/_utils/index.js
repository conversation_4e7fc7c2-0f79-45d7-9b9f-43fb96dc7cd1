/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import request from '@base/utils/request';
import { reportAnalytics } from '@base/utils/utils';
import Taro from '@tarojs/taro';
import isArray from 'lodash/isArray';
import isFunction from 'lodash/isFunction';

/**
 *
 * @description 合并列表数据
 * @param {*} current
 * @param {*} list
 */
export const mergeListByTitle = (current, list, callback) => {
  if (!isArray(current)) return list;
  current.map((item) => {
    const formattedItem = isFunction(callback) ? callback(item) : item;
    const { title: itemTitle, list: itemList } = formattedItem;
    const index = list.findIndex(({ title }) => !itemTitle || title === itemTitle);
    if (index >= 0) {
      list[index].list = list[index].list.concat(itemList);
    } else {
      list.push(formattedItem);
    }
  });
  return list;
};

/**
 *
 * @description
 * @param {*} $component
 */
export function compatibleGetCurrentPage($component) {
  if (process.env.PLATFORM_ENV === 'swan') {
    const pages = Taro.getCurrentPages();
    const currentPage = pages[pages.length - 1];
    if (!currentPage) return;
    currentPage.selectComponent = () => ({
      $component,
    });
  }
}

/**
 *
 * @description 记录点击tab
 * @param {*} item
 */
export function tabItemTapCall(item) {
  if (process.env.MODE_ENV === 'wkd') {
    const { options } = item;
    if (options) {
      // 改成自定义tab后生效，其他的暂时屏蔽
      reportAnalytics({
        key: 'tab_click',
        options,
      });
    }
  }
}
export const creatSignature = (params) => {
  return new Promise((resolve, reject) => {
    request({
      url: '/api/dak/ShareDakDetail/saveSign',
      data: { ...params },
      onThen: (_, res) => {
        const { code, msg, data } = res;
        if (code === 0) {
          resolve({ data });
        } else {
          reject({ msg });
        }
      },
    });
  });
};

export const formatShareTime = (time, statue) => {
  return time
    ? decodeURIComponent(time).split('-').slice(1).join('-')
    : statue == 'inTime'
    ? '暂无'
    : '未出库';
};

/**
 * 手机号脱敏
 *  */
export function phoneDesensitization(phone) {
  const pat = /(\d{3})\d*(\d{4})/;
  return `${phone}`.replace(pat, '$1****$2');
}
