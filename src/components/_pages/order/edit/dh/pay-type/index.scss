/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb-payType {
  .kb-nav-item {
    &-label {
      display: flex;
      align-items: center;
    }
    &-value {
      display: flex;
      align-items: center;
      .content {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        max-width: 500px;
        .creditTag {
          display: flex;
          align-items: center;
          font-size: 28px;
        }
      }
      .kb-checkbox {
        margin-bottom: 5px;
        margin-left: 10px;
      }
    }
  }
  &-layout {
    &__list {
      margin: 30px;
    }
    &__item {
      margin-bottom: 20px;
      background: #f9f9f9;
      border: $width-base solid #e6e6e6;
      border-radius: 16px;
      &:last-child {
        margin-bottom: 0;
      }
      &--active {
        background: #f3fbff;
        border-color: $color-brand;
      }
    }
    &__card {
      display: flex;
      align-items: center;
      justify-content: space-between;
      box-sizing: border-box;
      height: 128px;
      padding: 0 30px;
      .title {
        color: #333333;
        font-size: 30px;
      }
      .desc {
        margin-top: 10px;
        color: #999999;
        font-size: 24px;
      }
    }
    &__card2 {
      padding: 0 30px 20px;
      .title {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 90px;
        color: #333333;
        font-size: 30px;
        &::after {
          position: absolute;
          right: 0px;
          bottom: 0;
          left: 0px;
          border-bottom: $width-base solid #e6e6e6;
          content: '';
        }
      }
      .product {
        margin-top: 15px;
        color: #666666;
        font-size: 24px;
      }
      .note {
        margin-top: 5px;
        color: #999999;
        font-size: 22px;
      }
    }
    &__footer {
      margin: 30px;
      .btn {
        display: flex;
        align-items: center;
        justify-content: center;
        box-sizing: border-box;
        height: 88px;
        color: #fff;
        font-size: 34px;
        background: $color-brand;
        border-radius: 44px;
      }
    }
  }
}
