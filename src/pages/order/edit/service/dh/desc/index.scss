/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb-service {
  height: 100%;
  margin: $spacing-h-md;
  margin-top: -10px;
  margin-bottom: 40px;
  padding: $spacing-h-md;
  overflow-y: scroll;
  background-color: $color-white;
  &-teshuquyu {
    height: auto;
    margin-top: $spacing-h-md;
    &__title {
      margin: 10px;
      font-weight: bold;
    }
    &__desc {
      margin: 0 20px 20px;
      color: $color-brand;
      font-size: 26px;
    }
  }
  &-floor {
    box-sizing: border-box;
    height: 100%;
    padding-top: 180px;
    .header {
      position: absolute;
      top: 0;
      right: 0;
      left: 0;
      margin: $spacing-h-md;
      padding: $spacing-h-md;
      background-color: $color-white;
      border-radius: $border-radius-lg;
      .line {
        margin-bottom: 10px;
        font-size: 28px;
      }
    }
  }
  &-block {
    margin-bottom: 10px;
    padding: $spacing-h-md;
    background-color: $color-white;
    border-radius: $border-radius-lg;
    &__title {
      margin-bottom: 10px;
    }
    &__content {
      color: #666666;
      font-size: 26rpx;
      .line {
        margin-top: 10px;
      }
    }
  }
  &-list {
    margin: 0 $spacing-h-md;
    .list-item {
      margin-top: $spacing-h-md;
      padding: $spacing-h-md;
      background: #ffffff;
      border-radius: 10px;
      .title {
        position: relative;
        padding-left: 30px;
        color: #333333;
        font-weight: 500;
        font-size: 30px;
        line-height: 48px;
        &::before {
          position: absolute;
          top: 50%;
          left: 0;
          width: 12px;
          height: 12px;
          border: $width-base solid $color-brand;
          border-radius: 50%;
          transform: translateY(-50%);
          content: '';
        }
      }
      .paragraph {
        padding: 10px 0;
        .imgBox {
          overflow-y: auto;
        }
      }
      .line {
        margin-bottom: 10px;
        padding: 0 30px;
        color: #666666;
        font-weight: 500;
        font-size: 24px;
        line-height: 1.6;
        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
  &-options {
    &-item {
      flex-grow: 1;
    }
  }
  &-footer {
    position: relative;
    margin-top: 10px;
    background-color: $color-white;
  }
}

.at-tabs__header {
  right: $spacing-h-md;
  left: $spacing-h-md;
  width: auto;
}
