import { formatSignDetail } from '@/components/_pages/out/reward/_utils/sign';
import { requestAsync } from '@base/utils/request';

// 获取签到详情
export async function getSignDetails(params) {
  const res = await requestAsync({
    url: '/g_wkd/v2/marketingActivity/ZfbSignReward/getSignDetails',
    data: params,
    toastLoading: false,
  }).then(({ data }) => formatSignDetail(data));
  return res;
}

// 签到
export async function runSign(params) {
  const res = await requestAsync({
    url: '/g_wkd/v2/marketingActivity/ZfbSignReward/sign',
    data: params,
    toastSuccess: true,
    toastError: true,
  });
  return res;
}

// 进入自动送积分
export async function toDayThreeRewardService(params) {
  const res = await requestAsync({
    url: '/g_wkd/v2/marketingActivity/ZfbSignReward/toDayThreeReward',
    data: params,
    toastSuccess: false,
    toastError: false,
  });
  return res;
}
