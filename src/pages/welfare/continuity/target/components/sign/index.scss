.kb-sign {
  &__package {
    display: flex;
    gap: 10px;
    align-items: center;
    justify-content: space-between;
    padding-top: 110px;
    padding-right: 20px;
    padding-left: 20px;
    &__item {
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 80px;
      height: 124px;
      border: $width-base solid #fcf4e7;
      &__imageContainer {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 60px;
        height: 60px;
      }
      &__image {
        width: 60px;
        height: 60px;
        &__today {
          width: 60px;
          height: 60px;
        }
        &__sign {
          width: 40px;
          height: 40px;
        }
      }
      &__desc {
        margin-top: 20px;
        color: #b17866;
        font-size: 18px;
      }
      &__badge {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 28px;
        color: #fff;
        font-size: 16px;
        line-height: 28px;
        text-align: center;
        background: #ff5624;
        border-radius: 14px 14px 14px 0;
        transform: translateY(-100%);
      }
    }
  }
}

.package_rotate {
  animation: rotateLeftRight 1s infinite;
}

@keyframes rotateLeftRight {
  0% {
    transform: rotate(20deg);
  }

  50% {
    transform: rotate(-20deg);
  }

  100% {
    transform: rotate(20deg);
  }
}
