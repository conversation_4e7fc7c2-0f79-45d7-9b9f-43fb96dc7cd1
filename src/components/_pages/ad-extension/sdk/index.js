/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { getStorage, noop, now, setStorage } from '@base/utils/utils';
import { getAccountInfoSync, setClipboardData, buriedPoint } from '@/utils/qy';
import { formatParamsOut } from '@base/components/_utils/index';
import request from '@base/utils/request';
import rules, { check } from '@base/utils/rules';
import Taro from '@tarojs/taro';
import isArray from 'lodash/isArray';
import isObject from 'lodash/isObject';
import isFunction from 'lodash/isFunction';
import idsMap, { adIdsMap } from '../mapAdId';
import { getAdConfig, _reportAnalytics as reportAnalytics, _getPage as getPage } from '../_utils';
import { checkIsShieldAd } from './shield';
import TuiaSDKLite from './tuia';
import { createInterstitialAd } from '../ad/intersitialAd';

/**
 *
 * @description 汇总上报数据统计
 * @param {*} status
 * @param {*} type
 * @param {*} opts
 */
function triggerReportAnalyticsByType(status, type, opts) {
  const typesMap = {
    interstitial: {
      load: 'cpad_onload',
      show: 'cpad_errormsg',
    },
    rewardedVideo: {
      error: 'rewarded_video_ad',
      show: 'rewarded_video_ad',
    },
  };
  const key = typesMap[type][status];
  if (key) {
    reportAnalytics({
      key,
      ...opts,
    });
  }
}
/**
 * @description 生成激励视屏或者插屏广告
 * @param {*} opts
 * @returns
 */
const adInsMap = {};
function getCurrentPagePath() {
  const { route: path = '' } = getPage(-1, false);
  return path;
}
export const AD_STORAGE_KEY = 'AD';
let dynamicAdMaps = null;
export const getCityConfigAdId = (key, _, patchAdIdsMap) => {
  const noDataError = new Error(`未获取到对应广告位：${key}`);
  return new Promise((resolve, reject) => {
    const config = { ...adIdsMap, ...patchAdIdsMap }[key];
    if (!process.env.MODE_ENV.includes('third')) {
      if (config) {
        resolve(config);
      } else {
        reject(noDataError);
      }
    } else if (dynamicAdMaps) {
      if (dynamicAdMaps[key]) {
        resolve(
          formatParamsOut({
            data: dynamicAdMaps[key],
            keys: [
              ['pop_up', 'id'],
              ['banner', 'banner'],
              ['type', () => 'interstitial'], //默认此项
            ],
          }),
        );
      } else {
        reject(noDataError);
      }
    } else {
      if (process.env.MODE_ENV === 'wkd') {
        reject(noDataError);
      } else {
        request({
          url: '/api/weixin/mini/Ad/showFlowAd',
          mastHasMobile: false,
          onThen: (res) => {
            const { code, data } = res || {};
            if (code === 0 && data) {
              dynamicAdMaps = formatParamsOut({
                data,
                keys: [
                  ['check_piece_page', 'query'],
                  ['order_details_page', 'order.detail'],
                  ['logistics_details_page', 'query.detail'],
                  ['subscribe_get_page', 'query.appointment'],
                  ['order_submit_page', 'order.result'],
                  ['stage_home_page', 'order.station'],
                ],
              });
              if (dynamicAdMaps[key]) {
                resolve(
                  formatParamsOut({
                    data: dynamicAdMaps[key],
                    keys: [
                      ['pop_up', 'id'],
                      ['banner', 'banner'],
                      ['type', () => 'interstitial'], //默认此项
                    ],
                  }),
                );
              } else {
                reject(noDataError);
              }
            } else {
              reject(noDataError);
            }
          },
        });
      }
    }
  });
};

// 插屏广告包装
export async function createInterstitialAdWrap(callback, closeShield) {
  const shield = await checkIsShieldAd(closeShield ? '-1' : '1');
  if (!shield && isFunction(callback)) {
    callback();
  }
  return shield;
}

/**
 *
 * 重要变更：兼容支付宝，微信同步时需要进一步验证
 * @param {*} opts
 * closeShield 关闭屏蔽检查，即无论如何允许广告展示
 * patchAdIdsMap 可用于补充动态的广告配置
 * max 当前声明周期内最大次数，冷启动清空
 * MIN_TS 用于控制显示延迟时间
 * onClose 主要用于判断视频广告是否正常退出
 * enableCloseMinTime 结合onClose,show,灵活控制是否达到领取条件
 * filter 灵活控制广告展示
 * @param {*} param1
 * @returns
 */
export function createAd(
  opts,
  {
    closeShield,
    patchAdIdsMap,
    max = 1,
    filter,
    MIN_TS = 10,
    onClose,
    onError,
    enableCloseMinTime = -1,
  } = {},
) {
  return new Promise((resolve, reject) => {
    createInterstitialAdWrap(() => {
      const { ad } = Taro.launchParams || {};
      const filterType = ['lotteryTask'].includes(opts);
      if (!filterType && (ad == 0 || ad == 1)) {
        reject();
        return;
      }
      // status === 'load' 仅做预加载处理
      const [key, status = 'show'] = opts.split('-');
      getCityConfigAdId(key, 'interstitial', patchAdIdsMap)
        .then(({ id: adUnitId, type }) => {
          console.log('key', key, 'interstitial', adUnitId);
          if (!adUnitId) return reject();
          const adUnitIdKey = `${adUnitId}-${key}`;
          const storageKey = `${AD_STORAGE_KEY}_${adUnitIdKey}`;
          const defaultConfig = {
            adIns: null,
            actionRef: {},
            path: '',
          };
          if (filterType) {
            adInsMap[adUnitIdKey] = defaultConfig;
          } else {
            adInsMap[adUnitIdKey] = adInsMap[adUnitIdKey] || defaultConfig;
          }

          let { actionRef, adIns } = adInsMap[adUnitIdKey];
          if (!adIns) {
            switch (type) {
              case 'rewardedVideo':
                adIns = Taro.createRewardedVideoAd({ adUnitId });
                break;
              case 'interstitial':
                adIns = Taro.createInterstitialAd({ adUnitId });
              default:
                break;
            }

            actionRef.count = 0;
            adInsMap[adUnitIdKey].adIns = adIns;
            adInsMap[adUnitIdKey].path = getCurrentPagePath();

            // 广告加载成功
            adIns.onLoad((opts) => actionRef.onLoad(opts));
            // 广告加载失败
            adIns.onError((opts) => actionRef.onError(opts));
            // 广告关闭
            adIns.onClose((opts) => actionRef.onClose(opts));
          }
          // 广告加载成功
          actionRef.onLoad = () => {
            actionRef.loaded = true;
            if (status === 'load') {
              triggerReportAnalyticsByType('load', type);
              // 预加载处理
              resolve();
            } else if (actionRef.triggerShow) {
              actionRef.triggerShow();
            }
          };
          // 广告加载失败
          actionRef.onError = ({ message, errCode, errMsg = message }) => {
            // 激励红包拉取失败统计
            triggerReportAnalyticsByType('error', type, {
              err: `${errCode} ${errMsg}`,
            });
            onError && onError(errMsg);
            reject(new Error(`${errMsg}，请稍后重试`));
          };
          // 广告关闭
          actionRef.onClose = (res) => {
            let { isEnded = false, type: closeType } = res || {};
            // 非激励广告，才会根据时间判断
            if (type !== 'rewardedVideo' && !isEnded && enableCloseMinTime > 0) {
              const timeIsReach = now() - (actionRef.showTime || now()) >= enableCloseMinTime;
              // 到达指定时长，或者点击跳转关闭的，都可以触发奖励
              if (timeIsReach || closeType === 'JUMP') {
                // 允许完成，可发奖励
                isEnded = true;
              }
            }

            onClose && onClose(isEnded, res);
            // 以下已经无效
            // if (isEnded) {
            //   resolve();
            // } else {
            //   reject(new Error(closeErrorMsg));
            // }
          };
          // 触发展示
          actionRef.triggerShow = (outTs) => {
            if (!filter && actionRef.count >= max) return;
            if (!actionRef.loaded) {
              // 未加载
              adIns
                .load()
                .then(() => {
                  actionRef.onLoad();
                })
                .catch((err) => {
                  actionRef.onError(err);
                });
              return;
            }
            getStorage({
              key: storageKey,
              complete: (res) => {
                const { ts: STORAGE_TS = 0, data } = res.data || {};
                const NOW_TS = now();
                const APP_TS = Taro.APP_TS || NOW_TS;
                const OUT_TS = outTs || MIN_TS;
                const delayTimer = Math.max(OUT_TS - (NOW_TS - APP_TS), MIN_TS);
                if (
                  filter &&
                  filter({
                    data,
                    NOW_TS,
                    APP_TS,
                    OUT_TS,
                    STORAGE_TS,
                  })
                )
                  return;
                actionRef.delay && clearTimeout(actionRef.delay);
                actionRef.delay = setTimeout(() => {
                  if (getCurrentPagePath() !== adInsMap[adUnitIdKey].path) {
                    return;
                  }
                  adIns
                    .show()
                    .then(() => {
                      actionRef.showTime = now(); // 广告展示时间
                      resolve();
                      actionRef.loaded = false; // 保证下次调用重新拉取
                      actionRef.count++;
                      setStorage({
                        key: storageKey,
                        data: {
                          count: actionRef.count,
                        },
                      });
                    })
                    .catch(({ message, errMsg = message }) => {
                      if (!outTs) {
                        actionRef.triggerShow(15000);
                        return;
                      }
                      triggerReportAnalyticsByType('show', type, {
                        errormsg: errMsg,
                        err: '激励视频 广告显示失败',
                      });
                      reject(new Error(errMsg));
                    });
                }, delayTimer);
              },
            }).catch(noop);
          };

          if (status === 'load') return;
          actionRef.triggerShow();
        })
        .catch((err) => {
          console.error(err.message);
        });
    }, closeShield);
  });
}

function getThirdAd(opts) {
  const { app_id, adUrl, url = adUrl, ...restOpts } = opts;
  const { miniProgram: { appId = '' } = {} } = getAccountInfoSync();
  const toastIns = Taro.kbToast({ status: 'loading' });
  const { userInfo } = Taro.kbLoginData || {};
  const { openid } = userInfo || {};

  const triggerReport = (to) => {
    reportAnalytics({
      ...restOpts.report,
      options: `三方拉取${to ? '成功' : '失败'}`,
      source: to,
    });
  };

  const triggerNavigator = (to) => {
    if (to) {
      adNavigator({
        ...restOpts,
        adUrl: to,
      });
    } else {
      const safeUrl = url.split(',').slice(1).join(',');
      if (!safeUrl) {
        Taro.kbToast({ text: '暂无信息' });
      } else {
        adNavigator({
          ...restOpts,
          adUrl: safeUrl,
        });
      }
    }
    triggerReport(to);
  };

  Taro.request({
    url: 'https://miniu.bypanghu.xyz/api/chGetTask',
    data: {
      uid: openid,
      ch: 'mw03',
      minch: appId,
      num: '1',
    },
    success: (res) => {
      let { data } = res.data || {};
      const url = (isArray(data) && data[0]) || '';
      triggerNavigator(url);
    },
    fail: () => triggerNavigator(),
    complete: () => {
      toastIns.close();
    },
  });
}

// 点击广告，领取奖励
function triggerReward(opts) {
  if (process.env.PLATFORM_ENV === 'alipay' && process.env.MODE_ENV === 'wkd') {
    const positions = ['57'];
    console.log('triggerReward', opts);
    if (positions.includes(opts && opts.position)) {
      request({
        url: '/v1/WeApp/collectReward',
        toastLoading: false,
      });
    }
  }
}

export const autoSubscribeKey = 'autoSubscribeKey';
export const handleSetAutoSubscribe = (data) => {
  if (process.env.PLATFORM_ENV === 'alipay' && process.env.MODE_ENV === 'wkd') {
    const { remark = '', adUrl, url } = data;
    if (remark.indexOf('subscribe') > -1) {
      let delay = 0;
      const match = remark.match(/delay_(\d+)/);
      if (match && match[1]) {
        delay = parseInt(match[1]) * 1000;
      }
      const action = remark.split('#')[1].split(',');
      let path = adUrl || url;
      path = path.replace(/\/index$/, '');
      setStorage({
        key: autoSubscribeKey,
        data: {
          action,
          delay,
          path,
        },
      });
    }
  }
};

/**
 * @description 广告导航器
 */
export function adNavigator(opts, _target = 'blank') {
  let {
    adUrl,
    ad_url = adUrl,
    url = ad_url,
    remark,
    options,
    app_id: appId,
    report,
    target = _target || 'blank',
    ...rest
  } = opts;

  // handleSetAutoSubscribe(opts);
  triggerReward(opts);

  if (process.env.PLATFORM_ENV === 'alipay') {
    buriedPoint({
      ...(Taro.getLaunchOptionsSync() || {}),
      action: 'click',
      report,
    });
  }

  /**
   *
   * @description 补充广告通用上报统计
   * position：广告位置
   * title：广告标题
   * status：copy-复制
   * 1、调用Taro.navigator
   * 上报时会补充 - status：click-点击|success-跳转成功|fail-跳转失败、message:错误信息；
   * 2、调用getThirdAd
   * 上报时会补充 - options: `三方拉取${to ? '成功' : '失败'}`,source: to；
   */
  const {
    key: reportKey,
    title: reportTitle,
    position: reportPosition,
    ...restReport
  } = report || {};
  report = {
    ...restReport,
    key: reportKey || 'event_push_ad',
    position: reportPosition || opts.position || 'ad',
    title: reportTitle || opts.title || 'empty',
  };
  opts.report = report;

  //官方插屏广告
  // createInterstitialAd:50_2025051325002045482
  console.log('url', url);
  if (url && /createInterstitialAd:/.test(url)) {
    const [, adUnitId] = url.split(':');
    console.log('插屏广告adUnitId', adUnitId);
    createInterstitialAd({
      adUnitId: adUnitId,
    }).then((interstitialAd) => {
      if (interstitialAd && interstitialAd.openAd) {
        interstitialAd.openAd();
      }
    });
    return;
  }

  if (/copy:/.test(url)) {
    url = url.replace(/copy:/, '');
    reportAnalytics({
      ...report,
      status: 'copy',
    });
    setClipboardData(url, remark);
    return;
  }

  if (url === 'videoAd') {
    /**
     * @todo 激励插屏广告统计，待统一优化
     */
    createAd('0')
      .then(() => {
        // 可以获取奖励
        console.log('跳转奖励领取页面');
      })
      .catch((err) => {
        Taro.kbToast({
          text: err.message,
        });
      });
    return;
  }

  // 推啊广告
  // Tower 任务: 支付宝微快递小程序接入广告SDK ( https://tower.im/teams/258300/todos/103761 )
  if (check('tuiaUrl', url).code === 0) {
    const tuiaPid = url.replace(rules.tuiaUrl.rule, '');
    if (tuiaPid) {
      TuiaSDKLite.execute({
        compatMode: true,
        data: {
          pid: tuiaPid,
        },
        success() {
          reportAnalytics({
            ...report,
            status: 'tuia-success',
          });
        },
        fail() {
          reportAnalytics({
            ...report,
            status: 'tuia-fail',
          });
        },
      });
      return;
    }
  }

  // 第三方广告
  if (check('thirdAd', url).code === 0) {
    getThirdAd(opts);
    return;
  }

  // 广告链接默认为完整链接  suffix 设置为 null
  if (check('path', url).code === 0 || check('pluginUrl', url).code === 0) {
    if (check('path', url).code === 0 && ad_url) {
      url = ad_url;
    }
    if (!url) return;
    // 小程序页面
    Taro.navigator({
      url,
      target,
      options,
      appId,
      report,
      force: true,
      ...rest,
    });
    return;
  }
  if (!url) return;
  Taro.navigator({
    url,
    appId,
    target: 'webview-' + target,
    force: true,
    report,
    suffix: null,
    ...rest,
  });
}

/**
 *
 * @description 预加载广告
 * @param {*} keys
 * @returns
 */
export function preloadAd(keys = Object.keys(idsMap)) {
  if (process.env.MODE_ENV === 'wkd' && process.env.PLATFORM_ENV === 'weapp') {
    if (!wx.preloadAd) return;
    const list = keys
      .map((key) => {
        const [unitId, type = 'banner'] = `${idsMap[key] || ''}`.split('.');
        return unitId ? { unitId, type } : null;
      })
      .filter((item) => item);
    wx.preloadAd(list);
  }
}

/**
 * 拉取admin后台广告
 * opts.type: 对应不同请求接口；
 * @param {any} params;
 * @param {{type:'1'|'2';limit:{key:string;count:number}}|true} opts 默认必须为true，保证通过getAdConfig调用正确接口；
 */
export const loadAdminAd = (params = {}, opts) => {
  const req = isObject(params) ? params : { position: params };
  return new Promise((resolve) => {
    getAdConfig(req, opts || true).then(resolve);
  });
};

/**
 *
 * @description 广告获取
 * @param {*} data
 * @returns
 */
export function requestAd(data, format) {
  return new Promise((resolve) => {
    loadAdminAd(data)
      .then((list) => {
        if (!format) return list;
        return list.map((item) => {
          const { adUrl: ad_url, id, imgUrl, remark, title } = item;
          const [adUrl = ad_url, app_id] = `${ad_url}`.split(',') || [];
          const newItem = {
            ad_url,
            adUrl,
            id,
            imgUrl,
            remark,
            title,
          };
          if (check('miniappID', app_id).code === 0) {
            newItem.app_id = app_id;
          }
          return newItem;
        });
      })
      .then(resolve);
  });
}

/**
 *
 * @description 检查广告位是否展示
 * ad = 0 屏蔽所有类型广告
 * ad = 1 屏蔽所有流量主，插屏等官方相关广告；
 * ad = 2 屏蔽所有自建广告
 * shieldIP: 是否是被屏蔽的ip: 0  不屏蔽 ,1  屏蔽
 */
export function checkAdCanShow(data) {
  return new Promise((resolve) => {
    if (process.env.MODE_ENV === 'wkd') {
      request({
        url: '/g_wkd/v1/WeApp/checkMobileIsBlockAdvertisements',
        data,
        autoRequestAfterLogin: true,
        toastLoading: false,
        timeout: 3000,
        onThen: (res) => {
          const { ad = '-1', shieldIP = '0' } = res.data || {};
          resolve({ ad, isShieldIp: `${shieldIP}` === '1' });
        },
      });
    } else {
      resolve({ ad: -1 });
    }
  });
}

/**
 *
 * @description
 * ad = 0 屏蔽所有类型广告
 * ad = 1 屏蔽所有流量主，插屏等官方相关广告；
 * ad = 2 屏蔽所有自建广告
 * type 当前广告位类型
 * @param {'0'|'1'|'2'} ad
 * @param {'1'|'2'} type
 */
export function checkAdShow(ad, type) {
  const ads = ['0', '1', '2'];
  const ad_ = `${ad}`;
  let type_ = `${type}`;
  type_ = ads.slice(1).includes(type_) ? type_ : '';

  if (!ads.includes(ad_)) return true; // 不在范围内的都可展示；
  if (ad_ === '0' || !type_) return false; // 没传type等同于ad===0
  return ad_ !== type_; // 相等代表要屏蔽
}
