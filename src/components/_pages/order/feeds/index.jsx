/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { useMemo } from '@tarojs/taro';
import { Image, View } from '@tarojs/components';
import { AtTabs, AtTabsPane } from 'taro-ui';
import KbLongList from '@base/components/long-list';
import classNames from 'classnames';
import { createFeedsTabs, createFormatList, useAdFeeds, useAffixTab } from './_utils';
import './index.scss';

const AdFeeds = () => {
  const tabs = createFeedsTabs();
  const { current, list, listData, handleSwitchTab, handleClick } = useAdFeeds();
  const idName = 'kb-adFeeds-tabs';
  const { tabStyles } = useAffixTab({ selector: `#${idName}` });

  const formatList = useMemo(() => createFormatList(list), [list]);
  return (
    <View className='kb-adFeeds'>
      <View id={idName} className='kb-adFeeds-tab--holder'>
        <View className='kb-adFeeds-tab--box' style={tabStyles}>
          <View className='kb-adFeeds-tab'>
            {tabs.map((item, index) => {
              const itemCls = classNames('kb-adFeeds-tab--item', {
                'kb-adFeeds-tab--active': index === current,
              });
              return (
                <View
                  key={item.key}
                  className={itemCls}
                  hoverClass='kb-hover'
                  onClick={handleSwitchTab.bind(null, index)}
                >
                  {item.title}
                </View>
              );
            })}
          </View>
        </View>
      </View>
      <AtTabs
        tabList={tabs}
        className='kb-tabs__hidetab'
        onClick={handleSwitchTab}
        current={current}
        height='auto'
        swipeable={false}
      >
        {tabs.map((item, index) => (
          <AtTabsPane key={item.key} current={current} index={index}>
            <View className='kb-adFeeds-listBox'>
              <KbLongList
                active={current == index}
                data={listData}
                enableMore
                enableRefresh={false}
                height='auto'
              >
                <View className='kb-adFeeds-list'>
                  {formatList &&
                    formatList.length > 0 &&
                    formatList.map((listArr) => {
                      return (
                        <View className='kb-adFeeds-col' key={listArr}>
                          {listArr &&
                            listArr.length > 0 &&
                            listArr.map((listItem) => {
                              return (
                                <View
                                  className='kb-adFeeds-item'
                                  key={listItem.id}
                                  onClick={() => handleClick(listItem)}
                                  hoverClass='kb-hover'
                                >
                                  <Image
                                    className='img'
                                    mode='widthFix'
                                    lazyLoad
                                    showMenuByLongpress
                                    src={listItem.imgUrl}
                                  />
                                </View>
                              );
                            })}
                        </View>
                      );
                    })}
                </View>
              </KbLongList>
            </View>
          </AtTabsPane>
        ))}
      </AtTabs>
    </View>
  );
};

AdFeeds.options = {
  addGlobalClass: true,
};

export default AdFeeds;
