/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { useState } from '@tarojs/taro';
import { View } from '@tarojs/components';
import { AtButton } from 'taro-ui';
import KbLoginAuth from '@base/components/login/auth';
import KbModal from '@base/components/modal';
import KbCreditComponent from '@/components/_pages/order/credit/credit-label';
import { useSelector } from '@tarojs/redux';
import { importFieldHide } from '@base/utils/utils';
import { useOpenVipService } from '../_uitls';
import './index.scss';

const KbOpenVip = (props) => {
  const { userVipData = {}, card = {}, agree, onOpen, onFail } = props;
  const { loginData: { userInfo: { mobile } = {} } = {} } = useSelector((state) => state.global);
  const [isOpened, setIsOpened] = useState(false);
  const { open } = useOpenVipService({
    onSuccess: () => {
      if (onOpen) {
        onOpen();
      }
    },
    onFail: (...arg) => {
      if (onFail) {
        onFail(...arg);
      }
    },
  });

  const confirmOpen = () => {
    const isSeriesVip = userVipData && userVipData.isSeriesVip == 1;
    if (isSeriesVip && card.type == 0) {
      Taro.kbToast({
        text: '如需变更会员权益套餐类型，请先关闭当前进行中的套餐，重新选择购买您心仪的套餐。',
      });
      return;
    }
    if (!agree) {
      Taro.kbToast({
        text: '请先阅读并同意会员服务协议',
      });
      return;
    }
    setIsOpened(true);
  };

  const handleClose = () => {
    setIsOpened(false);
  };

  const handleConfirm = () => {
    setIsOpened(false);
    open({ card });
  };

  return (
    <View>
      {!mobile ? (
        <KbLoginAuth
          useOpenType
          size='normal'
          text={`立即${userVipData.status == 1 ? '续费' : '开通'}`}
          scope='phoneNumber'
          showIcon={false}
          onAuthComplete={confirmOpen}
        />
      ) : (
        <AtButton type='primary' circle onClick={confirmOpen}>
          立即{userVipData.status == 1 ? '续费' : '开通'}
        </AtButton>
      )}
      {card.type == 1 && (
        <View>
          <KbCreditComponent open type='door' />
        </View>
      )}
      <KbModal
        isOpened={isOpened}
        className='kb-vipPayModal'
        top={false}
        onClose={handleClose}
        onCancel={handleClose}
        confirmText=''
        closable={false}
        full
      >
        <View>
          <View className='kb-vipPayModal-title'>
            <View className='title'>当前购买账号</View>
            <View className='phone'>{importFieldHide(mobile, 3, 7)}</View>
          </View>
          <View className='kb-vipPayModal-list'>
            <View className='kb-vipPayModal-item'>
              您的优享寄{card.type == 1 ? `自动续费(${card.level_name})` : card.name}
              将发送到该账号下，一经开通不支持退款、更换等操作
            </View>
            <View className='kb-vipPayModal-item'>
              若需切换账号，请退出该{process.env.PLATFORM_ENV == 'alipay' ? '支付宝' : '微信'}
              账号重新登录后开通
            </View>
          </View>
          <View className='kb-vipPayModal-footer'>
            <View className='kb-vipPayModal-confirm' onClick={handleConfirm} hoverClass='kb-hover'>
              继续购买
            </View>
            <View className='kb-vipPayModal-cancel' onClick={handleClose} hoverClass='kb-hover'>
              我再想想
            </View>
          </View>
        </View>
      </KbModal>
    </View>
  );
};

KbOpenVip.options = {
  addGlobalClass: true,
};

export default KbOpenVip;
