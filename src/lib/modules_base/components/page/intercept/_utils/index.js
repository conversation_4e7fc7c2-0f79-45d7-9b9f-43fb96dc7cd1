import { useEffect, useRef, useState } from "@tarojs/taro";

export function useSwitchShow(actionRef) {
  const ref = useRef({ time: 0, show: true });
  const [show, setShow] = useState(ref.current.show);

  const stop = () => clearTimeout(ref.current.time);

  const run = (s) => {
    ref.current.show = s;
    if (s) {
      setShow(false);
      stop();
      ref.current.time = setTimeout(() => {
        setShow(true);
      }, 0);
    } else {
      setShow(false);
    }
  };

  useEffect(() => {
    return () => {
      stop();
    };
  }, []);

  return {
    show,
    run,
    ref,
  };
}
