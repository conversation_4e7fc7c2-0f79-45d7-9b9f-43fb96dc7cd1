import Taro from '@tarojs/taro';
import CallbacksPool from '@base/utils/callbacksPool';
import { getCourierFromStorage } from '../../store-card/_utils';
import isEmpty from 'lodash/isEmpty';
import { checkCourierIsVip } from '../../order/_utils/courier.detail';

let lock = false;

function triggerConsume(data) {
  callbackIns.trigger(data);
  lock = false;
  try {
    Taro.kbUpdateRelationInfoIsVip(data.isVip);
  } catch (error) {}
}

// 批量检查时，拦截情况，保证只触发一次
const callbackIns = new CallbacksPool();
export function batchCheckCourierIsVip() {
  return new Promise((resolve) => {
    callbackIns.push(resolve);
    if (lock) {
      return;
    }
    lock = true;
    (async () => {
      let { data: relationInfo } = Taro.kbRelationInfo || {};
      if (!relationInfo || isEmpty(relationInfo)) {
        relationInfo = await getCourierFromStorage();
      }
      if (process.env.MODE_ENV === 'wkd') {
        // 检查快递员，更新vip状态
        const { type, courier_id } = relationInfo || {};
        if (type === 'courier' && courier_id) {
          const res = await checkCourierIsVip(courier_id);
          triggerConsume(res);
        } else {
          triggerConsume({ isVip: false });
        }
      } else {
        const { is_vip = false } = relationInfo || {};
        triggerConsume({ isVip: is_vip });
      }
    })();
  });
}
