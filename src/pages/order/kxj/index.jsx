import Taro, { Fragment, useTabItemTap } from '@tarojs/taro';
import { useTabSecondary } from '@/components/_pages/_utils/tab-secondary';
import { tabItemTapCall } from '@/components/_pages/_utils';

const Continuity = () => {
  useTabSecondary({
    pathname: '/pages-1/pages/order/kxj/target/index',
  });

  useTabItemTap(tabItemTapCall);

  return <Fragment />;
};

Continuity.config = {
  navigationBarTitleText: '',
  navigationStyle: 'custom',
};

export default Continuity;
