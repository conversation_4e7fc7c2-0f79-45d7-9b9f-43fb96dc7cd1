/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb-customCoupon {
  &-bgColor {
    background: #fd492c;
  }
  .kb-page__footer-view {
    background: transparent !important;
  }
  &-scrollview {
    height: 100%;
  }
  &-container {
    position: relative;
  }
  &-bg {
    width: 100%;
    .img {
      display: block;
      width: 100%;
    }
  }
  &-coupon {
    position: absolute;
    top: 395px;
    left: 50%;
    width: 100%;
    height: 900px;
    text-align: center;
    transform: translateX(-50%);
    &__tag {
      height: 50px;
      margin-top: 16px;
      color: #cd0100;
      font-weight: bold;
      font-size: 24px;
      line-height: 50px;
    }
    &__reward {
      position: relative;
      display: flex;
      align-items: baseline;
      justify-content: center;
      color: #ff2221;
      .tag {
        position: absolute;
        top: 40px;
        left: -100%;
        height: 32px;
        padding: 0 15px;
        color: #ff2221;
        font-size: 20px;
        line-height: 32px;
        text-align: center;
        background: #ffdbbc;
        border-radius: 8px 8px 0px 8px;
      }
      .unit {
        font-size: 48px;
      }
      .value {
        position: relative;
        font-weight: bold;
        font-size: 140px;
      }
    }
    &__type {
      margin-top: -24px;
      color: #ff2221;
      font-size: 36px;
    }
    &__btn {
      width: 422px;
      height: 96px;
      margin: 120px auto 0;
      .img {
        width: 100%;
        height: 100%;
      }
    }
    &__time {
      margin-top: 30px;
      color: #fffbdc;
      font-weight: bold;
      font-size: 26px;
    }
  }
}
