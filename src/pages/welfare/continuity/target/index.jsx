/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { Component, Fragment } from '@tarojs/taro';
import KbPage from '@base/components/page';
import KbSubscribe from '@base/components/subscribe';
import { AtCurtain } from 'taro-ui';
import { Button, View, ScrollView, Block, Image, Text } from '@tarojs/components';
import { tabItemTapCall } from '@/components/_pages/_utils';
import { getShareAppMessage } from '@/utils/share';
import { getUserStateByComplete } from '@base/utils/utils';
import KbAdExtension from '@/components/_pages/ad-extension';
import methods from './methods';
import './index.scss';

const ad = { position: 8 };
class Index extends Component {
  config = {
    navigationBarTitleText: '福利',
    transparentTitle: 'always',
    usingComponents:
      process.env.PLATFORM_ENV === 'alipay'
        ? {
            feeds: '../../../components/_pages/welfare/feeds/index',
            star: '../../../components/_pages/welfare/jjStar/index',
          }
        : {
            'kb-ad-custom': '../../../components/_pages/welfare/adCustom/index',
          },
  };
  constructor() {
    this.edition = process.env.PLATFORM_ENV + '_' + process.env.MODE_ENV;
    this.signBtnText = this.edition === 'alipay_yz' ? '去寄件' : '查看';
    this.state = {
      popupMoney: 0.01,
      day: 0,
      sign_day: [],
      disabled: false,
      isOpened: false,
      checkFollow: true,
      show: this.edition === 'alipay_yz',
      modalName: '',
      uid: '',
      taskLoading: true, //集集星球
      count: 1, //集集星球&灯火feeds
      welfareAdShow: false,
    };
    for (var i in methods) {
      this[i] = methods[i].bind(this);
    }
  }

  onTabItemTap = tabItemTapCall;

  componentDidMount() {
    this.getInitData();
    this.dataInit(); //初始化数据
    this.loadWelfareAd(); //福利广告
  }

  componentDidShow() {
    this.setState({
      count: this.state.count + 1,
    });
  }

  // 登录状态更新
  onUpdate = (data) => {
    const { userInfo = {} } = data;
    this.setState({
      uid: userInfo.openid,
    });
    if (getUserStateByComplete()) {
      this.getInitData();
    }
  };

  onShareAppMessage = (e) =>
    getShareAppMessage(e, {
      info: {
        action: 'continuity',
      },
      page: 'welfare.center',
    });

  render() {
    const {
      isOpened,
      popupMoney,
      sign_day,
      checkFollow,
      show,
      modalName,
      uid,
      taskLoading,
      count,
      disabled,
      welfareAdShow,
      day,
      ...rest
    } = this.state;
    return (
      <Fragment>
        <KbPage
          {...rest}
          className='kb-continuity-page'
          onUpdate={this.onUpdate}
          renderFooter={
            <AtCurtain
              isOpened={isOpened}
              onClose={this.onCloseCurtain}
              closeBtnPosition='top-right'
            >
              {modalName == '关注' ? (
                show ? (
                  <life-follow
                    className='lifeFollow'
                    sceneId='689a6fe9e2f1419ab5ff8bf0d12605a2'
                    checkFollow={checkFollow}
                    onCheckFollow={this.checkFollowCb}
                    onClose={this.closeCb}
                  />
                ) : (
                  ''
                )
              ) : modalName == '集集星球' ? (
                <Block>
                  <View className='head txt'>恭喜获得现金红包</View>
                  <View className='addMoney txt'>+{popupMoney}元</View>
                  <View className='know txt' onClick={this.onCloseCurtain}>
                    知道了
                  </View>
                  <View className='tip txt'>现金红包记录可至“支付宝-我的-余额”中查看</View>
                </Block>
              ) : modalName == '签到成功' ? (
                <Block>
                  <View className='success txt'>签到成功！</View>
                  <View className='redPackage txt'>恭喜获得现金红包</View>
                  <View className='money txt'>+{popupMoney}元</View>
                  <View className='wallet txt'>现金红包已存入您的钱包</View>
                  <View className='send txt' onClick={this.onJump}>
                    {this.signBtnText}
                  </View>
                </Block>
              ) : (
                ''
              )}

              <Image
                style='width:100%;height:auto'
                mode='widthFix'
                src={
                  modalName == '关注'
                    ? 'https://cdn-img.kuaidihelp.com/miniapp/miniapp_yz/welfare/popup/follow.png'
                    : modalName == '集集星球'
                    ? 'https://cdn-upload.kuaidihelp.com//m_news_server/2021/06/02/ueditors202106021622605094268629.png'
                    : modalName == '签到成功'
                    ? 'https://cdn-img.kuaidihelp.com/miniapp/miniapp_yz/welfare/popup/01.png?v=20211018'
                    : ''
                }
              />
            </AtCurtain>
          }
        >
          <ScrollView scrollY className='kb-scrollview'>
            <Image
              style='width: 100%'
              src='//cdn-img.kuaidihelp.com/miniapp/miniapp_ttzq/background/01.png'
              mode='widthFix'
            />
            {/* 触发checkFollowCb方法 */}
            <View style='display:none'>
              <life-follow
                className='lifeFollow'
                sceneId='689a6fe9e2f1419ab5ff8bf0d12605a2'
                checkFollow={checkFollow}
                onCheckFollow={this.checkFollowCb}
                onClose={this.closeCb}
              />
            </View>
            <KbSubscribe
              type='secondary'
              action='continuity'
              className='yz_continuity'
              onSubscribe={(e) => {
                Taro.kbToast({
                  text: e.code == 0 ? '订阅成功' : '取消订阅',
                });
              }}
            >
              <Image
                className='subsImag'
                src='//cdn-img.kuaidihelp.com/miniapp/miniapp_gift/guide/tips.png'
                mode='widthFix'
              />
            </KbSubscribe>

            <View className='kb-continuity-page__signBox'>
              <View className='sign'>
                <View className='kb-continuity-page__signTitle'>
                  <View className='kb-continuity-page__signNumber'>
                    已连续签到
                    <Text style='color: #f94a2a;padding: 0 8px'> {day} </Text>天
                  </View>
                  <View className='kb-continuity-page__box'>
                    {sign_day.map((item, index) => {
                      return (
                        <View class='kb-continuity-page__signDay'>
                          {
                            <View
                              className={`kb-continuity-page__signDayTitle ${
                                !!item.urlType ? 'kb-continuity-page__signDayHeadImg' : ''
                              }`}
                            >
                              {item.urlType == '1'
                                ? '惊喜红包'
                                : item.urlType == '2'
                                ? '超大红包'
                                : ''}
                            </View>
                          }
                          {item.sign_type ? (
                            <View className='kb-continuity-page__signImg'>
                              {index !== 0 && (
                                <View
                                  className='line red'
                                  style={`${
                                    index == 3 || index == 4 || index == 6 ? 'width: .5rem' : ''
                                  } ${index == 4 && 'left: -.3rem'}`}
                                />
                              )}
                              <View className='kb-continuity-page__signDayImgSuccess' />
                            </View>
                          ) : (
                            <View className='kb-continuity-page__signImg'>
                              {index !== 0 && (
                                <View
                                  className='line'
                                  style={`${
                                    index == 3 || index == 4 || index == 6 ? 'width: .5rem' : ''
                                  }; ${index == 4 && 'left: -.3rem'}`}
                                />
                              )}
                              {!!item.urlType ? (
                                <View className='kb-continuity-page__signImgRed'>
                                  <View className='kb-continuity-page__signDayImg kb-continuity-page__signDayImgs' />
                                </View>
                              ) : (
                                <View className='kb-continuity-page__signImgRed'>
                                  <View className='kb-continuity-page__signDayImg' />
                                </View>
                              )}
                            </View>
                          )}
                          <View>{!!item.sign_type ? '已签到' : item.day}</View>
                        </View>
                      );
                    })}
                  </View>

                  <View className='kb-continuity-page__signBtn'>
                    {disabled ? (
                      <Button
                        disabled
                        className='kb-continuity-page__signBtnBag kb-continuity-page__btnBagGrey'
                      >
                        今日已领取
                      </Button>
                    ) : (
                      <Button
                        hoverClass='view-hover-opacity'
                        hoverStopPropagation
                        onClick={this.SignBtn}
                        className='kb-continuity-page__signBtnBag'
                      >
                        签到领红包
                      </Button>
                    )}
                  </View>
                  <View className='kb-continuity-page__notes'>
                    注：连续签到后面金额越高，一旦漏签要重新开始呦~
                  </View>
                </View>
              </View>
            </View>
            <View className='kb-margin-lg-b kb-spacing-lg-lr'>
              <KbAdExtension data={ad} />
            </View>

            {this.edition === 'alipay_yz' && (
              <Fragment>
                {uid && (
                  <View className='adBox'>
                    <View className='headBox'>
                      <View className='headTitle'>
                        <View className='headLeftImg'>
                          <Image
                            className='img'
                            src='//cdn-img.kuaidihelp.com/miniapp/miniapp_ttzq/continuity/line.png'
                            mode='widthFix'
                          />
                        </View>
                        <View className='headTxt'>属你幸运</View>
                        <View className='headRightImg'>
                          <Image
                            className='img'
                            src='//cdn-img.kuaidihelp.com/miniapp/miniapp_ttzq/continuity/line.png'
                            mode='widthFix'
                          />
                        </View>
                      </View>
                    </View>
                    {/* eslint-disable */}
                    {taskLoading ? (
                      <star
                        uid={uid}
                        channel='10117'
                        onCallBack={this.onCallBackActivity}
                        count={count}
                      />
                    ) : (
                      <View className='noMore'>今天任务已做完，明天再来吧～</View>
                    )}
                    {/* eslint-enable */}
                  </View>
                )}
                {/* eslint-disable */}
                <feeds
                  spaceCode='ad_tiny_2021001187640769_202107142200015127'
                  pageSize={10}
                  count={count}
                />
                {/* eslint-enable */}
              </Fragment>
            )}
          </ScrollView>
        </KbPage>
      </Fragment>
    );
  }
}

export default Index;
