/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import KbButton from '@base/components/button';
import KbCheckbox from '@base/components/checkbox';
import KbPage from '@base/components/page';
import { View, ScrollView, Image } from '@tarojs/components';
import { connect } from '@tarojs/redux';
import Taro, { Component } from '@tarojs/taro';
import { AtButton, AtTabs, AtTabsPane } from 'taro-ui';
import { createDHServiceDesc } from '@/components/_pages/order/edit/dh/service/_utils/desc';
import classNames from 'classnames';
import { serviceIntroductionMap } from '@/components/_pages/order/_utils/order.edit.dh';
import {
  DHFloorMessage,
  teshuquyuMessage,
} from '../../../../../../components/_pages/order/edit/dh/service/_utils';
import './index.scss';

@connect(({ global }) => ({ loginData: global.loginData }))
class Index extends Component {
  static options = {
    addGlobalClass: true,
  };
  static config = {
    navigationBarTitleText: '增值服务',
  };
  constructor(props) {
    super(props);
    this.state = {
      current: 0,
      tabs: [
        { key: 'off', title: '(非床垫)收费标准' },
        { key: 'on', title: '(床垫)收费标准' },
      ],
      agree: true,
      serviceDesc: [],
    };
  }

  componentDidMount() {
    const { title, type, brand } = this.$router.params;
    const oDHServiceDescTitle = serviceIntroductionMap.find((i) => i.value === type) || {};
    if (title || type) {
      Taro.setNavigationBarTitle({
        title: title || oDHServiceDescTitle.label || '增值服务',
      });
    }
    const oDHServiceDesc = createDHServiceDesc({ brand });
    if (type) {
      this.setState({
        serviceDesc: oDHServiceDesc[type] || [],
      });
    }
  }

  handleFjClick = () => {
    const toastIns = Taro.kbToast({ status: 'loading' });
    Taro.downloadFile({
      url: 'https://software.kuaidihelp.com/bsky-cq.xlsx',
    })
      .then(({ tempFilePath }) => {
        toastIns.close();
        Taro.openDocument({
          filePath: tempFilePath,
          success: () => {
            console.log('打开文档成功');
          },
        });
      })
      .catch(({ errorMessage, message = errorMessage }) => {
        toastIns.update({ text: message });
      });
  };

  handlePreview = (item) => {
    Taro.previewImage({
      current: item.img.src,
      urls: [item.img.src],
    });
  };

  handleSwitch = (ev) => {
    this.setState({
      current: ev,
    });
  };

  // 是否同意协议
  handleSwitchAgree = () => {
    const { agree } = this.state;
    this.setState({ agree: !agree });
  };

  // 取消
  handleNavBack = () => {
    Taro.navigator();
  };

  // 确定
  handleSubmitForm = () => {
    const { agree } = this.state;
    if (!agree) {
      Taro.kbToast({
        text: '请阅读并同意《免责声明》',
      });
      return;
    }
    Taro.navigator();
  };

  render() {
    const { current = 0, tabs = [], agree, serviceDesc = [], ...rest } = this.state;
    const { type = 'normal', brand } = this.$router.params;

    return (
      <KbPage
        {...rest}
        renderFooter={
          <View className='kb-spacing-md kb-service-footer'>
            <View className='at-row at-row__align--center kb-spacing-md-lr kb-spacing-md-b'>
              <KbCheckbox
                label='我已阅读并同意'
                checked={agree}
                onChange={this.handleSwitchAgree}
                className='kb-color__black'
              />
              <View>
                <AtButton
                  className='kb-button__link'
                  size='small'
                  onClick={Taro.navigateToDocument.bind(this, 10)}
                >
                  《免责声明》
                </AtButton>
              </View>
            </View>
            <View className='kb-spacing-sm-t  at-row at-row__align--center kb-service-options'>
              <View className='kb-margin-sm-lr kb-service-options-item'>
                <KbButton
                  onClick={this.handleSubmitForm}
                  className='kb-button'
                  circle
                  type='primary'
                >
                  确定
                </KbButton>
              </View>
            </View>
          </View>
        }
      >
        <ScrollView style={{ height: '100%' }} scrollY>
          <View>
            {type === 'teshuquyu_fee' ? (
              <View className='kb-service kb-service-teshuquyu'>
                <View className='kb-service-teshuquyu__title'>特殊区域费-收费标准及明细</View>
                {teshuquyuMessage.map((iitem) => {
                  return (
                    <View className='kb-service-block' key={iitem.title}>
                      <View className='kb-service-block__title'>{iitem.title}</View>
                      <View className='kb-service-block__content'>
                        {iitem.desc.map((i) => {
                          return (
                            <View className='line' key={i}>
                              {i}
                            </View>
                          );
                        })}
                      </View>
                    </View>
                  );
                })}
                <View className='kb-service-teshuquyu__desc' onClick={this.handleFjClick}>
                  点击附件查看详情
                </View>
              </View>
            ) : brand === 'htky' && type === 'pickup_way' ? (
              <View className='kb-service-floor'>
                <View className='header'>
                  <View className='line'>货物 ≤40kg 免费上楼。</View>
                  <View className='line'>货物 ＞40kg 上楼费以物流回传为准。</View>
                </View>
                <AtTabs
                  tabList={tabs}
                  current={current}
                  onClick={this.handleSwitch}
                  swipeable={false}
                >
                  {tabs.map((item) => {
                    return (
                      <AtTabsPane key={item.key}>
                        <View className='kb-service'>
                          {DHFloorMessage &&
                            DHFloorMessage[item.key].map((iitem) => {
                              return (
                                <View className='kb-service-block' key={iitem.title}>
                                  <View className='kb-service-block__title'>{iitem.title}</View>
                                  <View className='kb-service-block__content'>
                                    {iitem.desc.map((i) => {
                                      return (
                                        <View className='line' key={i}>
                                          {i}
                                        </View>
                                      );
                                    })}
                                  </View>
                                </View>
                              );
                            })}
                        </View>
                      </AtTabsPane>
                    );
                  })}
                </AtTabs>
              </View>
            ) : serviceDesc && serviceDesc.length > 0 ? (
              <View className='kb-service-list'>
                {serviceDesc.map((item) => {
                  return (
                    <View className='list-item' key={item.title}>
                      <View className='title'>{item.title}</View>
                      <View className='paragraph'>
                        {item.content && item.content.length > 0
                          ? item.content.map((iitem) => {
                              return (
                                <View key={iitem}>
                                  {iitem.img && (
                                    <View className='imgBox'>
                                      <Image
                                        style={iitem.img.style}
                                        mode='widthFix'
                                        src={iitem.img.src}
                                        onClick={this.handlePreview.bind(this, iitem)}
                                      />
                                    </View>
                                  )}
                                  <View
                                    className={classNames('line', iitem.class)}
                                    style={iitem.style}
                                  >
                                    {iitem.text}
                                  </View>
                                </View>
                              );
                            })
                          : null}
                      </View>
                    </View>
                  );
                })}
              </View>
            ) : null}
          </View>
        </ScrollView>
      </KbPage>
    );
  }
}

export default Index;
