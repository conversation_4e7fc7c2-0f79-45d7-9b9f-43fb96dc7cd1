/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { Component, createRef } from '@tarojs/taro';
import KbPage from '@base/components/page';
import KbReward from '@/components/_pages/out/reward';
import KbRewardTotal from '~/components/_pages/out/reward/reward-total';

class RewardPage extends Component {
  config = {
    navigationBarTitleText: '',
    navigationStyle: 'custom',
    transparentTitle: 'always',
    navigationBarBackgroundColor: '#fff',
  };

  state = {};

  constructor(props) {
    super(props);
    this.rewardRef = createRef();
  }

  componentDidShow() {
    if (this.rewardRef && this.rewardRef.current) {
      if (this.rewardRef.current.onDidShow) {
        this.rewardRef.current.onDidShow();
      }
    }
  }

  updateState = (state = {}) => {
    this.setState({
      ...this.state,
      ...state,
    });
  };

  render() {
    const { signDetail, ...rest } = this.state;
    const { position = '' } = this.$router.params;
    return (
      <KbPage
        {...rest}
        className='kb-rewardPage'
        navProps={{
          forceAlipayLeft: true,
          fixed: true,
          theme: 'ghost',
          exactHome: true,
        }}
        renderNavLeft={<KbRewardTotal signDetail={signDetail} position={position} />}
        closeIosSafeArea
      >
        <KbReward
          ref={this.rewardRef}
          position={position}
          routerParams={this.$router.params}
          updateState={this.updateState}
        />
      </KbPage>
    );
  }
}

export default RewardPage;
