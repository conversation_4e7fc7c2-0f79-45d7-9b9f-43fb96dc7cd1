/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb-renewVipModal {
  &-head {
    position: relative;
    &--bg {
      height: 150px;
      background: $color-brand;
      border-radius: 20px 20px 40px 40px;
    }
    .card {
      width: 580px;
      height: 166px;
      margin: -100px auto 0;
      overflow: hidden;
      text-align: center;
      background: linear-gradient(90deg, #3e4055 0%, #535966 100%);
      border-radius: 10px;
      &-time {
        margin-top: 40px;
        color: #fafafa;
        font-weight: bold;
        font-size: 30px;
      }
      &-desc {
        margin-top: 5px;
        color: #d7bf9e;
        font-weight: bold;
        font-size: 30px;
      }
      &-tips {
        color: #999;
        font-size: 24px;
      }
    }
  }
  &-desc {
    width: 502px;
    height: 95px;
    margin: 30px auto;
    color: #999;
    font-size: 24px;
    line-height: 36px;
    text-align: center;
  }
  &-options {
    display: flex;
    justify-content: center;
    margin-top: 50px;
    font-weight: 500;
    font-size: 30px !important;
    .btn {
      margin: 0 20px;
    }
  }
  &-content {
    .kb-vipManage {
      &-card {
        &--list {
          display: flex;
          align-items: center;
          justify-content: space-around;
          height: 257px;
          margin: $spacing-h-md;
          background: #f2faff;
          border-radius: 8px;
        }
        &--item {
          text-align: center;
          .img {
            width: 80px;
            height: 80px;
          }
          .title {
            color: #333;
            font-size: 26px;
          }
          .desc {
            color: #999;
            font-size: 20px;
          }
        }
      }
    }
  }
}
