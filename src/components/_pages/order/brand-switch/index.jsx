/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { get } from '@/actions/brands';
import { orderAction } from '@/components/_pages/order/_utils/';
import { cancelOrder } from '@/components/_pages/order/_utils/order.action';
import apis from '@/utils/apis';
import KbCheckbox from '@base/components/checkbox';
import KbLongList from '@base/components/long-list';
import KbModal from '@base/components/modal';
import KbNoticeBar from '@base/components/notice-bar';
import { extractData } from '@base/utils/utils';
import { Text, View } from '@tarojs/components';
import { useDispatch, useSelector } from '@tarojs/redux';
import Taro, { useEffect, useState } from '@tarojs/taro';
import isArray from 'lodash/isArray';
import { AtAvatar, AtIcon } from 'taro-ui';
import { getBrandInfo } from '../../brand/_utils';
import { yjbrandMap } from './_utils';
import './index.scss';

const Index = (props) => {
  const { data } = props;
  const [current, updateCurrent] = useState();
  const [isOpened, updateIsOpened] = useState(false);
  const [active, updateActive] = useState(null);
  const [list, updateList] = useState(null);
  const dispatch = useDispatch();
  const { brands } = useSelector((state) => state.global);
  const listData = {
    api: {
      url: apis['order.quotation.yj'],
      formatResponse: ({ data }) => {
        const { quotation } = data || {};
        const list = isArray(quotation) && quotation.filter((item) => item.available != 0);
        if (list && list.length > 0) {
          return {
            data: {
              list,
            },
          };
        }
        return { data: void 0 };
      },
      onThen: (list) => {
        const { brand = '' } = props.data || {};
        updateList(list.filter((item) => item.brand !== brand));
      },
    },
  };

  const handleClose = () => updateIsOpened(false);
  const handleCancel = () => {
    handleClose();
    cancelOrder({ data, force: true, modal: false }).then(data.resolve).catch(data.reject);
  };
  const handleConfirm = () => {
    if (!current) {
      Taro.kbToast({
        text: '请选择要转寄的品牌',
      });
      return;
    }
    handleCancel();
    // 跳转寄件页，携带当前信息
    orderAction({
      data: {
        ...data,
        relation: {
          brand: current,
        },
      },
      action: 'brand-switch',
    });
  };

  // 跳转预估费详情页
  const handleDetail = (item, e) => {
    e.stopPropagation();
    Taro.navigator({
      url: 'order/amount',
      options: item,
      key: 'yjBrandQuotationInfo',
    });
  };

  // 切换当前current
  const handleChange = ({ brand }) => {
    updateCurrent(brand);
  };

  useEffect(() => {
    dispatch(get());
  }, []);

  useEffect(() => {
    if (!data) return;
    // 打开弹窗，请求当前的数据
    updateActive(
      extractData(data, [
        ['shipper_province', 'send_province'],
        ['shipper_city', 'send_city'],
        ['shipper_district', 'send_district'],
        ['shipper_address', 'send_detail'],
        ['shipping_province', 'receive_province'],
        ['shipping_city', 'receive_city'],
        ['shipping_district', 'receive_district'],
        ['shipping_address', 'receive_address'],
        ['weight', 'goods_weight'],
        ['delivery_type', 'product_type'],
        'volume',
      ]),
    );
    updateIsOpened(true);
  }, [data]);

  return (
    <KbModal
      top='温馨提示'
      isOpened={isOpened}
      onClose={handleClose}
      onCancel={handleCancel}
      onConfirm={handleConfirm}
      confirmText='更换其他品牌继续寄件'
      cancelText='不更换品牌，不寄了'
      closable={false}
      full
      footerLayout='column'
    >
      <View className='kb-brand-switch__notice'>
        <KbNoticeBar>注：换一个品牌，换一个体验</KbNoticeBar>
      </View>
      <View className='kb-brand-switch__list'>
        {isOpened && (
          <KbLongList data={listData} active={active} size='small'>
            {list && (
              <View className='kb-list kb-list-full'>
                {list.map((item) => {
                  const brandInfo = getBrandInfo(item.brand, brands);
                  return (
                    <View
                      key={item.brand}
                      className='kb-list__item'
                      hoverClass='kb-hover'
                      onClick={handleChange.bind(null, item)}
                    >
                      <View className='item-icon'>
                        <AtAvatar circle size='small' image={brandInfo.logo_link} />
                      </View>
                      <View className='item-content'>
                        <View className='item-content__title'>
                          {brands && brands[item.brand] && brands[item.brand].name}
                        </View>
                        <View className='item-content__desc'>
                          {(yjbrandMap[item.brand] && yjbrandMap[item.brand].desc) || ''}
                        </View>
                      </View>
                      <View
                        className='item-extra'
                        hoverClass='kb-hover-opacity'
                        hoverStopPropagation
                        onClick={handleDetail.bind(null, item)}
                      >
                        <Text className='kb-size__xs kb-color__grey'>预估：</Text>
                        <Text className='kb-size__base kb-color__red'>
                          {item.discount_price > 0 ? item.discount_price : '--'}元
                        </Text>
                        <AtIcon
                          className='kb-color__grey kb-icon-size__sm'
                          prefixClass='kb-icon'
                          value='arrow'
                        />
                      </View>
                      <View className='item-checkbox'>
                        <KbCheckbox
                          checked={item.brand === current}
                          onChange={handleChange.bind(null, item)}
                        />
                      </View>
                    </View>
                  );
                })}
              </View>
            )}
          </KbLongList>
        )}
      </View>
    </KbModal>
  );
};

Index.defaultProps = {
  data: null,
};
Index.options = {
  addGlobalClass: true,
};

export default Index;
