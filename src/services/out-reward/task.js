import { triggerRewardReportAnalytics } from '@/components/_pages/out/reward/_utils/reportAnalytics';
import { patchTaskList } from '@/components/_pages/out/reward/_utils/task';
import { requestAsync } from '@base/utils/request';
import isArray from 'lodash/isArray';

// 获取任务
export async function getTask(params) {
  const res = await requestAsync({
    url: '/g_wkd/v2/marketingActivity/ZfbSignReward/getTaskLists',
    data: params,
    toastLoading: false,
  }).then(({ data, ...restData }) => ({
    ...restData,
    data: isArray(data) && data.length > 0 ? { list: patchTaskList(data) } : void 0,
  }));
  return res;
}

// 完成任务：发放奖励
export async function afterDoneTask({ id, task_id = id, ad, money = 0 } = {}, position) {
  const { taskRewardAmount, adBizId } = ad || {};
  console.log('adBizId', adBizId);
  // taskRewardAmount 最大收益，不建议直接使用，应使用adBizId调用支付宝接口查询完成状态以及最大收益；
  const res = await requestAsync({
    url: '/g_wkd/v2/marketingActivity/ZfbSignReward/completeTask',
    data: {
      task_id,
      adBizId,
      taskRewardAmount,
    },
    quickTriggerThen: !adBizId,
    toastSuccess: true,
    toastError: true,
  });
  const { code } = res;
  if (`${code}` === '0') {
    // 统计-发放奖励
    triggerRewardReportAnalytics({ taskId: task_id, action: 'reward', money, taskRewardAmount }, position);
  }
  return res;
}
