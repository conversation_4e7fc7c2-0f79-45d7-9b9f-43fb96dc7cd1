/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb-brandInfo {
  padding: $spacing-h-md;
  background: $color-white;
  &-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .kb-brandInfo-label {
      margin-bottom: 0;
    }
    .kb-brandInfo-value {
      color: #999;
    }
  }
  &-label {
    position: relative;
    margin-bottom: $spacing-h-md;
    &::after {
      position: absolute;
      top: 50%;
      left: 130px;
      color: $color-red;
      transform: translateY(-50%);
      content: '*';
    }
  }
  &-value {
    box-sizing: border-box;
  }
  &-list {
    display: flex;
    flex-wrap: nowrap;
  }
  &-item {
    position: relative;
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
    order: 1;
    box-sizing: border-box;
    width: 288px;
    height: 210px;
    margin-right: 20px;
    border: $width-base solid #009fff;
    border-radius: 16px;
    &-name {
      position: relative;
      top: 0;
      left: 0;
      display: flex;
      align-items: center;
      .brand {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 182px;
        height: 72px;
        &-name {
          position: relative;
          z-index: 1;
          display: flex;
          flex: 1;
          align-items: center;
          justify-content: center;
          margin-top: -20px;
          margin-left: -10px;
          color: #ffffff;
          font-size: 26px;
        }
        .img-bg {
          position: absolute;
          top: 0;
          right: 0;
          bottom: 0;
          left: 0;
          width: 182px;
          height: 72px;
        }
      }
      .product {
        flex: 1;
        margin-top: -20px;
        color: $color-brand;
        font-size: 20px;
        text-align: center;
      }
    }
    &-price {
      display: flex;
      flex: 1;
      align-items: center;
      justify-content: center;
      margin-top: -15px;
      .price {
        display: flex;
        align-items: baseline;
        justify-content: center;
        color: #ff4c4c;
        font-weight: 500;
        font-size: 48px;
        .unit {
          font-size: 26px;
        }
      }
      .price2 {
        margin-left: 5px;
        color: #999999;
        font-size: 24px;
        .continuation-weight {
          margin-bottom: -2px;
          margin-left: 3px;
        }
      }
    }
    &-desc {
      margin-bottom: 15px;
      font-size: 22px;
      text-align: center;
    }
    &--notActive {
      color: #999 !important;
      border-color: #e6e6e6;
      .brand-name,
      .product,
      .price {
        color: #999;
      }
    }
  }
  &-chooseBrandModal {
    .footer {
      display: flex;
      margin-top: 50px;
      &-btn {
        display: flex;
        flex: 1;
        align-items: center;
        justify-content: center;
        box-sizing: border-box;
        height: 72px;
        margin: 0 $spacing-h-md;
        font-size: 32px;
        border-radius: 36px;
        &-confirm {
          color: $color-brand;
          background: $color-white;
          border: $width-base solid $color-brand;
        }
        &-cancel {
          color: $color-white;
          background: $color-brand;
          border: $width-base solid $color-brand;
        }
      }
    }
  }
}
