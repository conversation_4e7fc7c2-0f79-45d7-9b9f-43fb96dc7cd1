import { Image, View } from '@tarojs/components';
import { openCreditService } from '../../_utils/order.credit-pay';
import './index.scss';

const CreditHeader = (props) => {
  const { isOpenCredit, updateState } = props;
  const bgSrc =
    process.env.PLATFORM_ENV === 'weapp'
      ? 'https://cdn-img.kuaidihelp.com/wkd/credit_bg.png'
      : 'https://cdn-img.kuaidihelp.com/wkd/alipay_credit_bg.png';
  const bgOff =
    process.env.PLATFORM_ENV === 'weapp'
      ? 'https://cdn-img.kuaidihelp.com/wkd/credit_off.png'
      : 'https://cdn-img.kuaidihelp.com/wkd/alipay_credit_off.png';
  const bgOn =
    process.env.PLATFORM_ENV === 'weapp'
      ? 'https://cdn-img.kuaidihelp.com/wkd/credit_on.png'
      : 'https://cdn-img.kuaidihelp.com/wkd/alipay_credit_on.png';

  const openCredit = () => {
    updateState({ toCredit: true });
    openCreditService();
  };

  return (
    <View className='credit_header'>
      <Image src={bgSrc} className='credit_header__bg' />
      <Image
        src={isOpenCredit ? bgOn : bgOff}
        className='credit_header__button'
        onClick={isOpenCredit ? () => {} : openCredit}
      />
    </View>
  );
};

CreditHeader.options = {
  addGlobalClass: true,
};

export default CreditHeader;
