/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import CreditIndex from '@/components/_pages/order/credit_page';
import {
  checkCreditService,
  IsBackFromCredit,
} from '@/components/_pages/order/_utils/order.credit-pay';
import { connect } from '@tarojs/redux';
import Taro, { Component } from '@tarojs/taro';

import { handleAlipayCoupon } from '~/components/_pages/order/_utils/order.edit';
import { debounce } from '~base/utils/utils';
import KbPage from '~base/components/page';
import { getActivityDetail } from '~/components/_pages/order/kxj/_utils';
import { getConversionOrderExtraData } from '~/components/_pages/order/credit_page/_utils';
import './index.scss';

@connect(({ global }) => ({
  relationInfo: global.relationInfo,
}))
class Index extends Component {
  config = {
    navigationBarTitleText: '转寄订单',
  };
  constructor() {
    const { orderData, order_id } = this.$router.params;
    const linkParams = orderData ? JSON.parse(orderData) : {};
    const { relationInfo = {} } = this.props || {};
    const { isKdyOrder, relation: relation_, ...rest } = linkParams;

    /**
     * 如果是快递员订单，先取relationInfo中是否为优寄，如未获取到，默认为圆通。
     * 其他订单，默认取原订单中品牌
     */
    const relation = isKdyOrder
      ? relationInfo.type == 'brand'
        ? relationInfo
        : { brand: 'yt' }
      : relation_;

    this.state = {
      product_code: '',
      isOpenCredit: false,
      isRejectCredit: false,
      relation,
      ...rest,
      order_id,
    };
    this.handleAlipayCoupon = debounce(this.handleAlipayCoupon, 700, {
      trailing: true,
    });
  }

  componentDidShow() {
    const { toCredit } = this.state;
    checkCreditService().then((isOpenCredit) => {
      this.setState({
        isOpenCredit,
        initPage: true,
      });
      IsBackFromCredit()
        .then(() => {
          if (!isOpenCredit) {
            // 如果从支付分返回，且为false状态，需弹窗
            this.setState({
              isGuideCredit: true,
            });
            const topText = toCredit ? '开通失败' : '下单失败';
            Taro.kbModal({
              top: topText,
              closable: false,
              template: [
                {
                  tag: 'view',
                  className: 'kb-margin-md-b',
                  value: `由于您未开通${
                    process.env.PLATFORM_ENV == 'alipay' ? '芝麻先寄后付' : '微信支付分'
                  }授权，导致您无法使用先寄后付尊享服务;`,
                },
                {
                  tag: 'view',
                  children: [
                    {
                      tag: 'text',
                      className: 'kb-size__base',
                      value: '注:系统为您推荐以下寄件品牌满足您的寄递需求',
                    },
                  ],
                },
              ],
              cancelText: '',
              confirmText: '我知道了',
            });
          }
        })
        .catch(() => {});
    });
  }

  // 监听数据变化
  onPostMessage = (key, data) => {
    console.info('onPostMessage====> 29', key, data);
    switch (key) {
      case 'routerParamsChange':
        break;
      case 'cardSelect':
        if (process.env.PLATFORM_ENV === 'alipay' && data) {
          const _couponInfo = data.couponInfo || data || {};
          this.setState({
            activityCouponInfo: _couponInfo,
          });
          if (_couponInfo.card_type === 'kxj') {
            this.setState({
              alipayActivityCoupon: null,
            });
          } else if (_couponInfo.voucher_id) {
            this.setState({
              alipayActivityCoupon: _couponInfo,
              activityCouponInfo: null,
            });
          }
        }
        break;
      default:
        break;
    }
  };

  updateState = (params) => {
    this.setState({
      ...params,
    });
  };

  handleUpdate = (data) => {
    const { logined } = data || {};
    if (!logined) return;
    if (process.env.PLATFORM_ENV === 'weapp') {
      getActivityDetail({ vip: true }).then((res) => {
        this.setState({
          activityDetail: res,
        });
      });
    }
    const { order_id } = this.$router.params;
    getConversionOrderExtraData({ order_id }).then((v) => {
      const data = v || {};
      data.transform_coupon_id = data.coupon_id && data.coupon_id.coupon_id;
      delete data.coupon_id;
      this.setState({
        conversionOrderExtraData: data,
      });
    });
  };

  handleAlipayCoupon = handleAlipayCoupon;

  render() {
    const { ...rest } = this.state;
    return (
      <KbPage {...rest} onUpdate={this.handleUpdate}>
        <CreditIndex
          {...rest}
          updateState={this.updateState.bind(this)}
          handleAlipayCoupon={this.handleAlipayCoupon.bind(this)}
        />
      </KbPage>
    );
  }
}

export default Index;
