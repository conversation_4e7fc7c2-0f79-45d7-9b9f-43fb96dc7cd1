/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

export const back_sign_bill_descMap = {
  sxjd: [
    {
      title: '服务介绍',
      content: [
        {
          text: '指收件人正常货物签收后，为寄件人提供签收单签收返还服务',
        },
      ],
    },
    {
      title: '寄件须知',
      content: [
        {
          text: '1、原件回单：收件人货物正常签收后，为寄件人提供签收单原件签收，“原件”返还服务',
        },
        {
          text: '2、电子回单：收件人货物正常签收后，为寄件人提供签收单原件签收，“照片”返还服务；',
        },
        {
          text: '3、末端签收的所有回单签返仅返还至开单网点；。',
        },
      ],
    },
    {
      title: '收费标准',
      content: [
        {
          text: '回单原件：10元/票',
        },
        {
          text: '电子回单：5元/票',
        },
      ],
    },
  ],
  bt: [
    {
      title: '服务介绍',
      content: [
        {
          text: '提供签收单原件返回业务，保障客户交易安全，方便客户对账结算。',
        },
      ],
    },
    {
      title: '寄件须知',
      content: [
        {
          text: '收货人签收确认后的随货单据或签收回单返还给发货方的服务。',
        },
      ],
    },
    {
      title: '收费标准',
      content: [
        {
          text: '回单原件：10元/票',
        },
        {
          text: '电子回单：5元/票',
        },
        {
          text: '原件+电子：13元/票',
        },
      ],
    },
  ],
  ky: [
    {
      title: '服务介绍',
      content: [
        {
          text: '根据托运人需求，在派送货物同时，将托、收客户双方的货物清单，由收货人签字或盖章后，将回单原件或回单照片返回托运人的服务。',
        },
        {
          class: 'kb-margin-md-l',
          text: '• 选择签收单原件即提供回单原件及回单照片',
        },
        {
          class: 'kb-margin-md-l',
          text: '• 选择签收单照片即提供回单照片',
        },
        {
          class: 'kb-margin-md-l',
          text: '• 选择电子回单，上传回单文档或图片，可要求收方客户实名签收，没有丢失风险且时效快',
        },
      ],
    },
    {
      title: '寄件须知',
      content: [
        {
          text: '部分偏远地区可能无法提供服务，具体详情请咨询专属商务。',
        },
      ],
    },
    {
      title: '收费标准',
      content: [
        {
          text: '回单原件：7元/票',
        },
        {
          text: '回单照片：2元/票',
        },
      ],
    },
  ],
};

export const pickup_way_descMap = {
  sxjd: [
    {
      title: '服务介绍',
      content: [
        {
          text: '为客户提供派件网点到收件人地址的到门服务',
        },
      ],
    },
    {
      title: '寄件须知',
      content: [
        {
          text: '送货到门（客户指定所在地）及卸货服务',
        },
      ],
    },
    {
      title: '收费标准',
      content: [
        {
          text: '单票小于40公斤免费上楼',
        },
        {
          text: '单票大于40公斤20元起步：有电梯0.3元/公斤，无电梯楼层0.4元/公斤',
        },
      ],
    },
  ],
  bt: [
    {
      title: '服务介绍',
      content: [
        {
          text: '您所在区域为我司派送范围内，壹米滴答根据您的需求，将货物送到指定的地点。',
        },
      ],
    },
    {
      title: '收费标准',
      content: [
        {
          text: '单票超过40KG，0.4元每kg',
        },
      ],
    },
  ],
  ky: [
    {
      title: '取货爬楼费',
      content: [
        {
          img: {
            style: {
              width: '1041rpx',
              height: '367rpx',
            },
            src: 'https://cdn-img.kuaidihelp.com/wkd/miniApp/dh/packaging_service/ky/qhplf.png?v=1',
          },
        },
      ],
    },
    {
      title: '派货爬楼费',
      content: [
        {
          img: {
            style: {
              width: '1436rpx',
              height: '122rpx',
            },
            src: 'https://cdn-img.kuaidihelp.com/wkd/miniApp/dh/packaging_service/ky/phplf.png?v=1',
          },
        },
      ],
    },
  ],
};

export const package_service_descMap = {
  bt: [
    {
      title: '服务介绍',
      content: [
        {
          text: '及时贴心，让货物签收信息尽在掌握，消除发货人后顾之忧。',
        },
      ],
    },
    {
      title: '寄件须知',
      content: [
        {
          text: '壹米滴答为你的货物提供环保、经济、专业包装，并提供优质、特色的包装解决方案，可提供胶带、纸箱、纤袋、打包膜等包装材料。',
        },
      ],
    },
    {
      title: '收费标准',
      content: [
        {
          text: '木架：200元/方，最低40元/票，开单体积=货物体积*1.4',
        },
        {
          text: '木箱：300元/方，最低60元/票，开单体积=货物体积*1.4',
        },
        {
          class: 'kb-margin-md-t',
          text: '木托盘：不超过标准托盘尺寸，60元/块',
        },
        {
          text: '编织袋 ：100*120   5元/个',
        },
        {
          class: 'kb-margin-md-t',
          text: '纸箱',
        },
        {
          text: '1号纸箱：60*50*40  10元/个',
        },
        {
          text: '2号纸箱：50*32*54  8元/个',
        },
        {
          text: '3号纸箱：46*28*33  6元/个',
        },
        {
          text: '4号纸箱：30*23*23  4元/个',
        },
      ],
    },
  ],
  ky: [
    {
      title: '服务介绍',
      content: [
        {
          text: '为了更好的保障您的货物安全，跨越速运为您提供更专业、多样化、环保防水、防止信息泄露等的包装服务。',
        },
      ],
    },
    {
      title: '寄件须知',
      content: [
        {
          text: '以上收费标准及实际可提供的包装可能因服务(产品)或所在地区不同而有所差异，详情请咨询专属商务。',
        },
      ],
    },
    {
      title: '木架费-收费标准',
      content: [
        {
          text: '木架：190元/方，最低收费40元/件',
        },
        {
          text: '木箱：280元/方，最低收费60元/件',
        },
        {
          style: {
            marginLeft: '72rpx',
          },
          text: '注：木架/木箱费=打木架/木箱前货物原体积*1.4（体积系数）*单价】',
        },
        {
          text: '木托：木托0-120CM：60元/个',
        },
        {
          style: {
            marginLeft: '72rpx',
          },
          text: '木托121-160CM：100元/个',
        },
        {
          style: {
            marginLeft: '72rpx',
          },
          text: '木托161CM以上：140元/个',
        },
      ],
    },
    {
      title: '封箱/拉膜费-收费标准',
      content: [
        {
          text: '按件收费：0.5元/件',
        },
        {
          text: '按托收费：10元/托',
        },
      ],
    },
  ],
};

export const ggdsxf_descMap = {
  sxjd: [
    {
      title: '服务介绍',
      content: [
        {
          text: '寄件物品已至转运中心未发出派件，未派件上车。寄件用户修改派送地址需收取一定服务费用。',
        },
      ],
    },
    {
      title: '寄件须知',
      content: [
        {
          text: '按件收取。',
        },
      ],
    },
    {
      title: '收费标准',
      content: [
        {
          text: '跨区改单费：5元/票',
        },
        {
          text: '跨市改单费：最低5元/票，0.3元/kg，无封顶',
        },
        {
          text: '跨省改单费：最低5元/票，0.7元/kg，无封顶',
        },
        {
          class: 'kb-margin-md-t',
          text: '具体修改收费以实际情况为准，详情收费标准请咨询 揽件快递员。',
        },
      ],
    },
  ],
  bt: [
    {
      title: '服务介绍',
      content: [
        {
          text: '寄件物品已至转运中心未发出派件，未派件上车。寄件用户修改派送地址需收取一定服务费用。',
        },
      ],
    },
    {
      title: '寄件须知',
      content: [
        {
          text: '按件收取。',
        },
      ],
    },
    {
      title: '收费标准',
      content: [
        {
          text: '5+0.5*件数',
          text: '更改单手续费5元一票，如果一票多件为5元+0.5元每件。',
        },
      ],
    },
  ],
};

export const ctfwf_descMap = {
  sxjd: [
    {
      title: '服务介绍',
      content: [
        {
          text: '在派送环节，客户有拆木架、木箱、木质包装的需求，顺心捷达可提供其定制化的服务。',
        },
      ],
    },
    {
      title: '寄件须知',
      content: [
        {
          text: '按件收取。',
        },
      ],
    },
    {
      title: '收费标准',
      content: [
        {
          text: '收拆包费（家装产品）：',
        },
        {
          class: 'kb-color__grey',
          text: '最低收费7元/票，费率7元/件，无封顶收费；',
        },
        {
          class: 'kb-margin-md-t',
          text: '收拆木架费（家装产品）：',
        },
        {
          class: 'kb-color__grey',
          text: '最低收费12元/票，费率12元/件，无封顶收费；',
        },
        {
          class: 'kb-margin-md-t',
          text: '拆托（木架）费：',
        },
        {
          class: 'kb-color__grey',
          text: '单票4件及以下:免费;',
        },
        {
          class: 'kb-color__grey',
          text: '单票5件及以上:最低收费10元/票,续件5元/件,无封顶;',
        },
        {
          class: 'kb-margin-md-t',
          text: '拆木箱费：',
        },
        {
          class: 'kb-color__grey',
          text: '单票2件及以下:免费;',
        },
        {
          class: 'kb-color__grey',
          text: '单票3件及以上:最低收费10元/票，续件5元/件，无封顶;',
        },
        {
          class: 'kb-margin-md-t',
          text: '具体拆木架收费以实际情况为准，详情收费标准请咨询揽件快递员。',
        },
      ],
    },
  ],
  bt: [
    {
      title: '服务介绍',
      content: [
        {
          text: '在派送环节，客户有拆木架、木箱、木质包装的需求，壹米滴答可提供其定制化的服务。',
        },
      ],
    },
    {
      title: '寄件须知',
      content: [
        {
          text: '按件收取。',
        },
      ],
    },
    {
      title: '收费标准',
      content: [
        {
          text: '拆非木包装：8元/件',
        },
        {
          text: '拆托、 拆木架：12+6*（N-1）',
        },
        {
          text: '拆木箱：25*N',
        },
        {
          text: 'N：拆托/木架/木箱件数（拆卸前件数）',
        },
        {
          class: 'kb-margin-md-t',
          text: '具体拆木架收费以实际情况为准，详情收费标准请咨询揽件快递员。',
        },
      ],
    },
  ],
};

export const spsqyjsf_descMap = {
  sxjd: [
    {
      title: '服务介绍',
      content: [
        {
          text: '您填写的揽件/派件地址超出快递员服务区域，需额外支付超区费。',
        },
      ],
    },
    {
      title: '寄件须知',
      content: [
        {
          text: '按件收取。',
        },
      ],
    },
    {
      title: '收费标准',
      content: [
        {
          img: {
            style: {
              width: '1341rpx',
              height: '432rpx',
            },
            src: 'https://cdn-img.kuaidihelp.com/wkd/miniApp/dh/packaging_service/sxjd/spssft.png?v=1',
          },
        },
      ],
    },
  ],
};

export const ccf_descMap = {
  ky: [
    {
      title: '服务介绍',
      content: [
        {
          text: '针对客户寄递的单件物品超过标准收货限定尺寸的情况，需额外投入或调配资源进行收派中转，此附加服务需收取相应费用。',
        },
      ],
    },
    {
      title: '寄件须知',
      content: [
        {
          text: '按件收取。',
        },
      ],
    },
    {
      title: '收费标准',
      content: [
        {
          text: '任意一边长:0米＜长度≤1.8米:0元/件',
        },
        {
          text: '1.8米＜长度≤5米：50元/件',
        },
        {
          text: '5米＜长度≤6米：80元/件',
        },
        {
          text: '6米＜长度≤100米：120元/件',
        },
        {
          text: '单票收费上限1000元',
        },
        {
          text: '针对企业件(B2B)和电商件(B2C)客户会有所不同，企业件单边长度超1.8米、电商件单边长度超1.6米开始收取，详情收费标准请咨询揽件快递员。',
        },
        {
          class: 'kb-margin-md-t',
          text: '以上收费仅供参考，具体收费标准请联系揽件快递员',
        },
      ],
    },
  ],
};

export const czf_descMap = {
  ky: [
    {
      title: '服务介绍',
      content: [
        {
          text: '寄件单件物品，超出规定限重需支付一定服务费用。',
        },
      ],
    },
    {
      title: '寄件须知',
      content: [
        {
          text: '按件收取。',
        },
      ],
    },
    {
      title: '收费标准',
      content: [
        {
          text: '0KG＜件均重≤700KG，超重费0元/票',
        },
        {
          text: '700KG＜件均重≤1000KG，超重费100元/票',
        },
        {
          text: '1000KG＜件均重≤1500KG，超重费150元/票',
        },
        {
          text: '件均重1500KG以上，超重费200元/票',
        },
      ],
    },
  ],
};

export const cqf_descMap = {
  ky: [
    {
      title: '服务介绍',
      content: [
        {
          text: '您填写的揽件/派件地址超出快递员服务区域，需额外支付超区费。',
        },
      ],
    },
    {
      title: '寄件须知',
      content: [
        {
          text: '按件收取。',
        },
      ],
    },
    {
      title: '收费标准',
      content: [
        {
          text: '2元/KM',
        },
        {
          text: '具体超区收费以实际情况为准，详情收费标准请咨询揽件快递员。',
        },
      ],
    },
  ],
};

export const cmjf_descMap = {
  ky: [
    {
      title: '服务介绍',
      content: [
        {
          text: '在派送环节，客户有拆木架、木箱、木质包装的需求，跨越速运可提供其定制化的服务。',
        },
      ],
    },
    {
      title: '寄件须知',
      content: [
        {
          text: '按件收取。',
        },
      ],
    },
    {
      title: '收费标准',
      content: [
        {
          text: '20元/件',
        },
        {
          text: '具体拆木架收费以实际情况为准，详情收费标准请咨询揽件快递员。',
        },
      ],
    },
  ],
};

export const dcpsf_descMap = {
  ky: [
    {
      title: '服务介绍',
      content: [
        {
          text: '用户超过两次派送未按约定签收，第三次派送收取一定费用。',
        },
      ],
    },
    {
      title: '寄件须知',
      content: [
        {
          text: '按件收取。',
        },
      ],
    },
    {
      title: '收费标准',
      content: [
        {
          text: '20元/票',
        },
        {
          text: '具体多次派送收费以实际情况为准，详情收费标准请咨询揽件快递员。',
        },
      ],
    },
  ],
};

export const dtzpsfwf_descMap = {
  ky: [
    {
      title: '服务介绍',
      content: [
        {
          text: '快件到达目的地后暂不派送，待托运人通知跨越速运后再安排派件的一项“贴身秘书”服务。',
        },
      ],
    },
    {
      title: '寄件须知',
      content: [
        {
          text: '按件收取。',
        },
      ],
    },
    {
      title: '收费标准',
      content: [
        {
          text: '3元/票',
        },
        {
          text: '具体等通知派送收费以实际情况为准，详情收费标准请咨询揽件快递员。',
        },
      ],
    },
  ],
};

export const xgdzf_descMap = {
  ky: [
    {
      title: '服务介绍',
      content: [
        {
          text: '寄件物品已至转运中心未发出派件，未派件上车。寄件用户修改派送地址需收取一定服务费用。',
        },
      ],
    },
    {
      title: '寄件须知',
      content: [
        {
          text: '按件收取。',
        },
      ],
    },
    {
      title: '收费标准',
      content: [
        {
          text: '10元/次',
        },
        {
          text: '具体修改地址收费以实际情况为准，详情收费标准请咨询揽件快递员。',
        },
      ],
    },
  ],
};

export const xgfwf_descMap = {
  ky: [
    {
      title: '服务介绍',
      content: [
        {
          text: '寄件物品已至转运中心未发出派件，未派件上车。寄件用户修改派送地址需收取一定服务费用。',
        },
      ],
    },
    {
      title: '寄件须知',
      content: [
        {
          text: '按件收取。',
        },
      ],
    },
    {
      title: '收费标准',
      content: [
        {
          text: '50元/票',
        },
        {
          text: '具体修改服务收费以实际情况为准，详情收费标准请咨询揽件快递员。',
        },
      ],
    },
  ],
};

export const kpf_descMap = {
  ky: [
    {
      title: '服务介绍',
      content: [
        {
          text: '寄件用户和快递员约定时间揽收，揽收快递车辆并发车前往揽件目的地。用户中途取消订单，需支付一定空跑车辆服务费用。',
        },
      ],
    },
    {
      title: '寄件须知',
      content: [
        {
          text: '按件收取。',
        },
      ],
    },
    {
      title: '收费标准',
      content: [
        {
          text: '空跑费=空跑公里数*单价',
        },
        {
          text: '大货9.6：5元/KM',
        },
        {
          text: '冷链大货9.6：5元/KM',
        },
        {
          text: '大货7.6：3.5元/KM',
        },
        {
          text: '冷链大货7.6：3.5元/KM',
        },
        {
          text: '中货4.2：1.5元/KM',
        },
        {
          text: '小货：1.5元/KM',
        },
        {
          text: '面包：1.5元/KM',
        },
        {
          text: '冷链中货：1.5元/KM',
        },
        {
          text: '大货5.2：1.5元/KM',
        },
        {
          text: '具体空跑收费以实际情况为准，详情收费标准请咨询揽件快递员。',
        },
      ],
    },
  ],
};

export const dtzfwf_descMap = {
  bt: [
    {
      title: '服务介绍',
      content: [
        {
          text: '快件到达目的地后暂不派送，待托运人通知壹米滴答后再安排派件的一项安全配送服务。',
        },
      ],
    },
    {
      title: '寄件须知',
      content: [
        {
          text: '按件收取。',
        },
      ],
    },
    {
      title: '收费标准',
      content: [
        {
          text: '50元/票',
        },
        {
          text: '具体等通知派送收费以实际情况为准，详情收费标准请咨询揽件快递员。',
        },
      ],
    },
  ],
};

export const createDHServiceDesc = (opt = {}) => {
  const { brand } = opt;
  return {
    back_sign_bill: back_sign_bill_descMap[brand] || [],
    pickup_way: pickup_way_descMap[brand] || [],
    package_service: package_service_descMap[brand] || [],
    ggdsxf: ggdsxf_descMap[brand] || [],
    ctfwf: ctfwf_descMap[brand] || [],
    spsqyjsf: spsqyjsf_descMap[brand] || [],
    ccf: ccf_descMap[brand] || [],
    czf: czf_descMap[brand] || [],
    cqf: cqf_descMap[brand] || [],
    cmjf: cmjf_descMap[brand] || [],
    dcpsf: dcpsf_descMap[brand] || [],
    dtzpsfwf: dtzpsfwf_descMap[brand] || [],
    xgdzf: xgdzf_descMap[brand] || [],
    xgfwf: xgfwf_descMap[brand] || [],
    kpf: kpf_descMap[brand] || [],
    dtzfwf: dtzfwf_descMap[brand] || [],
  };
};
