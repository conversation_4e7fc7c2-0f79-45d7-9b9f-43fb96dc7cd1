/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb-addValueService {
  &-floorNum {
    position: relative;
    margin: -$spacing-h-md $spacing-h-md $spacing-h-md;
    padding-top: 5px;
    &::before {
      position: absolute;
      top: 0;
      right: $spacing-h-md;
      left: $spacing-h-md;
      border-bottom: $width-base solid #eaeaea;
      content: '';
    }
    .kb-nav-item-label {
      position: relative;
      padding-right: 20px;
      &::after {
        position: absolute;
        top: 50%;
        right: 0;
        color: $color-red;
        transform: translateY(-50%);
        content: '*';
      }
    }
  }
  &-submit {
    width: 208px;
    height: 72px;
    color: #ffffff;
    font-size: 30px;
    border-radius: 72px;
  }
  &-container {
    height: 100%;
    overflow-y: auto;
  }
}

.pickup_way-sxjd {
  .list-item {
    padding-top: 20px !important;
    padding-bottom: 20px !important;
  }
}
