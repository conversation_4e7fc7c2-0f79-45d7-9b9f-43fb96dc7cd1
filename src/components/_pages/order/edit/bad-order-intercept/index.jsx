/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { Fragment, useState, useEffect, useRef } from '@tarojs/taro';
import { View, Input } from '@tarojs/components';
import KbModal from '@base/components/modal';
import classNames from 'classnames';
import KbImgCode from './img-code';
import './index.scss';

const KbBadOrderIntercept = (props) => {
  const { open, onComplete = () => {} } = props;
  const [isOpened, setIsOpened] = useState(false);
  const ref = useRef({ code: '' });
  const [val, setVal] = useState();

  useEffect(() => {
    setIsOpened(!!open);
  }, [open]);

  const handleClose = () => {
    setVal('');
    ref.current.code = '';
    setIsOpened(false);
  };

  const handleImgCodeChange = (val) => {
    ref.current.code = val;
  };

  const handleChange = (ev) => {
    setVal(ev.detail.value);
  };

  const handleComplete = () => {
    if (!val) {
      Taro.kbToast({
        text: '请输入图形验证码',
      });
      return;
    }
    if (ref.current.code == val) {
      handleClose();
      onComplete();
    } else {
      Taro.kbToast({
        text: '图形验证码错误',
      });
    }
  };

  const completeBtnCls = classNames('kb-interceptOrder-complete', {
    'kb-interceptOrder-complete--disable': false,
  });
  return (
    <Fragment>
      <KbModal
        isOpened={isOpened}
        top={false}
        hideFooter
        full
        closable={false}
        onClose={handleClose}
        onCancel={handleClose}
      >
        {isOpened && (
          <View className='kb-interceptOrder'>
            <View className='kb-interceptOrder-title'>安全验证</View>
            <View className='kb-interceptOrder-content'>
              <View>
                <View className='kb-interceptOrder-code'>
                  <KbImgCode onChange={handleImgCodeChange} />
                </View>
                <View className='kb-interceptOrder-refresh'>点击图片刷新验证码</View>
              </View>
              <View className='kb-interceptOrder-inputBox'>
                <Input
                  className='input'
                  type='number'
                  autoFocus
                  value={val}
                  onInput={handleChange}
                  placeholder='请输入验证码'
                />
              </View>
              <View className='kb-interceptOrder-tips'>
                检测到您账号可能存在风控风险，请您完成安全验证后再继续下单。
              </View>
            </View>
            <View className={completeBtnCls} onClick={handleComplete}>
              完成
            </View>
          </View>
        )}
      </KbModal>
    </Fragment>
  );
};

KbBadOrderIntercept.options = {
  addGlobalClass: true,
};

KbBadOrderIntercept.defaultProps = {
  open: false,
  onComplete: () => {},
};

export default KbBadOrderIntercept;
