/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { Fragment } from '@tarojs/taro';
import KbModal from '@base/components/modal';
import { Image, View } from '@tarojs/components';
import { useGuideReceiveCoupon } from './_utils';
import './index.scss';

const GuideReceiveCoupon = () => {
  const { isOpened, handelClick } = useGuideReceiveCoupon();

  return (
    <Fragment>
      <KbModal
        isOpened={isOpened}
        top={false}
        title='确认要放弃优惠吗？'
        onClose={handelClick.bind(null, 'close')}
        onCancel={handelClick.bind(null, 'close')}
        confirmText=''
        cancelText=''
      >
        <View className='kb-guideReceiveCouponModal'>
          <View className='kb-guideReceiveCouponModal-tips'>领券寄件最高可省188元</View>
          <View className='kb-guideReceiveCouponModal-coupon'>
            <Image
              className='kb-guideReceiveCouponModal-coupon--img'
              mode='widthFix'
              src='https://cdn-img.kuaidihelp.com/wkd/miniApp/activity/center/img_coupon.png?v=1'
            />
          </View>
          <View className='kb-guideReceiveCouponModal-footer'>
            <View className='confirm' onClick={() => handelClick('confirm')} hoverClass='kb-hover'>
              领券寄件
            </View>
            <View className='cancel' onClick={() => handelClick('cancel')} hoverClass='kb-hover'>
              残忍离开
            </View>
          </View>
        </View>
      </KbModal>
    </Fragment>
  );
};

GuideReceiveCoupon.options = {
  addGlobalClass: true,
};

export default GuideReceiveCoupon;
