/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { Swiper, SwiperItem, View } from '@tarojs/components';
import { Fragment } from '@tarojs/taro';
import isArray from 'lodash/isArray';
import { useCheckIsShieldAd } from '../sdk/shield';
import AdItem from './ad-item';
import { useKbAdExtensionAdId } from './_utils';
import './index.scss';

/**
 * 微信官方广告组件位;
 */
const KbAdExtensionAd = (props) => {
  const { adUnitIdIndex, closeShield, ...restProps } = props;
  const { swiperHeight, onClose, onError } = restProps || {};
  const { unitIds, setUnitIds } = useKbAdExtensionAdId({ adUnitIdIndex });

  const handelSwiperClose = (e, index) => {
    const _unitIdArr = [...unitIds];
    _unitIdArr.splice(index, 1);
    setUnitIds(_unitIdArr);
    onClose(e);
  };

  const handelSwiperAdErr = (e, index) => {
    onError(e);
    handelSwiperClose(e, index);
  };

  const isSwiper = unitIds && isArray(unitIds);

  // 是否屏蔽官方广告
  const shield = useCheckIsShieldAd(closeShield ? '-1' : '1');

  return (process.env.PLATFORM_ENV === 'weapp' || process.env.PLATFORM_ENV === 'alipay') &&
    process.env.MODE_ENV !== 'third.post' ? (
    <Fragment>
      {!shield ? (
        <Fragment>
          {isSwiper ? (
            isArray(unitIds) && unitIds.length > 0 ? (
              <Swiper
                className='kb-ad__swiper'
                indicatorDots={unitIds.length > 1}
                interval={5000}
                circular
                autoplay
                style={{ height: swiperHeight + 'px' }}
              >
                {unitIds.map((item, index) => {
                  const itemCls = '';
                  return (
                    <SwiperItem key={item} className={itemCls}>
                      <View id={`ad-${index}`}>
                        <AdItem
                          {...restProps}
                          unitId={item}
                          onClose={(e) => handelSwiperClose(e, index)}
                          onError={(e) => handelSwiperAdErr(e, index)}
                        />
                      </View>
                    </SwiperItem>
                  );
                })}
              </Swiper>
            ) : null
          ) : (
            <AdItem {...restProps} unitId={unitIds} />
          )}
        </Fragment>
      ) : null}
    </Fragment>
  ) : (
    <Fragment />
  );
};

KbAdExtensionAd.defaultProps = {
  intervals: '2000',
  adUnitIdIndex: '',
  swiperHeight: 115,
  implant: true,
  wrapper: false,
  onLoad: () => {},
  onError: () => {},
  onClose: () => {},
};

KbAdExtensionAd.options = {
  addGlobalClass: true,
};

export default KbAdExtensionAd;
