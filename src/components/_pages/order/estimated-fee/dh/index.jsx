/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { Fragment, useEffect, useState } from '@tarojs/taro';
import { Text, View } from '@tarojs/components';
import KbNumber from '@base/components/number';
import numeral from 'numeral';
import { AtFloatLayout, AtIcon } from 'taro-ui';
import { isFullData } from '@base/utils/utils';
import { adNavigator } from '@/components/_pages/ad-extension/sdk';
import { transferWkdAddress } from '../../_utils';
import { formatServiceData, getIntroductionConfig } from '../../_utils/order.edit.dh';
import './index.scss';

const KbDHEstimatedFee = (props) => {
  const {
    address,
    extraInfo: { goods_weight, service },
    quotationData,
    total = 1,
    onProPriceClick = () => {},
    volume,
  } = props;
  const { cost_value } = service || {};

  const [feeList, setFeeList] = useState([]);
  const [estimatedFee, setEstimatedFee] = useState(0);
  const [estimatedWeight, setEstimatedWeight] = useState(0);
  const [loading] = useState(false);
  const [isOpened, setIsOpened] = useState(false);

  const onSwitchFee = () => {
    setIsOpened(!isOpened);
  };

  const triggerUpdate = ({ estimatedFee, feeList, estimatedWeight }) => {
    setFeeList(feeList);
    setEstimatedFee(estimatedFee);
    setEstimatedWeight(estimatedWeight);
  };

  const reset = () => {
    triggerUpdate({
      estimatedFee: 0,
      feeList: [],
    });
  };

  const formatFee = (v) => numeral(v * 1).format('0.00');

  const countFee = () => {
    const { discount_price, f_kg = 0, s_kg = 0, brand } = quotationData || {};
    const _cost_value = cost_value * 1 || 0;
    const { service_price } = formatServiceData(quotationData);
    if (_cost_value > 0) {
      const insuranceFeeIndex = service_price.findIndex((i) => i.service_code === 'insuranceFee');
      if (insuranceFeeIndex > -1) {
        service_price[insuranceFeeIndex].amount = _cost_value;
      } else {
        service_price.push({
          service_code: 'insuranceFee',
          service_name: '保价费',
          amount: _cost_value,
        });
      }
    }
    let service_price_fee = 0;
    if (service_price && service_price.length > 0) {
      service_price.map((item) => {
        service_price_fee = service_price_fee + item.amount * 1 || 0;
        if (item.service_name === '保价费') {
          item.introduction = true;
        } else {
          item.introduction = getIntroductionConfig(item.service_name, brand);
        }
      });
      service_price_fee = service_price_fee.toFixed(2);
    }
    const _discount_price = discount_price * 1 || 0;
    let nEstimatedWeight = numeral(f_kg).value() + numeral(s_kg).value();
    const feeList = [
      {
        key: 'weight',
        label: '预估重量',
        value: nEstimatedWeight,
        formatVal: `${nEstimatedWeight || '-'}kg`,
      },
      {
        key: 'fee',
        label: '预估运费',
        value: _discount_price,
        formatVal: `￥${formatFee(_discount_price)}`,
      },
      {
        key: 'service_price_fee',
        label: '预估增值总费用',
        value: service_price_fee,
        formatVal: `￥${formatFee(service_price_fee)}`,
        list: service_price,
        hide: !service_price_fee || service_price_fee * 1 <= 0,
      },
    ];
    let fee = 0;
    feeList.map((i) => {
      if (!['weight'].includes(i.key)) {
        fee = fee * 1 + i.value * 1;
      }
    });
    triggerUpdate({
      estimatedFee: numeral(fee).format('0.00'),
      feeList: feeList.filter((i) => !i.hide),
      estimatedWeight: nEstimatedWeight,
    });
  };

  useEffect(() => {
    let addrData = transferWkdAddress(address, 'address');
    if (!isFullData(addrData)) {
      reset();
      return;
    }
    if (quotationData && goods_weight) {
      countFee();
    }
  }, [address, quotationData, cost_value, goods_weight]);

  const handleServiceClick = (iitem) => {
    if (!iitem.introduction) return;
    if (iitem.service_code === 'insuranceFee') {
      onProPriceClick();
      return;
    }
    if (iitem.introduction.url) {
      adNavigator(iitem.introduction);
      return;
    }
    Taro.navigator({
      url: `order/edit/service/dh/desc`,
      options: {
        brand: quotationData.brand,
        type: iitem.introduction.value,
      },
    });
  };

  const { volume: volumeValue } = volume || {};
  const { volumeWeight, ratio: volumeRatio } = quotationData || {};

  return (
    <Fragment>
      {total <= 1 && (
        <View className='at-row at-row__justify--between at-row__align--center kb-size__sm kb-color__grey'>
          <View className='at-col'>
            <View className='at-row at-row__align--center'>
              <View className='kb-spacing-md-r'>
                <Text className='kb-color__black'>预估费用：</Text>
                {loading ? (
                  <AtIcon
                    prefixClass='kb-icon'
                    value='loading'
                    className='kb-icon-size__base kb-color__grey'
                  />
                ) : (
                  <KbNumber fontsize={[36, 22]} color='#ff4c4c' number={estimatedFee} unit='元' />
                )}
              </View>
            </View>
            <View className='kb-color__grey kb-size__sm'>
              按{estimatedWeight > 0 ? estimatedWeight : '-'}kg物品预估费用
            </View>
          </View>
          {estimatedFee >= 0 && (
            <View
              className={`kb-fee-shrink kb-spacing-xs ${isOpened ? 'kb-fee-shrink--active' : ''}`}
              hoverClass='kb-hover-opacity'
              onClick={onSwitchFee}
            >
              明细
            </View>
          )}
        </View>
      )}
      <AtFloatLayout
        className='kb-fee-detail-layout'
        isOpened={isOpened}
        onClose={onSwitchFee}
        title='运费明细'
      >
        <View className='kb-fee-detail'>
          <View className='kb-fee-detail__tips'>
            运费与增值服务合计的实际费用，将优先微信支付分扣款，请留意
            微信扣款消息。若金额大于500元，会分成多个账单扣除相应运费
          </View>
          <View className='kb-fee-detail__list'>
            {volumeWeight > 0 ? (
              <View className='kb-fee-detail__volume'>
                <View className='kb-color__orange kb-spacing-md-r'>
                  此单预估费用按照您录入的体积计费：
                </View>
                <View>
                  {volumeValue}CM³/{volumeRatio}
                  {1 * (volumeValue / volumeRatio) === 1 * volumeWeight ? '=' : '≈'}
                  {volumeWeight}kg
                </View>
              </View>
            ) : null}
            {feeList &&
              feeList.map((item) => {
                return (
                  <Fragment key={item.key}>
                    {item.key == 'service_price_fee' ? (
                      <View className='kb-fee-detail__service' key={item.key}>
                        <View className='kb-fee-detail__item'>
                          <View className='label'>
                            <View>预估增值总费用</View>
                            <View className='label-desc'>实际费用及项目以末端网点回传为准</View>
                          </View>
                          <View className='value'>{item.formatVal}</View>
                        </View>
                        <View>
                          {item.list &&
                            item.list.map((iitem) => {
                              return (
                                <View className='kb-fee-detail__item' key={iitem.service_code}>
                                  <View
                                    className='label'
                                    onClick={() => handleServiceClick(iitem)}
                                    hoverClass='kb-hover'
                                  >
                                    <Text>{iitem.service_name}</Text>
                                    {iitem.introduction && (
                                      <AtIcon
                                        prefixClass='kb-icon'
                                        value='question'
                                        className='kb-color__grey kb-icon-size__base kb-margin-sm-l'
                                      />
                                    )}
                                  </View>
                                  <View className='value'>￥{iitem.amount}</View>
                                </View>
                              );
                            })}
                        </View>
                      </View>
                    ) : (
                      <View className='kb-fee-detail__item' key={item.key}>
                        <View className='label'>{item.label}</View>
                        <View className='value'>{item.formatVal}</View>
                      </View>
                    )}
                  </Fragment>
                );
              })}
          </View>
        </View>
      </AtFloatLayout>
    </Fragment>
  );
};

KbDHEstimatedFee.options = {
  addGlobalClass: true,
};

KbDHEstimatedFee.defaultProps = {
  extraInfo: {},
};

export default KbDHEstimatedFee;
