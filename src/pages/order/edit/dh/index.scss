/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb-send {
  padding: $spacing-v-md $spacing-h-md;

  &__item {
    margin-bottom: $spacing-v-md;

    &:first-child,
    &:last-child {
      margin-bottom: 0;
    }
  }
}

.kb-login__auth--box {
  padding: $spacing-v-md $spacing-h-md;
  background-color: $color-white;
}

.yj-container {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 2;
  overflow: visible;
  background: #fff;
  &-list {
    padding: 0 20px;
  }
  &-msg {
    position: relative;
    z-index: 2;
    background: #fff;
    box-shadow: 0 10px 15px #000;
  }
}

.kb-scrollview-wrapper .wrapper-footer {
  z-index: 4 !important;
}

.send-step {
  display: flex;
  align-items: center;
  justify-content: space-around;
  margin-top: 16px;
  padding: 16px 24px;
  color: #999;
  font-weight: normal;
  font-size: 24px;
  background-color: #fff;
  border-radius: 6px;
  .first {
    color: #009fff;
  }
  .kb-color__arrow {
    margin-bottom: 6px;
    color: #8a8a8a;
    transform: scale(0.6);
  }
}

.kb-batchOrderTips {
  text-align: center;
  &__fail {
    &-num {
      margin: 0 $spacing-h-sm;
      font-size: 48px;
    }
    &-copy {
      height: 80px;
      font-size: 32px;
      line-height: 80px;
    }
  }
}

.kb-goodsInfo {
  border-bottom: $border-lightest;
}

.kb-block {
  margin-top: $spacing-h-md;
  overflow: hidden;
  border-radius: 10px;
}

.kb-proPrice {
  border-bottom: $border-lightest;
}

.kb-submitInterceptTips {
  color: #666;
  font-size: 28px;
  &-notice {
    margin-bottom: 30px;
    padding: $spacing-h-md;
    color: #ff7e27;
    background: #fffbe9;
    border-radius: 5px;
  }
}

.kb-fee-detail-layout {
  bottom: 100px;
  height: unset;
  margin-bottom: constant(safe-area-inset-bottom);
  margin-bottom: env(safe-area-inset-bottom);
}
