/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { Base64 } from 'js-base64';
import { check } from '@base/utils/rules';

const compatibleMap = {};

compatibleMap.userInfo = (data) => {
  if (process.env.MODE_ENV === 'wkd') {
    const {
      avatar_url,
      kb_id,
      nickname,
      session_id: sessionid,
      third_id,
      open_id,
      openid = open_id || third_id,
      user_id,
      user_name,
    } = data;
    let mobile = user_name;
    if (process.env.PLATFORM_ENV === 'alipay') {
      // 支付宝小程序手机号加密
      mobile = Base64.isValid(mobile) ? Base64.decode(mobile) : '';
    }
    mobile = check('phone_star', mobile).code == 0 ? mobile : '';
    return {
      user_id,
      nickname,
      avatar_url,
      kb_id,
      openid,
      sessionid,
      mobile,
    };
  } else {
    return data;
  }
};

export default compatibleMap;
