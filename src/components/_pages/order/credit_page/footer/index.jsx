import KbSubscribe from '@base/components/subscribe';
import { View } from '@tarojs/components';
import { useCreditFooter } from '../_utils/useCreditFooter';

import './index.scss';

const CreditFooter = (props) => {
  const { onCancel, onSubmit } = useCreditFooter(props);

  return (
    <View className='credit_footer'>
      <KbSubscribe action='sendToBrand' type='primary' circle onSubscribe={onSubmit.bind(null)}>
        提交转寄订单
      </KbSubscribe>
      <KbSubscribe
        action='cancel'
        circle
        className='credit_footer__button'
        onSubscribe={onCancel.bind(null)}
      >
        <View className='credit_footer__cancel'>残忍取消</View>
      </KbSubscribe>
    </View>
  );
};

CreditFooter.options = {
  addGlobalClass: true,
};

export default CreditFooter;
