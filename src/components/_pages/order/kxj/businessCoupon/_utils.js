/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { useState } from '@tarojs/taro';
import { getStorage, setStorage } from '@base/utils/utils';
import { useUpdate } from '@base/hooks/page';
import { addReceiveCoupon, getActivityDetail } from '../_utils';

export const useNewCustomerCouponGuide = (opt) => {
  const { mode = 1, maxSteps = 1, key = '' } = opt || {};

  const [isOpened, setIsOpened] = useState(false);
  const [step, updateStep] = useState(1);

  const triggerUpdateStep = (v) => {
    Taro.kbSetGlobalData(key, v);
    updateStep(v);
  };

  const triggerOpen = (v = 1) => {
    triggerUpdateStep(v);
    setIsOpened(true);
    setStorage({
      key: key,
      data: 1,
    });
  };

  useUpdate((loginRes) => {
    const { logined } = loginRes || {};
    if (logined) {
      const guideStep = Taro.kbGetGlobalData(key) || 1;
      if (guideStep != 1) {
        triggerOpen(guideStep);
      } else {
        getStorage({
          key: key,
        }).then((res) => {
          const hasOpen = res && res.data && res.data.data == 1;
          if (!hasOpen) {
            getActivityDetail({ activity: 'ali_new_customer' }).then((res) => {
              const { is_new, is_receive, is_use } = res || {};
              if (mode == 1) {
                // 没领券引导
                if (is_new == 1 && is_receive != 1) {
                  triggerOpen(1);
                }
              } else if (mode == 2) {
                // 没用券引导
                if (is_receive == 1 && is_use != 1) {
                  triggerOpen(1);
                }
              }
            });
          }
        });
      }
    }
  });

  const handleCancel = () => {
    setIsOpened(false);
    setStorage({
      key: key,
      data: 1,
    });
  };

  const handleNext = (_step) => {
    if (_step == maxSteps) {
      setIsOpened(false);
      triggerUpdateStep(1);
      if (mode == 1) {
        addReceiveCoupon({ activity: 'ali_new_customer' }).then((res) => {
          if (res.code == 0) {
            Taro.kbToast({
              text: '已为您全部领取至“我的优惠券优惠券“',
              onClose: () => {
                Taro.navigator({
                  url: 'order/card',
                });
              },
            });
          }
        });
      }
    } else {
      triggerUpdateStep(_step + 1);
      if (_step == 1) {
        setIsOpened(false);
        Taro.navigator({
          url: 'user',
        });
      }
    }
  };

  return {
    isOpened,
    step,
    handleCancel,
    handleNext,
  };
};
