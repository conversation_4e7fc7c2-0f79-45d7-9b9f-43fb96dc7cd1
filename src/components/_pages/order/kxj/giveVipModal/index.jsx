/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { Image, View, Text } from '@tarojs/components';
import Taro, { Fragment, useState, useEffect } from '@tarojs/taro';
import { AtCurtain } from 'taro-ui';
import { useUpdate } from '@base/hooks/page';
import isUndefined from 'lodash/isUndefined';
import { checkActivityIsPopFrame } from '../_utils';
import { orderAction } from '../../_utils';
import './index.scss';

const GiveVipModal = (props) => {
  // mode th网购退货
  const { mode = '', open } = props;
  const [isOpened, setIsOpened] = useState(false);
  const [detail, setDetail] = useState({});

  useUpdate((data) => {
    if (data.logined) {
      if (mode == 'th') return;
      checkActivityIsPopFrame().then((res) => {
        const { is_popFrame } = res || {};
        if (is_popFrame == 1) {
          setIsOpened(true);
          setDetail(res);
        }
      });
    }
  });

  useEffect(() => {
    if (isUndefined(open)) return;
    setIsOpened(!!open);
  }, [open]);

  const handleClose = () => {
    setIsOpened(false);
  };

  const handleSend = () => {
    orderAction({
      action: 'edit',
      data:
        mode === 'th'
          ? {
              editOrderType: 'th',
            }
          : {},
    });
  };

  const handleVip = () => {
    Taro.navigator({
      url: 'user/member',
    });
  };

  const handleRight = () => {
    Taro.navigator({
      url: 'user/member/right',
    });
  };

  return (
    <Fragment>
      <AtCurtain isOpened={isOpened} onClose={handleClose}>
        <View className='kb-giveVipModal'>
          <Image
            className='kb-giveVipModal-top'
            mode='widthFix'
            src={
              mode == 'th'
                ? 'https://cdn-img.kuaidihelp.com/miniapp/wkd/returns.png'
                : detail && detail.desc && detail.desc.includes('季卡')
                ? 'https://cdn-img.kuaidihelp.com/wkd/miniApp/activity2/shoudan02.png?v=2'
                : 'https://cdn-img.kuaidihelp.com/wkd/miniApp/activity2/shoudan01.png?v=2'
            }
          />
          <View className='kb-giveVipModal-bottom'>
            <View
              className='kb-giveVipModal-desc'
              onClick={handleRight}
              hoverClass='kb-hover-opacity'
            >
              优享寄VIP会员专属权益 ，<Text className='kb-color__brand'>前往查看特权详情&gt;</Text>
            </View>
            <View className='kb-giveVipModal-opt'>
              <View
                className='kb-giveVipModal-opt__btn'
                onClick={handleSend}
                hoverClass='kb-hover-opacity'
              >
                继续寄件
              </View>
              <View
                className='kb-giveVipModal-opt__btn kb-giveVipModal-opt__btn--vip'
                onClick={handleVip}
                hoverClass='kb-hover-opacity'
              >
                查看会员
              </View>
            </View>
          </View>
        </View>
      </AtCurtain>
    </Fragment>
  );
};

GiveVipModal.options = {
  addGlobalClass: true,
};

export default GiveVipModal;
