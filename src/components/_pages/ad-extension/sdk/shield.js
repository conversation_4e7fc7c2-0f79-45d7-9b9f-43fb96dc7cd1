import isBoolean from 'lodash/isBoolean';
import { getPageAsync, getStorageSync, setStorage } from '@base/utils/utils';
import CallbacksPool from '@base/utils/callbacksPool';
import { checkAdCanShow, checkAdShow } from '.';
import dayjs from 'dayjs';
import Taro, { useEffect, useState } from '@tarojs/taro';
import { useSelector } from '@tarojs/redux';
import { batchCheckCourierIsVip } from './vip';
import { AdCheckShowStorage } from './storage';
import { checkIsToday } from '@base/utils/date';
import { shieldAdminAd57 } from './shieldSpecialCase/query.57';

let lock = false;
let resCache = null;

// 批量检查时，拦截情况，保证只触发一次
const callbackIns = new CallbacksPool();
const triggerConsume = (data) => {
  callbackIns.trigger(data);
  lock = false;
};
function batchCheckAdCanShow(data) {
  return new Promise((resolve) => {
    callbackIns.push(resolve);
    if (resCache) {
      // 已有响应数据
      triggerConsume(resCache);
      return;
    }
    if (lock) {
      return;
    }
    lock = true;
    checkAdCanShow(data).then((res) => {
      resCache = res;
      triggerConsume(resCache);
    });
  });
}

// 获取限制缓存次数，非当日返回0
function getLimitCount(key) {
  const { data: limitStorageData, ts: limitStorageTs } = getStorageSync(key);
  const today = dayjs().format('YYYY-MM-DD');
  const lastTime = dayjs(limitStorageTs).format('YYYY-MM-DD');
  if (today !== lastTime) return 0;

  const storageCount = limitStorageData
    ? typeof limitStorageData === 'number'
      ? limitStorageData
      : 1
    : 0;

  return storageCount;
}

/**
 *
 * @param {{key:string}|undefined} limit
 */
export async function setShieldAdLimit(limit) {
  const { key, count = 1 } = limit || {};
  if (!key) return;
  const storageCount = getLimitCount(key);
  setStorage({ key, data: Math.min(1 + storageCount, count) });
}

// ad参数进入，屏蔽后，其他页面相同屏蔽规则
let paramsAd = '';

/**
 *
 * @param {"1"|"2"|"-1"} type 1：官方（流量主）广告；2：自建广告；-1：不屏蔽
 * @param {*} data 传给checkAdCanShow的参数
 * @param {{key:string;count:number}|undefined} limit 拦截限制
 * @param {{position:'ad'|string}} reqData  请求参数
 * @description 屏蔽广告：结合手机白名单与路由参数
 * Tower 任务: 微信-微快递-添加广告屏蔽功能 ( https://tower.im/teams/258300/todos/98752 )
 */
export async function checkIsShieldAd(type, data, limit, reqData) {
  const { position } = { ...reqData };

  if (type === '-1') {
    return false;
  }

  if (type === '2') {
    // 拦截57号广告
    const isShieldAdminAd57 = await shieldAdminAd57(position);
    if (isBoolean(isShieldAdminAd57)) {
      return isShieldAdminAd57;
    }

    // 自建广告，按照position屏蔽：ad - 强制屏蔽的position类型；
    const shieldPositions =
      process.env.PLATFORM_ENV === 'weapp'
        ? ['ad', '2', '3', '4', '18', '19', '20', '26', '27', '28', '41', '42']
        : [];
    if (!shieldPositions.includes(`${position}`)) {
      return false;
    }
  }

  const { key, count = 1 } = limit || {};
  if (key) {
    // 缓存
    // 暂时只支持配置当日拉取次数
    const storageCount = getLimitCount(key);
    if (storageCount >= count) {
      return true;
    }
  }

  // 参数带入的ad
  if (!paramsAd) {
    try {
      const {
        $router: { params: { ad } = {} },
      } = await getPageAsync();
      paramsAd = `${ad}`;
    } catch (error) {}
  }
  let canShow = checkAdShow(paramsAd, type);
  if (!canShow) {
    return true;
  }

  try {
    // 继续检查黑名单
    const resData = await batchCheckAdCanShow(data); // 黑名单屏蔽广告
    const { ad: resAd } = resData || {}; // 屏蔽类型
    canShow = checkAdShow(resAd, type);
  } catch (error) {}

  if (!canShow) {
    return true;
  }

  try {
    // 下单对象是否为vip快递员
    const { isVip } = await batchCheckCourierIsVip();

    if (isVip) {
      return true;
    }
  } catch (error) {}

  return false;
}

/**
 *
 * @param {"1"|"2"|"-1"} type 1：官方（流量主）广告；2：自建广告 -1 不屏蔽
 * @param {*} data
 * @param {{key:string;count:number}|undefined} limit
 */
export function useCheckIsShieldAd(type, data, limit, reqData = { position: 'ad' }) {
  const { isVip = false } = useSelector((state) => state.global);
  const [shield, setShield] = useState(true);
  useEffect(() => {
    // 是否屏蔽官方广告
    checkIsShieldAd(type, data, limit, reqData).then((s) => {
      setShield(s);
    });
  }, []);
  return type === '-1' ? false : shield || isVip;
}

/**
 *
 * @description 当天哪一次展示
 * @param {{position:string}} reqData
 */

const adCSStorageIns = new AdCheckShowStorage();
export async function checkTodayWhichTimeShow(reqData) {
  const { position } = reqData || {};
  const needCheckPositions = process.env.PLATFORM_ENV === 'alipay' ? ['50'] : [];

  // 检查广告展示次数
  const { ts, count = 0 } = await adCSStorageIns.get(position);

  const filter = (list) => {
    if (!needCheckPositions.includes(`${position}`)) return list;

    if (checkIsToday(ts)) {
      return list.filter((item) => {
        const { title } = item;
        const [, time = 0] = title.split('#');
        const num = Number(time);
        if (num > 0) {
          return num === 1 + Number(count);
        }
        return true;
      });
    }
    return list;
  };

  // 更新广告请求次数
  adCSStorageIns.set({ position });

  return filter;
}
