/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { Image, Text, View } from '@tarojs/components';
import Taro, { Fragment, useEffect, useMemo, useState } from '@tarojs/taro';
import classNames from 'classnames';
import { AtButton, AtFloatLayout, AtIcon } from 'taro-ui';
import './chooseCoupon.scss';

const ChooseCoupon = (props) => {
  const { alipayActivityCoupon, quotation, activityDetail, isReturnModule, noNavigator } = props;
  const { voucher_id = '', coupon_name = '' } = alipayActivityCoupon || {};

  const [isOpened, setIsOpened] = useState(false);

  const couponInfo = useMemo(() => {
    const {
      coupon_id,
      discount_type,
      discount_fee,
      goods_weight = 1,
      weight_limit,
    } = quotation || {};

    if (weight_limit && goods_weight > weight_limit) {
      return {
        over_limit_weight: true,
        weight_limit,
        errMsg: `超出当前运力品牌限重${weight_limit}KG,不支持使用优惠券`,
        errText: '暂不可用，超出当前运力品牌限重',
      };
    }
    return {
      card_id: coupon_id,
      discount_type: discount_type,
      discount_fee: discount_fee,
    };
  }, [quotation]);

  useEffect(() => {
    if (couponInfo.errMsg) {
      Taro.kbToast({
        text: couponInfo.errMsg,
      });
    }
  }, [couponInfo.errMsg]);

  const handleSwitchCoupon = () => {
    if (noNavigator) return;
    if (couponInfo.errMsg) {
      Taro.kbToast({
        text: couponInfo.errMsg,
      });
      return;
    }
    Taro.navigator({
      url: 'order/card',
      options: {
        pageSource: 'order_edit',
        action: 'normal',
        type: 'kxj',
        isReturnModule: process.env.PLATFORM_ENV === 'alipay' && isReturnModule ? 1 : '',
      },
    });
  };

  const handleActivityBar = (key) => {
    switch (key) {
      case 'receiveCoupon':
        Taro.navigator({
          url: 'order/kxj/coupon',
        });
        break;
      case 'vip':
        Taro.navigator({
          url: 'user/member',
        });
        break;
    }
  };

  const couponCls = classNames('kb-chooseCoupon__title', {
    'kb-chooseCoupon__coupon__title2': couponInfo && couponInfo.card_id,
  });

  const { is_new, is_receive, is_use } = activityDetail || {};

  return (
    <Fragment>
      <View className='kb-chooseCoupon__coupon' onClick={handleSwitchCoupon}>
        <View className={couponCls}>优惠券</View>
        <View className='kb-chooseCoupon__coupon__content'>
          <View className='kb-chooseCoupon__coupon__content--info'>
            {voucher_id ? (
              <View className='kb-color__red'>{coupon_name}</View>
            ) : couponInfo && couponInfo.card_id ? (
              <View className='kb-chooseCoupon__coupon__content--info-row'>
                <View className='kb-color__red'>
                  {couponInfo.discount_type === 'discount'
                    ? `${couponInfo.discount_fee}折优惠`
                    : `-￥${couponInfo.discount_fee}`}
                </View>
              </View>
            ) : couponInfo && couponInfo.errText ? (
              <View className='kb-color__grey'>{couponInfo.errText}</View>
            ) : (
              <View className='kb-color__grey'>暂无可用优惠券</View>
            )}
          </View>
          {!noNavigator && (
            <AtIcon
              prefixClass='kb-icon'
              value='arrow'
              className='kb-icon-size__base kb-color__grey'
            />
          )}
        </View>
      </View>
      {process.env.PLATFORM_ENV === 'alipay' ? (
        <Fragment>
          <View className='kb-chooseCoupon__tips'>
            <View className='inner'>
              特别注意：无需向快递员额外支付，快递员仅代取件！不管邮寄多轻，都要称重拍照留底！
            </View>
          </View>
          <View
            className='kb-chooseCoupon__couponGuide'
            onClick={() => setIsOpened(true)}
            hoverClass='kb-hover'
          >
            <Image
              className='kb-chooseCoupon__couponGuide-img'
              src='https://cdn-img.kuaidihelp.com/wkd/miniApp/kxj/coupon.png'
            />
            <View className='kb-chooseCoupon__couponGuide-content'>
              如您选择支付宝卡包券享<Text className='kb-color__red'>折扣</Text>,可在支付运费时
              <Text className='kb-color__red'>系统抵扣</Text>
            </View>
            <AtIcon
              prefixClass='kb-icon'
              value='arrow'
              className='kb-icon2 kb-color__red kb-icon-size__sm'
            />
          </View>
          <AtFloatLayout title='抵扣说明' isOpened={isOpened} onClose={() => setIsOpened(false)}>
            <View className='kb-chooseCoupon__explain'>
              <View className='kb-chooseCoupon__explain-tips'>
                如您选择支付宝卡包券享折扣，可在支付运费时系统抵扣&gt;
              </View>
              <Image
                className='kb-chooseCoupon__explain-img'
                src='https://cdn-img.kuaidihelp.com/wkd/miniApp/activity/coupon/desc.png'
              />
              <AtButton
                className='kb-chooseCoupon__explain-btn'
                type='primary'
                circle
                onClick={() => setIsOpened(false)}
              >
                我知道了
              </AtButton>
            </View>
          </AtFloatLayout>
        </Fragment>
      ) : (
        <View>
          {is_new == 1 ? (
            is_receive != 1 ? (
              <View
                className='kb-send__couponGuide'
                onClick={() => handleActivityBar('receiveCoupon')}
                hoverClass='kb-hover'
              >
                <Image
                  className='kb-send__couponGuide-img'
                  src='https://cdn-img.kuaidihelp.com/wkd/miniApp/kxj/coupon.png'
                />
                <View className='kb-send__couponGuide-content'>
                  您有<Text className='kb-color__red'>待领取</Text>的新客福利优惠券，
                  <Text className='kb-color__red'>立即领取</Text>
                </View>
                <AtIcon
                  prefixClass='kb-icon'
                  value='arrow'
                  className='kb-color__red kb-icon-size__sm'
                />
              </View>
            ) : is_use != 1 ? (
              <View
                className='kb-send__couponGuide'
                onClick={() => handleActivityBar('vip')}
                hoverClass='kb-hover'
              >
                <Image
                  className='kb-send__couponGuide-img'
                  src='https://cdn-img.kuaidihelp.com/wkd/miniApp/activity/icon_vip.png'
                />
                <View className='kb-send__couponGuide-content'>
                  新客完成首单寄件，<Text className='kb-color__red'>+送</Text>优享寄
                  <Text className='kb-color__red'>VIP</Text>会员卡
                  <Text className='kb-color__red' style={{ marginBottom: '2px' }}>
                    &gt;
                  </Text>
                </View>
              </View>
            ) : null
          ) : null}
        </View>
      )}
    </Fragment>
  );
};

ChooseCoupon.options = {
  addGlobalClass: true,
};

export default ChooseCoupon;
