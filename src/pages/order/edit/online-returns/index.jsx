import apis from '@/utils/apis';
import KbModal from '@base/components/modal';
import KbPage from '@base/components/page';
import KbTextarea from '@base/components/textarea';
import request from '@base/utils/request';
import { getStorageSync, setStorage, setStorageSync } from '@base/utils/utils';
import { Image, Picker, Text, View } from '@tarojs/components';
import Taro, { Fragment, useEffect, useRef, useState } from '@tarojs/taro';
import { AtIcon, AtInput, AtTextarea } from 'taro-ui';
import { receiveStorageKey } from '@/components/_pages/address/_utils';
import './index.scss';

const OnlineShopReturns = () => {
  const addressRef = useRef(null); // 未经修改的收件人地址
  const cachePlatform = getStorageSync('platform');
  const storagePlat = getStorageSync('curPlat');
  const cacheAddress = getStorageSync('address');

  const [curPlat, setCurPlat] = useState((storagePlat.data && storagePlat.data.curPlat) || 'pdd');
  const [curIdx, setCurIdx] = useState(0);
  const [tempBrand, setTempBrand] = useState('pdd');
  const [showBrand, setShowBrand] = useState(true);
  const [open, setOpen] = useState(false);
  const [modalType, setModalType] = useState('');
  const [returnPic, setReturnPic] = useState('');
  const [info, setInfo] = useState('');
  const [areaSize, setAreaSize] = useState('S'); // 地址识别框大小
  const [platformList, setPlatFormList] = useState(cachePlatform ? cachePlatform.data : []);
  const [name, setName] = useState('');
  const [mobile, setMobile] = useState('');
  const [region, setRegion] = useState('');
  const [address, setAddress] = useState('');

  const stepInfo = ['选择退货平台', '确认退货地址', '寄回商品'];

  const btnDisabled = !name || !mobile || !region || !address;

  useEffect(() => {
    let list = Taro.kbGetGlobalDataOnce(receiveStorageKey);
    if (cacheAddress && list && list.length > 0) {
      const {
        receive_address = '',
        receive_city = '',
        receive_district = '',
        receive_mobile = '',
        receive_name = '',
        receive_province = '',
      } = cacheAddress.data || {};

      // 默认省市区
      const defaultRegion =
        receive_province || receive_city || receive_district
          ? [receive_province, receive_city, receive_district]
          : undefined;

      setName(receive_name);
      setMobile(receive_mobile);
      setRegion(defaultRegion);
      setAddress(receive_address);
    }
  }, []);

  useEffect(() => {
    Taro.setNavigationBarTitle({
      title: '网购退货专属优惠',
    });
    if (!btnDisabled) {
      setCurIdx(1);
    }
    queryPlatList();
  }, [btnDisabled]);

  const queryPlatList = async () => {
    const res = await request({
      url: '/v1/Online/merchantReturnPlatforms',
    });
    if (res.code === 0) {
      setPlatFormList(res.data);
      setStorageSync('platform', res.data);
    }
  };

  const chooseBrand = (brand) => {
    const { type } = brand;
    if (returnPic) {
      setModalType('brand');
      setTempBrand(type);
      setOpen(true);
    } else {
      setCurPlat(type);
    }
  };

  const onConfirm = () => {
    if (modalType === 'brand') {
      setCurPlat(tempBrand);
      setOpen(false);
      setReturnPic('');
      setCurIdx(0);
      setInfo('');
      setName('');
      setMobile('');
      setAddress('');
      setRegion('');
    } else {
      uploadPic();
    }
  };

  const previewImg = () => {
    Taro.previewImage({
      urls: [`https://cdn-img.kuaidihelp.com/miniapp/wkd/picture_${curPlat}.png`],
    });
  };

  const clearInfo = () => {
    setInfo('');
    setName('');
    setMobile('');
    setAddress('');
    setRegion('');
  };

  const uploadSuccessCallback = (res, filePath) => {
    if (res.code == 0 && res.data && res.data.file_path) {
      request({
        url: '/v1/Ocr/ocrNamePhoneAddress',
        data: {
          img: res.data.file_path,
          type: 'merchant_return',
        },
        toastError: true,
        quickTriggerThen: true,
        toastLoading: '解析中...',
        onThen: ({ data }) => {
          Taro.hideToast();
          const {
            original: text,
            detail,
            city_name,
            province_name,
            county_name,
            name,
            phone,
          } = data || {};
          setReturnPic(filePath);
          setShowBrand(false);
          setCurIdx(1);
          setAreaSize('L');
          setInfo(text);
          setName(name);
          setMobile(phone);
          setRegion([province_name, city_name, county_name]);
          setAddress(detail);
          addressRef.current = detail;
        },
      });
    } else {
      setModalType('pic');
      setOpen(true);
    }
  };

  const uploadPic = () => {
    Taro.chooseImage({
      success(res) {
        const tempFilePaths = res.tempFilePaths;
        Taro.showToast({
          title: '解析中...',
          icon: 'loading',
        });
        request({
          url: '/v1/WeApp/uploadAttachments',
          data: {
            filePath: tempFilePaths[0],
            type: 'merchant_return',
          },
          requestDataType: 'file',
          toastError: true,
          quickTriggerThen: true,
          toastLoading: process.env.PLATFORM_ENV === 'alipay' ? false : '图片上传',
          onThen: (res) => uploadSuccessCallback(res, tempFilePaths[0]),
        });
      },
    });
  };

  const exchangeBrand = () => {
    if (showBrand) {
    } else {
      setShowBrand(true);
    }
  };

  const onValueChange = (e, type) => {
    if (type === 'name') {
      setName(e);
    } else if (type === 'mobile') {
      setMobile(e);
    } else if (type === 'region') {
      const chooice = e.target.value;
      setRegion(chooice);
    } else if (type === 'info') {
      setInfo(e);
    } else {
      setAddress(e);
    }
  };

  const intelligentPaste = () => {
    Taro.getClipboardData().then(({ data: word }) => {
      if (word) {
        setInfo(word);
        request({
          url: apis['address.parse'],
          data: {
            address: word,
          },
          onThen: ({ data }) => {
            const {
              address: identifyAddress,
              name: identifyName,
              mobile,
              city_name,
              province_name,
              county_name,
            } = data[0];
            setName(identifyName);
            setMobile(mobile);
            setRegion([province_name, city_name, county_name]);
            setAddress(identifyAddress);
          },
        });
      } else {
        Taro.kbToast({
          text: '粘贴板上没有内容哦',
        });
      }
    });
  };

  const submitReturns = () => {
    if (btnDisabled) {
      Taro.kbToast({
        text: '收件人信息不完整',
      });
    } else if (addressRef.current !== address) {
      request({
        url: '/g_wkd/v2/Yj/Order/validateMerchantReturnAddress',
        data: {
          address,
        },
        onThen: ({ code }) => {
          if (code === 0) {
            handleSuccessInfo();
          } else {
            Taro.showToast({ icon: 'none', title: '收件地址非商家地址，前往寄快递板块进行寄件' });
          }
        },
      });
    } else {
      handleSuccessInfo();
    }
  };

  const handleSuccessInfo = () => {
    const [province, city, district] = region || [];
    setStorage({
      key: 'curPlat',
      data: {
        curPlat,
      },
    });
    Taro.navigator({
      post: {
        type: 'addressBatch',
        data: {
          list: [
            {
              name,
              mobile,
              province,
              city,
              district,
              address,
            },
          ],
        },
      },
    });
  };

  const onFocusInfo = () => {
    setAreaSize('L');
  };

  const clearAreaInfo = () => {
    setInfo('');
  };

  const identifyInfo = () => {
    info.length > 0
      ? request({
          url: apis['address.parse'],
          data: {
            address: info,
          },
          onThen: ({ data }) => {
            const {
              address: identifyAddress,
              name: identifyName,
              mobile,
              city_name,
              province_name,
              county_name,
            } = data[0];
            setName(identifyName);
            setMobile(mobile);
            setRegion([province_name, city_name, county_name]);
            setAddress(identifyAddress);
          },
        })
      : Taro.showToast({
          title: '请先填写信息',
          icon: 'none',
        });
  };

  const choicePlat =
    platformList.filter((d) => d.type === curPlat).length > 0
      ? platformList.filter((d) => d.type === curPlat)[0].name
      : '拼多多';

  return (
    <KbPage>
      <View className='online-shop-returns'>
        <View className='send-step'>
          {stepInfo.map((d, idx) => (
            <Fragment key={d}>
              <Text className={idx <= curIdx ? 'curStep' : ''}>
                {idx + 1}.{d}
              </Text>
              {idx !== 2 && (
                <AtIcon
                  size='12'
                  className='kb-color__arrow'
                  prefixClass='kb-icon'
                  value='long-arrow'
                />
              )}
            </Fragment>
          ))}
        </View>
        <View className='return-platform'>
          <View className='return-title' onClick={exchangeBrand}>
            <View className='return-title-desc'>1.选择退货平台</View>
            {!showBrand && (
              <View className='return-title-brand'>
                {choicePlat}
                <AtIcon size='12' prefixClass='kb-icon' value='arrow' />
              </View>
            )}
          </View>
          {showBrand && (
            <Fragment>
              <View className='return-platform-chooice'>
                {platformList.map((f) => (
                  <View
                    onClick={() => chooseBrand(f)}
                    key={f.type}
                    className={`return-platform-chooice-radio ${
                      curPlat === f.type ? 'current' : ''
                    }`}
                  >
                    {f.name}
                  </View>
                ))}
              </View>
              <View className='return-platform-tips'>
                微快递当前支持识别上述平台退货，需您上传对应平台退货截
                图，便于识别您的的退货地址信息
              </View>
            </Fragment>
          )}
        </View>
        <View className='return-address'>
          <View className='return-title'>
            <View className='return-address-title-left'>2.确认退货地址</View>
            <View
              className='return-title-right'
              onClick={() => Taro.navigator({ url: 'order/edit/returns-demo' })}
            >
              截图示例
            </View>
          </View>
          <View className='return-address-content'>
            <View className='return-address-content-preview'>
              <Image
                className='return-address-content-preview-img'
                src={returnPic || `//cdn-img.kuaidihelp.com/miniapp/wkd/picture_${curPlat}.png`}
              />
              {!returnPic && (
                <View className='return-address-content-preview-mask' onClick={previewImg}>
                  <View className='top'>{choicePlat}退货页面</View>
                  <View className='bottom'>点击查看</View>
                </View>
              )}
            </View>
            <View className='return-address-content-upload'>
              {!returnPic && (
                <Fragment>
                  <View className='return-address-content-upload-desc'>
                    为帮助您快速识别退货地址
                  </View>
                  <View>请参照左图，上传退货页面截图</View>
                </Fragment>
              )}
              <View
                className='return-address-content-upload-btn'
                style={{
                  marginTop: returnPic ? '240rpx' : '32rpx',
                  background: returnPic ? '#fff' : '#009fff',
                  color: returnPic ? '#009FFF' : '#fff',
                  border: returnPic ? '2rpx solid #009FFF' : 'none',
                }}
                onClick={uploadPic}
              >
                <AtIcon
                  size='12'
                  prefixClass='kb-icon'
                  className='screenshot-icon'
                  value='screenshot'
                />
                <Text>{returnPic ? '重新上传退货截图' : '上传退货截图'}</Text>
              </View>
            </View>
          </View>
        </View>
        <View className='return-edit'>
          <View className='return-title'>3.编辑退货地址</View>
          {areaSize === 'S' ? (
            <View className='return-edit-paste'>
              <AtInput
                onFocus={onFocusInfo}
                className='return-edit-paste-input'
                placeholder='请粘贴地址信息'
                onChange={(e) => onValueChange(e, 'info')}
                value={info}
              />
              <Text onClick={intelligentPaste} className='return-edit-paste-icon'>
                智能粘贴
              </Text>
            </View>
          ) : (
            <View className='return-edit-paste'>
              <AtTextarea
                count={false}
                className='return-edit-paste-textarea'
                placeholder='请粘贴地址信息'
                value={info}
                onChange={(e) => onValueChange(e, 'info')}
              />
              <View className='manual-edit'>
                <Text className='manual-edit-clear' onClick={clearAreaInfo}>
                  清空
                </Text>
                <View className='manual-edit-identify' onClick={identifyInfo}>
                  识别地址
                </View>
              </View>
            </View>
          )}
          <View className='kb-form__item kb-form__item--name'>
            <View className='at-col'>
              <View className='at-row'>
                <View className='item-content'>
                  <AtInput
                    name='name'
                    title=''
                    placeholder='姓名'
                    value={name}
                    onChange={(e) => onValueChange(e, 'name')}
                  />
                </View>
              </View>
              {!name.length > 0 && (
                <View className='kb-color__red kb-size__base kb-margin-sm-b'>* 姓名不能为空</View>
              )}
            </View>
          </View>
          <View className='kb-form__item'>
            <View className='at-col'>
              <View className='at-row'>
                <View className='item-content'>
                  <AtInput
                    name='mobile'
                    title=''
                    placeholder='手机号或固话，多个号码中间用空格或；隔开'
                    value={mobile}
                    onChange={(e) => onValueChange(e, 'mobile')}
                  />
                </View>
              </View>
              {!mobile.length > 0 && (
                <View className='kb-color__red kb-size__base kb-margin-sm-b'>* 号码不能为空</View>
              )}
            </View>
          </View>
          <View className='region-picker form-item'>
            <Picker mode='region' value={region} onChange={(e) => onValueChange(e, 'region')}>
              <View className='region-picker-label' style={{ color: region ? '#000' : '' }}>
                {(region && region.join(' ')) || '省市区'}
              </View>
              <View className='region-picker-arrow'>
                <View className='region-picker-arrow-content' />
                <AtIcon
                  size='12'
                  className='region-picker-arrow-icon'
                  prefixClass='kb-icon'
                  value='arrow'
                />
              </View>
            </Picker>
          </View>
          <View className='kb-form__item form-item'>
            <View className='at-col'>
              <View className='at-row'>
                <View className='item-content item-content__edit'>
                  <View className='at-row at-row__align--center'>
                    <View className='at-col'>
                      <KbTextarea
                        placeholder='详细地址(如X栋X单元号)'
                        value={address}
                        maxLength={100}
                        count={false}
                        height={50}
                        focus
                        onChange={(e) => onValueChange(e, `address`)}
                      />
                      <AtIcon
                        prefixClass='kb-icon'
                        className='clear-icon'
                        value='delete'
                        color='red'
                        size={15}
                        onClick={clearInfo}
                      />
                    </View>
                  </View>
                </View>
              </View>
              {!address.length > 0 && (
                <View className='kb-color__red kb-size__base kb-margin-sm-b'>
                  * 详细地址不能为空
                </View>
              )}
            </View>
          </View>
        </View>
        <View className='return-footer'>
          <View
            className={`return-footer-btn ${btnDisabled ? 'btnDisabled' : ''}`}
            onClick={submitReturns}
          >
            去退货
          </View>
        </View>
        <KbModal
          closePosition='bottom'
          isOpened={open}
          top={null}
          title={modalType === 'brand' ? '更换退货平台' : '图片识别异常'}
          cancelText='取消'
          confirmText={modalType === 'brand' ? '仍然更换' : '重新上传'}
          onCancel={() => setOpen(false)}
          onConfirm={onConfirm}
          content={
            modalType === 'brand'
              ? '更换退货平台后，所有信息将会清空 您需要重新上传截图'
              : '抱歉，未识别到商家收货信息您可手动填写或重新上传截图'
          }
        />
      </View>
    </KbPage>
  );
};

export default OnlineShopReturns;
