/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { Fragment, useState, useEffect } from '@tarojs/taro';
import { View } from '@tarojs/components';
import KbModal from '@base/components/modal';
import { homePage } from '~/utils/config';
import './index.scss';

const KbStopReceiveOrder = (props) => {
  const { relationInfo, courierConfig } = props;
  const { type: relationInfoType } = relationInfo || {};
  const { receiving_order, receiving_order_content } = courierConfig || {};
  const [isOpened, setIsOpened] = useState(false);

  useEffect(() => {
    if (relationInfoType === 'courier' && receiving_order == 1) {
      setIsOpened(true);
    } else {
      setIsOpened(false);
    }
  }, [relationInfoType, receiving_order]);

  const handleClose = () => {
    setIsOpened(false);
  };

  const handleConfirm = () => {
    Taro.navigator({
      url: 'order/relation',
      options: {
        type: 'courier',
      },
    });
    return true;
  };
  const handleCancel = () => {
    Taro.navigator({
      url: homePage,
    });
    return true;
  };

  return (
    <Fragment>
      <KbModal
        isOpened={isOpened}
        top={false}
        closable={false}
        closeOnClickOverlay={false}
        title='温馨提示'
        cancelText='返回主页'
        confirmText='切换下单对象'
        onClose={handleClose}
        onConfirm={handleConfirm}
        onCancel={handleCancel}
      >
        <View className='kb-stopReceiveOrder'>
          <View>该快递员已暂停接单~~~</View>
          {receiving_order_content ? (
            <View className='kb-margin-md-t'>暂停时间：{receiving_order_content}</View>
          ) : null}
        </View>
      </KbModal>
    </Fragment>
  );
};

KbStopReceiveOrder.options = {
  addGlobalClass: true,
};

KbStopReceiveOrder.defaultProps = {
  open: false,
};

export default KbStopReceiveOrder;
