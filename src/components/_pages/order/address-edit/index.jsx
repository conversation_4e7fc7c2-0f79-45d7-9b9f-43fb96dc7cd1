/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import {
  addressKeys,
  chooseAddress,
  defaultSendStorageKey,
  getDefaultSendAddress,
  getErrorAddressIndex,
  receiveStorageKey,
} from '@/components/_pages/address/_utils';
import KbClipborard from '@/components/_pages/clipboard';
import KbAddressClipborardText from '@/components/_pages/order/address-edit/address-clipborard';
import KbArea from '@/components/_pages/order/address-edit/area';
import KbRealname from '@/components/_pages/order/realname';
import { addressList, formatAddress, getForm } from '@/components/_pages/order/_utils';
import KbCheckbox from '@base/components/checkbox';
import KbGps from '@base/components/gps';
import KbImagePicker from '@base/components/image-picker';
import KbSpeech from '@base/components/speech';
import KbTextarea from '@base/components/textarea';
import addressFormat from '@base/utils/addressFormat';
import Form from '@base/utils/form';
import {
  checkIsReLogin,
  createListener,
  debounce,
  getStorage,
  noop,
  setStorage,
} from '@base/utils/utils';
import { Text, View } from '@tarojs/components';
import { connect } from '@tarojs/redux';
import Taro, { Component, createRef, Fragment } from '@tarojs/taro';
import classNames from 'classnames';
import isArray from 'lodash/isArray';
import isEmpty from 'lodash/isEmpty';
import isUndefined from 'lodash/isUndefined';
import qs from 'qs';
import { AtButton, AtIcon, AtInput } from 'taro-ui';
import './index.scss';

@connect(({ global }) => ({
  loginData: global.loginData,
}))
class Index extends Component {
  static defaultProps = {
    relationInfo: {},
    storageKey: defaultSendStorageKey,
    data: {},
    org: '',
    useDefault: false,
    sendLocked: false,
    locked: false,
    lib: true,
    supportBatch: false,
    hideWaterTag: false,
    supportTemporary: false,
    pageName: '',
    hideSaveAddr: true,
    switchAddress: false,
    showClipborard: true,
    onChange: () => {},
    onCheck: () => {},
    onSwitch: () => {},
  };
  static options = {
    addGlobalClass: true,
  };
  constructor(props) {
    super(props);
    this.cityKeys = ['province', 'city', 'district', 'address'];
    const bars = [
      {
        key: 'ai',
      },
      {
        key: 'camera',
        icon: 'camera',
      },
      {
        key: 'mic',
        icon: 'mic',
      },
    ];
    const { org, addrList } = props;
    const _addressList = addrList || addressList;
    const specialAddrList = _addressList.map((d) => {
      return {
        ...d,
        placeholder:
          this.checkIsReturn() && d.key === 'receive' ? ['请填写商家退货地址'] : d.placeholder,
      };
    });
    const list = specialAddrList.filter((item) => item.key === org);

    this.state = {
      list: list.length > 0 ? list : specialAddrList,
      bars,
      receiveTotal: 0,
      receiveErrMsg: '',
      current: org,
      ai: {
        send: { open: false, value: '' },
        receive: {
          open: false,
          value: '',
        },
      },
      form: {
        data: {},
        disabled: true,
      },
      showTip: [false, false],
      switchAddressLoading: false,
    };
    this.realnameRef = createRef();
    this.clipborardRef = createRef();
    this.getDefaultAddress = debounce(this.getDefaultAddress, 300, {
      trailing: true,
    });
    this.getReceiveStorageListDebounce = debounce(this.getReceiveStorageListDebounce, 300);
  }

  componentDidMount() {
    this.getClipborardTipByStorageIsShowed();
    this.createForm().then(() => {
      // 表单缓存准备就绪后执行后以下更新逻辑
      const { relationInfo, data } = this.props;
      if (!this.triggerWhetherSendLocked(relationInfo)) {
        const { send_mobile, send_tel } = data || {};
        if (send_mobile || send_tel) return;
        this.getDefaultAddress();
      }
      this.getReceiveStorageList('init');
    });
  }

  componentDidShow() {
    this.getReceiveStorageList();
  }

  componentDidUpdate(preProps) {
    const {
      data: nextData,
      loginData: nextLoginData,
      relationInfo: nextRelationInfo,
      sendLocked: nextSendLocked,
    } = this.props;
    const { data, loginData, relationInfo } = preProps;

    // 更新信息
    if (nextData && nextData !== data) {
      const { clean, ...rest } = nextData;
      const { send_mobile, send_tel } = rest;
      this.updateFormData(rest);
      // 外部触发的清理动作
      if (clean) {
        this.receiveList = null;
      }
      //返回是否需要更新收/发件人信息
      if (send_mobile || send_tel) return;
    }
    !nextSendLocked && checkIsReLogin(nextLoginData, loginData, this.getDefaultAddress);
    if (
      relationInfo !== nextRelationInfo &&
      nextSendLocked &&
      this.triggerWhetherSendLocked(nextRelationInfo)
    ) {
      return;
    }
  }

  setShowTip = (data) => {
    if (!data) {
      this.setState({
        showTip: [true, true],
      });
    }
  };

  getClipborardTipByStorageIsShowed = () =>
    getStorage({
      key: 'clipborardShowed',
    })
      .then((res) => this.setShowTip(res.data))
      .catch(() => this.setShowTip());

  triggerWhetherSendLocked = (relationInfo) => {
    const { placeOrderConfig } = relationInfo || {};
    if (placeOrderConfig && 'senderInfo' in placeOrderConfig) {
      this.updateFormData({ ...placeOrderConfig.senderInfo });
      return true;
    }
    return false;
  };

  // 防抖处理后的
  getReceiveStorageListDebounce = (status = 'reload', data) => {
    const list = data || Taro.kbGetGlobalDataOnce(receiveStorageKey);
    if (list) {
      // 如果全局有数据，从全局获取
      this.updateFormDataByReceiveList(list);
    } else if (status === 'init') {
      // 初次加载才会使用本地缓存数据
      this.checkReceiveStorageList().then((res) => {
        if (isArray(res)) {
          this.updateFormDataByReceiveList(res);
        }
      });
    }
  };

  // 获取本地缓存的收件人列表
  checkReceiveStorageList = () => {
    return new Promise((resolve) => {
      getStorage({
        key: receiveStorageKey,
      })
        .then((res) => {
          const { data } = res.data || {};
          resolve(data);
        })
        .catch(() => {
          resolve();
        });
    });
  };
  getReceiveStorageList = (status) => {
    const { useDefault } = this.props;
    if (!useDefault || !this.formIns) return;
    this.getReceiveStorageListDebounce(status);
  };

  //检查表单上次是否有缓存地址
  checkLastStorageData = () => {
    let { storageKey } = this.props;
    return new Promise((resolve, reject) => {
      if (!storageKey) reject();
      getStorage({
        key: storageKey,
      })
        .then((res) => {
          const { data } = res.data;
          if (data && data.send_name) {
            resolve();
          } else {
            reject();
          }
        })
        .catch(() => {
          reject();
        });
    });
  };
  // 获取默认发件人
  getDefaultAddress = () => {
    const { useDefault, loginData: { logined } = {}, storageKey } = this.props;
    // 未登录或者不允许使用默认数据，则跳过缓存逻辑
    if (!useDefault || !logined) return;
    getDefaultSendAddress(storageKey).then((data) => {
      this.updateFormData(
        {
          ...data,
        },
        'send',
      );
    });
  };

  // 标准地址
  formatAddress = (data, type, opts) => formatAddress(data, type, opts).data;

  // 更新表单
  updateFormData = (data, key, keys) => {
    if (!this.formIns) return;
    this.formIns.update(key ? this.formatAddress(data, key, { reverse: true, keys }) : data);
  };
  clearFormData = (prefix) => {
    const clearGroup = {};
    addressKeys.forEach((key) => {
      clearGroup[`${prefix}_${key}`] = '';
    });
    this.formIns.update(clearGroup);
  };
  // 选择城市区域
  onChange_city = (key, data) => {
    const { sendLocked } = this.state;
    if (sendLocked && key == 'send') return;
    this.updateFormData(data, key, this.cityKeys.slice(0, -1));
  };

  // 创建表单
  // 表单输入变化
  onChange_form = () => {};
  onSubmit_form = () => {};
  createForm = () => {
    return new Promise((resolve) => {
      const { list } = this.state;
      let { storageKey, data, supportBatch } = this.props;
      let form = getForm({ list, data });
      if (!isEmpty(data)) {
        // 非空的数据，生成默认值
        Object.keys(data).forEach((key) => {
          if (form[key]) {
            form[key].defaultValue = data[key];
          }
        });
      }
      this.formIns = new Form(
        {
          form,
          storageKey,
          onUpdate: supportBatch ? this.triggerChange : this.props.onChange,
          onReady: resolve,
        },
        this,
      );
    });
  };

  // 获取批量收件人列表
  getReceiveLength = () => (isArray(this.receiveList) ? this.receiveList.length : 0);

  // 检测是否为批量发件：确定是否操作收件人相关，且收件人列表长度超过1
  checkIsBatchEdit = (key, total = this.getReceiveLength()) => {
    return this.props.supportBatch && key === 'receive' && total > 1;
  };

  // 根据收件人条目做拦截处理
  resolveByReceiveTotal = (key, then = noop) => {
    if (this.checkIsBatchEdit(key)) {
      this.handleBatch();
    } else {
      then();
    }
  };

  // 触发改变，将表单数据与收件人列表数据一起回传给父级
  triggerChange = (data = this.state.form.data) => {
    let receiveErrMsg = '';
    let { current } = this.state;
    const receiveTotal = this.getReceiveLength();
    const isBatch = this.checkIsBatchEdit('receive', receiveTotal);
    this.props.onChange({
      ...data,
      receiveList: isBatch ? this.receiveList : null,
    });
    if (isBatch) {
      const { errMsg, index } = getErrorAddressIndex(this.receiveList, false);
      receiveErrMsg = errMsg;
      this.errorIndex = index;
    }
    // 设置展示信息
    this.setState({
      current: this.checkIsBatchEdit(current, receiveTotal) ? '' : current,
      receiveTotal,
      receiveErrMsg,
    });
  };

  // 选择图片
  onImagePickerChange = (type, paths) => {
    // 监听图片识别
    createListener('parseImage', (data) => {
      this.switchAiStatus(type, { value: data.text, open: true });
    });
    Taro.navigator({
      url: `cutting?${qs.stringify({
        path: paths[0],
        type,
      })}`,
    });
  };

  // 语音录入变化
  onSpeechChange = (type, { result: value }) => this.switchAiStatus(type, { value, open: true });

  // 常用地址保存选项
  handleCheckChange = (type) => this.onClickBar(type, 'saveAddr');
  // 点击操作
  onClickBar = (type, action, e) => {
    e && e.stopPropagation();
    const { relationInfo } = this.props;

    switch (action) {
      case 'ai':
        const {
          ai,
          form: { data },
        } = this.state;
        const open = ai[type].open;
        let value = '';
        // 切换智能录入
        if (!open) {
          value = addressKeys
            .map((key) => data[`${type}_${key}`])
            .filter((item) => !!item)
            .join(' ');
        }

        this.switchAiStatus(type, { open: !open, value });
        break;
      case 'camera':
        break;
      case 'mic':
        break;
      case 'lib':
        this.resolveByReceiveTotal(type, () => {
          // 监听地址选择：source= list|edit  列表页|编辑页
          console.log('触发3', type);
          createListener('addressSelect', ({ data: res, source, list }) => {
            this.switchAiStatus(type, { open: false }, () => {
              if (list && list.length > 0) {
                this.updateFormDataByReceiveList(list);
                return;
              }
              const needTranKeys = ['list', 'ecode'];
              this.updateFormData(res, needTranKeys.includes(source) && type);
            });
          });
          let params = {
            org: type,
            action: 'select',
          };
          // 大客户参数
          if (relationInfo.customer) {
            params.pageType = 'company';
            params.customer_id = relationInfo.customer.id;
          }
          Taro.navigator({
            url: `address?${qs.stringify({ ...params })}`,
          });
        });

        break;
      case 'saveAddr':
        let saveAddrData = this.props.saveAddrData;
        saveAddrData[type] = !saveAddrData[type];
        this.props.onCheck({ action: 'saveAddr', saveAddrData });
        break;
    }
  };

  // 清除智能录入状态
  switchAiStatus = (type, data, then) => {
    const { ai } = this.state;
    this.setState(
      {
        ai: {
          ...ai,
          [type]: {
            ...ai[type],
            ...data,
          },
        },
      },
      then,
    );
  };

  // 定位
  onGps = (key, data) => {
    const { status } = data;
    const {
      form: { data: formData },
    } = this.state;
    const province = formData[`${key}_province`];
    if (status === 'init' && province) {
      return;
    }
    this.updateFormData(data, key, this.cityKeys);
  };

  // 智能录入输入
  onAiAction = (key, type, value) => {
    switch (key) {
      case 'change':
        this.switchAiStatus(type, {
          value,
        });
        break;
      case 'parse':
        const { ai } = this.state;
        addressFormat(ai[type].value).then((data) => {
          if (!data) return;
          const formData = this.formatAddress(data, type, { reverse: true });
          this.switchAiStatus(type, { open: false }, () => {
            this.updateFormData(formData);
          });
        });
        break;
    }
  };
  onPaste = (type, word) => {
    this.switchAiStatus(type, { open: true, value: word });
  };

  // 选择微信或支付宝的地址
  handleSelector = () => {
    chooseAddress()
      .then((data) => {
        data.name && this.updateFormData(data, 'send');
      })
      .catch((err) => {
        Taro.kbToast({
          text: err.message,
        });
        console.log(err);
      });
  };

  handleItemClick = (key) => {
    const { pageName, sendLocked, relationInfo = {}, isPickupAddress } = this.props;
    const { customer = {} } = relationInfo;
    const { id: customer_id } = customer;
    if (pageName == 'edit') {
      if (key === 'receive') {
        console.log('receive', key);
        this.handleBatch();
      } else {
        const {
          form: { data: formData },
        } = this.state;
        let sendData = {};
        let reg = /^send_/;
        Object.keys(formData).forEach((item) => {
          if (reg.test(item)) {
            sendData[item.replace(reg, '')] = formData[item];
          }
        });
        createListener('addressEdit', (data) => {
          const { data: nextData } = data || {};
          this.updateFormData(nextData, 'send');
        });
        sendLocked &&
          (sendData.locked = {
            name: true,
            address: true,
            area: true,
          });
        Taro.navigator({
          // key: "routerParamsChange",
          options: {
            ...sendData,
            org: 'send',
            type: customer_id ? 'company' : 'person',
            customer_id,
            isPickupAddress: isPickupAddress ? '1' : '0',
          },
          url: `address/edit`,
        });
      }
    }
  };

  // 同时更新收发件人信息
  updateSendAndReceive = (sendAndReceive) => {
    this.receiveList = null; // 清空批量内容
    this.updateFormData(sendAndReceive);
  };

  // 根据收件人列表更新表单
  updateFormDataByReceiveList = (list = this.receiveList) => {
    this.receiveList = list;
    const [data] = list;
    if (!data) {
      this.clearFormData('receive');
      return;
    }
    this.updateFormData(data ? data : {}, 'receive');
  };

  checkIsReturn = () => {
    const { isReturnModule } = this.props.relationInfo || {};
    return isReturnModule === 1;
  };

  // 跳转批量寄件
  handleBatch = (opts) => {
    let { action = 'edit', index = this.errorIndex } = opts || {};
    const { supportTemporary, extraInfo } = this.props;

    if (!this.checkIsBatchEdit('receive')) {
      // 非批量列表
      const {
        form: { data },
      } = this.state;
      const formData = this.formatAddress(data, 'receive');
      this.receiveList = [formData];
    }

    // 设置全局数据，以便在批量设置页获取
    Taro.kbSetGlobalData(receiveStorageKey, this.receiveList);

    createListener('addressBatch', ({ list, sendAndReceive }) => {
      if (sendAndReceive) {
        // 同时更新寄收件人
        this.updateSendAndReceive(sendAndReceive);
        return;
      }
      this.updateFormDataByReceiveList(list);
    });

    if (this.checkIsReturn()) {
      Taro.navigator({
        url: 'order/edit/online-returns',
      });
    } else {
      Taro.navigator({
        url: 'address/batch',
        options: {
          action,
          index,
          brand: extraInfo && extraInfo.brand,
          supportTemporary: supportTemporary ? 1 : 0,
        },
      });
    }
  };
  handleCloseClipborardTip(e) {
    e && e.stopPropagation();
    setStorage({
      key: 'clipborardShowed',
      data: 0,
    });
    this.setState({
      showTip: [false, false],
    });
  }
  handleClipborard(key, e) {
    e && e.stopPropagation();
    const { triggerGetData } = this.clipborardRef.current || {};
    triggerGetData &&
      triggerGetData(key)
        .catch(() => {
          Taro.kbToast({
            text: '未识别到数据，请复制完整信息后操作',
          });
        })
        .finally(() => {
          this.handleCloseClipborardTip();
        });
  }

  // 收发件信息互换
  handleSwitchAddress(ev) {
    ev.stopPropagation();
    const {
      form: { data = {} },
      list,
    } = this.state;
    const keys = [];
    list.map((item) => keys.push(item.key));
    const [key1, key2] = keys;
    const formData = { ...data };
    Object.keys(formData).map((key) => {
      if (key.includes(`${key1}_`)) {
        let arr = key.split('_');
        [formData[key], formData[`${key2}_` + arr[1]]] = [
          formData[`${key2}_` + arr[1]],
          formData[key],
        ];
      }
    });
    this.props.onSwitch(formData);
    this.setState(
      {
        switchAddressLoading: true,
      },
      () => {
        setTimeout(() => {
          this.formIns.update(formData);
          this.setState({
            switchAddressLoading: false,
          });
        }, 500);
      },
    );
  }

  isAddressEmpty() {
    const { form: { data = {} } = {} } = this.state;
    let empty = true;
    let keys = Object.keys(data) || [];
    for (let i = 0; i < keys.length; i++) {
      const key = keys[i];
      if (key !== 'disabled' && data[key]) {
        empty = false;
        break;
      }
    }
    return empty;
  }

  render() {
    const {
      ai,
      bars,
      list,
      current,
      receiveTotal,
      receiveErrMsg,
      form: { data },
      showTip,
      switchAddressLoading,
    } = this.state;
    const {
      lib,
      supportBatch,
      sendLocked,
      hideWaterTag,
      hideSaveAddr,
      saveAddrData,
      actionRef,
      switchAddress,
      showClipborard,
    } = this.props;
    let clipboardTipMap = {};
    clipboardTipMap['sendShowTip'] = showTip[0] && showTip[1] ? false : showTip[0];
    clipboardTipMap['receiveShowTip'] = showTip[1];
    const rootCls = classNames('kb-address', {
      'kb-address-spacing': !!current,
    });

    return (
      <View className={rootCls}>
        {switchAddress && receiveTotal <= 1 && !this.isAddressEmpty() ? (
          <View
            className='kb-address-qiehuan'
            onClick={this.handleSwitchAddress.bind(this)}
            hoverClass='kb-hover-opacity'
            hoverStopPropagation
          >
            <Text className='kb-icon kb-icon-qiehuan' />
          </View>
        ) : null}
        {list.map(
          /* eslint-disable */
          (item, index) => {
            /* eslint-disable no-new */
            const { key: itemKey } = item;
            const { has, data: valueItem } = formatAddress(data, itemKey, {
              reverse: false,
              keys: addressKeys,
            });
            const send_locked = sendLocked && itemKey == 'send';
            const itemCls = classNames('kb-address__item', `kb-address__item--${itemKey}`, {
              'kb-address__item-last': index == list.length - 1,
              'kb-address__item--open': current === itemKey,
            });
            const iconCls = classNames('kb-icon-size__lg', `kb-color__${item.color}`);
            const typeIconCls = classNames(
              'kb-address-type kb-size__sm kb-color__white kb-margin-lg-lr',
              `kb-background__${item.color}`,
            );
            const addressInfoCls = classNames(
              'at-col',
              'address-info-active',
              `address-info-active-${switchAddressLoading ? index : 100}`,
            );
            const ready = !isUndefined(valueItem.province);
            return (
              <View key={itemKey} className={itemCls}>
                <View
                  className='at-row at-row__align--center'
                  hoverClass='kb-hover'
                  onClick={this.handleItemClick.bind(this, itemKey)}
                >
                  {!hideWaterTag && <View className={typeIconCls}>{item.tag}</View>}
                  <View className='item-container at-col'>
                    <View className='at-row at-row__align--center item-info'>
                      {current !== itemKey ? (
                        <View className={addressInfoCls}>
                          {has ? (
                            <View className='item-info__value'>
                              {(valueItem.name || valueItem.mobile || valueItem.company) && (
                                <View className='item-info__value--label'>
                                  {valueItem.name && (
                                    <Text className='item-info__text'>{valueItem.name}</Text>
                                  )}
                                  {valueItem.company && (
                                    <Text className='item-info__text'>{valueItem.company}</Text>
                                  )}
                                  {valueItem.mobile && (
                                    <Text className='item-info__text'>{valueItem.mobile}</Text>
                                  )}
                                  {valueItem.mobile && itemKey === 'send' && (
                                    <KbRealname
                                      mode='text'
                                      phone={valueItem.mobile}
                                      actionRef={actionRef}
                                      didShowCheck
                                    />
                                  )}
                                </View>
                              )}
                              <View className='item-info__value--desc'>
                                {valueItem.porvince && <Text>{valueItem.porvince}</Text>}
                                {valueItem.city && <Text>{valueItem.city}</Text>}
                                {valueItem.district && <Text>{valueItem.district}</Text>}
                                {valueItem.address && <Text>{valueItem.address}</Text>}
                              </View>
                              {this.checkIsBatchEdit(itemKey, receiveTotal) && (
                                <View className='kb-color__brand kb-size__sm'>
                                  <Text>共{receiveTotal}个收件人</Text>
                                  {receiveErrMsg && (
                                    <Text className='kb-color__red'>（{receiveErrMsg}）</Text>
                                  )}
                                </View>
                              )}
                            </View>
                          ) : (
                            <View className='at-row at-row__align--center'>
                              <View className='item-info__placeholder'>
                                {item.placeholder.map((iitem, iindex) => (
                                  <View key={`ii_${iindex}`}>{iitem}</View>
                                ))}
                              </View>
                              {showClipborard ? (
                                <View className='kb-spacing-lg-lr'>
                                  <KbAddressClipborardText
                                    org={item.key}
                                    showTip={clipboardTipMap[`${item.key}ShowTip`]}
                                    onClose={this.handleCloseClipborardTip.bind(this)}
                                    onClipboard={this.handleClipborard.bind(
                                      this,
                                      `address-${item.key}`,
                                    )}
                                  />
                                </View>
                              ) : null}
                            </View>
                          )}
                        </View>
                      ) : (
                        !send_locked && (
                          <View className='item-bar at-row at-row__align--center at-row__justify--end'>
                            {bars.map((iitem) => {
                              const { key: iitemKey } = iitem;
                              const barCls = classNames(
                                'item-bar__item--bar',
                                `kb-color__${item.color}`,
                              );
                              return (
                                <View key={iitemKey} className='item-bar__item'>
                                  {iitemKey === 'ai' ? (
                                    <View
                                      onClick={this.onClickBar.bind(this, item.key, iitemKey)}
                                      hoverStopPropagation
                                      hoverClass='kb-hover'
                                      className={barCls}
                                    >
                                      {ai[item.key].open ? '传统' : '智能'}录入
                                    </View>
                                  ) : (
                                    <View onClick={this.onClickBar.bind(this, item.key, iitemKey)}>
                                      {iitemKey === 'camera' ? (
                                        <KbImagePicker
                                          onChange={this.onImagePickerChange.bind(this, item.key)}
                                          custom
                                        >
                                          <View
                                            className='item-bar__item--icon'
                                            hoverStopPropagation
                                            hoverClass='kb-hover'
                                          >
                                            <AtIcon
                                              prefixClass='kb-icon'
                                              value={iitem.icon}
                                              className={iconCls}
                                            />
                                          </View>
                                        </KbImagePicker>
                                      ) : iitemKey === 'mic' ? (
                                        <KbSpeech
                                          onChange={this.onSpeechChange.bind(this, item.key)}
                                          iconColor={item.color}
                                          size='mini'
                                        >
                                          <View
                                            className='item-bar__item--icon'
                                            hoverStopPropagation
                                            hoverClass='kb-hover'
                                          >
                                            <AtIcon
                                              prefixClass='kb-icon'
                                              value={iitem.icon}
                                              className={iconCls}
                                            />
                                          </View>
                                        </KbSpeech>
                                      ) : null}
                                    </View>
                                  )}
                                </View>
                              );
                            })}
                          </View>
                        )
                      )}
                      {lib && (
                        <Fragment>
                          {!send_locked && (
                            <View
                              onClick={this.onClickBar.bind(this, itemKey, 'lib')}
                              hoverStopPropagation
                              hoverClass='kb-hover'
                              className='item-bar__item--icon item-bar__item--lib'
                            >
                              <AtIcon prefixClass='kb-icon' value='book' className={iconCls} />
                            </View>
                          )}
                        </Fragment>
                      )}
                    </View>
                  </View>
                </View>
                {current === itemKey && (
                  <View className='kb-form'>
                    <Fragment>
                      {ai[itemKey].open ? (
                        <View className='kb-spacing-md'>
                          <View className='kb-form__item--ai'>
                            <KbTextarea
                              clear
                              count={false}
                              paste
                              keep
                              iconCls='kb-color__grey'
                              maxLength={-1}
                              placeholder='依次输入姓名，电话，地址，三者之间用空格或者逗号隔开，确认后自动填写信息。例如：素素，1588888****，上海市长宁区同协路****号**号楼。'
                              value={ai[itemKey].value}
                              onPaste={this.onPaste.bind(this, itemKey)}
                              onChange={this.onAiAction.bind(this, 'change', itemKey)}
                            />
                          </View>
                          <View className='kb-form__item--button'>
                            <AtButton
                              size='small'
                              disabled={!ai[itemKey].value}
                              circle
                              type='primary'
                              className='kb-display__inline-block kb-button__disabled--grey'
                              onClick={this.onAiAction.bind(this, 'parse', itemKey)}
                            >
                              识别地址
                            </AtButton>
                          </View>
                        </View>
                      ) : (
                        <Fragment>
                          {console.log('valueItem22', valueItem)}
                          <View className='kb-form__item kb-form__item--name'>
                            <View className='item-content'>
                              <AtInput
                                cursor={-1}
                                value={valueItem.name}
                                disabled={send_locked}
                                placeholder='姓名'
                                onChange={this.onChange_form.bind(this, `${itemKey}_name`)}
                              >
                                {item.selector && (
                                  <View onClick={this.handleSelector} hoverClass='kb-hover-opacity'>
                                    <AtIcon
                                      prefixClass='kb-icon'
                                      value={process.env.PLATFORM_ENV}
                                      className={iconCls}
                                    />
                                  </View>
                                )}
                              </AtInput>
                            </View>
                          </View>
                          <View className='kb-form__item'>
                            <View className='item-content'>
                              <AtInput
                                cursor={-1}
                                value={valueItem.mobile}
                                type='text'
                                placeholder='手机号或固话，如都填写中间用空格分开'
                                onChange={this.onChange_form.bind(this, `${itemKey}_mobile`)}
                              />
                            </View>
                          </View>
                          <KbArea
                            locked={send_locked}
                            value={valueItem}
                            onChange={this.onChange_city.bind(this, itemKey)}
                          />
                          <View className='kb-form__item'>
                            <View className='item-content item-content__edit'>
                              <View className='at-row at-row__align--center'>
                                <View className='at-col'>
                                  <KbTextarea
                                    placeholder='详细地址(如X栋X单元号)'
                                    value={valueItem.address}
                                    disabled={send_locked}
                                    maxLength={100}
                                    count={false}
                                    height={process.env.PLATFORM_ENV === 'weapp' ? 0 : 50}
                                    onChange={this.onChange_form.bind(this, `${itemKey}_address`)}
                                  />
                                </View>
                                <View className='item-content__item--extra'>
                                  {supportBatch && item.key === 'receive' ? (
                                    <View
                                      className='kb-color__brand kb-size__base'
                                      hoverClass='kb-hover-opacity'
                                      onClick={this.handleBatch}
                                    >
                                      批量寄件
                                    </View>
                                  ) : !send_locked ? (
                                    ready && (
                                      <KbGps
                                        auto={itemKey === 'send' && !valueItem.province}
                                        onGps={this.onGps.bind(this, itemKey)}
                                        iconColor={item.color}
                                      />
                                    )
                                  ) : null}
                                </View>
                              </View>
                            </View>
                          </View>
                          {!hideSaveAddr && (
                            <View className='kb-form__item kb-form__item--save'>
                              <KbCheckbox
                                className='kb-margin-xs-b'
                                labelClassName='kb-color__grey'
                                checked={saveAddrData[itemKey]}
                                label='保存到常用地址'
                                onChange={this.handleCheckChange.bind(this, itemKey)}
                              />
                            </View>
                          )}
                        </Fragment>
                      )}
                    </Fragment>
                  </View>
                )}
              </View>
            );
          },
        )}
        <KbClipborard actionRef={this.clipborardRef} mode='address' />
      </View>
    );
  }
}

export default Index;
