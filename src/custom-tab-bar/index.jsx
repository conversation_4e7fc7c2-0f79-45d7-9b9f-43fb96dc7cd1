import { View, Image } from '@tarojs/components';
import classNames from 'classnames';
import { useSelector } from '@tarojs/redux';
import { useGetTabBarConfig } from '@/components/_pages/custom-tab-bar/_utils';
import { usePatch } from '@/components/_pages/custom-tab-bar/_utils/patch';
import './index.scss';

const CustomTabBar = () => {
  const { badgeMap, showTabBar, tabBarMap } = useSelector((state) => state.global);
  const [tabs = [], selectedColor, color] = useGetTabBarConfig();
  const { current, switchTab } = usePatch(tabs);

  const handleSwitchTab = (index) => switchTab(index);

  const rootCls = classNames('kb-custom-tab', {
    'tab-hidden': !showTabBar,
  });

  return (
    <View className={rootCls}>
      {tabs.map((item, index) => {
        const badgeMapItem = badgeMap[index];
        const tabBarMapItemStatus = tabBarMap[index];
        const badgeCls = badgeMapItem
          ? classNames(
              'kb-custom-tab__item-badge',
              `badge-${badgeMapItem.text ? 'text' : 'dot'}`,
              `badge-${badgeMapItem.position || 'right'}`,
              `badge-animation-${badgeMapItem.animation || 'normal'}`,
            )
          : '';
        let style = { color };
        let src = item.iconPath;
        if (current === index) {
          style = { color: selectedColor };
          src = item.selectedIconPath;
        }

        const itemCls = classNames(item.className, {
          'kb-custom-tab__item--hidden': tabBarMapItemStatus === 'hide',
        });
        return (
          <View key={item.pagePath} className={itemCls} onClick={handleSwitchTab.bind(null, index)}>
            <Image mode='aspectFit' src={src} className='kb-custom-tab__image' />
            {item.text && item.showText ? (
              <View style={style} className='kb-custom-tab__text'>
                {item.text}
              </View>
            ) : null}
            {badgeMapItem ? <View className={badgeCls}>{badgeMapItem.text}</View> : null}
          </View>
        );
      })}
    </View>
  );
};

CustomTabBar.options = {
  addGlobalClass: true,
};

export default CustomTabBar;
