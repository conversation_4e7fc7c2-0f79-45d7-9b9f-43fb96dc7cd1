/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { preloadAd } from '@/components/_pages/ad-extension/sdk';
import { receiveStorageKey } from '@/components/_pages/address/_utils';
import KbAgreement from '@/components/_pages/agreement';
import KbAddressEdit from '@/components/_pages/order/address-edit';
import KbExtraInfo from '@/components/_pages/order/extra-info';
import KbEstimatedFeeList from '@/components/_pages/order/kxj/estimated-fee/list';
import { supportedBrands } from '@/components/_pages/order/kxj/estimated-fee/utils';
import { getActivityDetail, getReceiveCouponList } from '@/components/_pages/order/kxj/_utils';
import KbSubmitBar from '@/components/_pages/order/submit-bar';
import KbUnPayOrders from '@/components/_pages/order/unPayOrders';
import KbWaitPay from '@/components/_pages/order/waitpay';
import {
  fixDynamicFormsData,
  getForm,
  isFresh,
  transferWkdAddress,
} from '@/components/_pages/order/_utils';
import {
  confirmCredit,
  openCreditService,
} from '@/components/_pages/order/_utils/order.credit-pay';
import {
  bindActivity,
  cleanOrderEditFormInfo,
  getQuotationDetail,
  getUnPayOrders,
  handelDpWeightTips,
  handleCheckCreditService,
  handleECode,
  handleExtraData,
  handleHTBrandModal,
  handleOpenCredit,
  handleSecondConfirmOrder,
  handleSFKYDefaultWeight,
  handleSpringFestive,
  interceptChannel,
  interceptCreditFailOrder,
  onShowFeeWay,
} from '@/components/_pages/order/_utils/order.edit';
import KbStoreCardSelector from '@/components/_pages/store-card/selector';
import { checkCustomerAuthStatus } from '@/components/_pages/store-card/_utils';
import { homeActivityReportAnalytics } from '@/components/_pages/user/activity/_utils';
import { tabItemTapCall } from '@/components/_pages/_utils';
import apis from '@/utils/apis';
import { refreshControl, REFRESH_KEY_KXJ_COUPON } from '@/utils/refresh-control';
import { sendNoticeWkd } from '@/utils/subscribe';
import KbCheckbox from '@base/components/checkbox';
import KbModal from '@base/components/modal';
import KbPage from '@base/components/page';
import KbScrollView from '@base/components/scroll-view';
import Form from '@base/utils/form';
import logger from '@base/utils/logger';
import { getLaunchParams } from '@base/utils/navigator';
import {
  creatSchemeLink,
  debounce,
  getBoundingClientRect,
  noop,
  removeStorage,
  reportAnalytics,
  scanParse,
  setStorage,
} from '@base/utils/utils';
import { Image, Text, View } from '@tarojs/components';
import { connect } from '@tarojs/redux';
import Taro, { Component, createRef, Fragment } from '@tarojs/taro';
import classNames from 'classnames';
import isArray from 'lodash/isArray';
import isObject from 'lodash/isObject';
import { AtButton, AtIcon } from 'taro-ui';
import './index.scss';

@connect(({ global }) => ({
  loginData: global.loginData,
  serviceConfig: global.serviceConfig,
  cacheRelationInfo: global.relationInfo,
}))
class Index extends Component {
  config = {
    navigationBarTitleText: '寄快递',
    backgroundColorTop: '#f2f2f2',
  };
  constructor() {
    this.state = {
      realnamed: false,
      relationInfo: {
        dakId: '',
        courier_id: '',
        relation_id: '',
      },
      relationData: null,
      form: { data: {}, disabled: true },
      dynamicForms: fixDynamicFormsData(),
      addressData: null,
      total: 1,
      extraInfo: {},
      extraInfoData: null,
      estimatedFeeActive: false,
      estimatedData: {},
      equityCard: {},
      isOpenCredit: false,
      agree: false,
      scanCoupon: '',
      miniPostPrinterData: '',
      hTBrandTipsData: 0,
      springFestiveOpen: false,
      interceptChannelOpen: false,
      relationInfoLoading: false,
      oAsyncBatchStatus: {},
      courierConfig: {},
    };
    this.checkEstimatedFee = debounce(this.checkEstimatedFee, 300, {
      trailing: true,
    });
    this.handleSubmitForm = debounce(this.handleSubmitForm, 500, {
      leading: true,
      trailing: false,
    });
    this.interceptChannel = debounce(this.interceptChannel, 500, {
      trailing: true,
    });
    // refs
    this.realnameRef = createRef();
    this.extraInfoRef = createRef();
    this.addressEditRef = createRef();
    this.agreeActionRef = createRef();
    this.estimatedFeeListRef = createRef();
    this.couponModalRef = createRef();
  }

  onTabItemTap = tabItemTapCall;

  checkIsReturn = () => {
    const { isReturnModule } = this.props.cacheRelationInfo || {};
    return isReturnModule === 1;
  };

  componentDidMount() {
    console.log('this.$router.params', this.$router.params);
    this.createForm(() => {
      this.fixFormUpdateData(this.formFixData);
      if (!this.lockedRelation) {
        this.setState({
          relationData: {
            brand: 'sto',
          },
        });
      }
      this.lockedRelation = false;
    });
    const { source } = Taro.launchParams || {};
    if (source) {
      // 上报
      reportAnalytics({
        key: 'launch_source',
        source,
      });
    }
  }

  componentDidShow() {
    this.handleCheckCreditService();
    this.handleSecondConfirmOrder();
    preloadAd(['order.result']);
    if (refreshControl(REFRESH_KEY_KXJ_COUPON, 'check')) {
      this.checkIsReceiveCoupon();
    }
  }

  componentWillUnmount() {
    const relationInfo_temp = Taro.kbGetGlobalDataOnce('relationInfo_temp');
    console.log('relationInfo_temp', relationInfo_temp);
    if (relationInfo_temp && !this.hasEditOrder) {
      Taro.kbUpdateRelationInfo && Taro.kbUpdateRelationInfo(relationInfo_temp);
    }
    this.htBrandTimer && clearInterval(this.htBrandTimer);
  }

  // 登录状态更新
  onUpdate = (data) => {
    const { logined } = data;
    this.logined = logined;
    if (logined) {
      this.checkIsReceiveCoupon();
      // 外部进入时带着下单关系的场景
      let params = getLaunchParams(this);
      if (params.scene) {
        let { query = {} } = scanParse({ scene: params.scene });
        params = {
          ...params,
          ...query,
        };
      }
      this.onPostMessage('routerParamsChange', {
        params,
      });
    }
  };

  // 监听
  onPostMessage = (key, data) => {
    console.log('key', key, data);
    switch (key) {
      case 'routerParamsChange':
        // 路由参数变更
        const {
          params: {
            couponInfo,
            address,
            extraInfo,
            extraData,
            type = '',
            sign,
            uid,
            sub_uid,
            ...rest
          } = {},
          source,
          data: formData,
        } = data;
        if (source === 'goods') {
          this.setState({
            extraInfoData: formData,
          });
        }
        if (address) {
          this.setState({
            addressData: address,
          });
          if (extraInfo) {
            this.setState({
              extraInfoData: extraInfo,
            });
          }
          if (type === 'clone') {
            // 再来一单，重新下单
            this.cleanFormInfo(['estimatedData']);
          }
        }
        if (sign) {
          // 快递码
          this.handleECode({ sign });
        }
        if (uid || sub_uid) {
          // 邀请下单码-小程序码、分享
          bindActivity(uid || sub_uid);
        }
        if (extraData && extraData.type) {
          this.handleExtraData(extraData);
        }
        if (couponInfo) {
          this.onPostMessage('cardSelect', couponInfo);
        }
        // 进入寄快递携带下单信息
        const { brand } = rest;
        const relationList = [brand];
        const hasRelation = relationList.filter((item) => !!item).length > 0;
        if (hasRelation) {
          this.lockedRelation = true;
          this.setState({
            relationData: rest,
          });
        }
        break;
      case 'cardSelect':
        this.setState({
          couponInfo: data,
        });
        break;
    }
  };

  // 更新下单关系数据
  updateRelationInfo = (data) => {
    // 处理开学寄下单对象
    if (data && data.type) {
      const oBrand =
        data.type == 'brand' && data.brand && supportedBrands.find((i) => i.brand === data.brand);
      if (!oBrand) {
        Taro.kbSetGlobalData('relationInfo_temp', data);
      }
    }
    const { dynamicForms, smjData, type, brand } = data || {};
    let state = {
      relationInfo: {
        ...data,
        disabled: !!smjData, //扫码寄切换下单对象
      },
    };
    state.dynamicForms = fixDynamicFormsData(dynamicForms ? dynamicForms : {});
    this.setState({ ...state }, () => {
      this.handleSFKYDefaultWeight();
      this.checkEstimatedFee();
      this.interceptChannel((notActive) => {
        this.setState({
          interceptChannelOpen: notActive,
        });
      });
      if (type == 'brand' && brand == 'ht') {
        this.handleHTBrandModal('open');
      }
    });
  };

  // 处理下单对象loading
  dealRelationLoading(relationInfoLoading = false) {
    this.setState({
      relationInfoLoading,
    });
  }

  // 处理变化事件
  onChange = (key, e) => {
    const { data, nextData, receiveList } = e || {};
    switch (key) {
      case 'address':
        const { receive_province, send_province } = nextData;
        receive_province && send_province && (data.disabled = false);
        this.fixFormUpdateData(data);
        this.updateReceiveList(receiveList);
        this.checkEstimatedFee();
        break;
      case 'info':
        // 物品信息变化
        this.setState(
          {
            extraInfo: nextData,
          },
          () => {
            this.handleSFKYDefaultWeight();
            this.checkEstimatedFee();
          },
        );
        break;
      case 'estimatedFee':
        this.estimatedFee = data.estimatedFee || 0;
        break;
      case 'equityCard':
        this.equityCard = data;
        break;
      case 'coupon':
        this.coupon = data;
        break;
    }
  };

  // 处理滑动区域相关事件
  onHandleMoveArea = (key = 'size', data = {}) => {
    // console.log('处理滑动区域相关事件', key, data);
    // const { moveAreaStatus } = this.state;
    const { changeSource } = data || {};
    if (key == 'status') {
      this.setState({
        moveAreaStatus: data.status,
      });
    } else if (key == 'change') {
      if (data.product_code) {
        this.setState({
          extraInfoData: data,
        });
        return;
      }
      if (data.welfare) {
        this.welfare = data.welfare;
        return;
      }
      if (changeSource == 'list') {
        this.onHandleMoveArea('size');
      }
      if (changeSource == 'list' && data.quotationList) {
        this.quotationList = data.quotationList;
      }
    } else {
      const {
        relationInfo: { platform },
      } = this.state;
      this.minHeight = 150;
      this.smallHeight = 50;
      if (platform == 'yjkd_brand') {
        console.log('显示比价单1');
        try {
          Promise.all(
            [
              '.yj-container-msg',
              {
                selector: '.kb-estimatedFeeList-list--content',
                scope: this.estimatedFeeListRef.current.$scope,
              },
            ].map((item) => getBoundingClientRect(item)),
          ).then((e) => {
            const [cRes, cRes2] = e;
            if (!cRes || !cRes2) {
              return;
            }
            let contentHeightMax = cRes.top - 120;
            let contentHeight = cRes2.height + 30;
            this.maxHeight = Math.min(contentHeight, contentHeightMax);
            this.setState({
              moveAreaData: {
                curHeight: this.minHeight, // 当前高度
                smallHeight: this.smallHeight, // 最小高度
                minHeight: this.minHeight, // 默认高度
                maxHeight: this.maxHeight, // 最大高度
              },
              moveAreaPagePadding: cRes.height + this.minHeight,
            });
          });
        } catch (e) {}
      }
    }
  };

  // 切换是否同意
  onSwitchAgree = (agree) => {
    this.setState({ agree });
  };

  // 创建表单&表单变化
  onChange_form = () => {};
  onSubmit_form = () => {};
  createForm = (onReady) => {
    // 所有表单只包含地址信息，其他数据通过formatRequest合并
    // 这种方式可以利用form.disabled 判断是否展开物品信息
    // this.receiveList 批量下单时的收件人列表数据
    this.formIns = new Form(
      {
        form: getForm(),
        enableEmpty: false,
        api: {
          url: () => {
            const {
              relationInfo: { type: relationType, platform },
            } = this.state;
            if (this.submitAction == 'submitTemporary') {
              return apis[`order.edit.temporary`];
            }
            if (platform == 'yjkd_brand' || platform == 'yjkd_courier') {
              return apis[`order.edit.yjkd`];
            }
            return apis[`order.edit.${relationType}`];
          },
          toastError: true,
          onIntercept: (req) => {
            // 拦截批量请求
            const {
              relationInfo: { type: relationType, platform, brand, dynamicForms } = {},
              isOpenCredit,
              activityDetail,
            } = this.state;
            const { is_receive, is_use } = activityDetail || {};
            const { pay } = dynamicForms || {};
            if (this.submitAction == 'submitTemporary') {
              if (relationType != 'courier') {
                Taro.kbToast({
                  text: '该下单对象暂不支持临时单!',
                });
                return true;
              }
            }
            //下单立即开通支付分;
            if (process.env.PLATFORM_ENV == 'alipay') {
              if (
                !this.IsBackFromCreditPage &&
                !isOpenCredit &&
                platform == 'yjkd_brand' &&
                (pay == 2 || pay == 3)
              ) {
                this.IsBackFromCreditPage = false;
                this.handleOpenCredit({
                  isForceOpen: pay == 2 ? 1 : 0,
                  req,
                });
                return true;
              }
            }
            // 限重不能用券
            if (
              (brand == 'yt' || brand == 'yd') &&
              req.package_weight * 1 > 50 &&
              !this.limitWeightLock
            ) {
              Taro.kbModal({
                title: '温馨提示',
                top: false,
                content: '超出限重50KG，暂不可享受3元优惠券寄件',
                cancelText: '重新选择',
                confirmText: '继续原价下单',
                onConfirm: () => {
                  this.limitWeightLock = true;
                  this.couponNoUsed = true;
                  this.handleSubmitForm();
                },
              });
              return true;
            }
            this.limitWeightLock = false;
            // 德邦重量提示
            let dpWeight = req.package_weight || 1;
            let isOpenDpWeightNoMore = this.handelDpWeightTips('check');
            if (
              platform === 'yjkd_brand' &&
              brand === 'dp' &&
              isOpenCredit &&
              dpWeight < 3 &&
              !isOpenDpWeightNoMore &&
              this.submitAction != 'dpWeightTipsConfirm'
            ) {
              this.handelDpWeightTips('open');
              return true;
            }
            // 未使用券提示
            if (
              is_receive == 1 &&
              is_use != 1 &&
              (!isOpenCredit || !req.coupon_id) &&
              !this.withoutCouponLock &&
              !this.couponNoUsed
            ) {
              this.handelGuideCoupon('open');
              return true;
            }
            this.withoutCouponLock = false;
          },
          formatRequest: ({ extraData: [action = 'submit'] = [], ...req }) => {
            this.submitAction = action || 'submit';
            const { send_save, receive_save } = req;
            const {
              relationInfo: {
                type: relationType,
                account_phone,
                dak_id,
                join_code,
                brand,
                platform = '',
                courier = {},
                dynamicForms = {},
              } = {},
              extraInfo: {
                goods_name,
                goods_weight,
                goods_remark,
                package_images,
                orderPrice,
                reserve_start_time = '1h',
                reserve_end_time = '1h',
                product_code = '',
                volume = {},
                service,
                brand: print_brand,
              } = {},
              courierConfig = {},
              couponInfo,
            } = this.state;
            const isOpenOnlinePay = courierConfig && courierConfig.isOpenOnlinePay == 1;
            const {
              goods_name: dynamicGoods_name,
              appointmentTime: dynamicAppointmentTime,
              service: dynamicService,
              product_code: dynamicProduct_code,
              volume: dynamicVolume,
              coupon_welfare,
            } = dynamicForms || {};
            const { oService, arrive_pay, collection, cost_value, keep_account } = service || {};
            let addressData = transferWkdAddress(req);
            req = {
              ...addressData,
              is_fresh:
                isFresh(goods_name) || (dynamicGoods_name && dynamicGoods_name.customFresh)
                  ? '1'
                  : '0', //标志生鲜
              package_info: goods_name,
              package_weight: goods_weight,
              package_note: goods_remark,
              check_pics: isArray(package_images) ? package_images.join(',') : '',
            };
            if (dynamicAppointmentTime && dynamicAppointmentTime.isShow) {
              req.reserve_start_time = reserve_start_time;
              req.reserve_end_time = reserve_end_time;
              if (reserve_start_time == '1h' || !reserve_start_time) {
                req.reserve_start_time = '1h';
                req.reserve_end_time = '1h';
              }
            }
            if (dynamicService && dynamicService.isShow) {
              if (platform == 'yjkd_brand' && isObject(oService)) {
                req = {
                  ...req,
                  ...oService,
                };
              } else {
                console.log('cost_value', cost_value);
                req = {
                  ...req,
                  decVal: keep_account, //声明物品价值
                  proPrice: cost_value, //保价
                  collection_amount: collection, //代收货款
                  to_pay_amount: arrive_pay, //到付
                };
              }
            }
            if (dynamicProduct_code && dynamicProduct_code.isShow) {
              req.product_code = product_code;
            }
            if (dynamicVolume && dynamicVolume.isShow && isObject(volume)) {
              if (volume.checked) {
                req = {
                  ...req,
                  ...volume,
                };
              }
            }
            req.shipper_default = send_save || 0;
            req.shipping_default = receive_save || 0;
            req.channel = `mina_${Taro.systemInfo.platform}`;
            console.log(
              '下单————————relationType',
              relationType,
              'platform_____',
              platform,
              'print_brand',
              print_brand,
            );
            switch (relationType) {
              case 'courier':
                req.counterman_mobile = account_phone;
                req.source = Taro.expSource || 'focus';
                if (orderPrice > 0) {
                  req.freight = orderPrice;
                }
                if (this.equityCard && this.equityCard.card_id && !isOpenOnlinePay) {
                  req.card_id = this.equityCard.card_id;
                }
                // 填写快递员取件地址
                const isPickupAddress = courierConfig && courierConfig.isPickupAddress == '1';
                const pickupAddress = Taro.kbGetGlobalData('pickupAddress') || {};
                if (isPickupAddress && pickupAddress && pickupAddress.address) {
                  req.new_shipper_name = pickupAddress.name;
                  req.new_shipper_mobile = pickupAddress.mobile;
                  req.new_shipper_address = `${pickupAddress.province} ${pickupAddress.city} ${pickupAddress.district} ${pickupAddress.address}`;
                }
                break;
              case 'dak':
                req.inn_id = dak_id;
                break;
              case 'team':
                req.source = Taro.expSource || 'focus';
                req.join_code = join_code;
                break;
              case 'brand':
                req.brand = brand;
                if (platform == 'yjkd_courier') {
                  req.platform = 'yjkd';
                  req.courier_id = courier.courier_id;
                  if (this.coupon && this.coupon.id) {
                    req.couponId = this.coupon.id;
                  }
                } else if (platform == 'yjkd_brand') {
                  req.platform = 'yjkd';
                  if (coupon_welfare == 1 && this.welfare && this.welfare.id) {
                    req.welfare_id = this.welfare.id;
                  }
                  let quotationData = Taro.kbGetGlobalData('quotation_activity') || [];
                  let quotationItem = {};
                  if (quotationData && quotationData.length > 0) {
                    quotationItem = quotationData.find((item) => item.brand == brand) || {};
                  }
                  if (quotationItem && quotationItem.discount_price) {
                    req.freight = quotationItem.discount_price;
                  }
                  if (couponInfo && couponInfo.card_id && !this.couponNoUsed) {
                    req.coupon_id = couponInfo.card_id;
                  }
                }
                break;
            }
            if (this.checkIsReturn()) {
              req.is_merchant_return = 1;
            }
            req.activity = 'new_customer';
            return req;
          },
          onCustomCheck: (req) => {
            const {
              relationInfo: {
                type: relationType,
                dynamicForms = {},
                smjData,
                platform,
                brand,
              } = {},
              agree,
            } = this.state;
            const { serviceConfig } = this.props;
            const { isDecVal } = serviceConfig || {};
            const { service: dynamicService, weightLimitMax, weightLimitMin } = dynamicForms || {};
            const { relation_info: smjRelationInfo } = smjData || {};
            const { is_collection } = smjRelationInfo || {};
            const { package_info, package_weight, counterman_mobile } = req;
            if (!agree) {
              return { code: 101, msg: '请勾选服务协议!' };
            }
            const supportedBrand = supportedBrands.find((i) => i.brand === brand);
            if (!relationType || platform != 'yjkd_brand' || !brand || !supportedBrand) {
              return { code: 101, msg: '请选择下单快递品牌' };
            }
            if (!relationType || (relationType == 'courier' && !counterman_mobile)) {
              return { code: 101, msg: '请选择揽件快递员' };
            }
            if (!package_info) {
              return { code: 101, msg: '请选择物品类型!' };
            }
            if (dynamicService && dynamicService.isShow) {
              //检查顺丰快运-保价增值服务
              if (!req.proPrice && req.brand == 'sfky') {
                return { code: 101, msg: '请勾选保价服务!' };
              }
              //开启强制声明物品值，没有声明，需阻止
              if (isDecVal && !req.decVal) {
                return { code: 101, msg: '请声明物品价值后下单!' };
              }
            }
            if (weightLimitMax && package_weight > weightLimitMax * 1) {
              return {
                code: 101,
                msg: `此品牌暂不支持${weightLimitMax}KG以上的货物寄递；`,
              };
            }
            if (weightLimitMin && package_weight < weightLimitMin * 1) {
              return {
                code: 101,
                msg: `此品牌暂不支持低于${weightLimitMin}KG的货物寄递；`,
              };
            }
            if (smjRelationInfo) {
              //扫码寄验证必要增值服务是否填写
              if (this.receiveList) {
                return { code: 101, msg: '扫码寄无法批量寄件!' };
              }
              if (is_collection == 1 && !req.collection_amount) {
                return { code: 101, msg: '请在增值服务中填写代收金额！' };
              }
              if (is_collection == 2 && !req.to_pay_amount) {
                return { code: 101, msg: '请在增值服务中填写到付金额！' };
              }
            }
            if (platform == 'yjkd_brand') {
              const { available, unavailable_msg } = getQuotationDetail(brand) || {};
              if (available <= 0 && unavailable_msg) {
                return { code: 101, msg: unavailable_msg };
              }
            }
            if (this.receiveList) {
              return { code: 101, msg: '暂不支持批量寄件' };
            }
          },
          onThen: this.createDone,
        },
        onUpdate: (data) => {
          const { eventType, data: addressData } = data;
          if (eventType === 'clean') {
            // 清除操作
            this.cleanFormInfo(['addressData', 'extraInfoData', 'estimatedData'], { addressData });
          }
        },
        onReady,
      },
      this,
    );
  };

  fixFormUpdateData = (data = {}) => {
    if (this.formIns) {
      data && this.formIns.update(data);
      this.formFixData = null;
    } else {
      this.formFixData = data;
    }
  };

  // 创建订单完成
  createDone = ({ code, data }, req) => {
    this.couponNoUsed = false;
    console.log('req', req);
    let {
      order_number,
      order_id,
      describe,
      realname,
      upload,
      notActive,
      notInRange,
      mustCredit,
      online_pay,
      pass_time,
      ordersNum,
      succeed_order_ids,
      wx_after_pay,
      wx_after_pay_bind,
      collect_code,
      card_id,
      async_create_order,
      fail = [],
      reason = '',
      result = [],
    } = data || {};
    const { collection_amount, to_pay_amount, is_fresh, package_info } = req || {};
    const {
      relationInfo,
      relationInfo: {
        type: relationType,
        brand,
        platform,
        dynamicForms,
        courier,
        longitude,
        latitude,
        inn_name,
        address,
        smjData,
      },
    } = this.state;
    const { pay, cutPayDesc, coupon_welfare } = dynamicForms || {};
    // 触发协议签署
    this.agreeActionRef.current.signAgreement();
    logger.info('下单完成', code, data, req);
    logger.setFilterMsg(req.shipper_mobile || '000');
    if (req.brand === 'yjkd' && !req.platform) {
      logger.setFilterMsg('异常快递品牌');
    }
    if (code == 0) {
      const { query: { q } = {} } = Taro.getLaunchOptionsSync() || {};
      if (q && relationType == 'brand') {
        logger.info('订单提交完成', brand, platform);
        logger.setFilterMsg('非快递员');
      }
      // 拦截处理
      if (mustCredit > 0) {
        this.handleSpringFestive('open');
        return;
      }
      if (upload || realname) {
        this.realnameRef.current.interceptRealname({
          describe,
          realname,
          upload,
        });
        return;
      }
      if (notActive) {
        Taro.kbModal({
          content: notActive,
          confirmText: '我知道了',
        });
        return;
      }
      if (notInRange) {
        Taro.kbModal({
          content: notInRange,
          confirmText: '重选快递品牌',
          onConfirm: () => {
            Taro.navigator({
              url: 'order/relation',
              options: {
                type: relationType,
              },
            });
          },
        });
        return;
      }
      // 批量下单失败-同步模式
      if (fail && isArray(result) && result.length > 0) {
        reason && result.unshift(reason);
        if (isArray(fail) && fail.length > 0) {
          const list = this.receiveList.filter((item, index) => {
            return fail.findIndex((i) => i == index) > -1;
          });
          if (
            this.addressEditRef.current &&
            this.addressEditRef.current.updateFormDataByReceiveList
          ) {
            this.addressEditRef.current.updateFormDataByReceiveList(list);
            this.updateReceiveList(list);
          }
        }
        Taro.kbModal({
          content: result,
        });
        return;
      }
      // 强制开通支付分，如极兔品牌
      if (platform == 'yjkd_brand' && pay == 2 && !order_id) {
        if (wx_after_pay <= 0) {
          this.interceptCreditFailOrder('auth_fail');
          return;
        } else if (wx_after_pay_bind <= 0) {
          this.interceptCreditFailOrder('bind_fail');
          return;
        }
      }
      // 驿站小程序，半屏打开后下单成功上报
      const { source } = Taro.launchParams || {};
      if (source) {
        // 上报
        reportAnalytics({
          key: 'launch_source',
          source: `${source}_order_success`,
        });
      }
      // 清除编辑状态
      this.cleanEditStatus(() => {
        // 批量异步下单，不做处理
        if (async_create_order == 1) return;
        let url = 'order/result';
        let options = {};
        //跳转页面
        order_id = order_number || order_id;
        if (this.submitAction == 'submitTemporary') {
          Taro.navigator({
            url,
            options: {
              source: 'temporary',
              status: 'start',
              order_id,
            },
          });
          return;
        }
        if (smjData) {
          // 扫码寄
          options = {
            label: 'scan',
            is_fresh,
            package_info,
            collect_code,
            collection_amount,
            to_pay_amount,
          };
        }
        switch (relationType) {
          case 'courier':
            if (process.env.PLATFORM_ENV !== 'swan') {
              if (online_pay > 0) {
                url = 'order/pay';
              }
            }
            options = {
              ...options,
              customer_id: req.customer_id || '',
              status:
                card_id && card_id > 0
                  ? 'equity'
                  : collection_amount || to_pay_amount
                  ? ''
                  : 'credit',
            };
            break;
          case 'brand':
            if (platform == 'yjkd_brand') {
              // 兼容报价单数据可能存在的异步清除问题
              let quotationData = Taro.kbGetGlobalDataOnce('quotation_activity') || [];
              Taro.kbSetGlobalData('quotationList', quotationData);
              coupon_welfare == 1 && Taro.kbSetGlobalData('welfare', this.welfare);
            }
            options = {
              brand: platform == 'yjkd_courier' ? courier.brand : brand,
              platform,
              status: platform == 'yjkd_courier' || pay == 2 || pay == 3 ? 'credit' : '',
              cutPayDesc,
              estimatedFee: platform == 'yjkd_courier' ? this.estimatedFee : 0,
              wx_after_pay,
              wx_after_pay_bind,
              isActivity: 1,
            };
            break;
          case 'team':
            url = 'order/detail';
            options = {
              source: '',
              type: 'team',
            };
            break;
          case 'dak':
            options = {
              ...options,
              customer_id: req.customer_id || '',
              longitude,
              latitude,
              inn_name,
              address,
            };
            break;
        }
        this.sendNotice(order_id);
        //批量寄订单id
        let order_ids = succeed_order_ids ? succeed_order_ids : [order_id];
        Taro.kbSetGlobalData('order_ids', order_ids);
        options = {
          source: relationType,
          order_id,
          order_ids,
          pass_time,
          ordersNum,
          ...options,
        };
        const navigatorPage = () => {
          this.hasEditOrder = true;
          // 首页弹窗统计
          if (Taro.homeActivityRecord) {
            homeActivityReportAnalytics('下单', Taro.homeActivityRecord);
            if (platform == 'yjkd_brand' && brand) {
              homeActivityReportAnalytics(`下单-${brand}`, Taro.homeActivityRecord);
            }
            Taro.homeActivityRecord = '';
          }
          relationInfo.storageWay = 'order';
          if (smjData) {
            relationInfo.smjData = null;
            Taro.kbUpdateRelationInfo(relationInfo);
          }
          Taro.kbTriggerStorageRelation(relationInfo);
          if (process.env.PLATFORM_ENV === 'swan') {
            // 百度去除支付分标记
            const { status } = options;
            options.status = status === 'credit' ? '' : status;
          }
          Taro.navigator({
            url,
            options,
          });
        };
        if (
          process.env.PLATFORM_ENV == 'alipay' &&
          platform == 'yjkd_brand' &&
          (pay == 2 || pay == 3) &&
          order_id &&
          wx_after_pay == 1 &&
          wx_after_pay_bind == 1
        ) {
          console.info('检查是否需要下单二次确认', pay);
          //芝麻分用户确认逻辑
          confirmCredit({
            order_id,
            type: 'create',
            returnBackLink: creatSchemeLink({
              page: '/pages/order/result/index',
              query: 'creditSource=CreditSuccess',
            }),
            cancelBackLink: creatSchemeLink({
              page: pay == 2 ? '/pages/order/edit/send/index' : '/pages/order/result/index',
              query: `creditSource=${pay == 2 ? 'CreditAndOrderFail' : 'CreditFail'}`,
            }),
          }).then((cRes) => {
            console.info('二次确认跳转');
            console.info(cRes);
            if (cRes.code == 0 && cRes.data && cRes.data.sign) {
              console.info('需要=>二次确认跳转');
              //需要确认
              //缓存数据
              let quotation = Taro.kbGetGlobalDataOnce('quotationList') || [];
              let welfare = Taro.kbGetGlobalDataOnce('welfare') || {};
              setStorage({
                key: 'LastOrderResultData',
                data: {
                  quotation,
                  welfare,
                  pay,
                  sh_order_number: cRes.data.order_number,
                  ...options,
                },
              });
              const ZhiMaCredit = Taro.requirePlugin('ZhiMaCredit');
              ZhiMaCredit.startService({
                type: cRes.data.type,
                sign_str: cRes.data.sign,
                zm_service_id: cRes.data.zm_service_id,
                success: () => {
                  console.info('芝麻分插件跳转成功');
                },
                fail: () => {
                  console.info('芝麻分插件跳转失败');
                  Taro.kbToast({
                    text: '芝麻分插件跳转失败',
                  });
                },
                complete: () => {},
              });
            } else {
              //不需要确认
              navigatorPage();
            }
          });
        } else {
          navigatorPage();
        }
      });
    }
  };

  // 清除编辑状态或清除批量寄件列表
  cleanFormInfo = (keys, replaceData) => {
    this.setState(cleanOrderEditFormInfo(keys, replaceData));
  };
  cleanEditStatus = (then = noop) => {
    this.setState({
      couponInfo: null,
    });
    refreshControl(REFRESH_KEY_KXJ_COUPON);
    Taro.kbSetGlobalData('pickupAddress', {});
    this.formIns.clean();
    then();
    this.updateReceiveList();
    removeStorage({
      key: receiveStorageKey,
    });
  };
  getAuthCode() {
    return new Promise((resolve, reject) => {
      if (process.env.PLATFORM_ENV === 'alipay') {
        my.getAuthCode({
          scopes: ['order_service'],
          success: () => {
            resolve();
          },
          fail: () => {
            Taro.kbToast({
              text: '订单服务授权失败!',
            });
            reject();
          },
        });
      } else {
        resolve();
      }
    });
  }
  handleSubmitForm = async (action = '') => {
    const { relationInfo: { customer, type, courier_id } = {} } = this.state;
    //如果大客户未通过审核，无法下单
    if (customer && customer.id) {
      var customerAuthStatus = await checkCustomerAuthStatus(customer.id);
      if (customerAuthStatus != 1) return true;
    }
    if (type == 'courier') {
      const unPayOrders = await getUnPayOrders({
        courierId: courier_id,
      });
      if (unPayOrders.length > 0) {
        this.setState({
          unPayOrders,
        });
        return;
      }
    }
    this.getAuthCode().then(() => {
      this.onSubmit_form(action);
    });
  };
  handleCheck = () => {
    const res = this.formIns.check();
    res.msg = res.msg + '\n请重新输入后再提交订单';
    return res;
  };

  // 兼容老的订阅方案
  sendNotice = (order_id) => {
    if (!order_id) return;
    const { relationInfo } = this.state;
    const { type: relationType } = relationInfo || {};
    sendNoticeWkd({
      template_title: relationType === 'brand' ? 'grab_order' : 'order_notice',
      order_id,
    });
  };
  // 更新收件人类表
  updateReceiveList = (list = null) => {
    this.receiveList = list;
    const total = isArray(list) ? list.length || 1 : 1;
    this.setState({
      total,
    });
  };
  // 处理额外数据
  handleExtraData = handleExtraData;
  // 处理快递码逻辑
  handleECode = handleECode;
  // 检测预估运费
  checkEstimatedFee = () => {
    this.setState({
      estimatedFeeActive: !this.state.estimatedFeeActive,
    });
  };
  // 检测支付分开通情况
  handleCheckCreditService = handleCheckCreditService;
  // 拦截下单对象:不活跃快递员;
  interceptChannel = interceptChannel;
  // 强制开通支付分提示
  interceptCreditFailOrder = interceptCreditFailOrder;
  // 处理二次确认失败订单
  handleSecondConfirmOrder = handleSecondConfirmOrder;
  // 优寄快递员计费说明
  onShowFeeWay = onShowFeeWay;
  // 执行开通支付分逻辑
  handleOpenCredit = handleOpenCredit;
  handleAccountChange = () => {
    this.setState({ nowTimer: new Date().getTime() });
  };
  // 处理百世快递转换极兔快递弹窗
  handleHTBrandModal = handleHTBrandModal;
  // 春节快递快递品牌线上付通知
  handleSpringFestive = handleSpringFestive;
  // 不活跃快递员
  handleConfirmIntercept = () => {
    this.handleCloseIntercept();
    const relationInfo = { brand: 'sto', storageWay: 'scan' };
    Taro.kbUpdateRelationInfo(relationInfo);
    Taro.kbTriggerStorageRelation(relationInfo);
  };
  handleCloseIntercept = () => {
    this.setState({ interceptChannelOpen: false });
  };
  // 德邦下单重量提示
  handelDpWeightTips = handelDpWeightTips;
  // 处理顺丰快运默认重量
  handleSFKYDefaultWeight = handleSFKYDefaultWeight;
  // 清除未支付订单信息
  handleInterceptModalClose = () => {
    this.setState({
      unPayOrders: [],
    });
  };
  handleJumpTo() {
    Taro.navigator({
      url: 'order/kxj/coupon',
    });
  }
  checkIsReceiveCoupon() {
    getActivityDetail({ vip: true }).then((res) => {
      const { is_use } = res || {};
      this.setState({
        activityDetail: res,
      });
      getReceiveCouponList().then((list) => {
        const couponInfo = list && list.length && list[0];
        if (couponInfo && is_use != 1) {
          this.setState({
            couponInfo,
          });
        }
        this.setState({
          usedCouponList: list,
        });
      });
    });
  }
  handleSwitchCoupon() {
    const { couponInfo } = this.state;
    Taro.navigator({
      url: 'order/card',
      options: {
        action: 'select',
        type: 'kxj',
        current: couponInfo && couponInfo.card_id,
      },
    });
  }
  checkCoupon() {
    return new Promise((resolve) => {
      resolve(true);
    });
  }
  handelGuideCoupon(key, ev) {
    console.log('key,ev', key, ev);
    const close = () => {
      this.setState({
        isOpenGuideCoupon: false,
      });
    };
    switch (key) {
      case 'close':
        close();
        break;
      case 'open':
        this.setState({
          isOpenGuideCoupon: true,
        });
        break;
      case 'cancel':
        if (ev === 'button') {
          this.withoutCouponLock = true;
          this.handleSubmitForm();
        }
        close();
        break;
      case 'confirm':
        this.withoutCouponLock = true;
        openCreditService();
        close();
        break;
    }
  }
  handleVip() {
    Taro.navigator({
      url: 'user/member/right',
    });
  }

  render() {
    const {
      total,
      relationInfo,
      relationInfo: { platform = '', courier_id } = {},
      relationData,
      relationInfoLoading,
      form: { disabled, data: formData },
      dynamicForms,
      addressData,
      extraInfo = {},
      extraInfoData,
      estimatedFeeActive,
      estimatedData,
      agree,
      moveAreaData,
      moveAreaStatus,
      moveAreaPagePadding,
      isOpenCredit,
      nowTimer,
      scanCoupon,
      miniPostPrinterData,
      hTBrandTipsData,
      springFestiveOpen,
      interceptChannelOpen,
      isOpenDpWeightTips,
      isOpenDpWeightNoMore,
      courierConfig,
      userVipData,
      unPayOrders,
      activityDetail = {},
      couponInfo,
      usedCouponList,
      isOpenGuideCoupon,
      ...rest
    } = this.state;
    const { is_new, is_receive, is_use, is_vip } = activityDetail || {};

    const couponCls = classNames('kb-send__coupon__title', {
      'kb-send__coupon__title2': couponInfo && couponInfo.card_id,
    });

    return (
      <KbPage allowAuthPopup {...rest} onUpdate={this.onUpdate} cover={false}>
        <KbScrollView
          full={moveAreaStatus != 'min'}
          renderFooter={
            <Fragment>
              <View
                className='kb-box yj-container'
                onTouchMove={(e) => {
                  e.stopPropagation();
                }}
              >
                <View className='yj-container-list'>
                  <KbEstimatedFeeList
                    className='yj-container-list--content'
                    relationInfo={relationInfo}
                    address={formData}
                    weight={extraInfo.goods_weight}
                    volume={extraInfo.volume}
                    product_code={extraInfo.product_code}
                    isOpenCredit={isOpenCredit}
                    userVipData={userVipData}
                    couponInfo={couponInfo}
                    moveAreaStatus={moveAreaStatus}
                    moveAreaData={moveAreaData}
                    onChange={this.onHandleMoveArea.bind(this, 'change')}
                    onChangeArea={this.onHandleMoveArea.bind(this, 'status')}
                    ref={this.estimatedFeeListRef}
                  />
                </View>
                <View className='yj-container-msg kb-spacing-md-lr'>
                  <KbExtraInfo
                    ref={this.extraInfoRef}
                    data={extraInfoData}
                    relationInfo={relationInfo}
                    dynamicForms={dynamicForms}
                    address={formData}
                    total={total}
                    unfold={!disabled}
                    mode='yjkd'
                    onChange={this.onChange.bind(this, 'info')}
                    isOpenCredit={isOpenCredit}
                  />
                  <View className='kb-box at-row at-row__align--center at-row__justify--between kb-spacing-md-b'>
                    <View className='at-row at-row__align--center'>
                      <KbCheckbox
                        label='我已阅读并同意'
                        checked={agree}
                        onChange={this.onSwitchAgree}
                        className='kb-color__black'
                      />
                      <KbAgreement agreeType='serviceAgreement' actionRef={this.agreeActionRef} />
                    </View>
                    <View>
                      <KbSubmitBar
                        onClick={this.handleSubmitForm}
                        onCheck={this.handleCheck}
                        total={total}
                      />
                    </View>
                  </View>
                </View>
              </View>
            </Fragment>
          }
          ts={nowTimer}
        >
          <View
            className='kb-send'
            style={
              platform == 'yjkd_brand' ? { paddingBottom: `${moveAreaPagePadding || 150}px` } : ''
            }
          >
            <View className='kb-send__item' style={{ display: 'none' }}>
              <KbStoreCardSelector
                onChange={this.updateRelationInfo}
                data={relationData}
                loading={relationInfoLoading}
              />
            </View>
            <View
              className='kb-send__banner'
              onClick={this.handleJumpTo.bind(this)}
              hoverClass='kb-hover-opacity'
            >
              <Image
                className='kb-send__banner-img'
                src='https://cdn-img.kuaidihelp.com/wkd/miniApp/activity/guide-bg.png?v=1'
              />
              <Image
                className='kb-send__banner-img2'
                src='https://cdn-img.kuaidihelp.com/wkd/miniApp/activity/guide-btn.png?v=1'
              />
            </View>
            <View className='kb-send__item kb-margin-md-t'>
              <KbAddressEdit
                pageName='edit'
                locked
                useDefault
                switchAddress
                ref={this.addressEditRef}
                actionRef={this.realnameRef}
                extraInfo={extraInfo}
                relationInfo={relationInfo}
                data={addressData}
                showClipborard={false}
                onChange={this.onChange.bind(this, 'address')}
              />
            </View>
            <View className='kb-send__coupon'>
              <View className={couponCls}>新客优惠</View>
              <View className='kb-send__coupon__content'>
                <View className='kb-send__coupon__content--info'>
                  {couponInfo && couponInfo.card_id ? (
                    <View className='kb-send__coupon__content--info-row'>
                      <View className='kb-color__red'>
                        {couponInfo.discount_type === 'discount'
                          ? `${couponInfo.discount_fee}折优惠`
                          : `-￥${couponInfo.discount_fee}`}
                      </View>
                    </View>
                  ) : (
                    <View className='kb-color__grey'>
                      {usedCouponList && usedCouponList.length > 0
                        ? '有可用优惠券'
                        : '暂无可用优惠券'}
                    </View>
                  )}
                </View>
              </View>
            </View>
            {is_new == 1 ? (
              is_receive != 1 ? (
                <View
                  className='kb-send__couponGuide'
                  onClick={this.handleJumpTo.bind(this)}
                  hoverClass='kb-hover'
                >
                  <Image
                    className='kb-send__couponGuide-img'
                    src='https://cdn-img.kuaidihelp.com/wkd/miniApp/kxj/coupon.png'
                  />
                  <View className='kb-send__couponGuide-content'>
                    您有<Text className='kb-color__red'>待领取</Text>的新客福利优惠券，
                    <Text className='kb-color__red'>立即领取</Text>
                  </View>
                  <AtIcon
                    prefixClass='kb-icon'
                    value='arrow'
                    className='kb-color__red kb-icon-size__sm'
                  />
                </View>
              ) : is_vip == 1 ? (
                <View
                  className='kb-send__couponGuide'
                  onClick={this.handleVip.bind(this)}
                  hoverClass='kb-hover'
                >
                  <Image
                    className='kb-send__couponGuide-img'
                    src='https://cdn-img.kuaidihelp.com/wkd/miniApp/activity/icon_vip.png'
                  />
                  <View className='kb-send__couponGuide-content'>
                    您是<Text className='kb-color__red'>优享寄VIP会员</Text>，寄件单单返现金，
                    <Text className='kb-color__red'>特权详情</Text>
                  </View>
                  <AtIcon
                    prefixClass='kb-icon'
                    value='arrow'
                    className='kb-color__red kb-icon-size__sm'
                  />
                </View>
              ) : is_use != 1 ? (
                <View className='kb-send__couponGuide'>
                  <Image
                    className='kb-send__couponGuide-img'
                    src='https://cdn-img.kuaidihelp.com/wkd/miniApp/activity/icon_vip.png'
                  />
                  <View className='kb-send__couponGuide-content'>
                    新客完成首单寄件，<Text className='kb-color__red'>+送</Text>优享寄
                    <Text className='kb-color__red'>VIP</Text>会员卡
                  </View>
                </View>
              ) : null
            ) : null}
          </View>
          <KbModal
            isOpened={!!hTBrandTipsData}
            closable={false}
            closeOnClickOverlay={false}
            onClose={this.handleHTBrandModal.bind(this, 'close')}
            onConfirm={this.handleHTBrandModal.bind(this, 'confirm')}
            title='品牌更新告知'
            content='尊敬的客户，因”百世快递“现已跟“极兔速递”进行业务融合，您的下单品牌将切换为“极兔速递”为您服务;你也可在下单页继续选择其他快递品牌进行寄递，感谢您对“微快递”平台的大力支持!'
            confirmText={`${hTBrandTipsData > 0 ? hTBrandTipsData + 's' : '知道了'}`}
          />
          <KbModal
            isOpened={!!isOpenDpWeightTips}
            top={false}
            title='温馨提示'
            onClose={this.handelDpWeightTips.bind(this, 'close')}
            onCancel={this.handelDpWeightTips.bind(this, 'close')}
            confirmText=''
            cancelText=''
          >
            <View className='kb-dpWightTips'>
              <View className='kb-dpWightTips-content'>
                <View className='kb-dpWightTips-dot'>
                  尊敬的客户为了给您提供最优惠的寄件体验，当您包裹实际重量大于等于3kg时，系统建议您录入物品重量后下单,此时系统结算价格会更优惠;
                </View>
                <View className='kb-dpWightTips-dot'>
                  货物重量在不大于3kg时切勿随意录入重量，以免造成扣款损失;
                </View>
              </View>
              <View className='kb-dpWightTips-tips'>
                注:大于60kg的货物会按照原价计费无优惠， 请谨慎下单!
              </View>
              <View>
                <View>
                  <AtButton
                    type='primary'
                    circle
                    onClick={this.handelDpWeightTips.bind(this, 'confirm')}
                  >
                    我已知晓
                  </AtButton>
                </View>
                <View className='kb-text__center kb-margin-md-t'>
                  <KbCheckbox
                    label='不再提示'
                    checked={!!isOpenDpWeightNoMore}
                    onChange={this.handelDpWeightTips.bind(this, 'noMore')}
                    className='kb-color__black'
                  />
                </View>
              </View>
            </View>
          </KbModal>
        </KbScrollView>
        <KbWaitPay mode='modal' />
        <KbUnPayOrders
          courier_id={courier_id}
          order_ids={unPayOrders}
          onClose={this.handleInterceptModalClose}
        />
        <KbModal
          isOpened={isOpenGuideCoupon}
          top={false}
          title='确定要原价寄件吗？'
          onClose={this.handelGuideCoupon.bind(this, 'close')}
          onCancel={this.handelGuideCoupon.bind(this, 'cancel')}
          onConfirm={this.handelGuideCoupon.bind(this, 'confirm')}
          confirmText='尝试优惠寄件'
          cancelText='继续提交订单'
          footerLayout='column'
        >
          <View className='kb-guideCouponModal'>
            <View className='kb-guideCouponModal-tips'>原价寄件，将不能享受平台折扣</View>
            <Image
              className='kb-guideCouponModal-img'
              src='https://cdn-img.kuaidihelp.com/wkd/miniApp/activity/quan-3.png?v=1'
            />
          </View>
        </KbModal>
      </KbPage>
    );
  }
}

export default Index;
