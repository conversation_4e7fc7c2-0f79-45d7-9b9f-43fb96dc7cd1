import CallbacksPool from './callbacksPool';
import request from './request';

// 时间表示，在此keys中的，后台拉取
const minuteKeys = ['full', 'insert'];

let lock = false;
let cashData = null;

const callbackIns = new CallbacksPool();

// 触发回调池
const runTrigger = (minutesOpts, data) => {
  const hour = data[minutesOpts];
  callbackIns.trigger({ minutes: hour ? hour * 60 : 0 });
};

/**
 *
 * @description 获取展示频率： 注意此接口配置的是小时
 * @param {number|'full'|'insert'} minutes full代表全屏广告、insert代表插屏广告，直接传字符串时，从后台拉取配置
 * @returns
 */
export function getAdFrequencyMinutes(minutesOpts) {
  return new Promise((resolve) => {
    if (!minuteKeys.includes(minutesOpts)) {
      resolve({ minutes: minutesOpts });
    } else {
      callbackIns.push(resolve);
      if (cashData) {
        runTrigger(minutesOpts, cashData);
        return;
      }
      if (lock) return;
      lock = true;
      request({
        url: '/v1/WeApp/getAdvertisementsConfig',
        toastLoading: false,
        onThen: (res) => {
          const { data: resData } = res || {};
          cashData = resData;
          runTrigger(minutesOpts, resData);
          lock = false;
        },
      });
    }
  });
}
