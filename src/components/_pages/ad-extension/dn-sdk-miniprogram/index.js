/* eslint-disable */
/**
 * @dn-sdk/miniprogram v1.4.0
 * (c) 2024
 * @license ISC
 */
function t(t, n) {
  var r = ('undefined' != typeof Symbol && t[Symbol.iterator]) || t['@@iterator'];
  if (!r) {
    if (
      Array.isArray(t) ||
      (r = (function (t, n) {
        if (!t) return;
        if ('string' == typeof t) return e(t, n);
        var r = Object.prototype.toString.call(t).slice(8, -1);
        'Object' === r && t.constructor && (r = t.constructor.name);
        if ('Map' === r || 'Set' === r) return Array.from(t);
        if ('Arguments' === r || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)) return e(t, n);
      })(t)) ||
      (n && t && 'number' == typeof t.length)
    ) {
      r && (t = r);
      var o = 0,
        i = function () {};
      return {
        s: i,
        n: function () {
          return o >= t.length ? { done: !0 } : { done: !1, value: t[o++] };
        },
        e: function (t) {
          throw t;
        },
        f: i,
      };
    }
    throw new TypeError(
      'Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.',
    );
  }
  var a,
    u = !0,
    c = !1;
  return {
    s: function () {
      r = r.call(t);
    },
    n: function () {
      var t = r.next();
      return (u = t.done), t;
    },
    e: function (t) {
      (c = !0), (a = t);
    },
    f: function () {
      try {
        u || null == r.return || r.return();
      } finally {
        if (c) throw a;
      }
    },
  };
}
function e(t, e) {
  (null == e || e > t.length) && (e = t.length);
  for (var n = 0, r = new Array(e); n < e; n++) r[n] = t[n];
  return r;
}
function n(t, e, n) {
  return (
    (e = d(e)) in t
      ? Object.defineProperty(t, e, { value: n, enumerable: !0, configurable: !0, writable: !0 })
      : (t[e] = n),
    t
  );
}
function r(t, e) {
  if ('function' != typeof e && null !== e)
    throw new TypeError('Super expression must either be null or a function');
  (t.prototype = Object.create(e && e.prototype, {
    constructor: { value: t, writable: !0, configurable: !0 },
  })),
    Object.defineProperty(t, 'prototype', { writable: !1 }),
    e && o(t, e);
}
function o(t, e) {
  return (
    (o = Object.setPrototypeOf
      ? Object.setPrototypeOf.bind()
      : function (t, e) {
          return (t.__proto__ = e), t;
        }),
    o(t, e)
  );
}
function i(t) {
  var e = (function () {
    if ('undefined' == typeof Reflect || !Reflect.construct) return !1;
    if (Reflect.construct.sham) return !1;
    if ('function' == typeof Proxy) return !0;
    try {
      return Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})), !0;
    } catch (t) {
      return !1;
    }
  })();
  return function () {
    var n,
      r = c(t);
    if (e) {
      var o = c(this).constructor;
      n = Reflect.construct(r, arguments, o);
    } else n = r.apply(this, arguments);
    return a(this, n);
  };
}
function a(t, e) {
  if (e && ('object' === v(e) || 'function' == typeof e)) return e;
  if (void 0 !== e) throw new TypeError('Derived constructors may only return object or undefined');
  return u(t);
}
function u(t) {
  if (void 0 === t)
    throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
  return t;
}
function c(t) {
  return (
    (c = Object.setPrototypeOf
      ? Object.getPrototypeOf.bind()
      : function (t) {
          return t.__proto__ || Object.getPrototypeOf(t);
        }),
    c(t)
  );
}
function s(t, e) {
  if (!(t instanceof e)) throw new TypeError('Cannot call a class as a function');
}
function l(t, e) {
  for (var n = 0; n < e.length; n++) {
    var r = e[n];
    (r.enumerable = r.enumerable || !1),
      (r.configurable = !0),
      'value' in r && (r.writable = !0),
      Object.defineProperty(t, d(r.key), r);
  }
}
function f(t, e, n) {
  return (
    e && l(t.prototype, e), n && l(t, n), Object.defineProperty(t, 'prototype', { writable: !1 }), t
  );
}
function d(t) {
  var e = (function (t, e) {
    if ('object' !== v(t) || null === t) return t;
    var n = t[Symbol.toPrimitive];
    if (void 0 !== n) {
      var r = n.call(t, e || 'default');
      if ('object' !== v(r)) return r;
      throw new TypeError('@@toPrimitive must return a primitive value.');
    }
    return ('string' === e ? String : Number)(t);
  })(t, 'string');
  return 'symbol' === v(e) ? e : String(e);
}
function v(t) {
  return (
    (v =
      'function' == typeof Symbol && 'symbol' == typeof Symbol.iterator
        ? function (t) {
            return typeof t;
          }
        : function (t) {
            return t &&
              'function' == typeof Symbol &&
              t.constructor === Symbol &&
              t !== Symbol.prototype
              ? 'symbol'
              : typeof t;
          }),
    v(t)
  );
}
function p(t, e) {
  var n = (function (t) {
      if ('object' == v(t)) {
        var e = [];
        for (var n in t) e.push(''.concat(n, '=').concat(t[n]));
        return e.join('&');
      }
    })(e),
    r = t;
  return (
    n &&
      (r =
        /\?/.test(t) || /#/.test(t)
          ? /\?/.test(t) && !/#/.test(t)
            ? ''.concat(t, '&').concat(n)
            : !/\?/.test(t) && /#/.test(t)
            ? t.replace('#', '?'.concat(n, '#'))
            : t.replace('?', '?'.concat(n, '&'))
          : ''.concat(t, '?').concat(n)),
    r
  );
}
var _ = (function () {
    function t() {
      s(this, t);
    }
    return (
      f(t, null, [
        {
          key: 'error',
          value: function (t) {
            for (var e, n = arguments.length, r = new Array(n > 1 ? n - 1 : 0), o = 1; o < n; o++)
              r[o - 1] = arguments[o];
            (e = console).error.apply(
              e,
              [''.concat('[@dn-sdk/miniprogram v1.4.0]', ': ').concat(t)].concat(r),
            );
          },
        },
        {
          key: 'info',
          value: function (e) {
            for (var n, r = arguments.length, o = new Array(r > 1 ? r - 1 : 0), i = 1; i < r; i++)
              o[i - 1] = arguments[i];
            t.debug &&
              (n = console).info.apply(
                n,
                [''.concat('[@dn-sdk/miniprogram v1.4.0]', ': ').concat(e)].concat(o),
              );
          },
        },
        {
          key: 'log',
          value: function (e) {
            for (var n, r = arguments.length, o = new Array(r > 1 ? r - 1 : 0), i = 1; i < r; i++)
              o[i - 1] = arguments[i];
            t.debug &&
              (n = console).log.apply(
                n,
                [''.concat('[@dn-sdk/miniprogram v1.4.0]', ': ').concat(e)].concat(o),
              );
          },
        },
        {
          key: 'warn',
          value: function (t) {
            for (var e, n = arguments.length, r = new Array(n > 1 ? n - 1 : 0), o = 1; o < n; o++)
              r[o - 1] = arguments[o];
            (e = console).warn.apply(
              e,
              [''.concat('[@dn-sdk/miniprogram v1.4.0]', ': ').concat(t)].concat(r),
            );
          },
        },
        {
          key: 'devLog',
          value: function (e) {
            for (var n, r = arguments.length, o = new Array(r > 1 ? r - 1 : 0), i = 1; i < r; i++)
              o[i - 1] = arguments[i];
            t.isDev &&
              (n = console).log.apply(
                n,
                [''.concat('[@dn-sdk/miniprogram v1.4.0]', ': ').concat(e)].concat(o),
              );
          },
        },
      ]),
      t
    );
  })(),
  h = _;
(h.debug = !1), (h.isDev = !1);
var g,
  y,
  m = 'LOCAL_ID',
  A = 'QUEUE_ACTIONS',
  R = 'CLICK_ID',
  S = 'QUEUE_LOST_MAP',
  b = 'REMOTE_CONFIG',
  k = 'REQ_TOTAL',
  O = { init: 'init', reporting: 'reporting', fail: 'fail' },
  E = {
    maxSdkInstance: 4,
    maxQueueLength: 500,
    actionParamMaxLength: 1e4,
    autoTrack: !0,
    reportThreshold: 5,
    reportDelay: 1,
    inspectDelay: 30,
    cgiBatchSize: 50,
    requestConcurrency: 4,
    requestTimeout: 1e4,
    signVersion: '1.0',
  },
  T = {
    JS_RUN_ERROR: 'JS_RUN_ERROR',
    REQUEST_ERROR: 'REQUEST_ERROR',
    REQUEST_CONFIG_ERROR: 'REQUEST_CONFIG_ERROR',
    JS_QUEUE_LOG: 'JS_QUEUE_LOG',
    PROXY_ERROR: 'PROXY_ERROR',
    PROXY_POLYFILL: 'PROXY_POLYFILL',
    QUEUE_LOST_NUM: 'QUEUE_LOST_NUM',
    REQ_TOTAL: 'REQ_TOTAL',
    SIGN_ERROR: 'SIGN_ERROR',
  },
  I = 'ANDROID',
  N = 'IOS',
  w = 'WINDOWS',
  x = 'OSX',
  C = 'UNKNOWN',
  D = 1e3,
  L = 100,
  P = 10,
  U = 'START_APP',
  M = 'ENTER_FOREGROUND',
  q = 'ENTER_BACKGROUND',
  j = 'ADD_TO_WISHLIST',
  F = 'PURCHASE',
  V = 'APP_QUIT',
  W = 'TRUE',
  B = 'FALSE',
  K =
    ((y = function (t) {
      return ''.concat('@dn-sdk/miniprogram', '_').concat('production', '_').concat(t);
    }),
    {
      getSync: function (t) {
        var e;
        try {
          e = wx.getStorageSync(y(t));
        } catch (t) {
          return console.error('storage get error', t), e;
        }
        return e;
      },
      setSync: function (t, e) {
        try {
          wx.setStorageSync(y(t), e);
        } catch (t) {
          return console.error('storage set error', t), !1;
        }
        return !0;
      },
    }),
  Q = function () {
    if (g) return g;
    try {
      return (g = wx.getSystemInfoSync());
    } catch (t) {
      return {};
    }
  },
  G = (function () {
    var t;
    return function () {
      if (!t) {
        var e = Q(),
          n = e.system,
          r = void 0 === n ? '' : n,
          o = (null == r ? void 0 : r.split(' ')) || [],
          i = (function (t) {
            if (!t) return C;
            var e = (null == t ? void 0 : t.toUpperCase()) || '';
            return e.indexOf('ANDROID') > -1
              ? I
              : e.indexOf('IOS') > -1
              ? N
              : e.indexOf('MAC') > -1
              ? x
              : e.indexOf('WINDOWS') > -1
              ? w
              : C;
          })(o[0]),
          a = (function (t) {
            return !t || t.length <= 0
              ? ''
              : 2 === t.length
              ? t[1]
              : 3 === t.length && 'Windows' === t[0]
              ? ''.concat(t[1], ' ').concat(t[2])
              : t[t.length - 1];
          })(o);
        t = {
          benchmark_level: e.benchmarkLevel,
          device_brand: e.brand,
          screen_height: Math.floor(e.screenHeight),
          screen_width: Math.floor(e.screenWidth),
          wx_lib_version: e.SDKVersion,
          wx_version: e.version,
          wx_platform: e.platform,
          device_model: e.model,
          os: i,
          os_version: a,
        };
      }
      return t;
    };
  })(),
  H = (function () {
    var t;
    return function () {
      try {
        if (t) return t;
        t || (t = K.getSync(m) || ''), t || ((t = at()), K.setSync(m, t));
      } catch (t) {}
      return t;
    };
  })();
var J = (function () {
  var t = 'unknown',
    e = !1;
  return function () {
    if (!e)
      try {
        wx.getNetworkType({
          success: function (e) {
            t = e.networkType;
          },
          fail: function () {
            t = 'unknown';
          },
        }),
          wx.onNetworkStatusChange(function (e) {
            t = e.networkType;
          }),
          (e = !0);
      } catch (t) {}
    return t;
  };
})();
J();
var Y = (function () {
  var t;
  return function () {
    if (t) return t;
    try {
      var e = wx.getAccountInfoSync();
      return nt(e.miniProgram) ? (t = e.miniProgram) : {};
    } catch (e) {
      return {};
    }
  };
})();
function z(t, e) {
  try {
    var n = G(),
      r = {
        sdk_version: '1.4.0',
        sdk_name: '@dn-sdk/miniprogram',
        device_brand: null == n ? void 0 : n.device_brand,
        device_model: null == n ? void 0 : n.device_model,
        wx_version: null == n ? void 0 : n.wx_version,
        wx_lib_version: null == n ? void 0 : n.wx_lib_version,
        wx_platform: null == n ? void 0 : n.wx_platform,
        os: null == n ? void 0 : n.os,
        os_version: null == n ? void 0 : n.os_version,
        local_id: H(),
      },
      o = Object.assign(r, t);
    wx.request({
      url: 'https://api.datanexus.qq.com/data-nexus-trace/log',
      data: o,
      method: 'POST',
      timeout: E.requestTimeout,
      success: function (t) {
        'function' == typeof e && 200 === (null == t ? void 0 : t.statusCode) && e();
      },
    });
  } catch (n) {
    h.error(n);
  }
}
var $ = (function () {
  var t = {},
    e = Date.now().toString(),
    n = function () {
      try {
        var n = J();
        if ('none' !== n && 'offline' !== n) {
          var r = [];
          for (var o in t) {
            var i = null == o ? void 0 : o.split('_');
            r.push({
              req_total_action_set_id: i[0],
              req_total_timestamp: i[1],
              req_total_num: t[o],
            });
          }
          r.length &&
            ((e = Date.now().toString()),
            r.forEach(function (e) {
              var n = Object.assign({ log_type: T.REQ_TOTAL }, e),
                r = ''.concat(e.req_total_action_set_id, '_').concat(e.req_total_timestamp);
              z(n, function () {
                pt(t, r) && (delete t[r], K.setSync(k, t));
              });
            }));
        }
      } catch (n) {
        h.error(n);
      }
    };
  return {
    init: function () {
      try {
        setInterval(function () {
          var e = !1;
          for (var r in t)
            if (t[r] >= 15) {
              e = !0;
              break;
            }
          e && n();
        }, 6e4);
        var e = K.getSync(k) || {};
        Object.assign(t, e);
        var r = !1;
        for (var o in e)
          if (pt(e, o)) {
            r = !0;
            break;
          }
        r &&
          setTimeout(function () {
            n();
          }, 2e3);
      } catch (e) {
        h.error(e);
      }
    },
    set: function (n) {
      try {
        if (n) {
          var r = ''.concat(n, '_').concat(e),
            o = t[r] || 0;
          (t[r] = o + 1), K.setSync(k, t);
        }
      } catch (r) {
        h.error(r);
      }
    },
    report: n,
  };
})();
function X(t) {
  return new Promise(function (e, n) {
    wx.request({
      method: 'POST',
      url: 'https://api.datanexus.qq.com/data-nexus-config/v1/sdk/config/get',
      data: t,
      timeout: E.requestTimeout,
      success: function (t) {
        Z(t, e, 'config/get', n);
      },
      fail: function (t) {
        tt(t, 'config/get', n);
      },
    });
  });
}
function Z(t, e, n, r) {
  var o,
    i,
    a,
    u,
    c = null == t ? void 0 : t.statusCode,
    s = null == (o = null == t ? void 0 : t.data) ? void 0 : o.code;
  if (200 !== c || 0 !== s) {
    var l = s;
    200 !== c && (l = 'number' == typeof c ? -1 * c : -888),
      z({
        log_type: T.REQUEST_CONFIG_ERROR,
        message: 'cgiName: '
          .concat(n, ', statusCode: ')
          .concat(c, ', code: ')
          .concat(s, ', traceid: ')
          .concat(null == (a = null == t ? void 0 : t.data) ? void 0 : a.trace_id),
        code: l,
      }),
      null == r || r(null == (u = null == t ? void 0 : t.data) ? void 0 : u.data);
  } else e(null == (i = t.data) ? void 0 : i.data);
}
function tt(t, e, n) {
  z({
    log_type: T.REQUEST_ERROR,
    message: 'cgiName: '.concat(e, ' ,message: ').concat(null == t ? void 0 : t.errMsg, ' '),
    code: -1 * (null == t ? void 0 : t.errno),
  }),
    null == n || n(t);
}
var et = Object.prototype.toString,
  nt = function (t) {
    return '[object Object]' === et.call(t);
  },
  rt = function (t) {
    return '[object Array]' === et.call(t);
  },
  ot = function (t) {
    return '[object Function]' === et.call(t);
  },
  it = new Date().getTime();
function at() {
  var t = new Date().getTime(),
    e = Math.abs(1e3 * (t - it));
  return 'xxxxxxxxxxxx4xxxyxxxxxxxxxxxxxxx'.replace(/[xy]/g, function (n) {
    var r = 16 * Math.random();
    return (
      t > 0
        ? ((r = (t + r) % 16 | 0), (t = Math.floor(t / 16)))
        : ((r = (e + r) % 16 | 0), (e = Math.floor(e / 16))),
      ('x' === n ? r : (3 & r) | 8).toString(16).replace(/-/g, '')
    );
  });
}
var ut =
    /^v?(?:\d+)(\.(?:[x*]|\d+)(\.(?:[x*]|\d+)(\.(?:[x*]|\d+))?(?:-[\da-z\-]+(?:\.[\da-z\-]+)*)?(?:\+[\da-z\-]+(?:\.[\da-z\-]+)*)?)?)?$/i,
  ct = function (t) {
    if ('string' != typeof t) throw new TypeError('Invalid argument expected string');
    if (!ut.test(t))
      throw new Error("Invalid argument not valid semver ('".concat(t, "' received)"));
  },
  st = function (t) {
    return isNaN(Number(t)) ? t : Number(t);
  },
  lt = function (t) {
    var e = t.replace(/^v/, '').replace(/\+.*$/, ''),
      n = (function (t, e) {
        return -1 === t.indexOf(e) ? t.length : t.indexOf(e);
      })(e, '-'),
      r = e.substring(0, n).split('.');
    return r.push(e.substring(n + 1)), r;
  },
  ft = function (t, e) {
    [t, e].forEach(ct);
    for (var n = lt(t), r = lt(e), o = 0; o < Math.max(n.length - 1, r.length - 1); o++) {
      var i = parseInt(n[o] || '0', 10),
        a = parseInt(r[o] || '0', 10);
      if (i > a) return 1;
      if (a > i) return -1;
    }
    var u = n[n.length - 1],
      c = r[r.length - 1];
    if (u && c)
      for (
        var s = u.split('.').map(st), l = c.split('.').map(st), f = 0;
        f < Math.max(s.length, l.length);
        f++
      ) {
        if (void 0 === s[f] || ('string' == typeof l[f] && 'number' == typeof s[f])) return -1;
        if (void 0 === l[f] || ('string' == typeof s[f] && 'number' == typeof l[f]) || s[f] > l[f])
          return 1;
        if (l[f] > s[f]) return -1;
      }
    else if (u || c) return u ? -1 : 1;
    return 0;
  },
  dt = function (t) {
    return nt(t)
      ? ((function (t) {
          var e = [
            'user_action_set_id',
            'secret_key',
            'appid',
            'openid',
            'unionid',
            'user_unique_id',
            'auto_track',
            'auto_attr',
          ];
          for (var n in t)
            e.includes(n) || h.warn("Invalid property '".concat(n, "' found in config"));
        })(t),
        'number' != typeof t.user_action_set_id
          ? 'user_action_set_id 参数需为 number 类型'
          : t.user_action_set_id <= 0
          ? 'user_action_set_id 参数需大于 0'
          : 'string' != typeof t.secret_key
          ? 'secret_key 参数需为 string 类型'
          : '' === t.secret_key.trim()
          ? '缺少 secret_key 参数'
          : 32 !== t.secret_key.length
          ? 'secret_key 参数需为 32 位字符串'
          : 'string' != typeof t.appid
          ? 'appid 参数需为 string 类型'
          : '' !== t.appid.trim() || '缺少 appid')
      : '初始化参数需为 object 类型';
  };
function vt() {
  return E;
}
function pt(t, e) {
  return Object.prototype.hasOwnProperty.call(t, e);
}
function _t(t, e) {
  var n = {};
  return (
    e.forEach(function (e) {
      pt(t, e) && (n[e] = t[e]);
    }),
    n
  );
}
var ht = function (t) {
  try {
    return t && 'string' == typeof t
      ? -1 === (t = t.replace(/\s/g, '')).indexOf('.')
        ? t
        : t.split('.').slice(0, 2).join('.')
      : '';
  } catch (e) {
    return t;
  }
};
function gt(t, e, n) {
  var r = n.value;
  return (
    (n.value = function () {
      for (var n = arguments.length, o = new Array(n), i = 0; i < n; i++) o[i] = arguments[i];
      try {
        return r.apply(this, o);
      } catch (n) {
        try {
          h.error.apply(
            h,
            ['calling '.concat(t.constructor.name, '.').concat(e, ' error with arguments')].concat(
              o,
            ),
          ),
            h.error(n);
          var a = {
            log_type: T.JS_RUN_ERROR,
            message: '[safeExcutable] '
              .concat(t.constructor.name, '.')
              .concat(e, ': ')
              .concat(null == n ? void 0 : n.message),
            err_stack: null == n ? void 0 : n.stack,
          };
          ot(this.reportLog) ? this.reportLog(a) : z(a);
        } catch (a) {}
      }
    }),
    n
  );
}
var yt = function (t, e, n) {
    var r = n.value;
    return (
      (n.value = function () {
        if (this.inited) {
          for (var t = arguments.length, e = new Array(t), n = 0; n < t; n++) e[n] = arguments[n];
          return r.apply(this, e);
        }
        h.error('上报失败，请先完成初始化');
      }),
      n
    );
  },
  mt = Object.defineProperty,
  At = Object.getOwnPropertyDescriptor,
  Rt = function (t, e, n, r) {
    for (var o, i = r > 1 ? void 0 : r ? At(e, n) : e, a = t.length - 1; a >= 0; a--)
      (o = t[a]) && (i = (r ? o(e, n, i) : o(i)) || i);
    return r && i && mt(e, n, i), i;
  },
  St = (function () {
    function t(e) {
      var n = e.userActionSetId,
        r = e.maxLength,
        o = void 0 === r ? 500 : r;
      s(this, t),
        (this.lostActionMaps = {}),
        (this.stack = []),
        (this.localStorageKey = ''),
        (this.localStorageKey = ''.concat(A, '_').concat(null == n ? void 0 : n.toString())),
        (this.maxLength = o),
        (this.userActionSetId = n),
        this.setTimeStamp(),
        this.init();
    }
    return (
      f(t, [
        {
          key: 'getItems',
          value: function () {
            return this.stack;
          },
        },
        {
          key: 'getStorage',
          value: function () {
            var t,
              e = (null == (t = K) ? void 0 : t.getSync(this.localStorageKey)) || '[]';
            return JSON.parse(e);
          },
        },
        {
          key: 'reportLostNum',
          value: function () {
            var t = this,
              e = Object.assign({}, this.lostActionMaps),
              n = [];
            for (var r in e) {
              var o = null == r ? void 0 : r.split('_');
              n.push({
                queue_lost_session_id: o[0],
                queue_lost_timestamp: o[1],
                queue_lost_num: e[r],
              });
            }
            n.length &&
              (this.setTimeStamp(),
              n.forEach(function (e) {
                var n = Object.assign(
                    {},
                    { user_action_set_id: t.userActionSetId, log_type: T.QUEUE_LOST_NUM },
                    e,
                  ),
                  r = null == e ? void 0 : e.queue_lost_session_id,
                  o = null == e ? void 0 : e.queue_lost_timestamp,
                  i = ''.concat(r, '_').concat(o);
                z(n, function () {
                  pt(t.lostActionMaps, i) &&
                    (delete t.lostActionMaps[i], K.setSync(S, JSON.stringify(t.lostActionMaps)));
                });
              }));
          },
        },
        {
          key: 'getLostMaps',
          value: function () {
            return this.lostActionMaps;
          },
        },
        {
          key: 'init',
          value: function () {
            var t = this,
              e = this.getStorage(),
              n =
                null == e
                  ? void 0
                  : e.map(function (t) {
                      var e, n;
                      return t.inner_status === (null == (e = O) ? void 0 : e.reporting)
                        ? Object.assign({}, t, {
                            inner_status: null == (n = O) ? void 0 : n.fail,
                            is_retry: !0,
                            retry_count: t.retry_count + 1,
                          })
                        : t;
                    });
            (this.stack = n),
              (this.lostActionMaps = JSON.parse(K.getSync(S) || '{}')),
              setTimeout(function () {
                t.reportLostNum();
              }, 1e3);
          },
        },
        {
          key: 'addItem',
          value: function (t) {
            var e;
            null == (e = null == this ? void 0 : this.stack) || e.push(t);
          },
        },
        {
          key: 'removeItems',
          value: function (t) {
            var e,
              n =
                null == (e = null == this ? void 0 : this.stack)
                  ? void 0
                  : e.filter(function (e) {
                      return !(null != t && t.includes(null == e ? void 0 : e.action_id));
                    });
            this.stack = n;
          },
        },
        {
          key: 'updateForReportFail',
          value: function (t) {
            var e;
            this.stack =
              null == (e = this.stack)
                ? void 0
                : e.map(function (e) {
                    var n;
                    return null != t && t.includes(null == e ? void 0 : e.action_id)
                      ? Object.assign({}, e, {
                          inner_status: null == (n = O) ? void 0 : n.fail,
                          retry_count: e.retry_count + 1,
                          is_retry: !0,
                        })
                      : e;
                  });
          },
        },
        {
          key: 'updateForReporting',
          value: function (t) {
            var e;
            this.stack =
              null == (e = this.stack)
                ? void 0
                : e.map(function (e) {
                    var n;
                    return null != t && t.includes(null == e ? void 0 : e.action_id)
                      ? Object.assign({}, e, {
                          inner_status: null == (n = O) ? void 0 : n.reporting,
                        })
                      : e;
                  });
          },
        },
        {
          key: 'updateAllStack',
          value: function (t) {
            this.stack = t;
          },
        },
        {
          key: 'updateToStorage',
          value: function () {
            K.setSync(this.localStorageKey, JSON.stringify(this.stack));
          },
        },
        {
          key: 'updateLostAction',
          value: function (t) {
            if (t) {
              var e = ''.concat(t, '_').concat(this.timeStamp),
                n = this.lostActionMaps[e] || 0;
              (this.lostActionMaps[e] = n + 1), K.setSync(S, JSON.stringify(this.lostActionMaps));
            }
          },
        },
        {
          key: 'setTimeStamp',
          value: function () {
            this.timeStamp = Date.now().toString();
          },
        },
      ]),
      t
    );
  })();
Rt([gt], St.prototype, 'getItems', 1),
  Rt([gt], St.prototype, 'getStorage', 1),
  Rt([gt], St.prototype, 'reportLostNum', 1),
  Rt([gt], St.prototype, 'getLostMaps', 1),
  Rt([gt], St.prototype, 'init', 1),
  Rt([gt], St.prototype, 'addItem', 1),
  Rt([gt], St.prototype, 'removeItems', 1),
  Rt([gt], St.prototype, 'updateForReportFail', 1),
  Rt([gt], St.prototype, 'updateForReporting', 1),
  Rt([gt], St.prototype, 'updateAllStack', 1),
  Rt([gt], St.prototype, 'updateToStorage', 1),
  Rt([gt], St.prototype, 'updateLostAction', 1);
var bt = Object.defineProperty,
  kt = Object.getOwnPropertyDescriptor,
  Ot = function (t, e, n, r) {
    for (var o, i = r > 1 ? void 0 : r ? kt(e, n) : e, a = t.length - 1; a >= 0; a--)
      (o = t[a]) && (i = (r ? o(e, n, i) : o(i)) || i);
    return r && i && bt(e, n, i), i;
  },
  Et = (function (t) {
    r(n, St);
    var e = i(n);
    function n(t) {
      var r,
        o = t.userActionSetId,
        i = t.maxLength,
        a = void 0 === i ? 500 : i,
        u = t.ogEvents,
        c = void 0 === u ? [] : u;
      return s(this, n), ((r = e.call(this, { userActionSetId: o, maxLength: a })).ogEvents = c), r;
    }
    return (
      f(n, [
        {
          key: 'getReportableActions',
          value: function () {
            var t = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : 100,
              e = this.getItems(),
              n = [];
            return (
              null == e ||
                e.forEach(function (e) {
                  var r;
                  (null == n ? void 0 : n.length) < t &&
                    (null == e ? void 0 : e.inner_status) !==
                      (null == (r = O) ? void 0 : r.reporting) &&
                    (null == n || n.push(e));
                }),
              n
            );
          },
        },
        {
          key: 'addAction',
          value: function (t) {
            var e = this.getItems();
            if ((null == e ? void 0 : e.length) >= this.maxLength) {
              var n = '队列长度超过最大限制'.concat(
                this.maxLength,
                '条，SDK将按照行为优先级排序，丢弃优先级最低的行为事件',
              );
              h.warn(n),
                z({
                  user_action_set_id: this.userActionSetId,
                  log_type: T.JS_QUEUE_LOG,
                  message: n,
                });
              var r = this.sortQueue(t, e);
              h.debug &&
                h.info('超过'.concat(this.maxLength, '条按优先级排序的队列：'), r.concat([]));
              var o = r.pop();
              this.updateAllStack(r),
                this.updateLostAction((null == o ? void 0 : o.session_id) || '');
            } else this.addItem(t);
            this.updateToStorage();
          },
        },
        {
          key: 'removeActions',
          value: function (t) {
            this.removeItems(t), this.updateToStorage();
          },
        },
        {
          key: 'updateActionsForReportFail',
          value: function (t) {
            this.updateForReportFail(t), this.updateToStorage();
          },
        },
        {
          key: 'updateActionsForReporting',
          value: function (t) {
            this.updateForReporting(t), this.updateToStorage();
          },
        },
        {
          key: 'getReportableActionsLength',
          value: function () {
            var t = this.getItems().filter(function (t) {
              var e;
              return (
                (null == t ? void 0 : t.inner_status) !== (null == (e = O) ? void 0 : e.reporting)
              );
            });
            return null == t ? void 0 : t.length;
          },
        },
        {
          key: 'sortQueue',
          value: function (t, e) {
            var n = this,
              r = {},
              o = null == t ? void 0 : t.action_time,
              i = e.concat([t]),
              a = function (t) {
                return r[t.action_id] || (r[t.action_id] = n.caculateWeight(o, t)), r[t.action_id];
              };
            return i.sort(function (t, e) {
              return a(e) - a(t);
            });
          },
        },
        {
          key: 'caculateWeight',
          value: function (t, e) {
            var n,
              r = 0,
              o = this.formatWeight(t, null == e ? void 0 : e.action_time),
              i = o.ogWeight,
              a = o.sdkWeight,
              u = o.userWeight;
            null != (n = this.ogEvents) &&
              n.includes(null == e ? void 0 : e.action_type) &&
              (r += i),
              null != e && e.is_sdk_auto_track ? (r += a) : (r += u);
            var c = t - (null == e ? void 0 : e.action_time) + 1;
            return (r = c > 0 ? r + 1 / c : r);
          },
        },
        {
          key: 'formatWeight',
          value: function (t, e) {
            var n = D,
              r = P,
              o = L;
            return (
              t - e > 2592e6 && ((n /= 100), (r /= 100), (o /= 100)),
              { ogWeight: n, sdkWeight: r, userWeight: o }
            );
          },
        },
      ]),
      n
    );
  })();
Ot([gt], Et.prototype, 'getReportableActions', 1),
  Ot([gt], Et.prototype, 'addAction', 1),
  Ot([gt], Et.prototype, 'removeActions', 1),
  Ot([gt], Et.prototype, 'updateActionsForReportFail', 1),
  Ot([gt], Et.prototype, 'updateActionsForReporting', 1),
  Ot([gt], Et.prototype, 'getReportableActionsLength', 1),
  Ot([gt], Et.prototype, 'sortQueue', 1),
  Ot([gt], Et.prototype, 'caculateWeight', 1),
  Ot([gt], Et.prototype, 'formatWeight', 1);
var Tt = 'JS_RUN_ERROR',
  It = 'ENTER_BACKGROUND',
  Nt = 'ENTER_FOREGROUND',
  wt = 'START_APP',
  xt = 'TICKET',
  Ct = 'PAGE_VIEW',
  Dt = 'PAGE_LEAVE',
  Lt = 'SHARE',
  Pt = 'ADD_TO_WISHLIST',
  Ut = 'PAGE_UNLOAD',
  Mt = 'COMPONENT_VIEW',
  qt = 'COMPONENT_LEAVE',
  jt = 'COMPONENT_SHARE',
  Ft = 'COMPONENT_ADD_TO_WISHLIST',
  Vt = 'COMPONENT_PAGE_UNLOAD',
  Wt = [Nt, wt],
  Bt = [
    'REGISTER',
    'VIEW_CONTENT',
    'CONSULT',
    'ADD_TO_CART',
    'PURCHASE',
    'ACTIVATE_APP',
    'COMPLETE_ORDER',
    'ADD_TO_WISHLIST',
    'START_APP',
    'RESERVATION',
    'APPLY',
    'CLAIM_OFFER',
    'DELIVER',
    'SIGN_IN',
    'CONFIRM_EFFECTIVE_LEADS',
    'SCANCODE',
    'AD_PURCHASE',
    'PRE_CREDIT',
    'CREDIT',
    'WITHDRAW_DEPOSITS',
    'LANDING_PAGE_CLICK',
    'SELECT_COURSE',
    'ONE_DAY_LEAVE',
    'PRODUCT_VIEW',
    'PURCHASE_MEMBER_CARD',
    'ONLINE_CONSULT',
    'MAKE_PHONE_CALL',
    'ADD_DESKTOP',
    'RETURN',
    'PURCHASE_COUPON',
    'CUSTOMER_PROMOTION_PAGE_VIEW',
    'SCANCODE_WX',
    'OPEN_ACCOUNT',
    'WECOM_CONSULT',
  ],
  Kt = 'onLaunch',
  Qt = 'navigation',
  Gt = 'createAction',
  Ht = {
    ANDROID12: 1,
    ANDROID13: 1,
    ANDROID10: 1,
    ANDROID11: 1,
    ANDROID9: 1,
    ANDROID14: 1,
    'ANDROID8.1.0': 1,
    'ANDROID7.1.1': 1,
    'ANDROID7.1.2': 1,
    'ANDROID6.0.1': 1,
    'ANDROID8.0.0': 1,
    'ANDROID7.0': 1,
    'WINDOWS10 x64': 1,
    'WINDOWS11 x64': 1,
    'WINDOWS7 x64': 1,
  },
  Jt = (function () {
    var t;
    return function () {
      if (!t)
        try {
          var e = wx.getLaunchOptionsSync(),
            n = e.query.gdt_vid || '';
          n ? K.setSync(R, n) : (n = K.getSync(R) || ''),
            (t = {
              source_scene: e.scene,
              pkg_channel_id: e.query.wxgamepro || '',
              ad_trace_id: n,
            });
        } catch (e) {
          (t = {}), h.log('获取场景值和渠道号失败', e);
        }
      return t;
    };
  })(),
  Yt = (function () {
    var t = { page_url: '', page_title: '' },
      e = {};
    return {
      get: function () {
        return zt(t, e);
      },
      set: function (n) {
        var r,
          o,
          i,
          a = n.from,
          u = n.pageTitle;
        try {
          if (a === Kt) {
            var c = wx.getLaunchOptionsSync();
            t.page_url = p(null == c ? void 0 : c.path, null == c ? void 0 : c.query);
          } else if (a === Qt && t.page_url && u) {
            var s = $t(),
              l = Xt(s, !1);
            e[l] = u;
          } else {
            var f = $t(),
              d = f
                ? null ==
                  (i =
                    (null ==
                    (o =
                      null == (r = null == __wxConfig ? void 0 : __wxConfig.page)
                        ? void 0
                        : r[''.concat(f.route, '.html')])
                      ? void 0
                      : o.window) || {})
                  ? void 0
                  : i.navigationBarTitleText
                : '';
            (t.page_url = Xt(f, !0)), (t.page_title = d || '');
          }
          return zt(t, e);
        } catch (c) {
          return h.log('设置标题失败', c), t;
        }
      },
    };
  })(),
  zt = function (t, e) {
    var n = null == t ? void 0 : t.page_url,
      r = null == t ? void 0 : t.page_title;
    return e[n] && (r = e[n]), { page_title: r, page_url: n };
  },
  $t = function () {
    var t = getCurrentPages();
    return t[(null == t ? void 0 : t.length) - 1];
  },
  Xt = function (t, e) {
    var n = (null == t ? void 0 : t.route) || '';
    return e && n && (n = p(null == t ? void 0 : t.route, null == t ? void 0 : t.options)), n;
  },
  Zt = (function () {
    var t = '';
    return function () {
      var e, n, r;
      try {
        if (!t) {
          var o =
            null == (n = null == (e = null == wx ? void 0 : wx.webpackJsonp) ? void 0 : e[0])
              ? void 0
              : n[0];
          t =
            (null == (r = null == o ? void 0 : o[0]) ? void 0 : r.indexOf('taro')) > -1
              ? 'taro'
              : pt(window || {}, 'webpackJsonpcreateApp')
              ? 'kbone'
              : pt((window || globalThis).global || {}, 'mpvue')
              ? 'mpvue'
              : pt(wx || {}, 'createPage')
              ? 'uni-app'
              : (null == o ? void 0 : o.indexOf('remax-vendors')) > -1
              ? 'remax'
              : 'unknow';
        }
        return t;
      } catch (o) {
        return h.log('获取框架信息失败', o), (t = 'unknow');
      }
    };
  })(),
  te = (function () {
    function t() {
      s(this, t), (this.events = {});
    }
    return (
      f(
        t,
        [
          {
            key: 'subscribe',
            value: function (e, n) {
              t.checkCallback(n),
                rt(this.events[e]) ? this.events[e].push(n) : (this.events[e] = [n]);
            },
          },
          {
            key: 'once',
            value: function (e, n) {
              t.checkCallback(n), this.subscribe(this.onceEventName(e), n);
            },
          },
          {
            key: 'unsubscribe',
            value: function (e, n) {
              t.checkCallback(n),
                rt(this.events[e]) &&
                  (this.events[e] = this.events[e].filter(function (t) {
                    return t !== n;
                  })),
                rt(this.events[this.onceEventName(e)]) &&
                  (this.events[this.onceEventName(e)] = this.events[this.onceEventName(e)].filter(
                    function (t) {
                      return t !== n;
                    },
                  ));
            },
          },
          {
            key: 'publish',
            value: function (t) {
              for (var e = arguments.length, n = new Array(e > 1 ? e - 1 : 0), r = 1; r < e; r++)
                n[r - 1] = arguments[r];
              var o = Date.now();
              rt(this.events[t]) &&
                this.events[t].forEach(function (t) {
                  return t.apply(void 0, [o].concat(n));
                }),
                rt(this.events[this.onceEventName(t)]) &&
                  (this.events[this.onceEventName(t)].forEach(function (t) {
                    return t.apply(void 0, [o].concat(n));
                  }),
                  (this.events[this.onceEventName(t)] = []));
            },
          },
          {
            key: 'onceEventName',
            value: function (t) {
              return 'once_event_prefix_'.concat(t);
            },
          },
        ],
        [
          {
            key: 'checkCallback',
            value: function (e) {
              ot(e) || h.error(t.ERROR_CALLBACK_IS_NOT_A_FUNCTION);
            },
          },
        ],
      ),
      t
    );
  })(),
  ee = te;
ee.ERROR_CALLBACK_IS_NOT_A_FUNCTION = 'callback 不是函数';
var ne = new ee(),
  re = (function () {
    var t = !1;
    return function () {
      t ||
        ((t = !0),
        wx.onAppHide(function () {
          ne.publish('FLUSH_ALL');
        }));
    };
  })(),
  oe = (function () {
    var t = !1,
      e = !1,
      n = !0;
    return function () {
      t ||
        ((t = !0),
        setInterval(function () {
          n && ne.publish(xt);
        }, 6e4),
        wx.onAppShow(function () {
          e || (Yt.set({ from: Kt }), ne.publish(wt), (e = !0)), (n = !0), ne.publish(Nt);
        }),
        wx.onAppHide(function () {
          ne.publish(It), (n = !1);
        }));
    };
  })(),
  ie = (function () {
    function t() {
      s(this, t);
    }
    return (
      f(t, null, [
        {
          key: 'isEmpty',
          value: function (t) {
            return !t || '' === t.trim();
          },
        },
        {
          key: 'format',
          value: function (t) {
            for (var e = arguments.length, n = new Array(e > 1 ? e - 1 : 0), r = 1; r < e; r++)
              n[r - 1] = arguments[r];
            return t.replace(/\${(\d+)}/g, function (t, e) {
              return n[e];
            });
          },
        },
        {
          key: 'customStringify',
          value: function (t) {
            var e = [];
            return JSON.stringify(t, function (t, n) {
              if (void 0 === n) return 'undefined';
              if ('object' == v(n) && null !== n) {
                if (-1 !== e.indexOf(n)) return '[Circular]';
                e.push(n);
              }
              return n;
            });
          },
        },
      ]),
      t
    );
  })(),
  ae = /^([a-zA-Z][a-zA-Z\d_]{0,63})$/i,
  ue = /^ams_reserved_(.*)/i,
  ce = (function () {
    function t() {
      s(this, t);
    }
    return (
      f(t, null, [
        {
          key: 'validateActionType',
          value: function (e) {
            return ie.isEmpty(e)
              ? (h.error(t.ERROR_ACTION_TYPE_NULL), !1)
              : !!ae.test(e) || (h.error(t.ERROR_ACTION_TYPE_INVALID), !1);
          },
        },
        {
          key: 'validateActionParam',
          value: function (e) {
            if (!e) return !0;
            if (!nt(e)) return h.error(t.ERROR_ACTION_PARAM_IS_NOT_OBJECT), !1;
            for (var n in e) {
              if (ie.isEmpty(n)) return h.error(t.ERROR_ACTION_PARAM_KEY_NULL), !1;
              if (!ae.test(n)) return h.error(t.ERROR_ACTION_PARAM_KEY_INVALID), !1;
              ue.test(n) && h.warn(t.WARN_ACTION_PARAM_KEY_RESERVED);
              var r = e[n];
              if (!t.isValidValue(r))
                return h.error(ie.format(t.ERROR_ACTION_PARAM_VALUE_INVALID, n, r)), !1;
              if (rt(r)) {
                if (!t.isValidArrayValue(r)) {
                  for (var o = 0; o < r.length; o++)
                    h.error(
                      ie.format(
                        t.ERROR_ACTION_PARAM_VALUE_ARRAY_INVALID,
                        n,
                        ie.customStringify(r),
                        o,
                        r[o],
                      ),
                    );
                  return !1;
                }
                if (!t.checkArrayElementTypes(r))
                  return h.error(t.ERROR_ACTION_PARAM_VALUE_ARRAY_TYPE_UNUNIQUE), !1;
              }
            }
            return !0;
          },
        },
        {
          key: 'isValidValue',
          value: function (t) {
            return 'string' == typeof t || 'number' == typeof t || 'boolean' == typeof t || rt(t);
          },
        },
        {
          key: 'isValidArrayValue',
          value: function (t) {
            for (var e = 0; e < t.length; e++) {
              var n = t[e];
              if ('string' != typeof n && 'number' != typeof n && 'boolean' != typeof n) return !1;
            }
            return !0;
          },
        },
        {
          key: 'checkArrayElementTypes',
          value: function (t) {
            if (!t || t.length <= 1) return !0;
            for (var e = v(t[0]), n = 1; n < t.length; n++) if (v(t[n]) !== e) return !1;
            return !0;
          },
        },
      ]),
      t
    );
  })(),
  se = ce;
(se.ERROR_ACTION_TYPE_NULL = '在track方法中，action_type参数不能为空！'),
  (se.ERROR_ACTION_TYPE_INVALID =
    '在track方法中，action_type参数只能包含字母、数字和下划线，且只能以字母开头，长度不能超过64个字符！'),
  (se.ERROR_ACTION_PARAM_KEY_NULL = '在track方法中，action_param参数的key不能为空！'),
  (se.ERROR_ACTION_PARAM_KEY_INVALID =
    '在track方法中，action_param参数的key只能包含字母、数字和下划线，且不能以数字开头，长度不能超过64个字符！'),
  (se.WARN_ACTION_PARAM_KEY_RESERVED =
    "SDK内部预留参数的key均以'ams_reserved_'开头，该参数的值会被SDK内部覆盖，请不要使用！"),
  (se.ERROR_ACTION_PARAM_VALUE_INVALID =
    '在track方法中，action_param参数的value必须是String/Number/Boolean/Array中的一种！[key=${0}, value=${1}]'),
  (se.ERROR_ACTION_PARAM_VALUE_ARRAY_INVALID =
    '在track方法中，如果action_param参数中的某个元素的value是Array，那么这个Array中的每个元素必须是String/Number/Boolean中的一种！[key=${0}, value=${1}, 数组的第${2}个元素为${3}]'),
  (se.ERROR_ACTION_PARAM_VALUE_ARRAY_TYPE_UNUNIQUE =
    '在track方法中，如果action_param参数中的某个元素的value是Array，那么这个Array中所有元素的类型必须是同一种！'),
  (se.ERROR_ACTION_PARAM_IS_NOT_OBJECT = 'action_param 参数不是Object');
var le = (function () {
  function t() {
    s(this, t);
  }
  return (
    f(t, null, [
      {
        key: 'revise',
        value: function (t) {
          t > 0 && !this.isRevised && ((this.offsetTime = t - Date.now()), (this.isRevised = !0));
        },
      },
      {
        key: 'getRevisedcurrentTimeMillis',
        value: function () {
          return this.isRevised ? Date.now() + this.offsetTime : -1;
        },
      },
    ]),
    t
  );
})();
(le.offsetTime = 0), (le.isRevised = !1);
var fe =
  'undefined' != typeof globalThis
    ? globalThis
    : 'undefined' != typeof window
    ? window
    : 'undefined' != typeof global
    ? global
    : 'undefined' != typeof self
    ? self
    : {};
var de = { exports: {} };
!(function (t) {
  !(function (e) {
    function n(t, e) {
      var n = (65535 & t) + (65535 & e);
      return (((t >> 16) + (e >> 16) + (n >> 16)) << 16) | (65535 & n);
    }
    function r(t, e, r, o, i, a) {
      return n(
        (function (t, e) {
          return (t << e) | (t >>> (32 - e));
        })(n(n(e, t), n(o, a)), i),
        r,
      );
    }
    function o(t, e, n, o, i, a, u) {
      return r((e & n) | (~e & o), t, e, i, a, u);
    }
    function i(t, e, n, o, i, a, u) {
      return r((e & o) | (n & ~o), t, e, i, a, u);
    }
    function a(t, e, n, o, i, a, u) {
      return r(e ^ n ^ o, t, e, i, a, u);
    }
    function u(t, e, n, o, i, a, u) {
      return r(n ^ (e | ~o), t, e, i, a, u);
    }
    function c(t, e) {
      (t[e >> 5] |= 128 << e % 32), (t[14 + (((e + 64) >>> 9) << 4)] = e);
      var r,
        c,
        s,
        l,
        f,
        d = 1732584193,
        v = -271733879,
        p = -1732584194,
        _ = 271733878;
      for (r = 0; r < t.length; r += 16)
        (c = d),
          (s = v),
          (l = p),
          (f = _),
          (d = o(d, v, p, _, t[r], 7, -680876936)),
          (_ = o(_, d, v, p, t[r + 1], 12, -389564586)),
          (p = o(p, _, d, v, t[r + 2], 17, 606105819)),
          (v = o(v, p, _, d, t[r + 3], 22, -1044525330)),
          (d = o(d, v, p, _, t[r + 4], 7, -176418897)),
          (_ = o(_, d, v, p, t[r + 5], 12, 1200080426)),
          (p = o(p, _, d, v, t[r + 6], 17, -1473231341)),
          (v = o(v, p, _, d, t[r + 7], 22, -45705983)),
          (d = o(d, v, p, _, t[r + 8], 7, 1770035416)),
          (_ = o(_, d, v, p, t[r + 9], 12, -1958414417)),
          (p = o(p, _, d, v, t[r + 10], 17, -42063)),
          (v = o(v, p, _, d, t[r + 11], 22, -1990404162)),
          (d = o(d, v, p, _, t[r + 12], 7, 1804603682)),
          (_ = o(_, d, v, p, t[r + 13], 12, -40341101)),
          (p = o(p, _, d, v, t[r + 14], 17, -1502002290)),
          (d = i(d, (v = o(v, p, _, d, t[r + 15], 22, 1236535329)), p, _, t[r + 1], 5, -165796510)),
          (_ = i(_, d, v, p, t[r + 6], 9, -1069501632)),
          (p = i(p, _, d, v, t[r + 11], 14, 643717713)),
          (v = i(v, p, _, d, t[r], 20, -373897302)),
          (d = i(d, v, p, _, t[r + 5], 5, -701558691)),
          (_ = i(_, d, v, p, t[r + 10], 9, 38016083)),
          (p = i(p, _, d, v, t[r + 15], 14, -660478335)),
          (v = i(v, p, _, d, t[r + 4], 20, -405537848)),
          (d = i(d, v, p, _, t[r + 9], 5, 568446438)),
          (_ = i(_, d, v, p, t[r + 14], 9, -1019803690)),
          (p = i(p, _, d, v, t[r + 3], 14, -187363961)),
          (v = i(v, p, _, d, t[r + 8], 20, 1163531501)),
          (d = i(d, v, p, _, t[r + 13], 5, -1444681467)),
          (_ = i(_, d, v, p, t[r + 2], 9, -51403784)),
          (p = i(p, _, d, v, t[r + 7], 14, 1735328473)),
          (d = a(d, (v = i(v, p, _, d, t[r + 12], 20, -1926607734)), p, _, t[r + 5], 4, -378558)),
          (_ = a(_, d, v, p, t[r + 8], 11, -2022574463)),
          (p = a(p, _, d, v, t[r + 11], 16, 1839030562)),
          (v = a(v, p, _, d, t[r + 14], 23, -35309556)),
          (d = a(d, v, p, _, t[r + 1], 4, -1530992060)),
          (_ = a(_, d, v, p, t[r + 4], 11, 1272893353)),
          (p = a(p, _, d, v, t[r + 7], 16, -155497632)),
          (v = a(v, p, _, d, t[r + 10], 23, -1094730640)),
          (d = a(d, v, p, _, t[r + 13], 4, 681279174)),
          (_ = a(_, d, v, p, t[r], 11, -358537222)),
          (p = a(p, _, d, v, t[r + 3], 16, -722521979)),
          (v = a(v, p, _, d, t[r + 6], 23, 76029189)),
          (d = a(d, v, p, _, t[r + 9], 4, -640364487)),
          (_ = a(_, d, v, p, t[r + 12], 11, -421815835)),
          (p = a(p, _, d, v, t[r + 15], 16, 530742520)),
          (d = u(d, (v = a(v, p, _, d, t[r + 2], 23, -995338651)), p, _, t[r], 6, -198630844)),
          (_ = u(_, d, v, p, t[r + 7], 10, 1126891415)),
          (p = u(p, _, d, v, t[r + 14], 15, -1416354905)),
          (v = u(v, p, _, d, t[r + 5], 21, -57434055)),
          (d = u(d, v, p, _, t[r + 12], 6, 1700485571)),
          (_ = u(_, d, v, p, t[r + 3], 10, -1894986606)),
          (p = u(p, _, d, v, t[r + 10], 15, -1051523)),
          (v = u(v, p, _, d, t[r + 1], 21, -2054922799)),
          (d = u(d, v, p, _, t[r + 8], 6, 1873313359)),
          (_ = u(_, d, v, p, t[r + 15], 10, -30611744)),
          (p = u(p, _, d, v, t[r + 6], 15, -1560198380)),
          (v = u(v, p, _, d, t[r + 13], 21, 1309151649)),
          (d = u(d, v, p, _, t[r + 4], 6, -145523070)),
          (_ = u(_, d, v, p, t[r + 11], 10, -1120210379)),
          (p = u(p, _, d, v, t[r + 2], 15, 718787259)),
          (v = u(v, p, _, d, t[r + 9], 21, -343485551)),
          (d = n(d, c)),
          (v = n(v, s)),
          (p = n(p, l)),
          (_ = n(_, f));
      return [d, v, p, _];
    }
    function s(t) {
      var e,
        n = '',
        r = 32 * t.length;
      for (e = 0; e < r; e += 8) n += String.fromCharCode((t[e >> 5] >>> e % 32) & 255);
      return n;
    }
    function l(t) {
      var e,
        n = [];
      for (n[(t.length >> 2) - 1] = void 0, e = 0; e < n.length; e += 1) n[e] = 0;
      var r = 8 * t.length;
      for (e = 0; e < r; e += 8) n[e >> 5] |= (255 & t.charCodeAt(e / 8)) << e % 32;
      return n;
    }
    function f(t) {
      var e,
        n,
        r = '0123456789abcdef',
        o = '';
      for (n = 0; n < t.length; n += 1)
        (e = t.charCodeAt(n)), (o += r.charAt((e >>> 4) & 15) + r.charAt(15 & e));
      return o;
    }
    function d(t) {
      return unescape(encodeURIComponent(t));
    }
    function v(t) {
      return (function (t) {
        return s(c(l(t), 8 * t.length));
      })(d(t));
    }
    function p(t, e) {
      return (function (t, e) {
        var n,
          r,
          o = l(t),
          i = [],
          a = [];
        for (
          i[15] = a[15] = void 0, o.length > 16 && (o = c(o, 8 * t.length)), n = 0;
          n < 16;
          n += 1
        )
          (i[n] = 909522486 ^ o[n]), (a[n] = 1549556828 ^ o[n]);
        return (r = c(i.concat(l(e)), 512 + 8 * e.length)), s(c(a.concat(r), 640));
      })(d(t), d(e));
    }
    function _(t, e, n) {
      return e
        ? n
          ? p(e, t)
          : (function (t, e) {
              return f(p(t, e));
            })(e, t)
        : n
        ? v(t)
        : (function (t) {
            return f(v(t));
          })(t);
    }
    t.exports ? (t.exports = _) : (e.md5 = _);
  })(fe);
})(de);
var ve = (function (t) {
    return t && t.__esModule && Object.prototype.hasOwnProperty.call(t, 'default') ? t.default : t;
  })(de.exports),
  pe = 'function' == typeof btoa,
  _e = 'function' == typeof Buffer;
'function' == typeof TextDecoder && new TextDecoder();
var he,
  ge = 'function' == typeof TextEncoder ? new TextEncoder() : void 0,
  ye = Array.prototype.slice.call(
    'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=',
  );
(he = {}),
  ye.forEach(function (t, e) {
    return (he[t] = e);
  });
var me = String.fromCharCode.bind(String);
'function' == typeof Uint8Array.from && Uint8Array.from.bind(Uint8Array);
var Ae = pe
    ? function (t) {
        return btoa(t);
      }
    : _e
    ? function (t) {
        return Buffer.from(t, 'binary').toString('base64');
      }
    : function (t) {
        for (var e, n, r, o, i = '', a = t.length % 3, u = 0; u < t.length; ) {
          if (
            (n = t.charCodeAt(u++)) > 255 ||
            (r = t.charCodeAt(u++)) > 255 ||
            (o = t.charCodeAt(u++)) > 255
          )
            throw new TypeError('invalid character found');
          i +=
            ye[((e = (n << 16) | (r << 8) | o) >> 18) & 63] +
            ye[(e >> 12) & 63] +
            ye[(e >> 6) & 63] +
            ye[63 & e];
        }
        return a ? i.slice(0, a - 3) + '==='.substring(a) : i;
      },
  Re = _e
    ? function (t) {
        return Buffer.from(t).toString('base64');
      }
    : function (t) {
        for (var e = [], n = 0, r = t.length; n < r; n += 4096)
          e.push(me.apply(null, t.subarray(n, n + 4096)));
        return Ae(e.join(''));
      },
  Se = function (t) {
    if (t.length < 2)
      return (e = t.charCodeAt(0)) < 128
        ? t
        : e < 2048
        ? me(192 | (e >>> 6)) + me(128 | (63 & e))
        : me(224 | ((e >>> 12) & 15)) + me(128 | ((e >>> 6) & 63)) + me(128 | (63 & e));
    var e = 65536 + 1024 * (t.charCodeAt(0) - 55296) + (t.charCodeAt(1) - 56320);
    return (
      me(240 | ((e >>> 18) & 7)) +
      me(128 | ((e >>> 12) & 63)) +
      me(128 | ((e >>> 6) & 63)) +
      me(128 | (63 & e))
    );
  },
  be = /[\uD800-\uDBFF][\uDC00-\uDFFFF]|[^\x00-\x7F]/g,
  ke = _e
    ? function (t) {
        return Buffer.from(t, 'utf8').toString('base64');
      }
    : ge
    ? function (t) {
        return Re(ge.encode(t));
      }
    : function (t) {
        return Ae(
          (function (t) {
            return t.replace(be, Se);
          })(t),
        );
      };
var Oe = Object.defineProperty,
  Ee = Object.getOwnPropertyDescriptor,
  Te = function (t, e, n, r) {
    for (var o, i = r > 1 ? void 0 : r ? Ee(e, n) : e, a = t.length - 1; a >= 0; a--)
      (o = t[a]) && (i = (r ? o(e, n, i) : o(i)) || i);
    return r && i && Oe(e, n, i), i;
  },
  Ie = (function () {
    function t(e) {
      var n = this;
      s(this, t),
        (this.cgiBatchSize = E.cgiBatchSize),
        (this.reportThreshold = E.reportThreshold),
        (this.reportDelay = E.reportDelay),
        (this.triggerExecuteSend = (function (t) {
          var e,
            n = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : 0,
            r = [];
          return function () {
            for (var o = arguments.length, i = new Array(o), a = 0; a < o; a++) i[a] = arguments[a];
            return (
              clearTimeout(e),
              (e = setTimeout(function () {
                var e = t.apply(void 0, i);
                r.forEach(function (t) {
                  return t(e);
                }),
                  (r = []);
              }, n)),
              new Promise(function (t) {
                return r.push(t);
              })
            );
          };
        })(function () {
          n.executeSend();
        }, 1e3 * this.reportDelay)),
        (this.inspectDelay = E.inspectDelay),
        (this.inspectTimer = void 0),
        (this.isNeedContinueSend = !1),
        (this.getBaseInfo = e.getBaseInfo),
        (this.reportLog = e.reportLog),
        (this.queueManager = e.queueManager),
        this.flushSend(),
        this.startInspectTimer();
    }
    return (
      f(
        t,
        [
          {
            key: 'batchSend',
            value: function () {
              this.queueManager.getReportableActions(this.reportThreshold).length >=
              this.reportThreshold
                ? this.executeSend()
                : this.triggerExecuteSend(),
                this.startInspectTimer();
            },
          },
          {
            key: 'flushSend',
            value: function () {
              this.executeSend();
            },
          },
          {
            key: 'executeSend',
            value: function () {
              var e = this;
              if (t.currentRequestCount >= t.requestConcurrency) this.isNeedContinueSend = !0;
              else {
                this.isNeedContinueSend = !1;
                var n = (t.requestConcurrency - t.currentRequestCount) * this.cgiBatchSize,
                  r = this.queueManager.getReportableActions(n);
                if (!(r.length <= 0)) {
                  var o = this.getBaseInfo();
                  if (!o.openid && !o.unionid)
                    return h.error('上报失败，openid 和 unionid 请至少设置一个！');
                  n < this.queueManager.getReportableActionsLength() &&
                    (this.isNeedContinueSend = !0),
                    (t.currentRequestCount += Math.ceil(r.length / this.cgiBatchSize));
                  for (var i = [], a = 0; a < r.length; a += this.cgiBatchSize) {
                    var u = this.generateActionReportParams(r.slice(a, a + this.cgiBatchSize));
                    i.push(this.report(u));
                  }
                  Promise.all(i)
                    .then(function (t) {
                      var n = t.some(function (t) {
                        return t >= 0;
                      });
                      e.isNeedContinueSend && n && e.executeSend();
                    })
                    .catch(function (t) {
                      h.error(t),
                        e.reportLog({
                          message: 'executeSend catch: '.concat(t.message),
                          log_type: T.JS_RUN_ERROR,
                          err_stack: t.stack,
                        });
                    });
                }
              }
            },
          },
          {
            key: 'generateActionReportParams',
            value: function (t) {
              var e = [],
                n = [],
                r = this.getBaseInfo();
              return (
                t.forEach(function (t) {
                  n.push(t.action_id);
                  var r = Object.assign({}, t);
                  delete r.inner_status, e.push(r);
                }),
                { data: { info: r, actions: e }, actionIdList: n }
              );
            },
          },
          {
            key: 'dealSuccessData',
            value: function (t, e) {
              [51001, 51003].indexOf(null == t ? void 0 : t.code) > -1
                ? this.queueManager.updateActionsForReportFail(e)
                : this.queueManager.removeActions(e),
                0 !== (null == t ? void 0 : t.code) &&
                  (this.reportLog({
                    log_type: T.REQUEST_ERROR,
                    code: null == t ? void 0 : t.code,
                    message: 'trace_id: '
                      .concat(null == t ? void 0 : t.trace_id, '，msg: ')
                      .concat(null == t ? void 0 : t.message),
                  }),
                  h.error('上报失败：', t));
            },
          },
          {
            key: 'dealFailData',
            value: function (t, e) {
              this.queueManager.updateActionsForReportFail(e),
                this.reportLog({ log_type: T.REQUEST_ERROR, code: t.code, message: t.message }),
                h.error('上报失败：', t);
            },
          },
          {
            key: 'report',
            value: function (e) {
              var n = this,
                r = e.data,
                o = e.actionIdList;
              return (
                this.queueManager.updateActionsForReporting(o),
                h.debug &&
                  (h.info(
                    '上报行为类型: ',
                    '【'.concat(
                      r.actions
                        .map(function (t) {
                          return t.action_type;
                        })
                        .join('、'),
                      '】',
                    ),
                  ),
                  h.info('上报请求参数: ', r)),
                new Promise(function (e) {
                  var i, a, u, c, s, l, f, d;
                  $.set(null == (i = null == r ? void 0 : r.info) ? void 0 : i.user_action_set_id);
                  var p = Date.now();
                  try {
                    var _ = (function (t) {
                        var e = '',
                          n = null == t ? void 0 : t.appid,
                          r = null == t ? void 0 : t.secret_key,
                          o = null == t ? void 0 : t.sdk_version,
                          i = null == t ? void 0 : t.timestamp;
                        if (!(n && r && o && i && 32 === r.length)) return e;
                        for (var a = ve(o + n + i), u = 0; u < 32; u++)
                          e += u % 2 == 0 ? r[u] : a[u];
                        return e;
                      })({
                        appid: null == (a = null == r ? void 0 : r.info) ? void 0 : a.appid,
                        secret_key:
                          null == (u = null == r ? void 0 : r.info) ? void 0 : u.secret_key,
                        sdk_version:
                          null == (c = null == r ? void 0 : r.info) ? void 0 : c.sdk_version,
                        timestamp: p,
                      }),
                      g = (function (t) {
                        return arguments.length > 1 && void 0 !== arguments[1] && arguments[1]
                          ? (function (t) {
                              return t.replace(/=/g, '').replace(/[+\/]/g, function (t) {
                                return '+' == t ? '-' : '_';
                              });
                            })(ke(t))
                          : ke(t);
                      })(JSON.stringify(r));
                    (f = {
                      'Client-Time': p,
                      'Sign-Value': ve(
                        g +
                          (null == (s = null == r ? void 0 : r.info)
                            ? void 0
                            : s.user_action_set_id) +
                          (null == (l = null == r ? void 0 : r.info) ? void 0 : l.secret_key) +
                          _,
                      ),
                      'Sign-Version': E.signVersion,
                      'content-type': 'text/plain;charset=UTF-8',
                    }),
                      (d = g);
                  } catch (_) {
                    (f = { 'Client-Time': p }),
                      (d = r),
                      n.reportLog({
                        log_type: T.SIGN_ERROR,
                        message: 'sign error msg: '.concat(null == _ ? void 0 : _.message),
                        err_stack: null == _ ? void 0 : _.stack,
                      }),
                      h.error(_);
                  }
                  h.devLog('requestHeader: ', JSON.stringify(f)),
                    h.devLog('requestData: ', d),
                    wx.request({
                      url: 'https://api.datanexus.qq.com/data-nexus-cgi/miniprogram',
                      method: 'POST',
                      timeout: E.requestTimeout,
                      header: f,
                      data: d,
                      success: function (r) {
                        var i, a;
                        h.devLog(
                          '上报接口返回码:',
                          null == (i = null == r ? void 0 : r.data) ? void 0 : i.code,
                        );
                        var u =
                          (null == (a = null == r ? void 0 : r.header)
                            ? void 0
                            : a['Server-Time']) || -1;
                        if (
                          (le.revise(u),
                          (t.currentRequestCount -= 1),
                          200 === (null == r ? void 0 : r.statusCode))
                        )
                          return (
                            n.dealSuccessData(null == r ? void 0 : r.data, o),
                            void e((null == r ? void 0 : r.data).code)
                          );
                        var c = '';
                        try {
                          c =
                            'object' == v(null == r ? void 0 : r.data)
                              ? JSON.stringify(null == r ? void 0 : r.data)
                              : null == r
                              ? void 0
                              : r.data;
                        } catch (t) {
                          h.error(t);
                        }
                        var s = {
                          code:
                            'number' == typeof (null == r ? void 0 : r.statusCode)
                              ? -1 * r.statusCode
                              : -888,
                          message: 'statusCode: '
                            .concat(null == r ? void 0 : r.statusCode, ', data: ')
                            .concat(c),
                        };
                        n.dealFailData(s, o), e(s.code);
                      },
                      fail: function (r) {
                        h.devLog('上报失败:', r), (t.currentRequestCount -= 1);
                        var i = {
                          code:
                            'number' == typeof (null == r ? void 0 : r.errno) ? -1 * r.errno : -999,
                          message: null == r ? void 0 : r.errMsg,
                        };
                        n.dealFailData(i, o), e(i.code);
                      },
                    });
                })
              );
            },
          },
          {
            key: 'startInspectTimer',
            value: function () {
              var t = this;
              clearTimeout(this.inspectTimer),
                (this.inspectTimer = setTimeout(function () {
                  t.executeSend(), t.startInspectTimer();
                }, 1e3 * this.inspectDelay));
            },
          },
        ],
        [
          {
            key: 'setRequestConcurrency',
            value: function (e) {
              'number' == typeof e
                ? e < 1
                  ? h.error('网络请求最大并发量不能小于1')
                  : e > 10
                  ? h.error('网络请求最大并发量不能大于10')
                  : (t.requestConcurrency = e)
                : h.error('网络请求最大并发量需设置为数字');
            },
          },
        ],
      ),
      t
    );
  })(),
  Ne = Ie;
(Ne.currentRequestCount = 0),
  (Ne.requestConcurrency = E.requestConcurrency),
  Te([gt], Ne.prototype, 'batchSend', 1),
  Te([gt], Ne.prototype, 'flushSend', 1),
  Te([gt], Ne.prototype, 'executeSend', 1);
var we = (function () {
  var t = !1;
  return function () {
    if (!t)
      try {
        var e = wx.setNavigationBarTitle;
        Object.defineProperty(wx, 'setNavigationBarTitle', {
          get: function () {
            var t = this;
            return function () {
              var n = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {};
              try {
                Yt.set({ from: Qt, pageTitle: null == n ? void 0 : n.title });
              } catch (t) {}
              e.call(t, n);
            };
          },
        }),
          (t = !0);
      } catch (e) {
        h.error('proxySetNavigation failed', e);
      }
  };
})();
function xe(t, e, n) {
  var r = arguments.length > 3 && void 0 !== arguments[3] && arguments[3];
  try {
    var o = t[e];
    o || (o = function () {}),
      (t[e] = function () {
        for (var t = this, e = arguments.length, i = new Array(e), a = 0; a < e; a++)
          i[a] = arguments[a];
        var u = function () {
          return null == o ? void 0 : o.apply(t, i);
        };
        return (
          r &&
            (u = function () {
              return Promise.resolve().then(function () {
                return o.apply(t, i);
              });
            }),
          n.apply(this, [u].concat(i))
        );
      });
  } catch (o) {
    Ce('wrapHooks error：'.concat(o.message), o);
  }
}
var Ce = function (t, e) {
    h.error(t, e), z({ message: t, log_type: T.JS_RUN_ERROR, err_stack: e.stack });
  },
  De = Ct,
  Le = Dt,
  Pe = Lt,
  Ue = Pt,
  Me = Ut,
  qe = (function () {
    var t = !1;
    return function (e) {
      try {
        if (
          (ne.subscribe(De, function (t) {
            e(De, {}, t);
          }),
          ne.subscribe(Le, function (t) {
            e(Le, {}, t);
          }),
          ne.subscribe(Pe, function (t, n, r) {
            e(Pe, { target: n, trigger: r }, t);
          }),
          ne.subscribe(Ue, function (t) {
            e(Ue, {}, t);
          }),
          ne.subscribe(Me, function (t) {
            e(Le, {}, t);
          }),
          !t)
        ) {
          if (pt((window || globalThis).global || {}, 'mpvue')) {
            var n = global.Page;
            global.Page = function (t) {
              n(je(t));
            };
          } else {
            var r = Page;
            Page = function (t) {
              r(je(t));
            };
          }
          t = !0;
        }
      } catch (n) {
        h.error('pageProxy fail', n),
          z({ message: 'pageProxy fail', log_type: T.JS_RUN_ERROR, err_stack: n.stack });
      }
    };
  })(),
  je = function (t) {
    return (
      xe(
        t,
        'onShow',
        function (t) {
          ne.publish(De), t();
        },
        !1,
      ),
      xe(
        t,
        'onHide',
        function (t) {
          ne.publish(Le), t();
        },
        !1,
      ),
      'function' == typeof t.onShareAppMessage &&
        xe(
          t,
          'onShareAppMessage',
          function (t, e) {
            return (
              ne.publish(
                Pe,
                'APP_MESSAGE',
                'menu' === (null == e ? void 0 : e.from) ? 'MENU' : 'BUTTON',
              ),
              t()
            );
          },
          !1,
        ),
      'function' == typeof t.onShareTimeline &&
        xe(
          t,
          'onShareTimeline',
          function (t) {
            return ne.publish(Pe, 'TIME_LINE', 'MENU'), t();
          },
          !1,
        ),
      xe(
        t,
        'onAddToFavorites',
        function (t) {
          return ne.publish(Ue), t();
        },
        !1,
      ),
      xe(
        t,
        'onUnload',
        function (t) {
          ne.publish(Me), t();
        },
        !1,
      ),
      t
    );
  },
  Fe = Ct,
  Ve = Dt,
  We = Lt,
  Be = Pt,
  Ke = Mt,
  Qe = qt,
  Ge = jt,
  He = Ft,
  Je = Vt,
  Ye = (function () {
    var t = !1;
    return function (e) {
      try {
        if (
          (ne.subscribe(Ke, function (t) {
            e(Fe, {}, t);
          }),
          ne.subscribe(Qe, function (t) {
            e(Ve, {}, t);
          }),
          ne.subscribe(Ge, function (t, n, r) {
            e(We, { target: n, trigger: r }, t);
          }),
          ne.subscribe(He, function (t) {
            e(Be, {}, t);
          }),
          ne.subscribe(Je, function (t) {
            e(Ve, {}, t);
          }),
          !t)
        ) {
          var n = Component;
          (Component = function (t) {
            n(ze(t));
          }),
            (t = !0);
        }
      } catch (n) {
        h.error('componentProxy fail', n),
          z({ message: 'componentProxy fail', log_type: Tt, err_stack: n.stack });
      }
    };
  })(),
  ze = function (t) {
    var e, n;
    return (
      (null != t && t.methods) || (t.methods = {}),
      xe(
        t.methods,
        'onShow',
        function (t) {
          ne.publish(Ke), t();
        },
        !1,
      ),
      xe(
        t.methods,
        'onHide',
        function (t) {
          ne.publish(Qe), t();
        },
        !1,
      ),
      'function' == typeof (null == (e = t.methods) ? void 0 : e.onShareAppMessage) &&
        xe(
          t.methods,
          'onShareAppMessage',
          function (t, e) {
            return (
              ne.publish(
                Ge,
                'APP_MESSAGE',
                'menu' === (null == e ? void 0 : e.from) ? 'MENU' : 'BUTTON',
              ),
              t()
            );
          },
          !1,
        ),
      'function' == typeof (null == (n = t.methods) ? void 0 : n.onShareTimeline) &&
        xe(
          t.methods,
          'onShareTimeline',
          function (t) {
            return ne.publish(Ge, 'TIME_LINE', 'MENU'), t();
          },
          !1,
        ),
      xe(
        t.methods,
        'onAddToFavorites',
        function (t) {
          return ne.publish(He), t();
        },
        !1,
      ),
      xe(
        t.methods,
        'onUnload',
        function (t) {
          ne.publish(Je), t();
        },
        !1,
      ),
      t
    );
  },
  $e = (function () {
    function t() {
      s(this, t), (this.special_method_symbol = Symbol('special_method_symbol'));
    }
    return (
      f(t, [
        {
          key: 'onPurchase',
          value: function (t) {
            'number' != typeof t && h.warn('付费金额需要为数字'),
              t <= 0 && h.warn('付费金额需要大于0'),
              this.wrapTrack(F, { value: t });
          },
        },
        {
          key: 'onEnterForeground',
          value: function () {
            this.wrapTrack(M);
          },
        },
        {
          key: 'onEnterBackground',
          value: function () {
            this.wrapTrack(q);
          },
        },
        {
          key: 'onAppStart',
          value: function () {
            this.wrapTrack(U);
          },
        },
        {
          key: 'onAppQuit',
          value: function () {
            this.wrapTrack(V);
          },
        },
        {
          key: 'onAddToWishlist',
          value: function () {
            this.wrapTrack(j);
          },
        },
        {
          key: 'wrapTrack',
          value: function (t, e) {
            this.track(t, Object.assign(e || {}, n({}, this.special_method_symbol, 1)));
          },
        },
      ]),
      t
    );
  })(),
  Xe = (function (t) {
    r(n, $e);
    var e = i(n);
    function n() {
      return s(this, n), e.apply(this, arguments);
    }
    return (
      f(n, [
        {
          key: 'onPageShow',
          value: function () {
            this.wrapTrack(Ct);
          },
        },
        {
          key: 'onPageHide',
          value: function () {
            this.wrapTrack(Dt);
          },
        },
      ]),
      n
    );
  })(),
  Ze = '（如果确认无误，请忽略该提示）',
  tn = (function () {
    var t = [];
    return {
      requestActionList: function () {
        try {
          X({ conf_name: 'data_nexus_common', conf_key: 'action_types' }).then(function (e) {
            rt(e) && (t = e);
          });
        } catch (t) {
          h.error(t);
        }
      },
      getActionList: function () {
        return t;
      },
    };
  })();
function en(e, n) {
  try {
    e.is_sdk_auto_track ||
      ((function (e) {
        try {
          var n = tn.getActionList();
          if (!n.includes(e)) {
            var r,
              o = t(n);
            try {
              for (o.s(); !(r = o.n()).done; ) {
                var i = r.value;
                if (nn(i, e) <= parseInt((0.3 * i.length).toString())) {
                  h.warn(
                    '通过SDK上报的'
                      .concat(e, '行为名称可能有误，请检查该行为类型是否为腾讯广告提供的标准行为！')
                      .concat(Ze),
                  );
                  break;
                }
              }
            } catch (t) {
              o.e(t);
            } finally {
              o.f();
            }
          }
        } catch (n) {
          h.error(n);
        }
      })(e.action_type),
      'minigame' === n
        ? (function (t, e) {
            var n, r, o;
            try {
              ['PURCHASE', 'ADD_TO_CART'].includes(t.action_type) &&
                t.action_param &&
                pt(t.action_param, 'value') &&
                ('number' != typeof (null == (n = t.action_param) ? void 0 : n.value)
                  ? h.warn('通过SDK上报的'.concat(t.action_type, '行为携带的金额参数需要为数字！'))
                  : (null == (r = t.action_param) ? void 0 : r.value) <= 0
                  ? h.warn('通过SDK上报的'.concat(t.action_type, '行为携带的金额参数需要大于0！'))
                  : 'minigame' === e &&
                    (null == (o = t.action_param) ? void 0 : o.value) < 100 &&
                    h.warn(
                      '通过SDK上报的'
                        .concat(
                          t.action_type,
                          '行为携带的金额参数可能有误，金额的单位为‘分’，请检查金额是否正确！',
                        )
                        .concat(Ze),
                    ));
            } catch (t) {
              h.error(t);
            }
          })(e, n)
        : 'miniprogram' === n &&
          (function (t) {
            try {
              var e = null == t ? void 0 : t.action_type,
                n = (null == t ? void 0 : t.action_param) || {};
              'PURCHASE' === e &&
                pt(n, 'value') &&
                ('number' != typeof (null == n ? void 0 : n.value)
                  ? h.warn('通过SDK上报的'.concat(e, '行为携带的金额参数需要为数字！'))
                  : (null == n ? void 0 : n.value) <= 0 &&
                    h.warn('通过SDK上报的'.concat(e, '行为携带的金额参数需要大于0！')));
            } catch (e) {
              h.error(e);
            }
          })(e));
  } catch (t) {
    h.error(t);
  }
}
function nn(t, e) {
  try {
    if (0 === t.length) return e.length;
    if (0 === e.length) return t.length;
    for (var n = [], r = 0; r <= e.length; r++) n[r] = [r];
    for (var o = 0; o <= t.length; o++) n[0][o] = o;
    for (var i = 1; i <= e.length; i++)
      for (var a = 1; a <= t.length; a++)
        e.charAt(i - 1) === t.charAt(a - 1)
          ? (n[i][a] = n[i - 1][a - 1])
          : (n[i][a] = Math.min(n[i - 1][a - 1] + 1, n[i][a - 1] + 1, n[i - 1][a] + 1));
    return n[e.length][t.length];
  } catch (n) {
    h.error(n);
  }
}
function rn(t) {
  try {
    t &&
      !/^[a-zA-Z0-9_\-]+$/.test(t) &&
      h.warn('通过SDK上报的openid：'.concat(t, '可能有误，请检查openid是否正确！').concat(Ze));
  } catch (t) {
    h.error(t);
  }
}
var on = Object.defineProperty,
  an = Object.getOwnPropertyDescriptor,
  un = function (t, e, n, r) {
    for (var o, i = r > 1 ? void 0 : r ? an(e, n) : e, a = t.length - 1; a >= 0; a--)
      (o = t[a]) && (i = (r ? o(e, n, i) : o(i)) || i);
    return r && i && on(e, n, i), i;
  },
  cn = Symbol('initializedInstance'),
  sn = Symbol('autoTrack'),
  ln = Symbol('actionTime'),
  fn = (function (t) {
    r(o, Xe);
    var e = i(o);
    function o(t) {
      var n, r, i;
      if (
        (s(this, o),
        ((n = e.call(this)).env = 'production'),
        (n.sdk_version = '1.4.0'),
        (n.sdk_name = '@dn-sdk/miniprogram'),
        (n.deviceInfo = {}),
        (n.gameInfo = {}),
        (n.session_id = ''),
        (n.log_id = 0),
        (n.inited = !1),
        'function' != typeof (null == wx ? void 0 : wx.getSystemInfo) ||
          'function' != typeof (null == wx ? void 0 : wx.createSelectorQuery))
      )
        return h.error('SDK只可以在微信小程序中使用'), a(n);
      var c = vt();
      if (o[cn].length >= c.maxSdkInstance) return h.error('初始化超过上限'), a(n);
      var l = dt(t),
        f = Y();
      if (!0 !== l) return h.error(l), a(n);
      var d = null == f ? void 0 : f.appId;
      if (d && d !== t.appid)
        return h.error('初始化失败，传入的appid与当前小程序appid不一致'), a(n);
      (n.config = t),
        (n.config.auto_track = null == (r = null == t ? void 0 : t.auto_track) || r),
        (n.config.auto_attr = null == (i = null == t ? void 0 : t.auto_attr) || i),
        (n.openid = t.openid),
        (n.unionid = t.unionid);
      var v = t.user_action_set_id;
      return o[cn].includes(v)
        ? (h.error('请勿重复初始化SDK'), a(n))
        : ((n.reportLog = n.reportLog.bind(u(n))),
          (n.getTrackBaseInfo = n.getTrackBaseInfo.bind(u(n))),
          (n.deviceInfo = G()),
          (n.gameInfo = (function () {
            var t = Jt(),
              e = Zt();
            return Object.assign({ framework: e }, t);
          })()),
          (n.session_id = at()),
          $.init(),
          (n.queueManage = new Et({
            userActionSetId: v,
            maxLength: c.maxQueueLength,
            ogEvents: Bt,
          })),
          (n.actionReporter = new Ne({
            getBaseInfo: n.getTrackBaseInfo,
            reportLog: n.reportLog,
            queueManager: n.queueManage,
          })),
          (n.inited = !0),
          o[cn].push(v),
          n.subscribeEvent(),
          n.useAutoTrack(),
          re(),
          'release' === (null == f ? void 0 : f.envVersion)
            ? (h.info('初始化成功'), a(n))
            : ((function (t) {
                var e = t.conf_name,
                  n = t.conf_key,
                  r = t.sdk_version,
                  o = t.default_download_url,
                  i = t.fail_handler;
                X({ conf_name: e, conf_key: n })
                  .then(function (t) {
                    if (nt(t)) {
                      var e = null == t ? void 0 : t.blackVersions,
                        n = null == t ? void 0 : t.minVersion,
                        a = null == t ? void 0 : t.bestVersion,
                        u = null == t ? void 0 : t.downloadUrl,
                        c = o;
                      return (
                        u && /^https/.test(u) && (c = u),
                        rt(e) && (null == e ? void 0 : e.indexOf(r)) > -1
                          ? (null == i || i(),
                            void h.error(
                              '初始化失败！当前SDK版本存在兼容问题，请尽快升级至最新版！下载地址：'.concat(
                                c,
                              ),
                            ))
                          : n && ft(r, n) < 0
                          ? (null == i || i(),
                            void h.error(
                              '初始化失败！当前SDK版本过低，请尽快升级至最新版！下载地址：'.concat(
                                c,
                              ),
                            ))
                          : (a &&
                              ft(r, a) < 0 &&
                              h.warn(
                                '新版本SDK已上线，强烈建议您升级至最新版，尽早享受新特性！下载地址：'.concat(
                                  c,
                                ),
                              ),
                            void h.info('初始化成功'))
                      );
                    }
                    h.info('初始化成功');
                  })
                  .catch(function () {
                    h.info('初始化成功');
                  });
              })({
                conf_name: 'mini_program_sdk_common',
                conf_key: 'version',
                sdk_version: n.sdk_version,
                default_download_url:
                  'https://sr-home-1257214331.cos.ap-guangzhou.myqcloud.com/sdk/dn-sdk-miniprogram/dn-sdk-miniprogram.zip',
                fail_handler: function () {
                  n.inited = !1;
                },
              }),
              tn.requestActionList(),
              rn(t.openid),
              a(n)));
    }
    return (
      f(
        o,
        [
          {
            key: 'track',
            value: function (t, e) {
              var n,
                r,
                o,
                i = se.validateActionType(t),
                a = se.validateActionParam(e);
              if (i && a) {
                !this.openid && !this.unionid && h.warn('缺少 openid 或 unionid');
                var u = (function (t) {
                  return vt()[t];
                })('actionParamMaxLength');
                if (JSON.stringify(e || {}).length > u)
                  return void h.error(
                    '监测到超过'.concat(u, '的上报日志：').concat(t, ' ').concat(e),
                  );
                var c = !(null == e || !e[sn]),
                  s = this.createAction(t, e || {}, c);
                'release' !== (null == (n = Y()) ? void 0 : n.envVersion) && en(s, 'miniprogram'),
                  null == (r = this.queueManage) || r.addAction(s),
                  null == (o = this.actionReporter) || o.batchSend();
              }
            },
          },
          {
            key: 'flush',
            value: function () {
              var t;
              null == (t = this.actionReporter) || t.flushSend();
            },
          },
          {
            key: 'setOpenId',
            value: function (t) {
              var e;
              t && 'string' == typeof t
                ? ((this.openid = t),
                  'release' !== (null == (e = Y()) ? void 0 : e.envVersion) && rn(t))
                : h.error('openid 格式错误');
            },
          },
          {
            key: 'setUnionId',
            value: function (t) {
              t && 'string' == typeof t ? (this.unionid = t) : h.error('unionid 格式错误');
            },
          },
          {
            key: 'subscribeEvent',
            value: function () {
              var t = this;
              ne.subscribe('FLUSH_ALL', function () {
                var e;
                t.flush(), null == (e = t.queueManage) || e.reportLostNum(), $.report();
              });
            },
          },
          {
            key: 'subscribeAppEvent',
            value: function () {
              var t = this;
              ne.subscribe(It, function (e) {
                var n;
                t.autoTrack(It, {}, e), null == (n = t.actionReporter) || n.flushSend();
              }),
                ne.subscribe(Nt, function (e) {
                  t.autoTrack(Nt, {}, e);
                }),
                ne.subscribe(wt, function (e) {
                  t.autoTrack(wt, {}, e);
                }),
                ne.subscribe(xt, function (e) {
                  t.autoTrack(xt, {}, e);
                });
            },
          },
          {
            key: 'getTrackBaseInfo',
            value: function () {
              var t,
                e = Y(),
                n = Object.assign(
                  {},
                  this.deviceInfo,
                  _t(this.config, [
                    'user_action_set_id',
                    'appid',
                    'openid',
                    'secret_key',
                    'user_unique_id',
                    'unionid',
                  ]),
                  {
                    local_id: H(),
                    sdk_name: this.sdk_name,
                    sdk_version: this.sdk_version,
                    openid: this.openid,
                    unionid: this.unionid,
                    framework: this.gameInfo.framework,
                    inner_param: { app_env_version: e.envVersion, app_version: e.version },
                  },
                ),
                r = n;
              return (
                (null != (t = this.config) && t.auto_attr) ||
                  (r = _t(n, [
                    'local_id',
                    'sdk_name',
                    'sdk_version',
                    'openid',
                    'unionid',
                    'user_action_set_id',
                    'appid',
                    'secret_key',
                    'user_unique_id',
                    'inner_param',
                  ])),
                r
              );
            },
          },
          {
            key: 'createAction',
            value: function (t, e) {
              var n,
                r,
                o = arguments.length > 2 && void 0 !== arguments[2] && arguments[2],
                i = Yt.get();
              (null != (n = Wt) && n.includes(t)) || (i = Yt.set({ from: Gt })),
                null != e && e[sn] && delete e[sn];
              var a = Date.now();
              null != e && e[ln] && ((a = null == e ? void 0 : e[ln]), delete e[ln]);
              var u = {
                action_id: at(),
                action_param: e,
                action_time: a,
                action_type: t,
                is_retry: !1,
                is_sdk_auto_track: o,
                retry_count: 0,
                revised_action_time: le.getRevisedcurrentTimeMillis(),
                log_id: ++this.log_id,
                session_id: this.session_id,
                pkg_channel_id: this.gameInfo.pkg_channel_id,
                source_scene: this.gameInfo.source_scene,
                network_type: J(),
                page_url: (null == i ? void 0 : i.page_url) || '',
                page_title: (null == i ? void 0 : i.page_title) || '',
                ad_trace_id: this.gameInfo.ad_trace_id,
              };
              null != e &&
                e[this.special_method_symbol] &&
                ((u.inner_param = { is_special_method: !0 }), delete e[this.special_method_symbol]);
              var c = u;
              return (
                (null != (r = this.config) && r.auto_attr) ||
                  (c = _t(u, [
                    'action_id',
                    'action_param',
                    'action_time',
                    'action_type',
                    'is_sdk_auto_track',
                    'log_id',
                    'session_id',
                    'is_retry',
                    'retry_count',
                    'revised_action_time',
                  ])),
                c
              );
            },
          },
          {
            key: 'reportLog',
            value: function (t) {
              var e,
                n,
                r = {
                  user_action_set_id: null == (e = this.config) ? void 0 : e.user_action_set_id,
                  appid: null == (n = this.config) ? void 0 : n.appid,
                  session_id: this.session_id,
                };
              z(Object.assign(r, t));
            },
          },
          {
            key: 'autoTrack',
            value: function (t, e, r) {
              var o = Object.assign(e || {}, n(n({}, sn, !0), ln, r));
              this.track(t, o);
            },
          },
          {
            key: 'useAutoTrack',
            value: function () {
              var t;
              if (null != (t = this.config) && t.auto_track) {
                this.subscribeAppEvent(), oe();
                var e = !1,
                  n = G(),
                  r = K.getSync(b);
                if ((null == r ? void 0 : r.ap) === W) (e = !0), h.info('cache ap true');
                else if ((null == r ? void 0 : r.ap) === B) (e = !1), h.info('cache ap false');
                else if (null != n && n.os && n.os_version) {
                  var o = n.os + n.os_version;
                  (e = !!Ht[o]),
                    'IOS' === n.os &&
                      (function (t, e, n) {
                        if (t && 'string' == typeof t) {
                          var r = +t.split('.')[0];
                          if (r >= e && r <= n) return !0;
                        }
                        return !1;
                      })(n.os_version, 13, 17) &&
                      (e = !0);
                }
                'devtools' === n.wx_platform && (e = !0),
                  e && (we(), Ye(this.autoTrack.bind(this)), qe(this.autoTrack.bind(this))),
                  this.getAutoProxyRemoteConfig();
              }
            },
          },
          {
            key: 'getAutoProxyRemoteConfig',
            value: function () {
              var t,
                e,
                n = G();
              n.os &&
                n.os_version &&
                null != (t = this.config) &&
                t.user_action_set_id &&
                (function (t) {
                  return new Promise(function (e) {
                    wx.request({
                      method: 'POST',
                      url: 'https://api.datanexus.qq.com/data-nexus-config/v1/sdk/minigame/get',
                      data: t,
                      timeout: E.requestTimeout,
                      success: function (t) {
                        Z(t, e, 'minigame/get');
                      },
                      fail: function (t) {
                        tt(t, 'minigame/get');
                      },
                    });
                  });
                })({
                  conf_name: 'MP',
                  conf_param: {
                    user_action_set_id: null == (e = this.config) ? void 0 : e.user_action_set_id,
                    sdk_version: this.sdk_version,
                    os_type: (null == n ? void 0 : n.os) || '',
                    os_version: ht(n.os_version),
                    device_brand: (null == n ? void 0 : n.device_brand) || '',
                    weixin_lib_version: (null == n ? void 0 : n.wx_lib_version) || '',
                    weixin_version: (null == n ? void 0 : n.wx_version) || '',
                  },
                }).then(function (t) {
                  nt(t) && (h.info('remote ap data: ', t), K.setSync(b, t));
                });
            },
          },
        ],
        [
          {
            key: 'setRequestConcurrency',
            value: function (t) {
              Ne.setRequestConcurrency(t);
            },
          },
          {
            key: 'setDebug',
            value: function (t) {
              h.debug = t;
            },
          },
        ],
      ),
      o
    );
  })(),
  dn = fn;
(dn[cn] = []),
  un([gt, yt], dn.prototype, 'track', 1),
  un([gt, yt], dn.prototype, 'flush', 1),
  un([gt], dn.prototype, 'setOpenId', 1),
  un([gt], dn.prototype, 'setUnionId', 1),
  un([gt], dn.prototype, 'subscribeEvent', 1),
  un([gt], dn.prototype, 'subscribeAppEvent', 1),
  un([gt], dn.prototype, 'getTrackBaseInfo', 1),
  un([yt], dn.prototype, 'autoTrack', 1),
  un([gt], dn.prototype, 'useAutoTrack', 1);
export { dn as SDK };
