import { adNavigator, loadAdminAd } from '@/components/_pages/ad-extension/sdk';
import { useCheckIsCollected } from '@base/hooks/collect';
import { useUpdate } from '@base/hooks/page';
import { Image, Swiper, SwiperItem, View } from '@tarojs/components';
import Taro, { useEffect, useRef, useState } from '@tarojs/taro';
import classNames from 'classnames';
import isFunction from 'lodash/isFunction';
import { AtCurtain } from 'taro-ui';

const adAutoNavigatorPositions =
  process.env.MODE_ENV === 'wkd' && process.env.PLATFORM_ENV === 'alipay' ? ['17'] : [];

/**
 *
 * @description position 广告位；checkCollect 是否检查收藏
 * @param {{
 *   position: string;
 *   checkCollect?: boolean;
 *   allowFull?: boolean;
 *   className?: string;
 *   onLoad?:(has:boolean,ads:Record<string,any>[]) => void;
 *   onClose?:() => void;
 *   multiple?: boolean;
 *   swiperProps?: import('@tarojs/components/types/Swiper').SwiperProps
 *   loadAd?: boolean; // 是否拉取广告
 * }} props
 * @returns
 */
const KbAdCurtain = (props) => {
  const ref = useRef({});
  const {
    floatRef,
    position,
    checkCollect,
    allowFull,
    className,
    onLoad,
    onClose,
    multiple,
    swiperProps,
    loadAd = true,
  } = props;
  const [isOpened, setIsOpened] = useState(false);
  const [ads, setAds] = useState([]);

  // 切换float广告位展示
  const switchFloatOpenChange = (o) => {
    const { onOpenChange } = (floatRef && floatRef.current) || {};
    if (isFunction(onOpenChange)) {
      onOpenChange(o);
    }
  };

  const handleClose = () => {
    setIsOpened(false);
    if (isFunction(onClose)) {
      onClose();
    }
    switchFloatOpenChange(true);
  };

  const handleClick = (item) => {
    adNavigator(item);
    handleClose();
  };

  const triggerOnLoad = (has = false, list = []) => {
    if (isFunction(onLoad)) {
      onLoad(has, list);
    }
  };

  // 触发拉取广告
  const triggerGetAd = () => {
    if (!position) {
      triggerOnLoad();
      return;
    }
    loadAdminAd(position).then((list) => {
      // 有官方广告
      const hasOfficeAd = list.hasOfficeAd;
      const has = list.length > 0;
      setAds(multiple ? list : list.slice(0, 1));
      // 非多广告位，没有配置图片，不打开弹窗
      const ad0 = list[0] || {};
      setIsOpened(has && (multiple || ad0.imgUrl));
      triggerOnLoad(has, list);
      switchFloatOpenChange(!(hasOfficeAd || has));

      // 如果没有图片，且为非多广告位，则直接触发跳转
      if (has && !multiple && !ad0.imgUrl && adAutoNavigatorPositions.includes(position)) {
        adNavigator(ad0);
      }
    });
  };

  const stop = () => {
    clearTimeout(ref.current.delayTime);
  };
  const { run } = useCheckIsCollected(({ isCollected, status }) => {
    if (isCollected && status !== 'init') {
      // 初始获取已收藏，不做广告拉取
      ref.current.delayTime = setTimeout(() => {
        triggerGetAd();
      }, 3000);
    }
  });

  useUpdate(
    (loginData) => {
      if (loginData.logined && loadAd) {
        if (checkCollect) {
          run();
        } else {
          triggerGetAd();
        }
      }
    },
    [checkCollect, loadAd],
  );

  useEffect(() => {
    return () => {
      stop();
    };
  }, []);

  const rootCls = classNames(className, {
    'kb-curtain-full': !!allowFull,
  });

  const { autoplay = true, indicatorDots = ads.length > 1, ...restSwiperProps } = swiperProps || {};

  return (
    <AtCurtain isOpened={isOpened} onClose={handleClose} className={rootCls}>
      {isOpened && (
        <Swiper
          {...restSwiperProps}
          autoplay={autoplay}
          indicatorDots={indicatorDots}
          className='kb-curtain__swiper'
        >
          {ads.map((item) => {
            return (
              <SwiperItem key={item.id} className='kb-curtain__swiper--item'>
                <View className='kb-curtain' onClick={handleClick.bind(null, item)}>
                  <Image mode='widthFix' className='kb-curtain__image' src={item.imgUrl} />
                </View>
              </SwiperItem>
            );
          })}
        </Swiper>
      )}
    </AtCurtain>
  );
};

KbAdCurtain.config = {
  usingComponents:
    process.env.PLATFORM_ENV === 'alipay'
      ? {
          'ad-xlight': '../adXlight',
        }
      : {},
};

export default KbAdCurtain;
