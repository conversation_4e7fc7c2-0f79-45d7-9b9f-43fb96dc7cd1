/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

export const product_code_map = {
  jd: [
    { label: '特惠送', value: 'p1' },
    { label: '特快送', value: 'p2' },
  ],
  sf: [
    { label: '标快', value: 'offer' },
    { label: '特快', value: 'express' },
  ],
  sfky: [{ label: '标准达', value: 'SE0141' }],
  sxjd: [
    { label: '网点自提', value: '1' },
    { label: '送货上门', value: '2' },
  ],
  cngg: [
    { label: '当日取', value: 'today' },
    { label: '2小时', value: 'two_hour' },
  ],
};

export const getProductCodeFromLabel = (brand, label) => {
  if (brand && label) {
    const arr = product_code_map[brand] || [];
    const item = arr.find((i) => i.label === label);
    return item && item.value;
  }
};
