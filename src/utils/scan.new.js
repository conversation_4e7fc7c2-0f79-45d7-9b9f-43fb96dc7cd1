import { getPage, scanParse } from '~base/utils/utils';

export function scanActionNew(page = getPage()) {
  return new Promise((resolve) => {
    const {
      path = '',
      params: { q, scene, qrCode },
    } = page.$router;
    const qrCodeUrl = q || qrCode || scene;
    const { url, query, originUrl } = scanParse(qrCodeUrl);

    const regex = /^\/?(?:pages-\d+\/)?pages\/(.+?)(?:\/index)?$/;
    const pageMath = path.match(regex);
    const pathKey = (pageMath && pageMath[1]) || path;

    switch (pathKey) {
      default:
        resolve({ url, query, originUrl });
        break;
    }
  });
}
