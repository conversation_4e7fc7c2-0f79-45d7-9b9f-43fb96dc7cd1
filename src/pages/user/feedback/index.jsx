/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { Component, Fragment } from '@tarojs/taro';
import KbPage from '@base/components/page';
import { View, ScrollView, Text, Image } from '@tarojs/components';
import { AtButton, AtTextarea, AtInput, AtIcon } from 'taro-ui';
import KbImagePicker from '@base/components/image-picker';
import request from '@base/utils/request';
import rules from '@base/utils/rules';
import { getAdConfig } from '~/components/_pages/ad-extension/_utils';
import classNames from 'classnames';
import './index.scss';

export default class Index extends Component {
  config = {
    navigationBarTitleText: '意见反馈',
  };

  constructor() {
    this.state = {
      type: 1,
      content: '',
      phone: '',
      images: [],
      waybill_no: '',
      qrCode: '',
    };
    this.imagePickerApi = {
      url: '/v1/WeApp/uploadAttachments',
      data: {
        type: 'merchant_return',
      },
      formatResponse: ({ data, ...rest }) => {
        let list = [];
        if (Array.isArray(data)) {
          list = data.map(({ data: item, ...rest }) => ({
            ...rest,
            data: { img: item.file_path },
          }));
        }
        return {
          ...rest,
          data: list,
        };
      },
    };
    this.typeList = [
      { label: '功能异常', value: 1 },
      { label: '展示异常', value: 3 },
      { label: '新功能建议', value: 2 },
      { label: '客服咨询', value: 4 },
    ];
  }

  handleUpdate = () => {};

  handleImagePickerChange = (images) => {
    this.setState({
      images,
    });
  };

  handleSubmit = () => {
    const { content, phone, images, type, waybill_no } = this.state;
    if (content.length < 8) {
      return Taro.kbToast({ text: '描述内容过于少，请完善后提交！' });
    }
    if (content.length > 300) {
      return Taro.kbToast({ text: '描述内容过多，请完善后提交！' });
    }
    if (!phone) {
      return Taro.kbToast({ text: '请输入手机号后，点击提交！' });
    }
    if (phone && phone.length == 11 && rules.check('phone', phone).code != 0) {
      return Taro.kbToast({ text: '请输入正确十一位手机号码后，点击提交！' });
    }
    if (!waybill_no) {
      return Taro.kbToast({ text: '请填写运单号后再次提交！' });
    }
    request({
      url: '/g_wkd/v2/FeedBack/add',
      data: {
        content,
        phone,
        images,
        type,
        waybill_no,
      },
      onThen: (res) => {
        Taro.kbToast({ text: res.msg || '--' });
        if (res.code == 0) {
          this.setState({
            type: 1,
            content: '',
            phone: null,
            images: [],
          });
        }
      },
    });
  };

  getQrCode = () => {
    getAdConfig({ position: '15' }).then((res) => {
      const qrCode = res[0].imgUrl;
      this.setState({
        qrCode,
      });
    });
  };

  handleChooseOrder = () => {
    Taro.navigator({
      url: 'order/chooseOrder',
    });
  };

  handleFeedbackRecord = () => {
    Taro.navigator({
      url: 'user/feedback/record',
    });
  };

  componentDidMount() {
    this.getQrCode();
  }

  onPostMessage = (key, data) => {
    if (key == 'chooseOrder') {
      this.setState({
        waybill_no: data.order_id,
      });
    }
  };

  render() {
    const { type, content, phone, images, waybill_no, qrCode, ...rest } = this.state;

    return (
      <KbPage
        {...rest}
        onUpdate={this.handleUpdate}
        renderFooter={
          <Fragment>
            {type == 4 ? null : (
              <View className='kb-feedback-bottom'>
                <AtButton
                  disabled={!type || !content || !phone || !waybill_no}
                  type='primary'
                  circle
                  onClick={this.handleSubmit}
                >
                  提交
                </AtButton>
              </View>
            )}
          </Fragment>
        }
      >
        <ScrollView scrollY className='kb-feedback-scrollWrap'>
          <View className='kb-card'>
            <View className='kb-card__top'>
              <View className='kb-required' />
              <View>请选择反馈问题的类型</View>
            </View>
            <View className='kb-card__bottom kb-bars'>
              {this.typeList.map((item) => (
                <AtButton
                  key={item.value}
                  type={type == item.value ? 'primary' : ''}
                  circle
                  size='small'
                  onClick={() => this.setState({ type: item.value })}
                >
                  {item.label}
                </AtButton>
              ))}
            </View>
          </View>
          {type == 4 ? (
            <View className='kb-qrCode kb-card'>
              <View className='kb-qrCode__wrap at-row at-row__direction--column'>
                <View className='kb-qrCode__title'>
                  <View>添加企业微信群</View>
                  <View>进行问题反馈咨询</View>
                </View>
                <View className='kb-qrCode__content'>
                  <Image className='kb-qrCode__img' src={qrCode} showMenuByLongpress />
                  <View className='kb-size__base kb-qrCode__text'>微信扫一扫识别二维码</View>
                </View>
              </View>
            </View>
          ) : (
            <Fragment>
              <View className='kb-card'>
                <View className='kb-card__top'>
                  <View className='kb-required' />
                  <View>请填写您的问题和意见</View>
                </View>
                <View className='kb-card__bottom'>
                  <AtTextarea
                    className='kb-textarea'
                    value={content}
                    placeholder='请填写8个字以上的描述，以便我们更快的定位问题'
                    maxLength={300}
                    count={false}
                    onChange={(v) => this.setState({ content: v })}
                  />
                </View>
              </View>
              <View className='kb-card'>
                <View className='kb-card__top'>
                  <View className='kb-required' />
                  <View>
                    关联图片<Text className='kb-text__sm'>（最多上传三张）</Text>
                  </View>
                </View>
                <View className='kb-card__bottom'>
                  <KbImagePicker
                    count={3}
                    onChange={this.handleImagePickerChange}
                    api={this.imagePickerApi}
                    files={images}
                    path='https://upload.kuaidihelp.com/merchantreturn/'
                    custom
                  >
                    <View className='picker-bar__default' hoverClass='kb-hover'>
                      <AtIcon className='kb-rotate' prefixClass='kb-icon' value='wrong' />
                      <View className='picker-bar__default--text'>添加图片</View>
                    </View>
                  </KbImagePicker>
                </View>
              </View>
              <View
                className='kb-card'
                hoverClass='kb-hover-opacity'
                onClick={this.handleChooseOrder}
              >
                <View className='kb-card__top'>
                  <AtIcon prefixClass='kb-icon' value='send' size={16} />
                  <View
                    className={classNames('kb-flex-1 kb-spacing-md-lr kb-spacing-xs-tb', {
                      'kb-color__grey-placeholder': !waybill_no,
                    })}
                  >
                    {waybill_no ? waybill_no : '选择关联近30天运单号（必填）'}
                  </View>
                  <AtIcon prefixClass='kb-icon' value='arrow' size={14} />
                </View>
              </View>
              <View className='kb-card'>
                <View className='kb-card__top'>
                  <AtIcon prefixClass='kb-icon' value='mobile' size={14} />
                  <AtInput
                    value={phone}
                    placeholder='请留下您的手机号'
                    type='number'
                    maxLength={12}
                    onChange={(v) => this.setState({ phone: v })}
                  />
                </View>
              </View>
              <View
                className='kb-color__grey kb-size__base kb-spacing-md-tb kb-text__center'
                hoverClass='kb-hover-opacity'
                onClick={this.handleFeedbackRecord}
              >
                反馈记录
              </View>
            </Fragment>
          )}
        </ScrollView>
      </KbPage>
    );
  }
}
