/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { cardPayTips } from '@/components/_pages/order/card-bar/sdk';
import { updatePrintStatus } from '@/components/_pages/order/_utils';
import { checkIsShop } from '@/components/_pages/store-card/_utils';
import { checkIsFromShare } from '@/utils/share';
import request from '@base/utils/request';
import { dateCalendar, extractData, getPage } from '@base/utils/utils';
import Taro from '@tarojs/taro';
import isArray from 'lodash/isArray';
import isEmpty from 'lodash/isEmpty';
import numeral from 'numeral';
import { getIntroductionConfig } from './order.edit.dh';

// 格式化实名信息
const formatRealnameInfo = (status) => {
  let realnameInfo;
  if (status == 1) {
    realnameInfo = {
      color: 'brand',
      status: '已实名',
    };
  } else if (status == 0) {
    realnameInfo = {
      color: 'grey',
      status: '实名未采集',
    };
  }
  return realnameInfo;
};

// 创建按钮列表
export const createBars = (data, { type = 'list', orderType } = {}) => {
  const {
    order_id,
    waybill,
    canPrint = false,
    proof_image,
    bakOrder,
    activeEquityStatus,
    statusInfo,
    pay = 0,
    order_state = 'other',
    pay_status,
    isReadonly = false,
    brand,
    source,
    placeOrderConfig,
    is_verified = true,
    canOperate,
    supportGetWaybill,
    is_share_pay_order,
  } = data || {};
  if (!order_id) return null;
  const bars = [];
  const isPaid = pay > 0;
  const { supportClone = true } = placeOrderConfig || {};
  // 驿站中，微快递优寄品牌同步的订单
  const fromWkdYYj = ['yyj', 'kbyj', 'yhj', 'online'].includes(source);
  // 驿站中，微快递快递员同步的订单
  const fromWkdCourier = source == 'focus';
  const needPay = checkIsNeedPayOrder({
    orderType,
    order_state,
    source,
    isReadonly,
    is_share_pay_order,
    pay_status,
  });
  let { cancel = false, key } = statusInfo || {};
  let cloneText = supportClone ? (order_state === 'canceled' ? '重新下单' : '再来一单') : '';

  // 已下单状态，且未回填单号
  const isWaitOrder = bakOrder != '1' && key === 'wait_deal' && canOperate;

  switch (orderType) {
    case 'receive':
      // 收到
      cancel = isWaitOrder;
      break;
    case 'send':
      // 寄出
      if (cancel && !isPaid) {
        if (process.env.MODE_ENV === 'wkd') {
          const cancelStates = ['dealed', 'collected'];
          cancel =
            !isReadonly &&
            pay_status !== 'paid' &&
            brand !== 'mzgj' &&
            !(brand === 'sxjd' && cancelStates.includes(order_state));
        } else {
          cancel = isWaitOrder;
        }
      } else {
        cancel = false;
      }
      break;

    default:
      break;
  }
  // 部分按钮驿站端订单详情屏蔽了
  const isDetailShowBtn = process.env.MODE_ENV == 'wkd' || type != 'detail';
  // 临时屏蔽取消与申请退款；
  const receiveKeys = ['receive', 'wzg'];
  const isReceiveOrder = receiveKeys.includes(orderType);
  // const isHideDE
  if (cancel && orderType !== 'receive' && isDetailShowBtn) {
    // 取消
    bars.push({
      key: 'cancel',
      label: isReceiveOrder ? '删除记录' : '取消订单',
      // action: 'cancel',
    });
  }
  if ((orderType !== 'history' && isDetailShowBtn) || orderType === 'receive') {
    // 分享好友
    bars.push({
      key: 'share',
      page: 'order.detail',
      info: data,
      label: '分享好友',
      openType: 'share',
    });
  }

  if (process.env.PLATFORM_ENV !== 'swan') {
    if (needPay) {
      // 支付
      bars.push({
        key: 'pay',
        label: '支付',
      });
    }
  }

  if (isReceiveOrder && order_state !== 'canceled') {
    bars.push({
      key: 'back',
      label: '寄回发件人',
    });
  } else if (cloneText) {
    // 驿站小程序，从微快递优寄和快递员同步的订单，不显示再来一单
    if (!(process.env.MODE_ENV == 'yz' && (fromWkdYYj || fromWkdCourier))) {
      // 再来一单
      bars.push({
        key: 'clone',
        label: cloneText,
      });
    }
  }

  if (type === 'detail') {
    if (process.env.MODE_ENV === 'wkd') {
      const { refundResult, refund_status, collect_courier_mobile, is_logistic } = data;
      // @微快递：退卡，验证手机
      if (pay_status === 'refundCard') {
        return [{ key: pay_status, label: '我要退卡', action: pay_status }];
      }
      if (!is_verified) {
        return [{ key: 'checkPhone', label: '验证手机查看完整信息' }];
      }
      const showRefundBarKeys = ['refund', 'custom'];
      const showContactCourierKeys = ['courierNotFunds', 'refundFail'];
      if (orderType !== 'history' && !isReadonly) {
        if (
          refundResult &&
          showRefundBarKeys.includes(refund_status) &&
          (!waybill || !is_logistic)
        ) {
          // 申请退款，联系客服处理
          bars.push({
            key: 'refundDeal',
            label: refundResult.tips,
          });
        }
        if (collect_courier_mobile && showContactCourierKeys.includes(refund_status)) {
          bars.push({
            key: 'contactCourier',
            label: '联系快递员',
          });
        }
      }
    }
    if (proof_image) {
      // 底单
      bars.push({
        key: 'certificate',
        label: '查看底单',
      });
    }

    if (orderType != 'receive' && canOperate) {
      const isWkd = process.env.MODE_ENV === 'wkd';
      if (!isWkd) {
        // 驿站小程序，从微快递优寄和快递员同步的订单，不显示修改订单
        if (
          isWaitOrder &&
          activeEquityStatus != '1' &&
          supportClone &&
          !(fromWkdYYj || fromWkdCourier)
        ) {
          // 修改
          bars.push({
            key: 'redit',
            label: '修改订单',
          });
        }
      }
      if (canPrint) {
        // 打印，微快递企业订单需要预打印
        bars.push({
          key: isWkd && supportGetWaybill && waybill ? 'gprint' : 'print',
          label: '打印面单',
        });
      }
    }
  }
  return bars.length > 0 ? bars : null;
};

/**
 *
 * @description 创建订单状态信息
 * @param {object} data
 * @param {string} orderType = 'tcjs'|'其他'
 * @returns
 */
export const createOrderStatusInfo = (data, { orderType } = {}) => {
  let statusItem = null;
  const { can_cancel = '1', status, order_state = 'other', waybill, order_id, platform } = data;

  if (process.env.MODE_ENV === 'wkd') {
    const statusMap =
      orderType === 'tcjs'
        ? {
            wait_pay: {
              name: '待支付',
              color: 'red',
              cancel: true,
            },
            accepted: {
              name: '已受理',
              cancel: true,
            },
            wait_pick_up: {
              name: '待取货',
              color: 'brand',
            },
            distribution: {
              name: '配送中',
              color: 'brand',
            },
            finished: {
              name: '已完成',
              color: 'black',
              notice: false,
            },
            canceled: {
              name: '已取消',
              color: 'grey',
              notice: false,
            },
            error: {
              name: '异常订单',
              color: 'red',
            },
          }
        : {
            temporary: {
              name: '待提交',
              color: 'red',
              cancel: true,
            },
            wait_deal: {
              //含驿站
              name: '已下单',
              color: 'brand',
              cancel: true,
            },
            dealed: {
              name: '已受理',
              color: 'brand',
              cancel: true,
            },
            canceled: {
              //包含微掌柜扫码下单&驿站
              name: '已取消',
              color: 'grey',
              repeat: '重新下单',
            },
            rejected: {
              name: '已拒绝',
              color: 'orange',
            },
            closed: {
              name: '已关闭',
            },
            pickup: {
              //含驿站
              name: '已完成',
              repeat: '再来一单',
            },
            refunded: {
              name: '已完成',
              repeat: '重新下单',
            },
            signed: {
              name: '已签收',
            },
            undistributed: {
              name: '待分配',
              color: 'brand',
              cancel: true,
            },
            distributedfail: {
              name: '分配失败',
              cancel: true,
            },
            unPrint: {
              //微掌柜扫码下单
              name: '未打印',
              color: 'brand',
            },
            printed: {
              //微掌柜扫码下单&驿站
              name: '已打印',
              color: 'black',
            },
            prePrint: {
              //微掌柜扫码下单
              name: '预打印',
              color: 'brand',
            },
            delivering: {
              name: '派件中',
            },
            sending: {
              name: '运送中',
            },
            collected: {
              name: '已发货',
            },
            question: {
              name: '问题件',
            },
            wait_pickup: {
              name: '待取件',
            },
          };
    statusItem = statusMap[order_state];
  } else {
    const list = [
      {
        name: '已下单',
        key: 'wait_deal',
        cancel: true,
        color: 'brand',
        addWaybill: false,
      },
      { name: '已打印', key: 'finished', color: 'brand' },
      { name: '已完成', key: 'finished', color: 'orange' },
      { name: '已取消', key: 'canceled', color: 'grey' },
      { name: '未完成', key: 'canceled', color: 'grey' },
    ];
    statusItem = list.find((item) => item.name === status);
  }
  const disableShowWaybill = ['wait_deal', 'canceled']; // 禁止显示运单号的key
  const showOrderIdStatus = ['wait_deal', 'dealed'];

  let { notice = true, key = order_state, cancel, name, ...rest } = statusItem || {};
  const showWaybill =
    process.env.MODE_ENV === 'wkd' ? !!waybill : waybill && !disableShowWaybill.includes(key);
  const showOrderId =
    process.env.MODE_ENV === 'wkd' &&
    showOrderIdStatus.includes(key) &&
    platform == 'yjkd' &&
    !!order_id;
  return {
    name: status || name,
    key,
    realname: true,
    notice,
    showWaybill,
    showOrderId,
    cancel: cancel && can_cancel == '1', // can_cancel 目前仅快递 /v1/order/getlist 接口有返回此字段
    ...rest,
  };
};

function getCurrentDataItem(key, data) {
  return data[key];
}

// 创建提示语
function createTips(data, { name }) {
  let tips = '';
  const { content } = data.refundResult || {};
  if (content) {
    tips = content;
  }

  const { source, order_state, collect_courier_name, collect_courier_mobile, pay_status } = data;

  if (source === 'team') {
    // 团队
    tips = getCurrentDataItem(order_state, {
      wait_deal: `下单成功，${collect_courier_name}正在分配快递员，请耐心等待，若无快递员处理订单，超过三天将自动取消订单。`,
      dealed: `快递员${
        collect_courier_name || ''
      }已接受订单，请保持手机通畅，等待快递员与您取得联系。`,
      canceled: '已为您取消订单，请选择其他快递员或快递公司下单。',
    });
  } else if (source === 'dak') {
    tips = '驿站暂不支持上门取件，如有问题请联系驿站！';
  } else if (!tips) {
    tips = getCurrentDataItem(order_state, {
      canceled: '感谢您对微快递支持，如遇什么问题，欢迎反馈',
      pickup: '感谢您对微快递支持，欢迎再次使用',
    });
    if (!collect_courier_mobile) {
      if (order_state === 'dealed') {
        tips = `${name}已受理，已为您安排附近快递员，请保持手机畅通，等待快递员与您取得联系`;
      } else if (order_state === 'wait_deal') {
        tips = `已为您推送至${name}，请保持手机畅通，等待快递员与您取得联系`;
      }
    } else {
      if (order_state === 'wait_deal' || order_state === 'pickup') {
        tips = getCurrentDataItem(pay_status, {
          waitpay:
            order_state === 'pickup'
              ? '快递员已成功打印订单，请尽快完成支付'
              : '快递员已受理订单，可先完成支付，等待订单后续处理',
          paid: '此订单已支付完成，等待快递员后续处理',
        });
      }
    }
  }

  return tips;
}

// 创建退款相关结果
export function createRefundResult({ dak_id: dakId, refund_status }) {
  // 退款状态处理
  const relationLabel = checkIsShop({ dakId }) ? '驿站' : '快递员';
  const refund_statusMap =
    process.env.MODE_ENV === 'wkd'
      ? {
          refund: {
            tips: '申请退款',
          },
          courierRefunding: {
            tips: '快递员审核中',
            label: '退款中',
            content: '您的退款申请已提交，等待快递员处理',
          },
          courierNotFunds: {
            tips: '快递员同意退款但余额不足',
            content: '您的退款申请快递员已同意，但快递员账户余额不足，请联系快递员',
          },
          custom: {
            tips: '联系客服处理',
            label: '已拒绝',
            content: '您的退款申请已被快递员拒绝，若有异议，请联系客服处理',
          },
          customRefunding: {
            tips: '客服审核中',
            label: '退款中',
            content: '您的退款申请已提交客服，等待客服核实处理',
          },
          customRefund: {
            tips: '客服拒绝',
            label: '已拒绝',
            content: '您的退款申请经客服核实，不满足退款要求，已被拒绝',
          },
          refundFail: {
            tips: '客服同意但退款失败',
            content: '快递员账户余额不足，请联系快递员',
          },
          refunding: {
            tips: '退款成功但未到账',
            label: '已退款',
            content:
              '您已退款成功，预计1-3天内退回原账户，请以银行实际入账时间为准，感谢您使用微快递',
          },
          refundSuccess: {
            tips: '退款成功且已到账',
            label: '已退款',
          },
        }
      : {
          0: {
            content: `您的退款申请已提交，等待${relationLabel}处理。`,
            label: '退款中',
          },
          1: {
            content: `您已退款成功，预计1-3天内退回原账户，请以银行实际入账时间为准，感谢您使用${
              process.env.MODE_ENV !== 'third.post' ? '快宝驿站' : '中邮驿站'
            }。`,
            label: '退款成功',
          },
          2: {
            content: `退款失败，${relationLabel}账户余额不足，请联系${relationLabel}。`,
          },
          3: {
            content: `您的退款申请已被${relationLabel}拒绝，若有异议，请联系客服处理`,
          },
          4: {
            content: '您的退款申请已提交客服，请等待客服核实处理。',
            label: '退款中',
          },
          5: {
            content: '退款失败，请联系客服。',
          },
          6: {
            content: '您的退款申请经客服核实，不满足退款要求，已被拒绝。',
          },
        };
  const refundResult = ((data) => (status) => {
    const result = data[`${status}`];
    return result
      ? {
          label: '退款失败',
          ...result,
        }
      : null;
  })(refund_statusMap)(refund_status);
  return refundResult;
}

/**
 *
 * @description 获取请求的地址与参数
 * @param {*} params
 */
export const getApiUrlAndData = (params) => {
  const { order_id, order_random } = params;
  if (process.env.MODE_ENV === 'wkd') {
    let url = '';
    let data = null;
    let nonceKey = null;
    const { type, brand, out_order_no } = params;
    if (out_order_no) {
      // 处理交易单号转换订单号逻辑
      return {
        url: '/g_order_core/v2/Orders/getOrderIdByTradeNumber',
        data: {
          trade_number: out_order_no,
        },
      };
    }
    switch (type) {
      case 'temporary':
        // 临时单
        url = '/v1/WeApp/getByOrderReadyId';
        data = { order_id };
        nonceKey = 'order_id';
        break;
      case 'history':
        url = '/v1/order/getinfo';
        data = { order_number: order_id };
        nonceKey = 'order_number';
        break;
      case 'tcjs':
        url = '/g_wkd/v1/rush/Rush/detail';
        data = { order_id, brand };
        break;
      case 'receive':
        url = '/v1/WeApp/getSharedOrder';
        data = { order_number: order_id };
        break;
      case 'wzg': // 根据订单的source
        url = '/g_order_core/v2/mina/WsOrders/getWsOrderDetail';
        data = { order_no: order_id };
        break;
      // case "dak": // 根据订单的source
      //   url = "/g_order_core/v2/mina/Dak/getDakOrderInfo";
      //   data = { order_no: order_id };
      //   break;

      default:
        url = checkIsFromShare(params) ? '/v1/WeApp/getSharedOrder' : '/v1/order/getinfo';
        nonceKey = 'order_number';
        data = { order_number: order_id };
        break;
    }

    // 其他额外参数，暂时不确定来源；
    const extraData = extractData(params, [
      ['waybill_no', 'express_number'],
      ['source', 'orderSource'],
      'order_random',
      'platform_name',
      'template_name',
      'stat_action',
      'customer_id',
    ]);

    return {
      url,
      nonceKey,
      data: {
        ...data,
        ...extraData,
      },
    };
  } else {
    let form_data = { order_id, order_random };
    return {
      url: '/api/weixin/mini/minpost/MiniDak/orderInfo',
      data: form_data,
    };
  }
};

/**
 *
 * @description 修正source主要将 wkd -> wzg
 * @param {*} source
 * @returns
 */
function fixSource(source) {
  return source === 'wkd' ? 'wzg' : source;
}
/**
 * 创建 sourceBrand 用于logo展示
 */
function createSourceBrand(source) {
  // 指定订单来源需要显示对应的logo，驿站、微掌柜、快递团队
  const sourceLogos = ['dak', 'wzg', 'team'];
  const realSource = fixSource(source);
  if (sourceLogos.includes(realSource)) {
    return realSource;
  }
  return '';
}

/**
 *
 * @description 兼容订单详情
 * @param {*} item
 * @returns
 */
export const formatOrderDetail = (item, opts) => {
  const { userInfo: { user_id: loginUid = '' } = {} } = Taro.kbLoginData || {};
  const { orderType, type = 'detail', user_id: currentUid = loginUid } = opts;
  const keysMap = {
    tcjs: [
      ['receive_province', 'receiver_province'],
      ['receive_city', 'receiver_city'],
      ['receive_district', 'receiver_district'],
      ['receive_address', 'receiver_address'],
      ['receive_name', 'receiver_name'],
      ['receive_mobile', 'receiver_phone'],
      ['receive_house_num', 'receiver_house_num'],
      ['receive_longitude', 'receiver_lng'],
      ['receive_latitude', 'receiver_lat'],
      ['create_time', 'create_at'],
      ['order_state', 'eng_status'],
      ['pay', 'price'],
      'send_name',
      ['send_mobile', 'send_phone'],
      'send_house_num',
      ['send_longitude', 'send_lng'],
      ['send_latitude', 'send_lat'],
      'send_province',
      'send_city',
      'send_district',
      'send_address',
      'collect_object_type',
      ['collect_courier_mobile', 'rush_mobile'],
      ['collect_courier_name', 'rush_name'],
      'brand',
      'brand_name',
      'status',
      'source',
      'is_open_order',
      'append_brand',
      'append_tips',
      'tips_fee',
      'reserve_time',
      ['goods_name_id', 'cargo_type_id'],
    ],
    other: [
      ['send_name', 'send_user'],
      ['send_mobile', 'send_user_mobile'],
      ['send_tel', 'send_address_telphone'],
      ['send_province', 'send_address_province'],
      ['send_city', 'send_address_city'],
      ['send_district', 'send_address_county'],
      ['send_address', 'send_address_detail'],
      ['receive_name', 'receive_user'],
      ['receive_mobile', 'receive_user_mobile'],
      ['receive_tel', 'receive_address_telphone'],
      ['receive_province', 'receive_address_province'],
      ['receive_city', 'receive_address_city'],
      ['receive_district', 'receive_address_county'],
      ['receive_address', 'receive_address_detail'],
      ['waybill', 'express_number'],
      ['last_logistics', 'order_message'],
      ['brand', 'express_rand'],
      ['incomplete', ({ order_number }) => !order_number],
      ['source', ({ source }) => fixSource(source)],
      ['sourceBrand', ({ source }) => createSourceBrand(source)],
      ['status', ({ zhuangtai, order_state_cname = zhuangtai }) => order_state_cname],
      ['goods_weight', 'wupin_weight'],
      ['goods_remark', 'note'],
      ['pay', 'need_pay'],
      ['status', ({ zhuangtai, order_state_cname = zhuangtai }) => order_state_cname],
      ['proof_image', 'certificate_path'],
      ['collect_code', 'pickup_code'],
      ['goods_name', 'articleInfo'],
      'package_weight',
      ['volume', 'wupin_volume'],
      ['goods_remark', 'note'],
      ['pay', 'need_pay'],
      [
        'collect_object_type',
        ({ counterman_mobile, dak, team, express_rand }) => {
          if (dak && dak.inn_name) return 'dak';
          if (counterman_mobile) return 'courier';
          if (team && team.account_name) return 'team';
          if (express_rand === 'dp') return 'dp';
          return '';
        },
      ],
      ['collect_courier_mobile', 'counterman_mobile'],
      ['collect_courier_id', 'counterman_id'],
      ['collect_courier_name', 'counterman_name'],
      'dispatch_name',
      'dispatch_mobile',
      'express_shop_name',
      'create_time',
      'order_state',
      'platform',
      'dak',
      'team',
      'dak_pickup_code',
      'type',
      'equity_card_id',
      'activity_double12',
      'activity_newYear',
      'activity_springFestival',
      'activity',
      'help_status',
      'is_fresh',
      'cutPayDesc',
      'real_name_status',
      'max_order_id', //app分享支付的相关id
      'is_share_pay_order', //是否app分享支付
      'counterman_id', //是否公司注册快递员
      's_fee',
      'f_fee',
      'f_weight',
      's_weight',
      'place_volume',
      'is_member',
      'member_discount_name',
    ],
    common: [
      'use_preferential', //是否使用优惠
      'second_pay_order', //二次支付
      'packing_money',
      ['product_type', 'delivery_type'], //品牌类型
      'other_money',
      ['pro_price', 'real_warrant_price'],
      ['price', ({ freight, price }) => (price ? freight : '')],
      [
        'push_price',
        ({ freight, price, pay_packing_money }) => {
          return price
            ? pay_packing_money > 0
              ? numeral(freight - pay_packing_money * 1).format('0.00')
              : freight
            : '';
        },
      ],
      ['isPushBill', ({ price }) => !!price],
      'pay_status',
      'refund_status',
      'can_cancel',
      'order_number',
      ['wkdSupportPrint', 'wkd_support_print'],
      [('supportPrint', 'support_print')],
      ['supportGetWaybill', 'support_get_waybill'],
      ['order_id', ({ order_number, order_id = order_number }) => order_id],
      [
        'isReadonly',
        ({ user_id: itemUid = '' }) =>
          currentUid && itemUid ? `${currentUid}` !== `${itemUid}` : false,
      ],
      ['order_tips', ({ order_tips, fail_reason }) => order_tips || fail_reason],
      'new_shipper_name',
      'new_shipper_mobile',
      'new_shipper_address',
      'activity_coupon_desc',
      'coupon_disabled_reason',
      'activity_name',
      'reserve_time_desc',
      'settlement_price_details',
      'wait_pay_freight',
      'express_freight',
      'freightBase',
      'is_partner_order',
      'applet',
      'live_print',
      'big_package',
      'shipper_zipcode_del',
      'order_channel_type',
      'is_courier_order',
      'pay_packing_money',
      'is_exception_sign_order',
      'is_logistic',
      'freight',
      ['arrive_pay', 'toPayAmount'],
      ['cabinet_info', ({ cabinet_info }) => (!isEmpty(cabinet_info) ? cabinet_info : '')],
      ['isPutIn', 'is_put_in'],
      ['product_type_en', 'delivery_type_en'],
    ],
  };

  const key = keysMap[orderType] ? orderType : 'other';
  const keys = keysMap[key];
  // 公共的key
  keys.push(...keysMap.common);
  if (type === 'detail') {
    // 详情数据
    const extraKeysMap = {
      tcjs: [
        ['collect_code', 'pickup_code'],
        ['goods_name', 'cargo_type'],
        ['goods_weight', 'weight'],
        ['goods_remark', 'remark'],
        ['waybill', 'delivery_no'],
        'currentTime',
        'waitPayEndTime',
        'cancel_time',
        'is_use_insurance',
        'insurance_fee',
        'cargo_price',
        'distance',
        'privacyNumber',
        'service',
        'car_name',
        'fee',
      ],
      other: [
        'is_verified',
        'commission',
        'reserve_end_time',
        'reserve_start_time',
        'shipper_zipcode_del',
        'time',
        'item',
        'shop_name',
      ],
      common: [
        ['goods_remark', 'note'],
        ['customer_id', 'courier_work_customer_id'],
        'items_freight',
        'payment',
        'freight',
        'freights',
        'type',
        'can_hurry',
        'update_time',
        'order_finish_code',
        'refund',
        'settlement_data',
        'repeat_settlement_data',
      ],
    };
    keys.push(...extraKeysMap[key], ...extraKeysMap.common);
  }
  if (orderType === 'tcjs') {
    item.source = orderType;
    item.collect_object_type = 'courier';
  }
  return extractData(item, keys);
};

function formatDateByTime(date) {
  return date ? dateCalendar(date, { timer: true }) : '';
}

/**
 * 追加详情信息
 */
export const addOrderDetailInfo = (data, { brands: brandsMap, orderType, reckonFeeInfo }) => {
  const { create_time, update_time, cancel_time, ...restData } = data;
  const { brand, source, dak, collect_courier_name, isReadonly, product_type, order_tips } =
    restData;
  const { name = '', logo_link: logo = '', tel = '' } = brandsMap[brand] || {};
  const brandInfo = {
    name,
    logo: logo.replace('http:', 'https:'),
    tel,
    product_type,
  };
  let tips = '';

  // 创建日期或更新日期，注：只读订单展示更新日期
  let otherDateInfo = null;
  if (process.env.MODE_ENV === 'wkd') {
    let { name, logo } = brandInfo;
    if (!name) {
      const { name: sourceName, logo_link: sourceLogo } = brandsMap[source] || {};
      if (source === 'dak') {
        name = (dak && dak.inn_name) || sourceName;
        logo = sourceLogo;
      } else if (source === 'team') {
        name = collect_courier_name || sourceName;
        logo = sourceLogo;
      }
      brandInfo.name = name;
      brandInfo.logo = logo;
    }
    if (!isReadonly && orderType !== 'tcjs') {
      tips = createTips(data, { name });
    }
    otherDateInfo = {
      cancel_time: formatDateByTime(cancel_time),
      update_time: formatDateByTime(isReadonly && update_time),
    };
  }

  return {
    ...restData,
    ...otherDateInfo,
    create_time: formatDateByTime(create_time),
    brandInfo,
    tips: order_tips || tips,
    reckonFeeInfo,
  };
};

/**
 *
 * @param {*} param0.data
 * @param {*} param0.orderType 订单类型，以页面参数为依据，用于区分订单类型（目前实现很不合理，订单详情中的数据应该有所体现）
 * @param {*} param0.orderDate 历史订单取消时需要此数据
 * @returns
 */
export const formatResponseOrderDetail = (
  { data: $$originalData, orderDate, orderType },
  req,
  page,
) => {
  let data = $$originalData;
  if (process.env.MODE_ENV === 'wkd') {
    // @微快递数据处理
    data = formatOrderDetail($$originalData, {
      orderType,
      user_id: req.user_id,
    });
    // 处理待补款字段
    if (data.repeat_settlement_data && !isEmpty(data.repeat_settlement_data)) {
      data.first_settlement_data = data.settlement_data;
      data.settlement_data = data.repeat_settlement_data;
    }
    // 兼容微快递权益次卡使用
    if (data.type === 'cardPay') {
      const { equity_card_id } = data;
      data.equity_card_id = equity_card_id || '1';
    }
  }
  // 兼容处理快递员取件地址
  if (data.new_shipper_address) {
    const [
      new_shipper_province = '',
      new_shipper_city = '',
      new_shipper_district = '',
      ...new_shipper_address
    ] = data.new_shipper_address.split(' ');
    data = {
      ...data,
      new_shipper_province,
      new_shipper_city,
      new_shipper_district,
      new_shipper_address: new_shipper_address.join(''),
    };
  }
  if (!data.order_id) {
    return { data: void 0 };
  }
  const equity_card_id = data.equity_card_id;
  data.activeEquityStatus = '0';
  if (equity_card_id && equity_card_id != '0') {
    data.activeEquityId = equity_card_id;
    data.activeEquityStatus = '1';
  }
  page && updatePrintStatus(data, page);
  const {
    real_name_status,
    dak_id: dakId,
    collect_object_type: type,
    collect_courier_id,
    collect_courier_name,
    collect_courier_mobile,
    dak,
    team,
    dispatch_name,
    dispatch_mobile,
    ...restData
  } = data;
  const statusInfo = createOrderStatusInfo(data, { orderType });
  const realnameInfo = formatRealnameInfo(real_name_status);
  const refundResult = createRefundResult(data);
  let relationData = null;
  if (collect_courier_name || collect_courier_mobile) {
    relationData = {
      name: collect_courier_name,
      phone: collect_courier_mobile,
    };
    collect_courier_id && (relationData[type === 'dak' ? 'dakId' : 'id'] = collect_courier_id);
  }

  //派件快递员，仅在派送中状态时存在
  let dispatchCourier = null;
  if (dispatch_name || dispatch_mobile) {
    dispatchCourier = {
      name: dispatch_name,
      phone: dispatch_mobile,
    };
  }

  if (process.env.MODE_ENV === 'wkd') {
    // 微快递处理订单详情
    switch (type) {
      case 'dak':
        const { inn_name, inn_id, concat_phone, ...restDak } = dak;
        relationData = {
          name: inn_name,
          phone: concat_phone,
          dakId: inn_id,
          ...restDak,
        };
        break;
      case 'team':
        const { account_name } = team;
        relationData = { name: account_name };
        break;
      case 'dp':
        relationData = { name: '德邦小哥', phone: '95353' };
        break;
    }
  }

  return {
    data: {
      relationData: {
        type,
        dakId: dakId && dakId != '0' ? dakId : '',
        ...relationData,
      },
      refundResult,
      collect_courier_id,
      collect_courier_name,
      collect_courier_mobile,
      dispatchCourier,
      ...restData,
      realnameInfo,
      statusInfo,
      dak,
      orderTypeAndDate: {
        type: orderType,
        date: orderDate,
      },
    },
  };
};

// 格式化数字
export const formatNumeral = (val, unit = '元', defaultValue) => {
  if ((!val || val == 0) && defaultValue) {
    val = defaultValue;
  } else {
    val = numeral(val || 0).format('0.00');
  }
  return `${val}${unit}`;
};

// 信息展示索引配置项
export const createInfoMap = ({ orderType } = {}) => {
  return {
    package: [
      {
        key: 'goods_name',
        icon: 'more',
      },
      {
        key:
          process.env.MODE_ENV != 'wkd' || orderType === 'tcjs' ? 'goods_weight' : 'package_weight',
        icon: 'weight',
        render: (val, place_volume) => {
          return `${formatNumeral(val, '公斤', '--')}${
            place_volume ? `/${place_volume || 0}cm³` : ''
          }`;
        },
      },
      {
        key: process.env.MODE_ENV === 'wkd' ? 'pic' : 'package_images',
        icon: 'pic',
        render: (val) =>
          `${(process.env.MODE_ENV === 'wkd' ? val : val && val.length) ? '查看' : '暂无'}照片`,
        bar: true,
        hide: orderType === 'tcjs',
      },
      // {
      //   key: 'place_volume',
      //   icon: 'volume',
      //   render: (val) => `${val || 0}cm³`,
      // },
      {
        key: 'pay',
        icon: 'money',
        render: (val) => formatNumeral(val),
        bar: true,
      },
    ],
  };
};

// 回填单号
export const backfillWaybill = ({ waybill, brand, order_id, ...api }) => {
  return new Promise((resolve, reject) => {
    request({
      url: '/api/weixin/mini/minpost/order/backfill',
      form: {
        waybill: {
          reg: 'waybill_num',
        },
      },
      data: {
        order_id,
        waybill,
        brand,
      },
      toastError: true,
      ...api,
      onThen: ({ code, data, msg }) => {
        if (code == 0 && data === true) {
          resolve(data);
        } else {
          reject(new Error(msg));
        }
      },
    });
  });
};

/**
 * @description 获取包裹图片
 */
export const getPackagePics = ({ order_id, type = 'send' }) => {
  return new Promise((resolve, reject) => {
    if (type === 'tcjs') {
      reject(new Error('同城急送无需获取包裹图片'));
      return;
    }
    request({
      url: '/v1/GrabOrder/getUploads',
      data: { order_id },
      toastLoading: false,
      onThen: ({ data }) => {
        resolve(
          isArray(data) && data.length > 0
            ? data.map((item) => item.replace('http://upload.kuaidihelp.com/graborder/', ''))
            : null,
        );
      },
    });
  });
};

/**
 * @description 获取关系头像
 */
export const getRelationAvatar = ({ id, type, orderType }) => {
  return id
    ? `https://upload.kuaidihelp.com/touxiang/counterman_${id}.jpg`
    : orderType == 'tcjs'
    ? `https://upload.kuaidihelp.com/touxiang/counterman_.jpg`
    : `https://cdn-img.kuaidihelp.com/brand_logo/icon_${type}.png?v=20240412`;
};

/**
 *
 * @description 创建支付信息
 * @param {*} data
 * @param {*} param1
 * @returns
 */
export const createPayInfo = (data, { orderType, type: pageType, serviceData = {} }) => {
  let info = '';
  let money = 0;
  let color = 'red';
  let help = false;
  if (!data) return {};
  const {
    isReadonly,
    refundResult,
    refund,
    refund_status,
    pay_status,
    applet,
    type,
    pay,
    freight,
    is_share_pay_order,
  } = data;
  if (!isReadonly || is_share_pay_order) {
    // 标准订单
    if (checkIsKbyjOrder(data)) {
      switch (pay_status) {
        case 'waitpay':
          info = '待支付';
          money = applet && applet.need_pay;
          help = true;
          break;
      }
      help = false;
    } else {
      const waitPayKeys = ['waitpay', 'waitCut', 'granted'];
      if (refundResult && refund_status !== 'refund') {
        info = refundResult.label;
        money = refund && refund.refund_money;
      } else if (pay_status === 'paid' || (applet && type === 'cardPay')) {
        help = !!(applet && applet.express_fee > 0);
        if (type === 'cardPay') {
          info = cardPayTips;
        } else {
          info = '已支付';
          money = pay;
        }
      } else if (waitPayKeys.includes(pay_status)) {
        const { total = 0 } = serviceData;
        info = pay_status === 'granted' ? '代扣款' : '待支付';
        money = formatNumeral(1 * freight + total, '');
      } else if (type === 'cardPay' && pageType !== 'list') {
        info = cardPayTips;
      }
    }
  } else {
    // 只读订单
    if (orderType === 'wzg') {
      switch (pay_status) {
        case 'paid':
          info = '已支付';
          money = pay;
          break;
        case 'waitpay':
          info = '待支付';
          money = freight;
          help = true;
          break;
      }
    }
  }
  if (info === '已支付') {
    color = 'green';
  }
  if (info && pageType === 'detail' && money > 0) {
    info = `${info}￥${money}`;
  }
  if (pageType === 'list') {
    help = false;
  }
  return { info, money, color, help };
};

// 是否百世大货物流订单
export const checkIsDHOrder = (data) => {
  const { big_package } = data || {};
  return big_package == 1;
};

// （网购退货、优寄订单）月结订单
export const checkIsKbyjOrder = (data) => {
  const { source } = data || {};
  return source === 'kbyj';
};

/**
 *
 * @description 订单费用详情
 * @param {*} data
 * @param {*} param1
 * @returns
 */
export const createPayInfoDetail = (data, { orderType }) => {
  let list = [];
  const {
    payment,
    items_freight,
    type,
    applet,
    freights = 0,
    pay_status,
    freight = 0,
    source,
    pay = 0,
    pro_price = 0,
    packing_money = 0,
    f_weight = 0,
    s_weight = 0,
    price = 0,
    coupon = 0,
    activeEquityId,
    points = 0,
    settlement_price_details,
    settlement_data,
    first_settlement_data,
    brand,
    is_courier_order,
  } = data || {};
  let { other_money = 0 } = data || {};
  const isDH = checkIsDHOrder(data);
  if (process.env.MODE_ENV === 'wkd') {
    if (isDH) {
      const { act_money = 0 } = applet || {};
      const isSettlement = !!(act_money > 0); // 是否结算
      // 从列表中取运费
      const expressFreightItem = settlement_price_details.find((i) => i.type == 0) || {};
      const _settlement_price_details = settlement_price_details.filter((i) => i.type != 0);
      // 整理数据
      let service_total_fee = 0;
      const price_details_list = _settlement_price_details.map((item) => {
        service_total_fee += item.fee * 1 || 0;
        const introduction = getIntroductionConfig(item.name, brand);
        return {
          ...item,
          introduction,
          icon: item.toast || !!introduction,
          show: true,
        };
      });
      service_total_fee = numeral(service_total_fee).format('0.00');
      list = [
        {
          name: `${isSettlement ? '实际' : '预估'}运费`,
          price: `￥${expressFreightItem.fee}`,
          show: true,
        },
        {
          key: 'service',
          name: `${isSettlement ? '实际' : '预估'}增值总费用`,
          price: `￥${service_total_fee}`,
          list: price_details_list,
          border: true,
          show: service_total_fee > 0,
        },
      ];
    } else if (checkIsKbyjOrder(data) && !isDH) {
      const { act_money = 0 } = applet || {};
      const {
        price = 0,
        charging_weight,
        settlement_volume,
        settlement_price_details = [],
      } = settlement_data || {};
      const {
        price: first_price,
        charging_weight: first_charging_weight,
        settlement_volume: first_settlement_volume,
        settlement_price_details: first_settlement_price_details = [],
      } = first_settlement_data || {};
      // console.log('first_settlement_data',first_settlement_data)
      // console.log('settlement_data', settlement_data);
      // console.log('first_settlement_price_details', first_settlement_price_details);
      const price_details_list = settlement_price_details.map((item) => {
        const first_item = first_settlement_price_details.find((i) => i.name === item.name) || {};
        return {
          name: item.name,
          price: item.fee,
          first_price: first_item && first_item.fee,
          icon: item.toast,
          show: true,
        };
      });
      list = [
        [
          {
            name: '已支付',
            price: act_money,
            show: true,
          },
          {
            name: '总费用',
            price: price,
            first_price: first_price,
            show: true,
          },
          {
            name: '结算重量',
            price: charging_weight,
            first_price: first_charging_weight,
            unit: 'KG',
            show: true,
          },
          {
            name: '结算体积',
            price: settlement_volume,
            first_price: first_settlement_volume,
            unit: 'cm³',
            show: true,
          },
        ],
        price_details_list,
      ];
      return list;
    } else if (orderType === 'wzg') {
      list = [
        {
          name: '商品金额明细',
          price: `￥${payment}`,
          show: true,
        },
        {
          name: '运费',
          price: `￥${items_freight}`,
          show: items_freight > 0,
        },
      ];
    } else {
      const isBrandOrderType = ['online', 'kbyj', 'yyj', 'vhome_yhj'].includes(source);
      const { vas_money = 0, warrant_money = 0, coupon_money = 0, act_money = 0 } = applet || {};
      const isCardPay = type === 'cardPay'; //权益次卡
      const xuZhongFei = pay_status == 'waitpay' ? freight : act_money;
      const shouZhouFei = numeral(freights).value() - numeral(xuZhongFei).value();
      other_money = numeral(other_money).format('0.00');
      const expressFee =
        numeral(pay).value() -
        numeral(pro_price).value() -
        numeral(other_money).value() -
        numeral(packing_money).value();
      const courierExpressFee = is_courier_order
        ? numeral(freights).value() - numeral(packing_money).value()
        : freights;
      list = [
        {
          name: '快递费',
          price: `￥${
            isBrandOrderType
              ? numeral(expressFee).format('0.00')
              : numeral(courierExpressFee).format('0.00')
          }`,
          show: !isCardPay,
        },
        {
          name: '保价费',
          price: `￥${pro_price}`,
          show: isBrandOrderType && pro_price > 0,
        },
        {
          key: 'packing_money',
          name: '包装费用',
          price: `￥${packing_money}`,
          show: isBrandOrderType && packing_money > 0,
        },
        {
          key: 'packing_money',
          name: '包装费',
          price: `￥${packing_money}`,
          hideFeeStandards: true,
          show: is_courier_order && packing_money > 0,
        },
        {
          name: '其他费用',
          price: `￥${other_money}`,
          show: isBrandOrderType && other_money > 0,
        },
        {
          name: `${cardPayTips}`,
          price: `¥ ${numeral(shouZhouFei).format('0.00')}`,
          show: isCardPay,
        },
        {
          name: '续重费',
          price: `￥${numeral(xuZhongFei).format('0.00')}`,
          show: isCardPay,
        },
        {
          name: '一元保障',
          price: `￥${vas_money}`,
          show: vas_money > 0,
        },
        {
          name: '保价费用',
          price: `￥${warrant_money}`,
          show: warrant_money > 0,
        },
        {
          name: '抵用券',
          price: `-￥${coupon_money}`,
          show: coupon_money > 0,
        },
      ];
    }
  } else {
    let s_fee = numeral(price).value() - numeral(f_weight).value();
    list = [
      {
        name: '首重',
        price: `￥${f_weight}`,
        show: !!f_weight,
      },
      {
        name: '续重',
        price: `￥${s_fee > 0 ? numeral(s_fee).format('0.00') : 0}`,
        show: !!s_weight,
      },
      {
        name: '权益次卡',
        price: `使用权益次卡抵扣首重`,
        show: !!activeEquityId,
      },
      {
        name: '预估费用',
        price: `￥${price}`,
        show: !!price,
      },
      {
        name: '保价费用',
        price: `￥${pro_price}`,
        show: !!pro_price,
      },
      {
        name: '优惠券',
        price: `-￥${coupon}`,
        value: -coupon,
        show: !!coupon,
      },
      {
        name: '积分抵扣',
        price: `-￥${points}`,
        value: -points,
        show: !!points,
      },
    ];
  }
  return list.length > 0 ? list.filter((i) => !!i.show) : null;
};

// 获取订单相关判断条件---主要用于统一订单列表和详情判断逻辑
export const getOrderCondition = (orderType, orderData) => {
  const { order_state, status, is_open_order, brand } = orderData || {};
  if (orderType === 'tcjs') {
    let brandArr = brand ? brand.split(',') : [];
    return {
      tcjs_no_brand:
        ['accepted', 'wait_pay', 'canceled'].includes(order_state) &&
        is_open_order == 1 &&
        brandArr.length > 1
          ? {
              logo: order_state === 'accepted' ? '分配运力中' : status,
              text: '同城急送',
            }
          : false,
    };
  }
  return {};
};

// 处理德邦合伙人订单弹窗
export const _handleDpPartnerModal = (key, data) => {
  const _page = getPage();
  switch (key) {
    case 'close':
      _page.setState({
        isOpenPartnerModal: false,
      });
      break;
    case 'open':
      _page.setState({
        isOpenPartnerModal: data,
      });
      break;
  }
};

const formatFee = (v) => numeral(v * 1).format('0.00');
export const getFeeChildren = (data = {}) => {
  let { f_kg = 0, s_kg = 0, brand, s_fee = 0, f_fee = 0, package_weight = 0, price } = data;
  let f_v, f_v1, s_v, s_v1;

  // 订单详情
  if (data.order_number || data.order_id) {
    f_kg = 30;
    s_kg = package_weight - f_kg;
  }

  if (brand === 'sxjd') {
    const totalKg = f_kg + s_kg;
    if (s_fee == 0 || s_fee == '0.00') {
      f_v = `￥${formatFee(f_fee ? f_fee * totalKg : price)}`;
      f_v1 = `${totalKg}kg`;
      s_v = '￥0.00';
      s_v1 = '0kg';
    } else {
      f_v = '￥0.00';
      f_v1 = '0.00';
      s_v = `￥${formatFee(s_fee * totalKg)}`;
      s_v1 = `${totalKg}kg`;
    }
  } else {
    f_v = `￥${formatFee(f_fee)}`;
    f_v1 = `${f_kg}kg`;
    s_v = `￥${formatFee(s_fee * s_kg)}`;
    s_v1 = `${s_kg}kg`;
  }

  return [
    {
      key: 'f_kg',
      label: brand == 'sxjd' ? '最低收费' : '首重费用',
      value: f_v,
      value1: f_v1,
    },
    {
      key: 's_fee',
      label: '续重费用',
      value: s_v,
      value1: s_v1,
    },
  ];
};

export const checkIsNeedPayOrder = (data) => {
  if (!data) return false;
  const enablePayStatus = ['waitpay', 'waitCut'];
  const { orderType, order_state, source, isReadonly, is_share_pay_order, pay_status } = data;

  return orderType === 'tcjs'
    ? order_state === 'wait_pay'
    : source === 'kbyj'
    ? false
    : (!isReadonly || source === 'wzg' || is_share_pay_order) &&
      enablePayStatus.includes(pay_status);
};
