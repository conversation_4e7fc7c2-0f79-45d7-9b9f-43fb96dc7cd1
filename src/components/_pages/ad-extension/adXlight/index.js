/* eslint-disable no-undef */
const modalPlugin1 = requirePlugin('xlightPlugin');
const modalPlugin2 = requirePlugin('xlightPlugin2');

Component({
  props: {
    unitId: '',
    unitType: '1', // 1全屏 2插屏
    type: '1', // 1: xlightPlugin(支付宝数字推广平台媒体插件-2021001192652032) 、2或3: xlightPlugin2(码上有钱插件-2021003196619202，3是勾选模式);
    onAdInstance: () => {},
    onAdLoad: () => {},
    onAdSuccess: () => {},
    onAdError: () => {},
    onAdClose: () => {},
    rtaExtMap: {},
  },
  data: {
    interstitialAd: {},
  },
  onInit() {
    const { unitId, unitType, type } = this.props;
    const modalPlugin = type === '1' ? modalPlugin1 : modalPlugin2;
    const ad = new modalPlugin.CreateInterstitialAd();
    this.props.onAdInstance && this.props.onAdInstance(ad);
    this.setData({
      interstitialAd: ad,
    });

    this.data.interstitialAd.onLoad(() => {
      this.props.onAdLoad(this);
      if (unitType == 1 && unitId) {
        this.showAd();
      }
    });

    // 广告加载失败
    this.data.interstitialAd.onError((err) => {
      console.log('onError', err);
      this.props.onAdError(err, this);
    });

    // 广告加载成功
    if (this.data.interstitialAd && this.data.interstitialAd.onSuccess) {
      this.data.interstitialAd.onSuccess(() => {
        console.log('onSuccess');
        this.props.onAdSuccess(this);
      });
    }

    // 广告关闭
    this.data.interstitialAd.onClose((res) => {
      console.log('onClose', res);
      this.props.onAdClose(res, this);
    });
  },
  methods: {
    showAd() {
      const { unitId, rtaExtMap } = this.props;
      this.data.interstitialAd.show({
        spaceCode: unitId, // 填入资源位创建后对应的 unit-id
        rtaExtMap,
      });
    },
    onInsuranceStatus(code) {
      console.log('onInsuranceStatus-code', code);
    },
  },
});
