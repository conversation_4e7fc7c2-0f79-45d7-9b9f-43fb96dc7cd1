import Taro from '@tarojs/taro';

// 将 scheme 转换为 my.navigateToMiniProgram 的参数
function schemeToParams(scheme) {
  if (!scheme.startsWith('alipays:')) {
    return { message: '! 非 alipays: 开头' };
  }
  var params = {};
  var parseQuery = (str) => {
    return str
      .replace(/^.*?\?/, '')
      .split('&')
      .map((s) => {
        var p = s.includes('=') ? s.indexOf('=') : s.length;
        return [s.slice(0, p), s.slice(p + 1)].map(decodeURIComponent);
      });
  };
  for (var [k, v] of parseQuery(scheme)) {
    if (k == 'appId') {
      if (v.length != 16) {
        return { message: `! 非 16 位 appId '${v}'` };
      }
    } else if (k == 'page') {
      k = 'path';
    } else if (k == 'query') {
      var o = {};
      for (var [x, y] of parseQuery(v)) {
        o[x] = y;
      }
      v = o;
    } else {
      return { message: `! 不支持参数 '${k}' ` };
    }
    params[k] = v;
  }
  return { params };
}

// 使用 scheme 跳转小程序
export function navigateToMiniProgramScheme({ scheme, success, fail }) {
  var { params } = schemeToParams(scheme);
  if (params) {
    my.navigateToMiniProgram({ ...params, success, fail });
  } else {
    my.ap.openURL({
      url: scheme,
      success,
      fail,
    });
    // fail && fail({ error: -1, errorMessage: `无效的小程序 scheme ${scheme}: ${message}` });
  }
}

export function checkIsAlipayScheme(urlPath) {
  return /^alipays:/.test(urlPath);
}

export function checkIsAlipayH5(urlPath) {
  return /^alipays\:|render\.alipay/.test(urlPath);
}

function showToast(text) {
  if (Taro.kbToast) {
    Taro.kbToast({ text });
  } else {
    Taro.showToast({
      title: text,
      icon: 'none',
    });
  }
}

export function navigateToAlipayUrl(urlPath) {
  if (checkIsAlipayScheme(urlPath)) {
    navigateToMiniProgramScheme({
      scheme: urlPath,
      fail: (error) => {
        showToast(JSON.stringify(error));
      },
    });
    return true;
  }
  if (checkIsAlipayH5(urlPath)) {
    my.ap.navigateToAlipayPage({
      path: urlPath,
      fail: (err) => {
        showToast(JSON.stringify(err));
      },
    });
    return true;
  }
}
