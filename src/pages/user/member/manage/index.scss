/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb-vipManage {
  &-head {
    height: 340px;
    &--bg {
      height: 180px;
      background: $color-brand;
      border-radius: 0 0 80px 80px;
    }
    .kb-recordCard {
      position: relative;
      box-sizing: border-box;
      width: 710px;
      height: 312px;
      margin: -150px auto;
      padding: 30px;
      color: #d7bf9e;
      background: linear-gradient(90deg, #3e4055 0%, #5c616d 100%);
      border-radius: 20px;
      &-cardName {
        color: #fafafa;
        font-weight: 800;
        font-size: 45px;
      }
      &-body {
        display: flex;
        margin-top: 10px;
        font-size: 24px;
      }
      &-footer {
        position: absolute;
        right: 0;
        bottom: 0;
        left: 0;
        display: flex;
        align-items: center;
        height: 88px;
        padding: 0 30px;
        color: #fff1df;
        background: #63646d;
        border-radius: 0 0 20px 20px;
        .price {
          display: flex;
          align-items: baseline;
        }
        .money {
          font-size: 36px;
        }
        .original {
          margin-left: 20px;
          color: #999;
          font-size: 24px;
          text-decoration: line-through;
        }
      }
      &-cardTag {
        position: absolute;
        top: 0;
        right: 0;
        display: flex;
        align-items: center;
        height: 52px;
        padding: 0 $spacing-h-md 0 40px;
        color: $color-black-1;
        font-size: $font-size-base;
        line-height: 52px;
        background: linear-gradient(to right, #cca066, #fee1b5);
        border-radius: 0 $border-radius-lg 0 40px;
        &_arrow {
          position: relative;
          display: flex;
          align-items: center;
          justify-content: center;
          width: 25px;
          height: 25px;
          margin-right: 5px;
          background: #3d444d;
          border-radius: 50%;
          &::after {
            width: 6px;
            height: 10px;
            font-size: 24px;
            border: $width-base solid #d7bf9e;
            border-top-color: transparent;
            border-left-color: transparent;
            transform: rotate(45deg) translate(-2px, -3px);
            content: '';
          }
        }
      }
    }
  }
  &-card {
    margin: 20px;
    padding: $spacing-h-md;
    background: #fff;
    border-radius: 10px;
    &--desc {
      color: #666666;
      font-size: 26px;
      text-align: center;
    }
    &--list {
      display: flex;
      align-items: center;
      justify-content: space-around;
      height: 257px;
      margin: $spacing-h-md 0;
      background: #f2faff;
      border-radius: 8px;
    }
    &--item {
      text-align: center;
      .img {
        width: 80px;
        height: 80px;
      }
      .title {
        color: #333;
        font-size: 26px;
      }
      .desc {
        color: #999;
        font-size: 20px;
      }
    }
  }
}
