/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb-couponCenter {
  &-swiper {
    position: relative;
    width: 710px;
    height: 280px;
    margin: 20px auto;
    .swiper {
      position: relative;
      z-index: 1;
      width: 100%;
      height: 100%;
      &-item {
        width: 100%;
        height: 100%;
      }
      &-img {
        width: 100%;
        height: 100%;
      }
    }
    .rule {
      position: absolute;
      top: 20px;
      right: 20px;
      z-index: 2;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 98px;
      height: 42px;
      color: #ffffff;
      font-weight: 500;
      font-size: 26px;
      background: rgba(0, 0, 0, 0.18);
      border-radius: 21px;
    }
  }
  &-rules {
    position: absolute;
    top: 50px;
    right: 0;
    height: 54px;
    padding: 0 $spacing-h-md 0 $spacing-h-lg;
    color: #fff;
    font-size: $font-size-base;
    line-height: 54px;
    background: #f4400e;
    border-radius: 50px 0 0 50px;
    &-list {
      &-item {
        display: flex;
        margin-bottom: 8px;
        font-size: $font-size-base;
        text-align: justify;
      }
      &-index {
        width: 40px;
      }
    }
  }
  &-vip {
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-sizing: border-box;
    height: 128px;
    margin: 20px;
    padding: 20px;
    background: #ffffff;
    border-radius: 10px;
    &__img {
      width: 84px;
      height: 84px;
      margin: 0 30px;
    }
    &__body {
      position: relative;
      flex: 1;
      padding-left: 30px;
      text-align: left;
      .title {
        color: #333333;
        font-weight: bold;
        font-size: 30px;
      }
      .desc {
        margin-top: 10px;
        color: #999999;
        font-weight: 500;
        font-size: 22px;
      }
      &::before {
        position: absolute;
        top: 50%;
        left: 0;
        width: $width-base;
        height: 70px;
        background: #ffdfbf;
        border-radius: 1px;
        transform: translateY(-50%);
        content: '';
      }
    }
  }
  &-day {
    box-sizing: border-box;
    margin: 20px;
    padding: 20px;
    background: #ffffff;
    border-radius: 10px;
    &__box {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin: 10px 0;
    }
    &__block {
      display: flex;
      flex: 1;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 148px;
      margin: 0 10px;
      background: #fff3e6;
      border-radius: 10px;
      .img {
        width: 30px;
        height: 30px;
      }
      .user {
        color: #333333;
        font-weight: bold;
        font-size: 32px;
      }
      .desc {
        margin-top: 10px;
        color: #999999;
        font-weight: 500;
        font-size: 24px;
      }
    }
  }
  &-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
    .title {
      color: #333333;
      font-weight: bold;
      font-size: 32px;
    }
    .desc {
      color: #666666;
      font-weight: 500;
      font-size: 24px;
    }
    .img {
      display: block;
      width: 47px;
      height: 41px;
      margin-right: 10px;
    }
    .img2 {
      width: 36px;
      height: 36px;
      margin-right: 10px;
    }
  }
  &-subtract {
    margin: 20px;
    padding: 20px;
    background: #ffffff;
    border-radius: 10px;
    &__tabs {
      display: flex;
      align-items: center;
    }
    &__tabItem {
      width: 108px;
      height: 42px;
      margin-right: 20px;
      color: #666666;
      font-weight: 500;
      font-size: 24px;
      line-height: 42px;
      text-align: center;
      background: #ffffff;
      border: $width-base solid #eaeaea;
      border-radius: 21px;
      &:last-child {
        margin-right: 0;
      }
      &--active {
        position: relative;
        color: #fff;
        background: #ff4c4c;
        border-color: #ff4c4c;
        &::after {
          position: absolute;
          bottom: -8px;
          left: 50%;
          width: 20px;
          height: 20px;
          background: #ff4c4c;
          transform: translateX(-50%) rotate(45deg);
          content: '';
        }
      }
    }
    &__list {
      display: flex;
      flex-wrap: wrap;
      margin-bottom: 25px;
      padding-top: 20px;
    }
    &__item {
      position: relative;
      display: flex;
      flex-direction: column;
      width: 310px;
      height: 312px;
      margin: 0 8px 20px;
      padding: 4px;
      background: #ffdfbf;
      border-radius: 10px;
      .brand-tag {
        position: absolute;
        top: 0;
        left: 50%;
        z-index: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        height: 48px;
        margin: 0 auto;
        padding: 0 20px;
        color: #6a1f01;
        font-weight: 500;
        font-size: 24px;
        white-space: nowrap;
        background: #ffdfbf;
        border-radius: 0px 0px 10px 10px;
        transform: translateX(-50%);
        .img-brand {
          width: 40px;
          height: 40px;
          margin-right: 10px;
        }
      }
      .content {
        position: relative;
        display: flex;
        flex: 1;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding-top: 50px;
        background: #ffffff;
        border-radius: 6px;
        &-price {
          color: #ff5624;
          font-weight: 800;
          font-size: 72px;
          .unit {
            font-size: 36px;
          }
        }
        &-desc {
          color: #999999;
          font-weight: 500;
          font-size: 24px;
        }
      }
      .footer {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 20px 0 16px;
      }
      .btn {
        width: 198px;
        height: 48px;
        color: #ffffff;
        font-weight: bold;
        font-size: 30px;
        line-height: 48px;
        text-align: center;
        background: linear-gradient(to left, #ff5353, #ffa83f);
        border-radius: 24px;
      }
      .btn2 {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 198px;
        height: 48px;
        color: #ff4c4c;
        font-weight: bold;
        font-size: 30px;
        text-align: center;
        background: #fff5e5;
        border: 2px solid #ff4c4c;
        border-radius: 24px;
        .img-arrow {
          width: 14px;
          height: 20px;
          margin-left: 10px;
        }
      }
    }
  }
  &-discount {
    margin: 20px;
    padding: 20px;
    background: #ffffff;
    border-radius: 10px;
    &__list {
      margin-bottom: 40px;
    }
    &__item {
      display: flex;
      align-items: center;
      box-sizing: border-box;
      width: 650px;
      margin: 0 auto 20px;
      padding: 4px;
      background: #ffdfbf;
      border-radius: 10px;
    }
    &__inner {
      display: flex;
      flex: 1;
      align-items: center;
      box-sizing: border-box;
      height: 180px;
      background: #ffffff;
      border-radius: 10px;
      .left {
        position: relative;
        width: 188px;
        text-align: center;
        &::after {
          position: absolute;
          top: 50%;
          right: 0;
          width: $width-base;
          height: 118px;
          background: #ffdfbf;
          transform: translateY(-50%);
          content: '';
        }
      }
      .price {
        display: flex;
        align-items: baseline;
        justify-content: center;
        color: #ff5624;
        font-weight: 800;
        font-size: 60px;
        .unit {
          font-size: 28px;
        }
      }
      .desc {
        color: #999999;
        font-weight: 500;
        font-size: 24px;
      }
      .right {
        padding-left: 20px;
      }
      .title {
        color: #5c0704;
        font-weight: 800;
        font-size: 36px;
      }
      .text {
        margin-top: 10px;
        color: #666666;
        font-weight: 500;
        font-size: 22px;
      }
      .tag {
        height: 32px;
        margin-top: 15px;
        padding: 0 20px;
        color: #ff4c4c;
        font-weight: 500;
        font-size: 18px;
        line-height: 32px;
        text-align: center;
        background: rgba(255, 245, 235, 0.06);
        border: $width-base solid #ff4c4c;
        border-radius: 15px;
      }
    }
    &__btn {
      width: 50px;
      padding-left: 4px;
      color: #6a1f01;
      font-weight: bold;
      font-size: 30px;
      text-align: center;
      &2 {
        color: #ff4c4c;
      }
    }
  }
  &-btnAll {
    position: relative;
    height: 88px;
    margin: 20px 40px;
    color: #ffffff;
    font-weight: bold;
    font-size: 34px;
    line-height: 88px;
    text-align: center;
    background: linear-gradient(to left, #ff5353, #ffa83f);
    border-radius: 44px;
    box-shadow: 0px 3px 14px 0px rgba(236, 36, 26, 0.18);
    .tag {
      position: absolute;
      top: -20px;
      right: 40px;
      width: 130px;
      height: 46px;
    }
  }
  &-block {
    width: 750px;
    height: 360px;
    margin: 0 auto;
  }
}
