/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { Component } from '@tarojs/taro';
import KbPage from '@base/components/page';
import { Image, View } from '@tarojs/components';
import request from '~base/utils/request';
import KbScrollView from '~base/components/scroll-view';
import './index.scss';

export default class Index extends Component {
  config = {
    navigationBarTitleText: '详情',
  };

  constructor() {
    this.state = {
      info: null,
    };
  }

  componentDidMount() {
    this.getInfo();
  }

  getInfo = () => {
    const { id } = this.$router.params;
    request({
      url: '/g_wkd/v2/FeedBack/detail',
      data: {
        id,
      },
      toastLoading: true,
      toastError: true,
      onThen: (res) => {
        const { code, data } = res || {};
        if (code == 0) {
          this.setState({
            info: data,
          });
        }
      },
    });
  };

  render() {
    const { info, ...rest } = this.state;

    return (
      <KbPage {...rest} className='kb-hide__footer'>
        <KbScrollView scrollY className='kb-scrollview'>
          <View className='kb-feedback-detail'>
            {info && (
              <View>
                <View className='kb-feedback-detail-time'>{info.create_at}</View>
                <View className='at-row at-row__justify--end  at-row__align--center kb-margin-xl-tb'>
                  <View className='kb-feedback-detail-content'>{info.content}</View>
                  <Image
                    className='kb-feedback-detail-avatar'
                    src='https://cdn-img.kuaidihelp.com/m/yzAppDefaultAvatar.png'
                  />
                </View>
                {info &&
                  Array.isArray(info.images) &&
                  info.images.map((img) => (
                    <View className='kb-margin-md-b at-row at-row__justify--end' key={img}>
                      <Image src={img} className='kb-feedback-detail-img kb-margin-md-l' />
                      <View className='kb-placeholder' />
                    </View>
                  ))}
              </View>
            )}
            {info.reply && (
              <View className='kb-feedback-detail-reply'>
                <View className='kb-feedback-detail-time kb-margin-xl-t'>{info.update_at}</View>
                <View className='at-row at-row__justify--start at-row__align--center kb-margin-xl-tb'>
                  <Image
                    className='kb-feedback-detail-avatar'
                    src='https://cdn-img.kuaidihelp.com/wkd/miniApp/login_wkd.png'
                  />
                  <View>
                    <View className='kb-color__grey kb-size__base kb-margin-xs-b'>客服</View>
                    <View className='kb-feedback-detail-content'>{info.reply}</View>
                  </View>
                </View>

                {info &&
                  Array.isArray(info.reply_images) &&
                  info.reply_images.map((img) => (
                    <View className='at-row at-row__align--center kb-margin-md-b' key={img}>
                      <View className='kb-placeholder' />
                      <Image src={img} className='kb-feedback-detail-img kb-margin-md-r' />
                    </View>
                  ))}
              </View>
            )}
          </View>
        </KbScrollView>
      </KbPage>
    );
  }
}
