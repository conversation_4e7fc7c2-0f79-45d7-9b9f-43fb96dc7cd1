/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb-chooseCoupon {
  &__coupon {
    position: relative;
    z-index: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: 98px;
    padding: 0 $spacing-h-md;
    background: $color-white;
    border-radius: $border-radius-lg;
    &__title {
      position: relative;
      color: $color-grey-1;
    }
    &__title2 {
      position: relative;
      padding-right: 190px;
      &::after {
        position: absolute;
        top: 50%;
        right: 0;
        width: 180px;
        height: 32px;
        color: #ff4c4c;
        font-weight: 500;
        font-size: 20px;
        font-family: PingFang SC;
        line-height: 32px;
        text-align: center;
        background: rgba(255, 76, 76, 0.06);
        border: $width-base solid #ff4c4c;
        border-radius: 6px;
        transform: translateY(-50%);
        content: '智能匹配最佳优惠';
      }
    }
    &__content {
      display: flex;
      align-items: center;
      &--info {
        font-size: $font-size-base2;
        &-row {
          display: flex;
          align-items: center;
        }
        &-icon {
          width: 32px;
          height: 32px;
          margin-right: $spacing-h-sm;
        }
      }
      .kb-icon {
        vertical-align: unset;
      }
    }
  }
  &__tips {
    height: 40rpx;
    margin-top: -20px;
    padding: 35px $spacing-h-md 0;
    overflow: hidden;
    color: #ffffff;
    font-size: 22rpx;
    white-space: nowrap;
    background: rgba(255, 76, 76, 0.48);
    border-radius: 0rpx 0rpx 10rpx 10rpx;
    .inner {
      width: fit-content;
      transform: translateX(500px);
      animation: scroll 12s linear infinite;
      @keyframes scroll {
        0% {
          transform: translateX(500px);
        }
        100% {
          transform: translateX(-100%);
        }
      }
    }
  }
  &__couponGuide {
    display: flex;
    align-items: center;
    box-sizing: border-box;
    height: 54px;
    margin-top: $spacing-h-md;
    margin-bottom: $spacing-h-md;
    padding: 0 $spacing-h-lg;
    color: $color-grey-1;
    font-size: 24px;
    background: #fffbe9;
    border-radius: 50px;
    &-img {
      width: 30px;
      height: 30px;
    }
    &-content {
      display: flex;
      flex: 1;
      flex-wrap: nowrap;
      align-items: center;
      box-sizing: border-box;
      width: 600px;
      padding-left: 5px;
    }
    .kb-icon {
      vertical-align: unset;
    }
  }
  &__explain {
    &-tips {
      padding: $spacing-h-md;
      color: $color-grey-1;
      font-size: $font-size-base;
      text-align: center;
    }
    &-img {
      display: block;
      width: 620px;
      height: 410px;
      margin: 30px auto;
    }
    &-btn {
      margin: 0 30px;
    }
  }
}

// 微信新客立减
.kb-send {
  &__couponGuide {
    display: flex;
    align-items: center;
    width: fit-content;
    height: 54px;
    margin: $spacing-h-md auto;
    padding: 0 $spacing-h-lg;
    color: $color-grey-1;
    font-size: $font-size-base;
    background: #fffbe9;
    border-radius: 50px;
    &-img {
      width: 30px;
      height: 30px;
    }
    &-content {
      display: flex;
      align-items: center;
      margin: 0 $spacing-h-sm;
    }
    .kb-icon {
      vertical-align: unset;
    }
  }
}
