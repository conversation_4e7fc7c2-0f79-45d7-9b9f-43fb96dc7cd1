/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { Image, View } from '@tarojs/components';
import { useState } from '@tarojs/taro';
import { AtCurtain } from 'taro-ui';
import { toDayThreeRewardService } from '~/services/out-reward/sign';
import { useUpdate } from '~base/hooks/page';
import './index.scss';

const KbAutoReward = (props) => {
  const { onFresh, onShowInsertAd } = props;
  const [isOpened, setIsOpened] = useState(false);

  const triggerOnShowInsertAd = () => {
    if (onShowInsertAd) {
      onShowInsertAd();
    }
  };

  useUpdate(() => {
    toDayThreeRewardService().then((res) => {
      if (res.code == 0) {
        setIsOpened(true);
      } else {
        triggerOnShowInsertAd();
      }
    });
  }, []);

  const handleClose = () => {
    setIsOpened(false);
    if (onFresh) {
      onFresh();
    }
    triggerOnShowInsertAd();
  };

  return (
    <AtCurtain isOpened={isOpened} onClose={handleClose}>
      <View className='kb-autoReward-box' onClick={handleClose} hoverClass='kb-hover-opacity'>
        <Image
          className='kb-autoReward-box--bg'
          mode='widthFix'
          src='https://cdn-img.kuaidihelp.com/wkd/miniApp/reward2/jfbg.png?v=1'
        />
        <Image
          className='kb-autoReward-box--coin'
          mode='widthFix'
          src='https://cdn-img.kuaidihelp.com/wkd/miniApp/reward2/jb.png?v=1'
        />
      </View>
    </AtCurtain>
  );
};

KbAutoReward.options = {
  addGlobalClass: true,
};

export default KbAutoReward;
