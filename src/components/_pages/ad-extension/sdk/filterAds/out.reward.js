import { getPage } from '~base/utils/utils';

const positions =
  process.env.PLATFORM_ENV === 'alipay' && process.env.MODE_ENV === 'wkd'
    ? ['47', '48', '49', '50']
    : [];
/**
 *
 * @description 特定广告过滤
 */
export function filterAdsOutReward(list, data) {
  return new Promise((resolve) => {
    const { position } = data || {};
    if (!positions.includes(position)) {
      resolve(list);
    } else {
      // 判断页面
      const page = getPage();
      const { $router } = page || {};
      const { path, params } = $router || {};
      const { position: paramsPosition = '' } = params || {};
      const reg = /^reward(\d+)?-/;
      let [, titlePrefix] = `${path}`.match(/\/(reward\d+)\/index/) || [];
      if (paramsPosition || !titlePrefix) {
        // 过滤标题前缀
        titlePrefix = `reward${paramsPosition}`;
      }
      const listFileted = list.filter((item) => {
        let { title = '' } = item;
        if (!reg.test(title)) {
          // 非reg对应前缀的，补充默认前缀；
          title = `reward-${title}`;
        }
        return title.startsWith(`${titlePrefix}-`);
      });
      console.log('titlePrefix', titlePrefix);
      console.log('list', list);
      console.log('listFileted', listFileted);

      resolve(listFileted);
    }
  });
}
