/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

const config = {
  pages: [
    'pages/query/index',
    'pages/query/appointment/index',
    'pages/query/detail/index',
    'pages/order/card/index',
    'pages/order/edit/index',
    'pages/user/index',
    'pages/webview/index',
    'pages/welfare/continuity/index',
    // 'pages/queryExpress/notice/notice',
    'pages/queryExpress/result/result',
    'pages/sendRecord/detail/detail',
    'pages/sendRecord/voucher/voucher',
    'pages/pickup/pickup',
    'pages/welfare/cash/index',
  ],
  window: {
    navigationBarBackgroundColor: '#099dff',
    navigationBarTitleText: '微快递',
    backgroundColor: '#f2f2f2',
    enablePullDownRefresh: false,
  },
  tabBar: {
    custom: process.env.PLATFORM_ENV === 'weapp',
    color: '#666666',
    selectedColor: '#099dff',
    borderStyle: 'black',
    backgroundColor: '#ffffff',
    list: [
      {
        pagePath: 'pages/query/index',
        iconPath: 'assets/tab/wkd/search.png',
        selectedIconPath: 'assets/tab/wkd/search-active.png',
        text: '查快递',
      },
      {
        pagePath: 'pages/order/edit/index',
        iconPath: 'assets/tab/wkd/send.png',
        selectedIconPath: 'assets/tab/wkd/send-active.png',
        text: '寄快递',
      },
      ...(process.env.PLATFORM_ENV === 'alipay'
        ? [
            // {
            //   pagePath: 'pages/order/kxj/center/index',
            //   iconPath: 'assets/tab/wkd/center.png',
            //   selectedIconPath: 'assets/tab/wkd/center-active.png',
            //   text: '领券中心',
            // },
            {
              pagePath: 'pages/welfare/cash/index',
              iconPath: 'assets/tab/wkd/welfare.png',
              selectedIconPath: 'assets/tab/wkd/welfare-active.png',
              text: '天天领现金',
            },
          ]
        : []),
      ...(process.env.PLATFORM_ENV === 'weapp'
        ? [
            {
              pagePath: 'pages/welfare/continuity/index',
              iconPath: 'assets/tab/wkd/jyhs.png',
              selectedIconPath: 'assets/tab/wkd/jyhs-active.png',
              text: '旧衣回收',
            },
          ]
        : []),
      {
        pagePath: 'pages/user/index',
        iconPath: 'assets/tab/wkd/user.png',
        selectedIconPath: 'assets/tab/wkd/user-active.png',
        text: '我的',
      },
    ],
  },
};

// 分包
config.subPackages = [
  {
    root: 'pages', // 查件、活动相关
    preloadRule: 'pages/query/index',
    pages: [
      // 查件
      'brand/index',
      'IDcode/index',
      'query/list/index',
      'query/feedback/index',
      'query/feedback/reason/index',
      'query/match/index',
      'query/notice/index',
      'query/detail/target/index',
      // 快递柜
      'kdg/kdg',
      'kdg/result/index',
      // 福利中心
      'welfare/continuity/target/index',
      'welfare/welfare',
      'welfare/center/center',
      'welfare/commission/commission',
      'welfare/invitation/invitation',
      'welfare/inviteList/inviteList',
      'welfare/lend/lend',
      'welfare/profitList/profitList',
      'welfare/record/record',
      'welfare/share/share',
      'welfare/withdrawal/withdrawal',
      'welfare/lotterys/index',
      'welfare/lotterys/details',
      'welfare/lotterys/target/index',
      'welfare/lotterys/list',
      ...(process.env.PLATFORM_ENV === 'weapp' ? ['welfare/byn/index'] : []),
      // 桌面权益
      'desktop/index',
      'desktop/prize/index',
      // web支付页
      'webpay/index',
      // 搜索页
      'query/search/index',
    ],
  },
  {
    root: 'pages', // 寄件相关
    preloadRule: 'pages/order/edit/index',
    pages: [
      // 信用支付 - 微信支付分
      'order/credit-pay/index',
      // 同城急送
      'order/delivery/index',
      'order/delivery/goods/index',
      'order/delivery/append/index',
      'order/delivery/address/index',
      'order/delivery/address/edit/index',
      'order/delivery/address/choose/index',
      // 底单
      'order/voucher/index',
      // 地址
      'address/index',
      'address/batch/index',
      'address/edit/index',
      'address/import/index',
      'address/pickup/index',
      // 日期
      'calendar/index',
      // 图片剪切
      'cutting/index',
      // 城市选择
      'city/index',
      // 实名
      'realname/index',
      'realname/scan/index',
      // 实名列表
      'realnameList/index',
      // 下单
      'order/edit/goods/index',
      'order/edit/service/index',
      'customer/index',
      'customer/account/index',
      'customer/account/memberList/index',
      'customer/member/index',
      'customer/message/index',
      'customer/message/record/index',
      'customer/printer/index',
      'customer/qrcode/index',
      'customer/queryCode/index',
      'order/share/index',
      // 开学寄
      'order/kxj/coupon/index',
      'order/kxj/weekend/index',
      // 网购退货
      'order/edit/online-returns/index',
      // 网购退货步骤
      'order/edit/return-step/index',
      // 网购退货截图示例
      'order/edit/returns-demo/index',
      // 大件货 德邦线下
      'order/djj/index',
      // 微快递大货物流百世
      'order/edit/dh/index',
      'order/edit/service/dh/index',
      // 经济货运
      'order/edit/economy/index',
      'order/edit/service/dh/desc/index',
      // 寄件
      'order/edit/share/index',
      'order/edit/temporary/index',
      'ws/order/edit/index',
      'ws/order/edit-yhj/index',
      'order/edit/send/index',
      'user/ecode/edit/index',
      // 领取自定义优惠券
      'order/custom-coupon/index',
    ],
  },
  {
    root: 'pages',
    preloadRule: 'pages/order/edit/index',
    pages: [
      // 订单
      'order/amount/index',
      'order/cancel/index',
      'order/credit/index',
      'order/courier/check/index',
      'order/courier/message/index',
      'order/detail/index',
      'order/history/index',
      'order/integral/index',
      'order/integral/detail/index',
      'order/pic/index',
      'order/pay/index',
      'order/kdyPay/index',
      'order/result/index',
      'order/result/target/index',
      'order/relation/index',
      'order/select/index',
      'order/service/index',
      'order/station/index',
      'order/qj-detail/index',
      'order/print-scan/index',
      // 发票
      'invoice/index',
      'invoice/edit/index',
      'invoice/detail/index',
      'invoice/result/index',
      'invoice/title/index',
      'invoice/extra/index',
      'order/kdyPay/index', // 充值
      // 选择订单
      'order/chooseOrder/index',
    ],
  },
  {
    root: 'pages',
    preloadRule: 'pages/user/index',
    pages: [
      // 微商相关
      'ws/index',
      'ws/detail/index',
      'ws/query/index',
      'ws/bill/index',
      'ws/bill/detail/index',
      // 帮助文档
      'help/index',
      'help/relation/index',
      // 我的
      'user/relation/index',
      'user/setting/index',
      'user/relation/edit/index',
      'user/login/index',
      'user/ecode/index',
      'user/ecode/detail/index',
      'user/message/index',
      'user/message/detail/index',
      'user/service/index',
      'user/service/detail/index',
      'user/service/problem/index',
      'user/qrcode/index',
      'user/wallet/index',
      // 疫情查询
      'user/yiqing/index',
      'user/yiqing/detail/index',
      'user/yiqing/rank/index',
      // 会员相关
      'user/member/index',
      'user/member/right/index',
      'user/member/invite/index',
      'user/member/manage/index',
      'user/member/manage/close/index',
      'user/member/record/index',
      'user/member/record/detail/index',
      'order/index',
      // 运费比价
      'user/price/index',
      // 停发区查询
      'closedArea/index',
      'closedArea/result/index',
      'closedArea/selectBrand/index',
      'user/feedback/index',
      'user/feedback/record/index',
      'user/feedback/detail/index',
    ],
  },
  {
    root: 'pages',
    pages: ['out/choose-file/index', 'out/welfare/index', 'out/red-envelope/index'],
  },
];

if (process.env.PLATFORM_ENV === 'alipay') {
  config.pages.push(
    ...[
      'pages/bill/bill',
      'pages/bill/detail/index',
      'pages/webview-ghost/index',
      'pages/retain/index',
      'pages/welfare/coupon/index',
      'pages/retain/retain',
      'pages/welfare/attention/index',
      'pages/welfare/boutique/index',
      'pages/realnameAuth/realnameAuth',
      'pages/order/kxj/center/index',
    ],
  );
  config.subPackages[4].pages.push(
    'out/reward/index',
    'out/reward/target/index',
    'out/reward/integral-record/index',
    'out/reward1/index',
  );
  config.subPackages[0].pages.push('wealthy/index');
  config.plugins = {
    love: {
      version: '*',
      provider: '2021001131694653',
    },
    ZhiMaCredit: {
      version: '*',
      provider: '2021002151672975',
    },
    gomini: {
      version: '*',
      provider: '2021001185604232',
    },
    xlightPlugin: {
      version: '*',
      provider: '2021001192652032',
    },
    xlightPlugin2: {
      version: '*',
      provider: '2021003196619202',
    },
  };
  config.window.enableInPageRenderInput = 'YES';
  config.window.enableInPageRender = 'YES';
} else {
  config.functionalPages = {
    independent: true,
  };
  config.miniApp = {
    useAuthorizePage: true,
  };
  if (process.env.PLATFORM_ENV === 'weapp') {
    config.embeddedAppIdList = [
      'wx16af9eb52c6961c0',
      'wx1ac840bddf4b78cf',
      'wx61de8f0176155640',
      'wxf1a81af0f4e32ade',
      'wx9a713fd690f464c5',
      'wx9e33d0d6b7227315',
      'wxa8f61aded5dbe320',
    ];
    if (process.env.MODE_ENV === 'wkd') {
      // 微信微快递接入必应鸟优惠商城(仅在首个分包中接入)
      config.subPackages[0].plugins = {
        bynPlugin: {
          version: '2.1.1',
          provider: 'wx4c949e6978871ff8',
          genericsImplementation: {
            web: {
              'mp-webview': 'components/byn-webview/byn-webview',
            },
          },
        },
      };
    }
  }
}

module.exports = config;
