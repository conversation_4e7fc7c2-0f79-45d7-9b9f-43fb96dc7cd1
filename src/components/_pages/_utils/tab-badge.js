/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { useDidShow } from '@tarojs/taro';
import { getPage } from '@base/utils/utils';
import { checkIsTabPage } from '@base/utils/navigator';
import { toggleTabBarRedDot } from '@/utils/qy';

const tabBadgeKey = 'TAB_BADGE';

/**
 * 1、启动标记-检查红点标记情况，加锁仅执行一次
 * 2、解除标记-在tab页面操作
 */

// 触发解除徽标标记
export const triggerClearTabBadge = (key, data) => {
  const tabBadgeData = Taro.kbGetGlobalData(tabBadgeKey) || {};
  tabBadgeData[key] = data || '1';
  Taro.kbSetGlobalData(tabBadgeKey, tabBadgeData);
};

export const useTabBarRedDot = () => {
  // 消除标记
  const clear = (key) => {
    if (process.env.MODE_ENV === 'wkd') {
      const badgeIndex = Taro.kbGetGlobalData('badgeIndex') || {};
      switch (key) {
        case 'FLNew':
          if (badgeIndex.welfareTabIndex > -1) {
            toggleTabBarRedDot('hide', badgeIndex.welfareTabIndex, 'FLNew');
          }
          break;
      }
    }
  };
  const clearBadge = () => {
    const {
      $router: { path },
    } = getPage();
    if (checkIsTabPage(path)) {
      const tabBadgeData = Taro.kbGetGlobalData(tabBadgeKey) || {};
      Object.keys(tabBadgeData).map((key) => {
        if (key) {
          clear(key);
          delete tabBadgeData[key];
          Taro.kbSetGlobalData(tabBadgeKey, tabBadgeData);
        }
      });
    }
  };

  useDidShow(() => {
    clearBadge();
  });
};
