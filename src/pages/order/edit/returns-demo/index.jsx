import { Image, Text, View } from '@tarojs/components';
import Taro, { useEffect } from '@tarojs/taro';
import './index.scss';

const demoList = [
  {
    platform: '拼多多退货截图',
    desc: '先申请退货，商家同意退货后，再点击首页右下角【个人中心】-【退款售后】-点击退货订单 - 截图',
    pic: 'https://cdn-img.kuaidihelp.com/miniapp/wkd/picture_pdd.png',
  },
  {
    platform: '抖音退货截图',
    desc: '先申请退货，商家同意退货后，再点击首页右下角【我】-【抖音商城】-【退款售后】-点击退货订单 - 截图',
    pic: 'https://cdn-img.kuaidihelp.com/miniapp/wkd/picture_dy.png',
  },
  {
    platform: '快手退货截图',
    desc: '先申请退货，商家同意退货后，再点击首页右下角【我】-【快手小店】-顶部右滑选择【退款/售后】-退货订单 - 截图',
    pic: 'https://cdn-img.kuaidihelp.com/miniapp/wkd/picture_ks.png',
  },
  {
    platform: '京东退货截图',
    desc: '先申请退货，商家同意退货后，再点击首页右下角【我的】-【退换/售后】-点击退货订单 - 截图',
    pic: 'https://cdn-img.kuaidihelp.com/miniapp/wkd/picture_jd.png',
  },
  {
    platform: '唯品会退货截图',
    desc: '先申请退货，商家同意后，点击【个人中心】-【退货详情】 图含商家地址',
    pic: 'https://cdn-img.kuaidihelp.com/miniapp/wkd/picture_wph.png',
  },
  {
    platform: '淘宝天猫退货截图',
    desc: '先申请退货，商家同意退货后，再点击首页右下角【我】-【快手小店】-顶部右滑选择【退款/售后】-退货订单 - 截图',
    pic: 'https://cdn-img.kuaidihelp.com/miniapp/wkd/picture_tbtm.png',
  },
  {
    platform: '有赞退货截图示例',
    desc: '先到有赞精选小程序申请退货，商家同意后，点击【申请退款- 选择(仅退货）或（退货退款）】截图含商家地址的页面',
    pic: 'https://cdn-img.kuaidihelp.com/miniapp/wkd/picture_yz.png',
  },
];

const returnsIncludes = [
  { name: '商家同意退货', required: true },
  { name: '商品信息', required: false },
  { name: '退货地址', required: true },
];

const ReturnsDemo = () => {
  useEffect(() => {
    Taro.setNavigationBarTitle({
      title: '退货截图示例',
    });
  }, []);

  return (
    <View className='return-demo'>
      <View className='return-demo-header'>
        <View className='return-demo-header-left'>退货截图包含</View>
        <View className='return-demo-header-right'>
          {returnsIncludes.map((n, idx) => (
            <View key={n.name}>
              <Text className='info'>
                {idx + 1}.{n.name}
              </Text>
              <Text className={n.required ? 'required' : ''}>{`(${
                n.required ? '必含' : '选含'
              })`}</Text>
            </View>
          ))}
        </View>
      </View>
      {demoList.map((f) => (
        <View className='return-demo-list' key={f.platform}>
          <View className='return-demo-list-title'>{f.platform}</View>
          <Text className='return-demo-list-desc'>{f.desc}</Text>
          <View className='return-demo-list-pic'>
            <Image className='return-demo-list-pic-container' src={f.pic} />
          </View>
        </View>
      ))}
    </View>
  );
};

export default ReturnsDemo;
