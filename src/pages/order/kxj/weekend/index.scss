/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb-couponWeekend {
  &-swiper {
    position: relative;
    width: 710px;
    height: 280px;
    margin: 20px auto;
    .swiper {
      width: 100%;
      height: 100%;
      &-item {
        width: 100%;
        height: 100%;
      }
      &-img {
        width: 100%;
        height: 100%;
      }
    }
    .rule {
      position: absolute;
      top: 20px;
      right: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 98px;
      height: 42px;
      color: #ffffff;
      font-weight: 500;
      font-size: 26px;
      background: rgba(0, 0, 0, 0.18);
      border-radius: 21px;
    }
  }
  &-rules {
    position: absolute;
    top: 50px;
    right: 0;
    height: 54px;
    padding: 0 $spacing-h-md 0 $spacing-h-lg;
    color: #fff;
    font-size: $font-size-base;
    line-height: 54px;
    background: #f4400e;
    border-radius: 50px 0 0 50px;
    &-list {
      &-item {
        display: flex;
        margin-bottom: 8px;
        font-size: $font-size-base;
        text-align: justify;
      }
      &-index {
        width: 40px;
      }
    }
  }
  &-vip {
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-sizing: border-box;
    height: 128px;
    margin: 20px;
    padding: 20px;
    background: #ffffff;
    border-radius: 10px;
    &__img {
      width: 84px;
      height: 84px;
      margin: 0 30px;
    }
    &__body {
      position: relative;
      flex: 1;
      padding-left: 30px;
      text-align: left;
      .title {
        color: #333333;
        font-weight: bold;
        font-size: 30px;
      }
      .desc {
        margin-top: 10px;
        color: #999999;
        font-weight: 500;
        font-size: 22px;
      }
      &::before {
        position: absolute;
        top: 50%;
        left: 0;
        width: $width-base;
        height: 70px;
        background: #ffdfbf;
        border-radius: $width-base;
        transform: translateY(-50%);
        content: '';
      }
    }
  }
  &-day {
    box-sizing: border-box;
    margin: 20px;
    padding: 20px;
    background: #ffffff;
    border-radius: 10px;
    &__box {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin: 10px 0;
    }
    &__block {
      display: flex;
      flex: 1;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 148px;
      margin-right: 20px;
      background: #fff3e6;
      border-radius: 10px;
      &:last-child {
        margin-right: 0;
      }
      .img {
        width: 30px;
        height: 30px;
      }
      .user {
        margin-left: 10px;
        font-weight: bold;
        font-size: 32px;
        &-vip {
          color: #5c0704;
        }
      }
      .desc {
        margin-top: 10px;
        color: #999999;
        font-weight: 500;
        font-size: 24px;
      }
    }
  }
  &-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
    .title {
      color: #333333;
      font-weight: bold;
      font-size: 32px;
    }
    .desc {
      color: #666666;
      font-weight: 500;
      font-size: 24px;
    }
    .img {
      display: block;
      width: 47px;
      height: 41px;
      margin-right: 10px;
    }
    .img2 {
      width: 36px;
      height: 36px;
      margin-right: 10px;
    }
  }
  &-discount {
    &__list {
      margin-top: 20px;
      margin-bottom: 40px;
    }
    &__item {
      display: flex;
      align-items: center;
      box-sizing: border-box;
      margin: 0 auto 20px;
      padding: 4px;
      background: #ffdfbf;
      border-radius: 10px;
    }
    &__item2 {
      width: 578px;
      height: 180px;
      margin: 0 auto 20px;
      background: #ffffff;
      border: 4px solid #ffdfbf;
      border-radius: 10px;
    }
    &__inner {
      display: flex;
      flex: 1;
      align-items: center;
      box-sizing: border-box;
      height: 180px;
      background: #ffffff;
      border-radius: 10px;
      .left {
        position: relative;
        width: 188px;
        text-align: center;
        &::after {
          position: absolute;
          top: 50%;
          right: 0;
          width: $width-base;
          height: 118px;
          background: #ffdfbf;
          transform: translateY(-50%);
          content: '';
        }
      }
      .price {
        display: flex;
        align-items: baseline;
        justify-content: center;
        color: #ff5624;
        font-weight: 800;
        font-size: 60px;
        .unit {
          font-size: 28px;
        }
      }
      .desc {
        color: #999999;
        font-weight: 500;
        font-size: 24px;
      }
      .right {
        padding-left: 20px;
      }
      .title {
        color: #5c0704;
        font-weight: 800;
        font-size: 36px;
      }
      .text {
        margin-top: 10px;
        color: #666666;
        font-weight: 500;
        font-size: 22px;
      }
      .tag {
        height: 32px;
        margin-top: 15px;
        padding: 0 20px;
        color: #ff4c4c;
        font-weight: 500;
        font-size: 18px;
        line-height: 32px;
        text-align: center;
        background: rgba(255, 245, 235, 0.06);
        border: $width-base solid #ff4c4c;
        border-radius: 15px;
      }
    }
    &__btn {
      width: 50px;
      padding-left: 4px;
      color: #6a1f01;
      font-weight: bold;
      font-size: 30px;
      text-align: center;
      &2 {
        color: #ff4c4c;
      }
    }
  }
  &-btnAll {
    position: relative;
    height: 88px;
    margin: 20px 40px;
    color: #ffffff;
    font-weight: bold;
    font-size: 34px;
    line-height: 88px;
    text-align: center;
    background: linear-gradient(to left, #ff5353, #ffa83f);
    border-radius: 44px;
    box-shadow: 0px 3px 14px 0px rgba(236, 36, 26, 0.18);
    .tag {
      position: absolute;
      top: -20px;
      right: 40px;
      width: 130px;
      height: 46px;
    }
  }
  &-openVip {
    box-sizing: border-box;
    height: 88px;
    margin: 30px 40px;
    padding: $width-base;
    color: #ff4c4c;
    font-weight: bold;
    font-size: 34px;
    background: linear-gradient(to left, #ff5353, #ffa83f);
    border: 2px solid;
    border-radius: 44px;
    box-shadow: 0px 3px 14px 0px rgba(236, 36, 26, 0.18);
    &-inner {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
      background: #fff;
      border-radius: 44px;
    }
  }
  &-notice {
    display: flex;
    margin: 20px 40px;
    color: #666;
    font-weight: 500;
    font-size: 24px;
    line-height: 42px;
  }
  &-block {
    width: 750px;
    height: 360px;
    margin: 0 auto;
  }
  // 引导分享弹窗
  &-guide {
    &-modal {
      width: 640px;
      height: 750px;
      padding: 0 !important;
      background: linear-gradient(0deg, #ffffff, #fcf2df);
      border-radius: 30px;
    }
    &-header {
      display: flex;
      align-items: center;
      justify-content: center;
      box-sizing: border-box;
      width: 420px;
      height: 66px;
      margin: 0 auto;
      background: #ffffff;
      border-radius: 0px 0px 20px 20px;
      .logo {
        width: 40px;
        height: 40px;
        margin-left: 10px;
        &-text {
          margin-bottom: 5px;
          margin-left: 10px;
          color: #333333;
          font-size: 27px;
        }
      }
      .close {
        margin: 0 10px 20px;
        color: #c5c5c5 !important;
        font-size: 18px !important;
      }
      .brand {
        width: 160px;
        height: 36px;
      }
    }
    &-text {
      margin-top: 50px;
      margin-bottom: 50px;
      color: #333333;
      font-size: 34px;
      font-style: italic;
      line-height: 60px;
      text-align: center;
      .num {
        padding: 0 5px;
        color: #f83e14;
        font-size: 48px;
      }
    }
    &-coupon {
      position: relative;
      width: 480px;
      height: 240px;
      margin: 0 auto;
      .img-bg {
        width: 100%;
        height: 100%;
      }
      .num {
        position: absolute;
        color: #f83e14;
        font-weight: bold;
      }
      .num1 {
        bottom: 70px;
        left: 42px;
        font-size: 42px;
        .unit {
          font-size: 16px;
        }
      }
      .num2 {
        top: 20px;
        right: 50px;
        font-size: 78px;
        .unit {
          font-size: 26px;
        }
      }
    }
    &-desc {
      margin: 20px;
      color: #666666;
      font-size: 26px;
      text-align: center;
    }
    &-footer {
      display: flex;
      justify-content: center;
      padding-top: 20px;
      .btn {
        box-sizing: border-box;
        width: 256px;
        height: 74px !important;
        margin: 0 !important;
        margin-right: 30px !important;
        padding: 0 !important;
        font-size: 36px;
        font-style: italic;
        line-height: 74px !important;
        border-radius: 40px;
        box-shadow: 0px 3px 14px 0px rgba(236, 36, 26, 0.18);
        &:last-child {
          margin-right: 0 !important;
        }
        &-send {
          padding: $width-base;
          color: #ff4c4c;
          background: linear-gradient(to left, #ff5353, #ffa83f);
          &-inner {
            background: #fff;
            border-radius: 40px;
          }
        }
        &-share {
          color: #ffffff;
          background: linear-gradient(to left, #ff5353, #ffa83f);
        }
      }
    }
  }
  // 被分享人弹窗
  &-share {
    &-modal {
      width: 640px;
      height: 770px;
      padding: 0 !important;
      background: linear-gradient(0deg, #ffffff, #fcf2df);
      border-radius: 30px;
    }
    &-header {
      display: flex;
      align-items: center;
      justify-content: center;
      box-sizing: border-box;
      width: 420px;
      height: 66px;
      margin: 0 auto;
      background: #ffffff;
      border-radius: 0px 0px 20px 20px;
      .logo {
        width: 40px;
        height: 40px;
        margin-left: 10px;
        &-text {
          margin-bottom: 5px;
          margin-left: 10px;
          color: #333333;
          font-size: 27px;
        }
      }
      .close {
        margin: 0 10px 20px;
        color: #c5c5c5 !important;
        font-size: 18px !important;
      }
      .brand {
        width: 160px;
        height: 36px;
      }
    }
    &-user {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding-top: 20px;
      .user-name {
        margin-top: 10px;
        color: #333333;
        font-size: 30px;
      }
    }
    &-text {
      margin-top: 10px;
      margin-bottom: 20px;
      color: #333333;
      font-size: 30px;
      font-style: italic;
      line-height: 60px;
      text-align: center;
      .num {
        padding: 0 5px;
        color: #f83e14;
      }
    }
    &-coupon {
      position: relative;
      width: 306px;
      height: 302px;
      margin: 0 auto;
      .img-bg {
        width: 100%;
        height: 100%;
      }
      .num {
        position: absolute;
        top: 40px;
        left: 50%;
        color: #f83e14;
        font-weight: bold;
        font-size: 82px;
        transform: translateX(-50%);
        .unit {
          font-size: 26px;
        }
      }
      .coupon-name {
        position: absolute;
        bottom: 45px;
        left: 50%;
        color: #fef8f2;
        font-weight: bold;
        font-size: 36px;
        white-space: nowrap;
        transform: translateX(-50%);
      }
    }
    &-footer {
      display: flex;
      justify-content: center;
      padding-top: 40px;
      .btn {
        box-sizing: border-box;
        width: 500px;
        height: 74px !important;
        margin: 0 !important;
        padding: 0 !important;
        font-size: 36px;
        font-style: italic;
        line-height: 74px !important;
        border-radius: 40px;
        box-shadow: 0px 3px 14px 0px rgba(236, 36, 26, 0.18);
        &-join {
          color: #ffffff;
          background: linear-gradient(to left, #ff5353, #ffa83f);
        }
      }
    }
  }
  // 膨胀成功弹窗
  &-shareSuccess {
    &-modal {
      width: 640px;
      height: 750px;
      padding: 0 !important;
      background: linear-gradient(0deg, #ffffff, #fcf2df);
      border-radius: 30px;
    }
    &-header {
      display: flex;
      align-items: center;
      justify-content: center;
      box-sizing: border-box;
      width: 420px;
      height: 66px;
      margin: 0 auto;
      background: #ffffff;
      border-radius: 0px 0px 20px 20px;
      .logo {
        width: 40px;
        height: 40px;
        margin-left: 10px;
        &-text {
          margin-bottom: 5px;
          margin-left: 10px;
          color: #333333;
          font-size: 27px;
        }
      }
      .close {
        margin: 0 10px 20px;
        color: #c5c5c5 !important;
        font-size: 18px !important;
      }
      .brand {
        width: 160px;
        height: 36px;
      }
    }
    &-text {
      margin-top: 50px;
      margin-bottom: 50px;
      color: #333333;
      font-size: 34px;
      font-style: italic;
      line-height: 60px;
      text-align: center;
      .num {
        padding: 0 5px;
        color: #f83e14;
        font-size: 48px;
      }
    }
    &-coupon {
      position: relative;
      width: 306px;
      height: 302px;
      margin: 0 auto;
      .img-bg {
        width: 100%;
        height: 100%;
      }
      .num {
        position: absolute;
        top: 40px;
        left: 50%;
        color: #f83e14;
        font-weight: bold;
        font-size: 82px;
        transform: translateX(-50%);
        .unit {
          font-size: 26px;
        }
      }
      .coupon-name {
        position: absolute;
        bottom: 45px;
        left: 50%;
        color: #fef8f2;
        font-weight: bold;
        font-size: 36px;
        white-space: nowrap;
        transform: translateX(-50%);
      }
    }
    &-footer {
      display: flex;
      justify-content: center;
      padding-top: 40px;
      .btn {
        box-sizing: border-box;
        width: 500px;
        height: 74px !important;
        margin: 0 !important;
        padding: 0 !important;
        font-size: 36px;
        font-style: italic;
        line-height: 74px !important;
        border-radius: 40px;
        box-shadow: 0px 3px 14px 0px rgba(236, 36, 26, 0.18);
        &-join {
          color: #ffffff;
          background: linear-gradient(to left, #ff5353, #ffa83f);
        }
      }
    }
  }
}

// 未领券挽留弹窗弹窗
.kb-guideReceiveCouponModal {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  &-tips {
    position: relative;
    width: 440px;
    height: 57px;
    margin-bottom: 40px;
    color: #ff4c4c;
    font-weight: bold;
    font-size: 26px;
    line-height: 57px;
    text-align: center;
    background: #fff4f4;
    border: $width-base solid #ff4c4c;
    border-radius: 50px;
    &::after {
      position: absolute;
      bottom: -12px;
      left: 50%;
      width: 20px;
      height: 20px;
      background: red;
      background: #fff4f4;
      border: $width-base solid #ff4c4c;
      border-top-color: #fff4f4;
      border-left-color: #fff4f4;
      transform: translateX(-50%) rotate(45deg);
      content: '';
    }
  }
  &-coupon {
    position: relative;
    box-sizing: border-box;
    width: 440px;
    height: 340px;
    &--img {
      width: 100%;
      height: 100%;
    }
  }
  &-footer {
    margin-top: 20px;
    .confirm {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 400px;
      height: 66px;
      margin: 0 auto;
      color: #ffffff;
      font-weight: 500;
      font-size: 30px;
      background: $color-brand;
      border-radius: 33px;
    }
    .cancel {
      margin-top: 30px;
      color: #999999;
      font-weight: 500;
      font-size: 26px;
      text-align: center;
    }
  }
}

// 领券未使用挽留弹窗
.kb-guideUseCouponModal {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  &-modal {
    padding: 50px 20px 20px;
  }
  &-tips {
    position: relative;
    display: flex;
    width: fit-content;
    height: 57px;
    margin: 0 auto 40px;
    padding: 0 40px;
    color: #fff;
    font-weight: 800;
    font-size: 26px;
    line-height: 57px;
    text-align: center;
    background: #ff4c4c;
    border: $width-base solid #ff4c4c;
    border-radius: 50px;
    &::after {
      position: absolute;
      bottom: -12px;
      left: 50%;
      width: 20px;
      height: 20px;
      background: #ff4c4c;
      transform: translateX(-50%) rotate(45deg);
      content: '';
    }
  }
  &-coupon {
    position: relative;
    box-sizing: border-box;
  }
  &-footer {
    margin-top: 20px;
    .confirm {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 400px;
      height: 66px;
      margin: 0 auto;
      color: #ffffff;
      font-weight: 500;
      font-size: 30px;
      background: $color-brand;
      border-radius: 33px;
    }
    .cancel {
      margin-top: 30px;
      color: #999999;
      font-weight: 500;
      font-size: 26px;
      text-align: center;
    }
  }
}
