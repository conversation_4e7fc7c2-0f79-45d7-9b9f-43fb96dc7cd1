/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { useState } from '@tarojs/taro';
import { useDidShowCom } from '@base/hooks/page';
import { retentionUserToLQZX } from '../_utls';

export const useGuideReceiveCoupon = () => {
  const [isOpened, setIsOpened] = useState(false);

  useDidShowCom(() => {
    const showLabel = Taro.kbGetGlobalData('showGuideReceiveCoupon');
    if (showLabel != 1 && retentionUserToLQZX('check')) {
      Taro.kbSetGlobalData('showGuideReceiveCoupon', 1);
      setIsOpened(true);
    } else if (showLabel == 1) {
      setIsOpened(false);
    }
  });

  const handelClick = (key) => {
    const close = () => {
      setIsOpened(false);
    };
    switch (key) {
      case 'close':
        close();
        break;
      case 'open':
        setIsOpened(true);
        break;
      case 'cancel':
        close();
        break;
      case 'confirm':
        Taro.navigator({
          url: 'order/kxj/center',
        });
        close();
        break;
    }
  };

  return {
    isOpened,
    handelClick,
  };
};
