import { Block, Image, Text, View } from '@tarojs/components';
import { formatLotteryDetail } from '@/components/_pages/welfare/_utils/welfare.lotterys';
import Taro from '@tarojs/taro';
import CountDown from './countdown';

import './index.scss';

const defaultData = {
  name: '',
  img: '',
  lottery_type: '0',
  custom: '',
  lLst: [{ avatar_url: '' }, { avatar_url: '' }, { avatar_url: '' }],
  lCount: '0',
  activityId: '0',
  money: '0',
  openPrizeTime: '00:00:00后开奖',
  whetherHasActivity: false,
  isLottery: false,
};

const WelfareEveryday = (props) => {
  const { list = [], getEveryDayLists = () => {}, disableClick = false } = props;

  const onCardHandle = ({ activityId, isLottery }) => {
    if (disableClick) {
      return;
    }
    Taro.navigateTo({
      url: `/pages-0/pages/welfare/lotterys/details?activityId=${activityId}&isLottery=${isLottery}`,
    });
  };

  return (
    <View className='kb-everyday'>
      {list.length == 0 ? (
        <View className='kb-everyday__desc' onClick={getEveryDayLists}>
          暂无数据，点我刷新
        </View>
      ) : (
        <View className='kb-everyday__package'>
          {list.map((item) => {
            const dataItem = formatLotteryDetail(defaultData, {
              ...item,
              currentTab: 'everyday',
              form_source: 'index',
            });
            const {
              lottery_time,
              lottery_type,
              money,
              rank_time,
              name = '',
              activityId,
              isLottery,
              img,
              source,
            } = dataItem;

            const nameFor = name.split('，')[1] || '';
            const caname = !!rank_time
              ? 'kb-everyday__package__itemsSign'
              : 'kb-everyday__package__items';

            return (
              <View
                key={item.id}
                className={caname}
                onClick={onCardHandle.bind(null, { activityId, isLottery })}
              >
                <View className={`${caname}__countdown`}>
                  <CountDown time={lottery_time} lottery_type={lottery_type} />
                </View>
                <Image src={img} className={`${caname}__image`} />
                {/* 如果自己设置图片，所有内容都不展示，只展示倒计时 */}
                {source == 'origin' && (
                  <Block>
                    {!!rank_time && <View className={`${caname}__name`}>{nameFor}</View>}
                    {!!rank_time && <View className={`${caname}__desc`}>待开奖</View>}
                    <View className={`${caname}__money`}>
                      <Text className={`${caname}__money__icon`}>¥</Text>
                      <Text>{money}</Text>
                    </View>
                  </Block>
                )}
              </View>
            );
          })}
        </View>
      )}
    </View>
  );
};

WelfareEveryday.addGlobalClass = true;

export default WelfareEveryday;
