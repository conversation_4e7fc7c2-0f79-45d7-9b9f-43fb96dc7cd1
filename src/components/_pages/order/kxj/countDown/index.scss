/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.countDown {
  display: flex;
  align-items: center;
  justify-content: center;
  padding-top: 35px;
  padding-bottom: 10px;
  color: #fc6500;
  font-size: 26px;
  &-block {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    margin: 0 10px;
    color: #fffbf7;
    font-size: 24px;
    background: linear-gradient(0deg, #fb4838, #f9935a);
    border-radius: 6px;
  }
}
