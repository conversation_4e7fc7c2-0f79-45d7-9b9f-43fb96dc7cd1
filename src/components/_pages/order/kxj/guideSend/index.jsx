/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { Image, View, Fragment } from '@tarojs/components';
import Taro, { useState } from '@tarojs/taro';
import { getStorage, setStorage } from '@base/utils/utils';
import { useUpdate } from '@base/hooks/page';
import { getActivityDetail } from '../_utils';
import './index.scss';

const GuideSend = (props) => {
  const { step = 1 } = props;
  const [isOpened, setIsOpened] = useState(false);

  useUpdate(() => {
    const checkOpen = (res) => {
      getActivityDetail().then((activityData) => {
        if (res && res.data && res.data.data == 1) {
          return;
        }
        const { is_receive, is_use } = activityData || {};
        if (is_receive == 1 && is_use != 1) {
          if (step == 1) {
            setIsOpened(true);
          } else if (step == 2) {
            const gStep = Taro.kbGetGlobalData('activity_guide_newCustomer');
            if (gStep && gStep == 1) {
              setIsOpened(true);
            }
          }
        }
      });
    };
    getStorage({
      key: 'activity_guide_newCustomer',
    })
      .then(checkOpen)
      .catch(checkOpen);
  });

  const handleClick = (key) => {
    if (key == 'cancel') {
      Taro.kbSetGlobalData('activity_guide_newCustomer', 2);
      setIsOpened(false);
      setStorage({
        key: 'activity_guide_newCustomer',
        data: 1,
      });
      return;
    }
    Taro.kbSetGlobalData('activity_guide_newCustomer', step);
    setIsOpened(false);
    if (step == 1) {
      Taro.navigator({
        url: 'user',
      });
    } else if (step == 2) {
      setStorage({
        key: 'activity_guide_newCustomer',
        data: 1,
      });
    }
  };

  return (
    <Fragment>
      {isOpened ? (
        <View className='guideSend'>
          {step == 2 ? (
            <Image
              className='guideSend-img02'
              mode='widthFix'
              src='https://cdn-img.kuaidihelp.com/wkd/miniApp/activity2/guide02.png?v=1'
              onClick={handleClick}
            />
          ) : (
            <View className='guideSend-img01'>
              <Image
                className='img'
                mode='widthFix'
                src='https://cdn-img.kuaidihelp.com/wkd/miniApp/activity2/guide01.png?v=1'
              />
              <View className='opt'>
                <View className='opt-btn opt-btn-cancel' onClick={() => handleClick('cancel')}>
                  跳过
                </View>
                <View className='opt-btn opt-btn-next' onClick={handleClick}>
                  下一步(1/2)
                </View>
              </View>
            </View>
          )}
        </View>
      ) : null}
    </Fragment>
  );
};

GuideSend.options = {
  addGlobalClass: true,
};

export default GuideSend;
