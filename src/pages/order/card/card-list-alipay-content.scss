/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb-alipay-coupon-item {
  display: flex;
  padding: $spacing-h-md;
  text-align: center;
  text-align: center;
  background: #ffffff;
  border-radius: 10rpx;
  &__container {
    margin-bottom: $spacing-h-md;
    border-radius: 16px;
    .disabled-reason {
      display: flex;
      align-items: center;
      height: 60px;
      padding-left: 10px;
      color: #ffffff;
      font-weight: 500;
      font-size: 22px;
    }
    &--disabled {
      padding: 0 10px 10px;
      color: #999 !important;
      background: #f8a3a3;
      .app-logo {
        filter: grayscale(100%);
      }
      .coupon-left,
      .coupon-right {
        background: #f5f5f5 !important;
      }
      .coupon-left-fee {
        color: #999 !important;
      }
      .coupon-right-btn {
        color: #fff !important;
        background: #999 !important;
      }
    }
  }
  &__app {
    position: relative;
    .app {
      &-logo {
        width: 66px;
        height: 66px;
      }
      &-name {
        position: absolute;
        top: 50px;
        left: 50%;
        width: 84rpx;
        height: 30rpx;
        font-size: 20rpx;
        line-height: 30px;
        background: #ffffff;
        border: 1px solid #e6e6e6;
        border-radius: 15rpx;
        transform: translateX(-50%);
      }
    }
  }
  &__coupon {
    flex: 1;
    margin-left: 30px;
    .coupon {
      &-app {
        margin-bottom: 5px;
        line-height: 45rpx;
        text-align: left;
      }
      &-box {
        display: flex;
      }
      &-left {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 148rpx;
        height: 148rpx;
        background: rgba(255, 76, 76, 0.06);
        border-right: 1px dashed #eee;
        border-radius: 16rpx;
        &-fee {
          color: #ff4c4c;
          font-size: 36px;
        }
        &-desc {
          margin-top: 5px;
          color: #999;
          font-size: 22rpx;
        }
      }
      &-right {
        display: flex;
        flex: 1;
        align-items: center;
        justify-content: space-between;
        box-sizing: border-box;
        height: 148rpx;
        padding: 0 20px;
        text-align: left;
        background: rgba(255, 76, 76, 0.06);
        border-radius: 16rpx;
        &-title {
          font-weight: bold;
          font-size: 32px;
        }
        &-desc {
          margin-top: 5px;
          color: #999999;
          font-size: 22px;
        }
        &-btn {
          width: 118rpx;
          height: 42rpx;
          color: #fff;
          font-size: 24rpx;
          line-height: 42px;
          text-align: center;
          background: #ff4c4c;
          border-radius: 21rpx;
        }
      }
    }
  }
}
