/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { pathUrlWithQuery } from '@/components/_pages/webview/_utils';
import { requestPayment } from '@/utils/qy';
import KbPage from '@base/components/page';
import Taro, { Component } from '@tarojs/taro';

class Index extends Component {
  config = {
    navigationBarTitleText: '支付',
  };

  constructor() {
    this.state = {};
  }

  mergeStatus = (status) => {
    const { src = `https://m.kuaidihelp.com/f/app-kdy/Recharge` } = this.$router.params;
    const srcDecode = decodeURIComponent(src);
    return pathUrlWithQuery(srcDecode, { status });
  };

  componentDidMount() {
    const { data } = this.$router.params;
    if (data) {
      const _data = JSON.parse(decodeURIComponent(data));
      requestPayment(_data)
        .then((res) => {
          console.log(res, 'requestPayment res', _data);
          Taro.navigator({
            url: this.mergeStatus('success'),
            target: 'webview-self',
            force: true,
          });
        })
        .catch((err) => {
          console.log(err, 'requestPayment err1', _data);
          Taro.navigator({
            url: this.mergeStatus('fail'),
            target: 'webview-self',
            force: true,
          });
        });
    }
  }

  render() {
    const { ...rest } = this.state;
    return <KbPage {...rest} />;
  }
}

export default Index;
