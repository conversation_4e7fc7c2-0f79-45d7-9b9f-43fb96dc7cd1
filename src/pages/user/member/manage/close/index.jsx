/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { Component } from '@tarojs/taro';
import { connect } from '@tarojs/redux';
import KbPage from '@base/components/page';
import { cancelAutoVip, vipRights } from '@/components/_pages/user/member/_uitls';
import { Image, Text, View } from '@tarojs/components';
import { AtButton } from 'taro-ui';
import './index.scss';

@connect(({ global }) => ({
  loginData: global.loginData,
}))
class Index extends Component {
  config = {
    navigationBarTitleText: '关闭自动续费',
  };

  constructor(props) {
    super(props);
    this.state = {
      userVipData: {},
    };
  }

  handleUpdate(data) {
    if (!data.logined) return;
  }

  // 数据回传
  onPostMessage = (type, e) => {
    const { params } = e || {};
    const { userVipData = {} } = params || {};
    switch (type) {
      case 'routerParamsChange':
        this.setState({
          userVipData,
        });
        break;
    }
  };

  handleContinue = () => {
    Taro.navigator();
  };

  handleClose = () => {
    const { userVipData } = this.state;
    const { name } = userVipData || {};
    cancelAutoVip().then((res) => {
      if (res) {
        setTimeout(() => {
          Taro.kbToast({
            text: `关闭${name}自动续费成功 `,
          });
        }, 1000);
        Taro.navigator({
          url: 'user/member',
          target: 'self',
        });
      }
    });
  };

  render() {
    const { userVipData, ...rest } = this.state;
    const { expires_time_format, total_order, total_profit, level_name } = userVipData || {};
    return (
      <KbPage
        {...rest}
        onUpdate={this.handleUpdate.bind(this)}
        renderFooter={
          <View className='kb-vipManageClose-footer'>
            <AtButton type='primary' circle onClick={this.handleContinue}>
              继续服务
            </AtButton>
            <AtButton className='btn-close' onClick={this.handleClose}>
              狠心关闭
            </AtButton>
          </View>
        }
      >
        <View className='kb-vipManageClose'>
          <View className='kb-vipManageClose-head'>
            <View className='kb-vipManageClose-head--bg' />
            <View className='kb-recordCard'>
              <View>
                <View className='kb-recordCard-cardName'>优享寄自动续费卡</View>
                <View className='kb-recordCard-body'>
                  <View>将于{expires_time_format}到期</View>
                </View>
              </View>
              <View className='kb-recordCard-footer'>
                <View className='desc'>
                  寄了<Text className='kb-color__white kb-size__lg'>{total_order}个</Text>
                  快递，累计节省运费
                  <Text className='kb-color__white kb-size__lg'>{total_profit}元</Text>
                </View>
              </View>
              <View className='kb-recordCard-cardType'>{level_name}</View>
            </View>
          </View>
          <View className='kb-vipManageClose-card'>
            <View className='kb-vipManageClose-card--desc'> - 您可能会失去以下特权 -</View>
            <View className='kb-vipManageClose-card--content'>
              {vipRights.map((item) => (
                <View className='right' key={item.icon}>
                  <View className='at-row at-row__align--center kb-spacing-md'>
                    <Image
                      className='right__icon'
                      mode='widthFix'
                      src={`https://cdn-img.kuaidihelp.com/wkd/miniApp/vip/vip_icon_${item.icon}.png`}
                    />
                    <View className='at-col'>
                      <View className='kb-size__base'>{item.name}</View>
                      <View className='kb-size__xs kb-color__grey'>{item.desc}</View>
                    </View>
                  </View>
                </View>
              ))}
            </View>
          </View>
        </View>
      </KbPage>
    );
  }
}

export default Index;
