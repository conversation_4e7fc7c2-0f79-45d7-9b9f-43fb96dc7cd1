/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { Image, Text, View } from '@tarojs/components';
import { PLATFORM_NAME } from '~/utils/config';
import classNames from 'classnames';
import { useAuthPrompt } from './_utils';
import './index.scss';

const AuthPrompt = (props) => {
  const { source } = props;
  const { isOpened, handleJumpToWkd } = useAuthPrompt(props);

  const rootCls = classNames('at-float-layout kb-authPrompt', {
    'at-float-layout--active': !!isOpened,
  });
  return (
    <View className={rootCls}>
      <View className='at-float-layout__overlay' />
      <View className='at-float-layout__container layout'>
        <View className='layout-body'>
          <View className='layout-body__content'>
            <View className='kb-authPrompt-container'>
              <View className='header'>
                <Image
                  className='bg-img'
                  mode='widthFix'
                  src='https://cdn-img.kuaidihelp.com/wkd/miniApp/svip/use-guide03.png'
                />
                <View className='title'>
                  <View>温馨提示</View>
                  <View className='title-tag'>『微快递专业版可使用』</View>
                </View>
              </View>
              <View className='content'>
                {/* 微快递引导 */}
                <View className='content-block'>
                  <View className='content-block-title'>
                    当前快递员已开通<Text className='kb-color__brand'>微快递专业版</Text>
                    寄件服务，全程免广告
                  </View>
                  <View className='card'>
                    <View className='card-left'>
                      <Image
                        className='avatar'
                        mode='widthFix'
                        src='https://cdn-img.kuaidihelp.com/wkd/miniApp/login_wkd.png'
                      />
                    </View>
                    <View className='card-body'>
                      <View className='title'>
                        微快递
                        <View className='title-tag'>专业版</View>
                      </View>
                      <View className='desc'>微快递{PLATFORM_NAME}小程序</View>
                    </View>
                    <View className='card-extra'>
                      <View className='btn' onClick={handleJumpToWkd} hoverClass='kb-hover-opacity'>
                        立即{source === 'edit' ? '寄件' : '查件'}
                      </View>
                    </View>
                  </View>
                </View>
              </View>
            </View>
          </View>
        </View>
      </View>
    </View>
  );
};

AuthPrompt.options = {
  addGlobalClass: true,
};

AuthPrompt.defaultProps = {
  source: 'edit', // edit 寄件; query 查件;
};

export default AuthPrompt;
