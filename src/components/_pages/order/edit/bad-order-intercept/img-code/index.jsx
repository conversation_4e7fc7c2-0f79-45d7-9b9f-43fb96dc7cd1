/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { useEffect, useScope } from '@tarojs/taro';
import { Canvas, View } from '@tarojs/components';
import './index.scss';

const KbImgCode = (props) => {
  const { width = 300, height = 50, onChange = () => {} } = props;
  const scope = useScope();

  // 生成4位随机数字
  const generateRandomNumber = () => {
    return Math.floor(1000 + Math.random() * 9000).toString();
  };

  // 生成随机颜色
  const getRandomColor = () => {
    const r = Math.floor(Math.random() * 256);
    const g = Math.floor(Math.random() * 256);
    const b = Math.floor(Math.random() * 256);
    return `rgb(${r},${g},${b})`;
  };

  // 绘制背景
  const drawBackground = (ctx) => {
    ctx.setFillStyle('#dcdcdc'); // 修改为更明显的背景色，如灰色
    ctx.fillRect(0, 0, 300, 100); // 绘制矩形背景
  };

  // 绘制干扰线
  const drawInterferenceLines = (ctx) => {
    for (let i = 0; i < 5; i++) {
      ctx.beginPath();
      ctx.moveTo(Math.random() * 300, Math.random() * 100); // 随机起点
      ctx.lineTo(Math.random() * 300, Math.random() * 100); // 随机终点
      ctx.setStrokeStyle(getRandomColor());
      ctx.stroke();
    }
  };

  // 绘制干扰点
  const drawInterferenceDots = (ctx) => {
    for (let i = 0; i < 50; i++) {
      ctx.beginPath();
      const x = Math.random() * 300;
      const y = Math.random() * 100;
      ctx.arc(x, y, 1.5, 0, 2 * Math.PI); // 增加干扰点半径至1.5
      ctx.setFillStyle(getRandomColor());
      ctx.fill();
    }
  };

  // 绘制数字
  const drawNumbers = (ctx, number, startX, fontSizes, spacings) => {
    for (let i = 0; i < number.length; i++) {
      ctx.save(); // 保存当前状态

      const fontSize = fontSizes[i]; // 使用之前生成的随机字体大小
      const y = Math.random() * 25 + 25; // 随机y位置，保证数字不超出画布
      const angle = (Math.random() * 30 * Math.PI) / 180; // 随机旋转角度（-30度到30度）

      ctx.setFontSize(fontSize); // 设置随机字体大小
      ctx.setFillStyle(getRandomColor()); // 随机字体颜色

      // 随机旋转
      ctx.translate(startX, y);
      ctx.rotate(angle);

      ctx.fillText(number[i], 0, 0); // 绘制数字
      ctx.restore(); // 恢复状态

      // 更新下一个数字的起始x坐标
      startX += fontSize + (i < number.length - 1 ? spacings[i] : 0);
    }
  };

  // 在 Canvas 上绘制数字，增加干扰
  const drawCaptcha = (number) => {
    const ctx = Taro.createCanvasContext('captchaCanvas', scope);

    // 计算每个数字的字体大小和间隔
    const fontSizes = [];
    const spacings = [];
    let totalWidth = 0;

    for (let i = 0; i < number.length; i++) {
      const fontSize = Math.floor(Math.random() * 25 + 20); // 随机字体大小，范围40-60
      const spacing = Math.random() * (fontSize * 0.5) - fontSize * 0.3; // 间隔可以为负，允许1/3的重叠

      fontSizes.push(fontSize);
      spacings.push(spacing);
      totalWidth += fontSize + (i < number.length - 1 ? spacing : 0); // 计算总宽度（最后一个数字不需要间隔）
    }

    // 计算居中起点
    const canvasWidth = width; // 假设画布宽度为300
    let startX = (canvasWidth - totalWidth) / 2; // 计算居中起点

    // 绘制背景
    drawBackground(ctx);

    // 绘制干扰线
    drawInterferenceLines(ctx);

    // 绘制干扰点
    drawInterferenceDots(ctx);

    // 绘制数字
    drawNumbers(ctx, number, startX, fontSizes, spacings);

    ctx.draw();
  };

  const generateAndDraw = () => {
    const randomNumber = generateRandomNumber();
    drawCaptcha(randomNumber);
    if (onChange) {
      onChange(randomNumber);
    }
  };

  useEffect(() => {
    generateAndDraw();
  }, []);

  const handleClick = () => {
    generateAndDraw();
  };

  return (
    <View className='kb-imgCode' onClick={handleClick}>
      <Canvas
        canvasId='captchaCanvas'
        style={{
          width: width + 'px',
          height: height + 'px',
        }}
      />
    </View>
  );
};

KbImgCode.options = {
  addGlobalClass: true,
};

KbImgCode.defaultProps = {
  width: 300,
  height: 50,
  onChange: () => {},
};

export default KbImgCode;
