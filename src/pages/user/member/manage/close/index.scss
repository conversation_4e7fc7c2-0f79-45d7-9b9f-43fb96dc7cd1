/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb-vipManageClose {
  &-head {
    height: 340px;
    &--bg {
      height: 180px;
      background: $color-brand;
      border-radius: 0 0 80px 80px;
    }
    .kb-recordCard {
      position: relative;
      box-sizing: border-box;
      width: 710px;
      height: 312px;
      margin: -150px auto;
      padding: 30px;
      color: #d7bf9e;
      background: linear-gradient(90deg, #3e4055 0%, #5c616d 100%);
      border-radius: 20px;
      &-cardName {
        color: #fafafa;
        font-weight: 800;
        font-size: 45px;
      }
      &-body {
        display: flex;
        margin-top: 10px;
        font-size: 24px;
      }
      &-footer {
        position: absolute;
        right: 0;
        bottom: 0;
        left: 0;
        display: flex;
        align-items: center;
        height: 88px;
        padding: 0 30px;
        color: #fff1df;
        background: #63646d;
        border-radius: 0 0 20px 20px;
        .price {
          display: flex;
          align-items: baseline;
        }
        .money {
          font-size: 36px;
        }
        .original {
          margin-left: 20px;
          color: #999;
          font-size: 24px;
          text-decoration: line-through;
        }
        .desc {
          color: #d7bf9e;
          font-size: 26px;
          text-align: center;
        }
      }
      &-cardType {
        position: absolute;
        top: 25px;
        right: 40px;
        color: #faddb1;
        font-weight: 800;
        font-size: 42px;
        font-style: italic;
      }
    }
  }
  &-card {
    margin: 20px;
    padding: $spacing-h-md;
    background: #fff;
    border-radius: 10px;
    &--desc {
      color: #666666;
      font-size: 26px;
      text-align: center;
    }
    &--content {
      display: flex;
      flex-wrap: wrap;
      margin: $spacing-h-md 0;
      padding: $spacing-h-lg 0;
      background: #f2faff;
      border-radius: 8px;
      .right {
        width: 50%;
        &__icon {
          width: 60px;
          height: 60px;
          margin-right: $spacing-h-md;
        }
      }
    }
  }
  &-footer {
    padding: $spacing-h-md $spacing-h-md 0;
    text-align: center;
    background: #fff;
    .btn-close {
      color: #999;
      font-size: 26px;
      border: none !important;
    }
  }
}
