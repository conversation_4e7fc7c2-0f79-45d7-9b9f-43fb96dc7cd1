import Taro, { useDidShow, useEffect, useRef, useScope } from '@tarojs/taro';
import { getPage } from '@base/utils/utils';
import debounce from 'lodash/debounce';
import qs from 'qs';
import { homePage } from '@/utils/config';
import { checkIsTabPage, createCompletePath } from '@base/utils/navigator';

// 跳转二级内页的tabs
export const secondaryTabs = ['welfare/continuity', 'order/kxj'];

// 跳转内页
const triggerNavigatorDebounce = debounce(
  (pathname, options) => {
    const query = qs.stringify(options);
    const url = !query ? pathname : `${pathname}?${query}`;
    Taro.navigateTo({
      url,
    });
  },
  500,
  { leading: true, trailing: false },
);

/**
 *
 * @description 记录tab页面路径，（需要判断是否为tab页）
 */
let tabPathname = '';
const recordTabPathnameDebounce = debounce(
  () => {
    // 不允许记录的tab
    const {
      $router: { path },
    } = getPage();
    // 仅当该页面是tab页时记录
    if (checkIsTabPage(path, (item) => !secondaryTabs.includes(item))) {
      tabPathname = path;
    }
  },
  500,
  { leading: true, trailing: false },
);

/**
 *
 * @description 记录当前tab页面路径
 */
export function useRecordTabPathname() {
  useEffect(() => {
    recordTabPathnameDebounce();
  }, []);
  useDidShow(() => {
    recordTabPathnameDebounce();
  });
}

/**
 *
 * @description tab二级跳转，主要用于将减少主包大小，将tab页放入二级页面，保留空白tab，应自动引导跳转；
 */
export function useTabSecondary({ pathname }) {
  const $scope = useScope();
  const actionRef = useRef({
    isAway: false,
    canUseSelf: null,
  });

  //   触发跳转
  const triggerNavigator = () => {
    actionRef.current.isAway = true;
    const { options } = $scope;
    triggerNavigatorDebounce(pathname, options);
  };

  useEffect(() => {
    triggerNavigator();
  }, []);

  useDidShow(() => {
    if (actionRef.current.isAway) {
      // 已经离开过，onShow后返回首页
      actionRef.current.isAway = false;
      Taro.switchTab({
        url: tabPathname || createCompletePath(homePage),
      });
    } else {
      triggerNavigator();
    }
  });
}
