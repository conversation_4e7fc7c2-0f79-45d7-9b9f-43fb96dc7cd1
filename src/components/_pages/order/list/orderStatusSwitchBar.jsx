/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { Fragment, useCallback, useMemo } from '@tarojs/taro';
import { Button, View } from '@tarojs/components';
import classNames from 'classnames';
import isFunction from 'lodash/isFunction';
import debounce from 'lodash/debounce';
import { createOrderTabsItems } from './_utils/orderStatusSwitchBar';
import './orderStatusSwitchBar.scss';

const OrderStatusSwitchBar = (props) => {
  const { onChange, value, type, itemType, loading } = props;
  const items = useMemo(() => createOrderTabsItems(type, itemType), [type, itemType]);

  const triggerChangeDebounce = useCallback(
    debounce(
      (v) => {
        if (isFunction(onChange)) {
          onChange(v);
        }
      },
      500,
      {
        leading: true,
        trailing: false,
      },
    ),
    [],
  );

  // 切换
  const handleClickBars = (v) => {
    if (loading) return;
    triggerChangeDebounce(v);
  };

  return (
    <Fragment>
      {items ? (
        <View className='kb-order-tabItems'>
          {items.map((item) => (
            <View key={item.key} className='kb-order-tabItems__item'>
              <Button
                size='mini'
                circle
                className={classNames('kb-order-tabItems__item--btn', {
                  'kb-order-tabItems__item--btn__active': item.key === value,
                })}
                onClick={() => handleClickBars(item.key)}
              >
                {item.label}
              </Button>
            </View>
          ))}
        </View>
      ) : null}
    </Fragment>
  );
};

OrderStatusSwitchBar.options = {
  addGlobalClass: true,
};

export default OrderStatusSwitchBar;
