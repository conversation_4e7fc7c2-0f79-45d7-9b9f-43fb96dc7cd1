/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb-ad-float {
  position: fixed;
  z-index: 999;

  transition: all 0.3s;

  &__image {
    max-width: 200px !important;
    max-height: 200px !important;
  }

  &__close {
    position: absolute;
    top: -40px;
    right: -15px;
    padding: 15px;

    .kb-icon-cancel {
      font-size: 25px;
      color: $color-grey-3;
      width: 25px;
      height: 25px;
      background-color: $color-white;
      border-radius: 50%;
      display: block;
    }
  }

  &__collapsed {
    transform: translateX(60%);
    opacity: 0.7;
  }

  // &__guid {
  //   display: flex;
  //   max-width: 200px !important;
  //   max-height: 200px !important;
  // }

  &__placement {

    &--rt,
    &--rb {
      right: $spacing-h-md;
    }

    &--lt,
    &--lb {
      left: $spacing-h-md;
    }

    &--lt,
    &--rt {
      top: 360px;
    }

    &--rb,
    &--lb {
      bottom: 360px;
    }
  }
}