/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { Image, View, Text } from '@tarojs/components';
import Taro, {
  Fragment,
  useCallback,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from '@tarojs/taro';
import isArray from 'lodash/isArray';
import debounce from 'lodash/debounce';
import classNames from 'classnames';
import { useDidShowCom } from '~base/hooks/page';
import { reportAnalyticsUnify } from '~base/utils/utils';
import { adNavigator, loadAdminAd } from '../sdk';
import { useCheckIsShieldAd } from '../sdk/shield';
import './float.scss';

const AdUnitIds =
  process.env.PLATFORM_ENV === 'weapp'
    ? {
      'query.detail': 'adunit-efef225f05c47874',
    }
    : {};

/**
 *
 * @description  微信官方广告组件位/自定义广告位 - 浮动;
 * @param {{position?:string;adType?:string;closeShield?:boolean;placement?:'rt'|'rb'|'lt'|'lb';closeable?:boolean;}} props
 * @returns
 */
const KbAdFloat = (props) => {
  const {
    adType,
    position,
    closeShield,
    placement = 'rt',
    closeable = true,
    actionRef: actionRefProps,
  } = props || {};
  const unitId = AdUnitIds[adType];
  const [imgUrl, updateImgUrl] = useState('');
  const [size, updateSize] = useState({});
  const actionRef = useRef(); //当前广告数据
  const actionRefAds = useRef(); //全部广告数据
  const refreshRef = useRef(false); //刷新标志

  useEffect(() => {
    if (!!adType || !position) return;
    const params = position ? [position] : [{ type: 'record' }, { type: '1' }];
    loadAdminAd(...params).then((adRes) => {
      const list = isArray(adRes) ? adRes : adRes.list;
      if (isArray(list) && list.length > 0) {
        actionRefAds.current = {
          list,
          cIndex: 0,
        };
        getCurrentAd();
      }
    });
  }, [adType, position]);

  useDidShowCom(() => {
    if (refreshRef.current) {
      getCurrentAd('switch');
      refreshRef.current = false;
    }
  });

  const getCurrentAd = (action = 'init') => {
    let { list, cIndex } = actionRefAds.current;
    if (action == 'switch' && list.length <= 1) return;
    if (cIndex > list.length - 1) {
      cIndex = 0;
    }
    const data = list[cIndex];
    actionRef.current = data;
    const { imgUrl, title } = data || {};
    if (imgUrl) {
      reportAnalyticsUnify({
        title,
        position: position || adType,
        action: 'ad-switch',
      });
      updateImgUrl(imgUrl);
    }
    actionRefAds.current.cIndex = cIndex + 1;
  };

  const handleImageLoad = (e) => {
    const {
      detail: { width, height },
    } = e;
    if (width && height) {
      updateSize({ width: `${width}px`, height: `${height}px` });
    }
  };

  // 关闭广告
  const handleClose = () => updateImgUrl('');

  // 点击广告
  const handleClickAd = () => {
    adNavigator(actionRef.current);
    refreshRef.current = true;
  };

  // 官方流量主广告展示统计
  const onAdLoad = () => {
    reportAnalyticsUnify({
      title: unitId,
      action: 'load',
    });
  };

  const shield1 = useCheckIsShieldAd(closeShield ? '-1' : '1'); // 是否屏蔽流量主广告
  const hasAd = !shield1 && (imgUrl || unitId);

  const [collapsed, setCollapsed] = useState(false);
  const [isOpened, setIsOpened] = useState(true);
  const rootCls = classNames('kb-ad-float', `kb-ad-float__placement--${placement}`, {
    'kb-ad-float__collapsed': collapsed,
  });

  const setCollapsedDebounce = useCallback(
    debounce(
      () => {
        setCollapsed(false);
      },
      300,
      { leading: false, trailing: true },
    ),
    [],
  );

  // Tower 任务: 支付宝-我的快递浮窗展示优化 ( https://tower.im/teams/258300/todos/108065 )
  useImperativeHandle(actionRefProps, () => ({
    onScroll: () => {
      setCollapsed(true);
      setCollapsedDebounce();
    },
    onOpenChange: (o) => {
      setIsOpened(o);
    },
  }));

  return hasAd && isOpened ? (
    <View className={rootCls}>
      {!!imgUrl && (
        <Fragment>
          <View hoverClass='kb-hover-opacity' onClick={handleClickAd}>
            <Image
              lazyLoad
              className='kb-ad-float__image'
              mode='widthFix'
              src={imgUrl}
              onLoad={handleImageLoad}
              style={size}
            />
          </View>
          {closeable && (
            <View
              className='kb-ad-float__close'
              hoverClass='kb-hover-opacity'
              onClick={handleClose}
            >
              <Text className='kb-icon kb-icon-cancel' />
            </View>
          )}
        </Fragment>
      )}
      {!!unitId && (
        <View hoverClass='kb-hover-opacity' className='kb-ad-float__guid'>
          <ad-custom unitId={unitId} style='height:auto;' onLoad={onAdLoad} />
        </View>
      )}
    </View>
  ) : (
    <Fragment />
  );
};
KbAdFloat.options = {
  addGlobalClass: true,
};
export default KbAdFloat;
