/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro from '@tarojs/taro';
import { getAdConfig } from '@/components/_pages/ad-extension/_utils';
import { adNavigator } from '@/components/_pages/ad-extension/sdk';
import { getPage, now, frequencyLimitByMinute } from '@base/utils/utils';
import { getUserVipInfo, getVipConfigList } from '@/components/_pages/user/_utils';
import { createInterstitialAd } from '@/components/_pages/ad-extension/ad/intersitialAd';
import { addReceiveCoupon, createKxjCouponListApiAndData } from '../_utils';
import { orderAction } from '../../_utils';

export const handleAdClick = (item) => {
  adNavigator(item);
};

// 拉取领券中心广告
export const getCouponCenterAds = () => {
  const _this = getPage();
  getAdConfig(
    {
      position: 23,
    },
    true,
  ).then((list) => {
    _this.setState({
      adList: list,
    });
  });
};

// 处理活动规则弹窗逻辑
export const handleRules = (key) => {
  const _this = getPage();
  switch (key) {
    case 'open':
      _this.setState({
        isOpened: true,
      });
      break;
    case 'close':
      _this.setState({
        isOpened: false,
      });
      break;
  }
};

export const handleVip = () => {
  Taro.navigator({
    url: 'user/member',
  });
};

export const activityRulesList = [
  '通过“微快递"平台指定活动专属入口内参与，可领取优惠券；',
  '本券为平台补贴券，限在微快递支付宝小程序寄快递线上开通芝麻分“先享后付”支付时使用，扫码下单或给专属快递员下单时不可用；',
  '货物超出限重无法使用优惠券，成功下单将按照官方无折扣原价计费扣款；',
  '活动合作折扣快递公司为京东、韵达、圆通、德邦、申通、菜鸟等，此优惠券适用产品以优惠券上标注的使用规则为准。',
  '此优惠券每次寄件限用一张，且只能用于寄件人付款的邮件，多张优惠券不可叠加使用。',
  '此优惠券上标有优惠券有效期，可前往我的优惠券查看。过期失效不予补偿，请尽快使用。',
  '如有退件，优惠券金额不予退还。此活动优惠，不可与其他活动优惠同享；',
  '暂不支持港澳台，以及海外寄件参与活动优惠；',
  '活动最终解释权归微快递所有！',
];

export const getUserVipInfoData = () => {
  const _this = getPage();
  getUserVipInfo().then((userVipData) => {
    if (userVipData && userVipData.status == 1) {
      getVipConfigList().then(({ code, data }) => {
        let card = {};
        if (code == 0 && data.length > 0) {
          card = data.filter((item) => item.id == userVipData.card_id)[0];
        }
        _this.setState({
          userVipData: {
            ...card,
            ...userVipData,
          },
        });
      });
    } else {
      _this.setState({
        userVipData,
      });
    }
  });
};

export const formatLQZXCoupon = (list = []) => {
  return list.map((item) => {
    const { discount_type, brand } = item || {};
    let subtract_key = [];
    if (discount_type === 'subtract') {
      subtract_key.push(1);
      if (['yd', 'cnsd'].includes(brand)) {
        // 时效高
        subtract_key.push(2);
      }
      if (['yd', 'yt'].includes(brand)) {
        // 价格优
        subtract_key.push(3);
      }
    }
    return {
      ...item,
      subtract_key,
    };
  });
};

export const createListData = () => {
  const _this = getPage();
  return {
    api: {
      ...createKxjCouponListApiAndData(),
      data: {
        activity: 'ali_coupon_center',
      },
      onThen: (list) => {
        _this.setState({
          list: formatLQZXCoupon(list),
        });
      },
    },
  };
};

export const createSubtractBars = () => {
  return [
    { key: '1', label: '推荐' },
    { key: '2', label: '时效高' },
    { key: '3', label: '价格优' },
  ];
};

export const handleSubtractBarClick = (item) => {
  const _this = getPage();
  _this.setState({
    currentSubtractBar: item.key,
  });
};

export const createCouponList = (list = [], opt) => {
  const { type = '', key = 1 } = opt || {};
  if (!list || list.length <= 0) {
    return [];
  }
  if (type === 'subtract') {
    return list.filter((item) => item.discount_type == type && item.subtract_key.includes(key * 1));
  } else {
    return list.filter((item) => item.discount_type == type);
  }
};

export const handleBarClick = (action = 'one', item = {}) => {
  const _this = getPage();
  const { userVipData = {}, list = [] } = _this.state;
  const isVip = userVipData.status == 1;
  const batch = !(action === 'one');
  if (action === 'edit') {
    orderAction({
      action: 'edit',
      data: {
        relation: { brand: item.brand },
      },
    });
    return;
  }
  // 领券限制
  if (isVip) {
    if (batch) {
      let discount_type = action === 'allSubtract' ? 'subtract' : 'discount';
      const isNotReceive = list
        .filter((item) => item.discount_type == discount_type)
        .every((item) => item.is_receive == 1);
      if (isNotReceive) {
        Taro.kbToast({
          text: '超出领取限制，请明日再来领券',
        });
        return;
      }
    }
  } else {
    // 非会员
    if (batch) {
      Taro.kbToast({
        text: '开通会员后，可领取全部优惠！',
      });
      return;
    }
  }
  // 参数处理
  const params = {
    activity: 'ali_coupon_center',
    coupon_id: item.card_id || '',
    rate_type: action == 'allSubtract' ? 'subtract' : action == 'allDiscount' ? 'discount' : '',
  };
  addReceiveCoupon(params).then((res) => {
    if (res.code == 0) {
      Taro.kbSetGlobalData('showGuideReceiveCoupon', 1);
      _this.listIns.loader();
      Taro.kbToast({
        text: `${batch ? '已为您领取全部优惠券，' : '领取成功,'}可前往“我的优惠券”查看`,
      });
    }
  });
};

// 挽留用户去领券中心领券
export const retentionUserToLQZX = (action) => {
  const key = 'retentionUserToLQZX';
  const _this = getPage();
  if (action === 'add') {
    const { list = [] } = _this.state;
    const isNotReceive = list.every((i) => i.is_receive != 1);
    if (isNotReceive) {
      Taro.kbSetGlobalData(key, now());
    }
  } else if (action === 'check') {
    const lastClickTs = Taro.kbGetGlobalData(key) || 0;
    const n = (now() - lastClickTs * 1) / 1000;
    return n <= 1;
  }
};

export const showXLightAd = () => {
  const key = 'kxj_center_xLight_cp';
  // 全屏广告
  createInterstitialAd({
    adUnitId: '50_2024030625000084365',
    onClose: () => {
      // 插屏广告
      frequencyLimitByMinute('check', key, 30).then((limit) => {
        console.log('展示广告-limit', limit);
        if (!limit) {
          createInterstitialAd({
            adUnitId: '50_2024030625000084364',
          }).then((interstitialAd) => {
            if (interstitialAd && interstitialAd.openAd) {
              interstitialAd.openAd().then(() => {
                frequencyLimitByMinute('limit', key);
              });
            }
          });
        }
      });
    },
  }).then((interstitialAd) => {
    if (interstitialAd && interstitialAd.openAd) {
      interstitialAd.openAd();
    }
  });
};
