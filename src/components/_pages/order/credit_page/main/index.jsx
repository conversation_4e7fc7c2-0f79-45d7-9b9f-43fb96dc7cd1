import KbLoader from '@base/components/loader';
import { View } from '@tarojs/components';
import { Fragment } from '@tarojs/taro';
import { connect } from '@tarojs/redux';
import EstimatedListItem from '../../estimated-fee/list/item';
import { useCreditList } from '../_utils/useCreditList';
import KbCreditComponent from '../../credit/credit-label';
import ChooseCoupon from '../../kxj/businessCoupon/chooseCoupon';
import './index.scss';

const CreditMain = (props) => {
  const { isOpenCredit, relation, product_code, initPage } = props;
  const { brand } = relation;

  const {
    isShowInline,
    formatList,
    quotationList = [],
    loading,
    welfare,
    handleChange,
    onJumpToWelfare,
    onChooseBrand,
    onNavigatorDetail,
    checkIsReturn,
    curQuotationItem,
    activityDetail,
    alipayActivityCoupon,
    activityCouponInfo,
  } = useCreditList(props);

  const underlineStyle = { justifyContent: 'flex-end', height: '100%' };

  return (
    <Fragment>
      {formatList && formatList.length > 0 && (
        <Fragment>
          <View className='kb-creditMain__content' style={isShowInline ? {} : underlineStyle}>
            <ChooseCoupon
              alipayActivityCoupon={alipayActivityCoupon}
              quotation={curQuotationItem}
              activityDetail={activityDetail}
              isReturnModule={checkIsReturn}
              activityCouponInfo={activityCouponInfo}
              noNavigator
            />
            {process.env.PLATFORM_ENV !== 'swan' && (
              <View className='kb-estimatedFeeList-list--credit'>
                <KbCreditComponent open={isOpenCredit} type='door' />
              </View>
            )}
            {formatList.map((item) => (
              <EstimatedListItem
                key={item.brand}
                item={item}
                isOpenCredit={isOpenCredit}
                brand={brand}
                loading={loading}
                product_code={product_code}
                welfare={welfare}
                handleChange={handleChange}
                onJumpToWelfare={onJumpToWelfare}
                onChooseBrand={onChooseBrand}
                onNavigatorDetail={onNavigatorDetail}
                alipayActivityCoupon={alipayActivityCoupon}
              />
            ))}
          </View>
          <View style='height:10px;' />
        </Fragment>
      )}
      {((loading && !quotationList.length) || !initPage) && (
        <View className='kb-creditMain__loader'>
          <KbLoader />
        </View>
      )}
    </Fragment>
  );
};

CreditMain.options = {
  addGlobalClass: true,
};

export default connect(({ global }) => ({
  cacheRelationInfo: global.relationInfo,
}))(CreditMain);
