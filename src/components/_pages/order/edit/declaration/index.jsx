/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { useState, useEffect } from '@tarojs/taro';
import { View } from '@tarojs/components';
import KbModal from '~base/components/modal';
import KbCheckbox from '~base/components/checkbox';
import { debounce } from '~base/utils/utils';
import './index.scss';

const KbDeclarationAgreement = (props) => {
  const { declaration, open, agree, onSwitchAgree = () => {}, onConfirm = () => {} } = props;
  const [isOpened, setIsOpened] = useState(false);

  useEffect(() => {
    setIsOpened(!!open);
  }, [open]);

  const handleSwitchAgree = () => {
    onSwitchAgree(!agree);
  };
  const handleCancel = () => {
    setIsOpened(false);
  };
  const handleConfirm = debounce(() => {
    setIsOpened(false);
    onSwitchAgree(true);
    onConfirm();
  }, 1000);

  return (
    <View className='kb-declaration-agreement'>
      <KbCheckbox
        label=''
        checked={agree}
        onChange={handleSwitchAgree}
        className='kb-color__black'
      />
      <View className='kb-declaration-agreement__content' onClick={handleSwitchAgree}>
        我已阅读并同意快递员声明：{declaration}
      </View>
      <KbModal
        isOpened={isOpened}
        title='快递员免责声明'
        top={false}
        onCancel={handleCancel}
        onClose={handleCancel}
        onConfirm={handleConfirm}
        closable={false}
        footerLayout='column'
        cancelText='取消'
        confirmText='同意并继续提交'
      >
        <View>{declaration}</View>
      </KbModal>
    </View>
  );
};

KbDeclarationAgreement.options = {
  addGlobalClass: true,
};

export default KbDeclarationAgreement;
