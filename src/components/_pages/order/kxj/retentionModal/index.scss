/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb-guideCouponModal {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  &-tips {
    position: relative;
    width: 440px;
    height: 57px;
    margin-bottom: 40px;
    color: #ff4c4c;
    font-size: 26px;
    line-height: 57px;
    text-align: center;
    background: #fff4f4;
    border: $width-base solid #ff4c4c;
    border-radius: 50px;
    &::after {
      position: absolute;
      bottom: -12px;
      left: 50%;
      width: 20px;
      height: 20px;
      background: red;
      background: #fff4f4;
      border: $width-base solid #ff4c4c;
      border-top-color: #fff4f4;
      border-left-color: #fff4f4;
      transform: translateX(-50%) rotate(45deg);
      content: '';
    }
  }
  &-coupon {
    position: relative;
  }
  &-img {
    width: 260px;
    height: 260px;
    margin-bottom: 20px;
  }
  &-fee {
    position: absolute;
    top: 40px;
    left: 50%;
    display: flex;
    align-items: baseline;
    justify-content: center;
    color: #868686;
    transform: translate(-50%);
    .fee {
      font-size: 60px;
    }
  }
  &-name {
    position: absolute;
    bottom: 60px;
    left: 50%;
    display: flex;
    align-items: baseline;
    justify-content: center;
    width: 280px;
    color: #fff;
    transform: translate(-50%);
  }
}
