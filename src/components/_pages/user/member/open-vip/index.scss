/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb-vipPayModal {
  &-title {
    display: flex;
    flex-direction: column;
    justify-content: center;
    height: 156px;
    text-align: center;
    background: #ebf9ff;
    border-radius: 20px 20px 0px 0px;
    .title {
      color: #333333;
      font-weight: bold;
      font-size: 36px;
    }
    .phone {
      color: $color-brand;
      font-size: 30px;
    }
  }
  &-list {
    padding: 30px 40px;
  }
  &-item {
    position: relative;
    margin-bottom: 20px;
    padding-left: 20px;
    color: #666;
    font-size: 28px;
    &::before {
      position: absolute;
      top: 0;
      left: 0;
      color: #ff4c4c;
      content: '*';
    }
  }
  &-footer {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }
  &-confirm {
    width: 400px;
    height: 66px;
    color: #ffffff;
    line-height: 66px;
    text-align: center;
    background: $color-brand;
    border-radius: 33px;
  }
  &-cancel {
    margin-top: 20px;
    color: #999;
    font-size: 26px;
  }
}
