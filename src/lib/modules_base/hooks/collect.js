import Taro, { useEffect, useRef } from '@tarojs/taro';
import isFunction from 'lodash/isFunction';

export function useCheckIsCollected(callback) {
  const ref = useRef({ count: 0 });

  const stop = () => {
    clearTimeout(ref.current.timer);
  };

  const run = async (status = 'init') => {
    stop();
    const { isCollected } = await Taro.isCollected();
    if (isFunction(callback)) {
      callback({ isCollected, status });
    }
    if (!isCollected) {
      ref.current.timer = setTimeout(() => run('reGet'), 1000);
    }
    ref.current.count++;
  };

  useEffect(() => {
    return () => {
      stop();
    };
  }, []);

  return {
    run,
  };
}
