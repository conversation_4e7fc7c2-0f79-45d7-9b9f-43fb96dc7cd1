/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import apis from '@/utils/apis';
import request from '@base/utils/request';
import { dateCalendar, extractData, removeStorageSync } from '@base/utils/utils';
import Taro from '@tarojs/taro';
import { lastEditOrderTypeKey } from '../edit/_utils/home';

/**
 *
 * @description 更新标题
 * @param {string} title
 */
export const setNavigationBarTitle = (data, brands) => {
  const names = [];
  const { account_company, account_name } = data;
  const { short_name = '', name: brandName } = brands[account_company] || {};
  short_name && names.push(short_name);
  account_name && names.push(account_name);
  const title = names.join(' - ');
  Taro.setNavigationBarTitle({ title });
  return { brandName, title };
};
/**
 *
 * @description 格式化快递员详情响应数据
 * @param {object} data
 */
export const formatResponseCourierDetail = (data) => {
  const { focusStatus, payStatus, courier_id, service_count_sum, service_count, last_login_time } =
    data;
  if (courier_id) {
    let { last_online, is_online = false } = last_login_time || {};
    let service_count_msg = '';
    const rebateTips = [];
    last_online = last_online && !is_online ? dateCalendar(last_online, { timer: true }) : '';
    // 揽件信息
    if (service_count_sum > 100) {
      service_count_msg = '很多';
    } else if (service_count_sum <= 10) {
      service_count_msg = '较少';
    } else {
      service_count_msg = '正常';
    }
    // 返券提醒
    if (process.env.PLATFORM_ENV !== 'swan') {
      focusStatus > 0 && rebateTips.push('首次关注');
      payStatus > 0 && rebateTips.push('下单在线支付');
    }
    if (process.env.MODE_ENV !== 'wkd') {
      const { phone, shop, name, ...rest } = data;
      data = {
        ...rest,
        account_phone: phone,
        account_shop: shop,
        account_name: name,
      };
    }
    return {
      data: {
        ...data,
        rebateTips: rebateTips.length ? rebateTips.join('/') + '返券' : '',
        service_count_msg,
        last_online,
        is_online,
        service_count: 1 * (service_count || 0),
      },
    };
  }
  return { data: void 0 };
};

export const getCourierParams = (params) => {
  const {
    idOrPhone,
    phone = idOrPhone,
    courier_id,
    courierId = idOrPhone || courier_id,
    index_shop_id,
  } = params || {};
  return {
    phone,
    courierId,
    index_shop_id,
  };
};

/**
 *
 * @description 获取
 * @param {*} param0
 * @returns
 */
export const getApiUrlAndDataCourierDetail = (params) => {
  const { phone, courierId, index_shop_id } = getCourierParams(params);
  return {
    url: apis['courier.detail'],
    nonceKey: 'phone',
    data:
      process.env.MODE_ENV === 'wkd'
        ? { type: 'courier', phone, index_shop_id }
        : { courier_id: courierId },
  };
};
export const getApiCourierDetail = (params) => {
  return {
    ...getApiUrlAndDataCourierDetail(params),
    formatResponse: ({ data }) => formatResponseCourierDetail(data),
  };
};

/**
 *
 * @description 创建功能按钮
 * @returns
 */
export const createBars = (data) => {
  let list =
    process.env.MODE_ENV === 'wkd'
      ? [
          {
            key: 'message',
            label: '留言',
            color: 'green',
          },
          {
            key: 'phone',
            label: '电话',
            color: 'blue',
          },
          {
            key: 'collect',
            label: '+收藏',
            color: 'grey',
          },
        ]
      : [];

  if (data) {
    const index = list.findIndex((item) => item.key === data.key);
    if (index > 0) {
      list.splice(index, 1, {
        ...list[index],
        ...data,
      });
    }
  }
  return list;
};

/**
 * @description 收藏与取消收藏切换，包含：快递员，驿站，团队
 */
export const switchCourierCollect = (
  { account_phone: phone, is_focused, courier_id, dak_id, join_code, customer_id },
  cancelReason,
) => {
  return new Promise((resolve, reject) => {
    request({
      url: apis[`courier.${is_focused ? 'cancel' : 'collect'}`],
      toastLoading: false,
      nonceKey: 'courier_id,dak_id',
      data: {
        type: dak_id ? 'dak' : join_code ? 'team' : 'courier',
        phone,
        courier_id,
        dak_id,
        join_code,
        courier_customer_id: customer_id,
        ...cancelReason,
      },
      onThen: ({ code, msg, data }) => {
        if (code == 0) {
          if (is_focused) {
            // 取消快递员时，更改当前下单对象及缓存的下单对象
            resetRelation(courier_id);
          }
          resolve(data);
        } else {
          reject(new Error(msg));
        }
      },
    });
  });
};

export const cancelReasons = [
  { label: '地址变化', key: '1' },
  { label: '长时间未取件', key: '2' },
  { label: '服务态度差', key: '3' },
  { label: '其他', key: '4' },
];

const checkedCourierIdMap = {};
/**
 *
 * @description 检查快递员是否为vip
 * @param {*} id
 * @returns
 */
export function checkCourierIsVip(id) {
  const courierId = `${id}`;
  return new Promise((resolve) => {
    if (checkedCourierIdMap.hasOwnProperty(`${courierId}`)) {
      const mapRes = checkedCourierIdMap[courierId];
      resolve(mapRes);
      return;
    }
    request({
      url: '/v1/WeApp/courierIsVip',
      toastLoading: false,
      data: { courierId },
      onThen: ({ data }) => {
        const { isVip, isSVip = false } = data || {};
        const res = {
          isVip: isVip > 0,
          isSVip: isSVip > 0,
        };
        checkedCourierIdMap[courierId] = res;
        resolve(res);
      },
    });
  });
}

/**
 *
 * @description 格式化下单关系数据，注：目前仅用于微快递的数据格式化
 * @param {*} data
 * @returns
 */
export function formatRelationInfo(data) {
  if (process.env.MODE_ENV !== 'wkd') return data;
  const {
    type: dataType,
    courier_id,
    account_phone,
    dakId,
    dak_id = dakId,
    join_code,
    brand,
    dynamicForms,
    storageWay,
    isReturnModule = 0,
  } = data || {};
  const isCourier = courier_id || account_phone;
  const type = isCourier
    ? dataType || 'courier'
    : dak_id
    ? 'dak'
    : join_code
    ? 'team'
    : dataType === 'tcjs'
    ? dataType
    : brand
    ? 'brand'
    : null;
  let info = data;
  switch (type) {
    case 'courier':
      info = extractData(data, [
        'account_name',
        'account_phone',
        'account_company',
        'account_shop',
        'courier_id',
        'smjData', //扫码寄信息
        'customer', //大客戶信息
        ['brand', ({ account_company, brand }) => brand || account_company],
      ]);
      break;
    case 'team':
      info = extractData(data, [
        'account_name',
        ['join_code', ({ courier_id, join_code = courier_id }) => join_code],
      ]);
      break;
    case 'dak':
      info = extractData(data, [
        'address',
        'inn_name',
        ['dak_id', ({ courier_id, dakId, dak_id = courier_id || dakId }) => dak_id],
        'longitude',
        'latitude',
        'dak_mobile',
        'smjData', //扫码寄信息
        'customer', //大客戶信息
      ]);
      break;
    case 'brand':
      const { account_phone, account_name, brand, courier } = data;
      info = extractData(data, ['describe', 'name', 'brand', 'delivery_type']);
      if (brand === 'dp') {
        // 德邦小哥特殊处理
        info.describe = account_phone;
        info.name = account_name;
      }
      if (brand == 'yjkd' && courier) {
        info.platform = 'yjkd_courier';
        info.courier = courier;
      }
      break;
  }
  if (type) {
    info = {
      ...info,
      dynamicForms,
      storageWay,
      type,
      isReturnModule,
    };
  }
  return info;
}

/**
 * @description 当取消快递员收藏后，清除掉该用户下单对象的缓存，默认下单对象至圆通快递；
 */
export function resetRelation(courier_id) {
  console.log('Taro.kbRelationInfo', Taro.kbRelationInfo);
  if (!courier_id) return;
  const { data: curRelationInfo = {} } = Taro.kbRelationInfo || {};
  const { type: relationInfoType, courier_id: cur_courier_id } = curRelationInfo || {};
  if (relationInfoType === 'courier' && cur_courier_id === courier_id) {
    console.log('更改当前下单对象');
    // Taro.kbUpdateRelationInfo({
    //   brand: 'yt',
    //   storageWay: 'reset',
    // });
    Taro.kbUpdateRelationInfo(
      {
        brand: 'yjkd',
        storageWay: 'reset',
      },
      false,
    );
    // removeStorageSync(relationStorageKey);
    removeStorageSync(lastEditOrderTypeKey);
  }
}
