/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import request from '@base/utils/request';

export const getConversionOrderExtraData = (opt) => {
  const { order_id } = opt || {};
  return new Promise((resolve) => {
    if (process.env.PLATFORM_ENV == 'weapp') {
      return resolve({});
    }
    request({
      url: '/g_wkd/v2/Yj/Order/conversionOrderExtraData',
      data: {
        order_id,
      },
      onThen: (res) => {
        resolve(res.data || {});
      },
    });
  });
};
