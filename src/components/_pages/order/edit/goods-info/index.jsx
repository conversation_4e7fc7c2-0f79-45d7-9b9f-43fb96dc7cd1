/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { useEffect, useState } from '@tarojs/taro';
import { View, Text } from '@tarojs/components';
import { AtIcon } from 'taro-ui';
import classNames from 'classnames';
import { isFresh } from '../../_utils';
import './index.scss';

const GoodsInfo = (props) => {
  const { relationInfo, data, dynamicForms } = props;

  const [formatGoods, setFormatGoods] = useState();
  const [isFreshGoods, setIsFresh] = useState(false);

  // 处理物品信息显示
  useEffect(() => {
    const { goods_name, goods_weight, goods_remark, volume: oVolume } = data || {};
    // 物品信息显示
    const { goods_name: { customFresh: dynamicFresh } = {} } = dynamicForms || {};
    const { checked, volume } = oVolume || {};
    let goodFormValue = '';
    if (goods_name) {
      goodFormValue += `${goods_name}/`;
    }
    if (goods_weight > 0) {
      goodFormValue += `${goods_weight}kg/`;
    }
    if (checked && volume * 1 > 0) {
      goodFormValue += `${volume}cm³/`;
    }
    if (goods_remark) {
      goodFormValue += `${goods_remark}/`;
    }
    goodFormValue = goodFormValue ? goodFormValue.substring(0, goodFormValue.length - 1) : '';
    setFormatGoods(goodFormValue);
    let isFreshGoods = dynamicFresh || isFresh(goods_name);
    setIsFresh(isFreshGoods);
  }, [data, dynamicForms]);

  const handleGridClick = () => {
    const { dynamicForms: { volume: dynamicVolume } = {} } = relationInfo || {};
    Taro.navigator({
      url: `order/edit/goods`,
      key: 'routerParamsChange',
      options: {
        relationInfo,
        data,
        defaultExpand: dynamicVolume && dynamicVolume.defaultExpand,
        mode: 'goods',
        pageSource: 'dh',
        dynamicForms: dynamicForms || {},
      },
      onArrived: () => {},
    });
  };

  const rootCls = classNames('kb-goodsInfo', {
    'kb-goodsInfo--isFresh': isFreshGoods,
  });
  return (
    <View className={rootCls} onClick={handleGridClick} hoverClass='kb-hover'>
      <View className='kb-goodsInfo-label'>
        物品信息
        <View className='kb-goodsInfo-label--required'>必填</View>
      </View>
      <View>
        <Text>{formatGoods}</Text>
        <AtIcon prefixClass='kb-icon' value='arrow' className='kb-color__grey kb-icon-size__base' />
      </View>
    </View>
  );
};

GoodsInfo.options = {
  addGlobalClass: true,
};

export default GoodsInfo;
