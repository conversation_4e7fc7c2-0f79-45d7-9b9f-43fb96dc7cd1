/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { save } from '@base/actions/global';

export const setRelationInfo = (params = {}) => {
  return (dispatch) =>
    dispatch(
      save({
        relationInfo: params,
      }),
    );
};
export const setServiceConfigInfo = (params = {}) => {
  return (dispatch) =>
    dispatch(
      save({
        serviceConfig: params,
      }),
    );
};

export const setRelationVip = (params = {}) => {
  return (dispatch) =>
    dispatch(
      save({
        isVip: params.isVip,
        isSVip: params.isSVip,
      }),
    );
};
