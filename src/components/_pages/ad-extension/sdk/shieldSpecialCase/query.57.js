import Taro from '@tarojs/taro';
import request from '~base/utils/request';

/**
 *
 * @description admin广告位57号拦截；
 * Tower 任务: 支付宝——微快递营销页面优化调整 ( https://tower.im/teams/258300/todos/105207 )
 */
export function shieldAdminAd57(position) {
  if (process.env.PLATFORM_ENV === 'alipay') {
    return new Promise((resolve) => {
      if (position === '57') {
        const { scene } = Taro.getLaunchOptionsSync();
        const isFromMyCollected = `${scene}` === '1002' || process.env.DEBUG_ENV === 'OPEN'; // 我的小程序入口
        if (isFromMyCollected) {
          request({
            url: '/v1/WeApp/checkCollectStatus',
            toastLoading: false,
            onThen: (res) => {
              const { is_reward } = res.data || {};
              resolve(!(`${is_reward}` === 'true'));
            },
          });
        }
      } else {
        resolve(null);
      }
    });
  }
  return null;
}
