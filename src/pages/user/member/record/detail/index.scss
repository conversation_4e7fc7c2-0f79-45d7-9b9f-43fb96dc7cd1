/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb-vipDetail {
  position: relative;
  margin: $spacing-h-md;
  padding: $spacing-h-md;
  background: #fff;
  border-radius: 10px;
  &--disabled {
    color: #999 !important;
    background-image: linear-gradient(to right, #fff, #fff);
    -webkit-filter: grayscale(100%); /* Chrome, Safari, Opera */
    filter: grayscale(100%);
    &-icon {
      position: absolute;
      top: 30px;
      right: 30px;
      .img {
        width: 150px;
        height: 150px;
      }
    }
  }
  &-head {
    display: flex;
    padding-bottom: 20px;
    border-bottom: $border-lightest;
  }
  &-logo {
    width: 128px;
    height: 128px;
    margin-right: 20px;
    background: $color-brand;
    border-radius: 10px;
    .img {
      width: 100%;
      height: 100%;
    }
  }
  &-price {
    display: flex;
    align-items: center;
    margin-top: 20px;
    .price {
      display: flex;
      align-items: baseline;
      margin-right: 20px;
    }
    .first-tag {
      width: 208px;
      height: 36px;
      color: $color-brand;
      font-size: 22px;
      line-height: 36px;
      text-align: center;
      border: $width-base solid $color-brand;
      border-radius: 6px;
    }
  }
  &-desc {
    color: #999;
    font-size: 24px;
  }
  &-list {
    padding: $spacing-h-md 0;
    border-bottom: $border-lightest;
  }
  &-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 60px;
    color: #666;
    font-size: 28px;
  }
  &-label {
    color: #999;
  }
  &-footer {
    margin-top: 20px;
    color: #bbb;
    font-size: 24px;
    text-align: center;
  }
  .kb-icon {
    vertical-align: unset;
  }
}
