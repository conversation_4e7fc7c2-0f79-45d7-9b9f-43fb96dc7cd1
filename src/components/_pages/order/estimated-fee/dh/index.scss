/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb-fee-detail {
  font-size: 28px;

  &__volume {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: $spacing-v-md;
    padding: $spacing-v-sm $spacing-h-md;
    font-size: $font-size-sm;
    background-color: $color-grey-8;
    border-radius: 20px;
  }

  &__title {
    border-bottom: $border-lightest;
  }

  &__tips {
    margin: $spacing-h-md;
    padding: $spacing-h-md;
    color: #ff7e27;
    font-size: 22px;
    background: #fffbe9;
    border-radius: 10px;
  }

  &__list {
    padding: 0 $spacing-h-lg $spacing-h-lg;
  }

  &__item {
    display: flex;
    justify-content: space-between;
    padding-bottom: $spacing-h-md;
  }

  &__service {
    padding: 20px 0;
    border-top: $border-lightest;
    .label-desc {
      color: #999999;
      font-size: 24px;
    }
  }
}

.kb-fee {
  &-shrink {
    position: relative;
    padding-right: 25px;
    &::after {
      position: absolute;
      top: 20px;
      right: 0;
      width: 0;
      height: 0;
      border: 10px solid #999;
      border-right-color: transparent;
      border-bottom-color: transparent;
      border-left-color: transparent;
      content: '';
    }
    &--active {
      &::after {
        top: 8px;
        border-top-color: transparent;
        border-bottom-color: #999;
      }
    }
  }
}
