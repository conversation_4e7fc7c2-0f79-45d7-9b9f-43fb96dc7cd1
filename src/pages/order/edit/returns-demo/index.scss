.return-demo {
  width: 100vw;
  min-height: 100vh;
  padding: 32rpx;
  background-color: #f2f2f2;
  &-header {
    display: flex;
    width: 640rpx;
    padding: 32rpx;
    background: #fff;
    border-radius: 10rpx;
    &-left {
      color: #333;
      font-weight: bold;
      font-size: 30rpx;
    }
    &-right {
      margin-left: 32rpx;
      color: #666;
      font-weight: 500;
      font-size: 24rpx;
      line-height: 42rpx;
      .info {
        margin-right: 16rpx;
      }
      .required {
        color: #009fff;
      }
    }
  }
  &-list {
    width: 640rpx;
    height: 932rpx;
    margin-top: 20rpx;
    padding: 30rpx;
    background: #fff;
    border-radius: 10rpx;
    &-title {
      color: #333;
      font-weight: bold;
      font-size: 30rpx;
    }
    &-desc {
      height: 59rpx;
      color: #666;
      font-weight: 500;
      font-size: 24rpx;
      line-height: 36rpx;
    }
    &-pic {
      width: 100%;
      width: 368rpx;
      height: 734rpx;
      margin: 32rpx auto;
      &-container {
        width: 100%;
        height: 100%;
      }
    }
  }
}
