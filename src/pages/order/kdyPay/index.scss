/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb-pay {
  &__title {
    position: relative;
    left: 50%;
    display: inline-block;
    text-align: center;
    transform: translateX(-50%);
    &::after {
      position: absolute;
      top: 53%;
      right: -55%;
      display: inline-block;
      width: 30px;
      height: 0.2px;
      padding-left: $spacing-h-md;
      background-color: $color-grey-8;
      content: '';
    }
    &::before {
      position: absolute;
      top: 53%;
      left: -55%;
      display: inline-block;
      width: 30px;
      height: 0.2px;
      padding-right: $spacing-h-md;
      background-color: $color-grey-8;
      content: '';
    }
  }
  &__title,
  &__total,
  &__item {
    box-sizing: border-box;
    padding: $spacing-v-md $spacing-h-md;
    &-content {
      max-width: 500px;
    }
  }

  &__total {
    margin-top: $spacing-v-md;
    padding: $spacing-v-xl $spacing-h-md;
    border-top: $border-lightest;
  }

  &__title {
    padding-top: $spacing-v-xl;
    color: $color-grey-2;
  }

  &__notPay {
    height: 80px;
    margin: 0 $spacing-h-md;
    font-size: $font-size-lg;
    line-height: 80px;
  }

  &__successModal {
    .kb-modal__footer {
      .at-button {
        padding-right: 40px;
        padding-left: 40px;
        font-size: 28px;
      }
    }
  }
}
.kb-order {
  &-wrap {
    padding-bottom: 70px;
    text-align: center;
    background-color: $color-brand;
  }
  &-content {
    margin-top: -50px;
  }
}
