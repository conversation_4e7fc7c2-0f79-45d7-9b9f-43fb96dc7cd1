import { formatNumeral } from '@base/utils/numeral';
import { randomCode } from '@base/utils/utils';
import dayjs from 'dayjs';

export function formatSignDetail(data) {
  const { integral = 0, money = 0, sign_info, sign_reward_rules, coupon = '' } = data || {};
  const { sign_date = '', consecutive_days = '0' } = sign_info || {};
  const consecutive_daysNum = 1 * consecutive_days;
  const today = dayjs().format('YYYY-MM-DD');
  const todayIndex = sign_date === today ? consecutive_daysNum - 1 : consecutive_daysNum;

  return {
    integral: formatNumeral(integral),
    money: formatNumeral(money),
    list: [...new Array(7)].map((_, index) => {
      const day = index + 1;
      const { sign: integral = day * 10 } = (sign_reward_rules && sign_reward_rules[day]) || {};
      return {
        key: randomCode(),
        isToday: index === todayIndex,
        isSign: index < consecutive_daysNum,
        integral,
        day,
        coupon: day === 4 || day === 7 ? coupon : '',
      };
    }),
  };
}
