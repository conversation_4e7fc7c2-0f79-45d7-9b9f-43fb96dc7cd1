/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { Component } from '@tarojs/taro';
import { connect } from '@tarojs/redux';
import KbPage from '@base/components/page';
import { getUserVipInfo } from '@/components/_pages/user/_utils';
import { getShareAppMessage } from '@/utils/share';
import { Image, Text, View } from '@tarojs/components';
import { AtButton } from 'taro-ui';
import './index.scss';

@connect(({ global }) => ({
  loginData: global.loginData,
}))
class Index extends Component {
  config = {
    navigationBarTitleText: '续费管理',
  };

  constructor(props) {
    super(props);
    this.state = {};
  }

  handleUpdate(data) {
    if (!data.logined) return;
    this.getUserVipData();
  }

  onShareAppMessage = getShareAppMessage;

  getUserVipData() {
    getUserVipInfo({ isFull: true }).then((data) => {
      this.setState({
        userVipData: data,
      });
    });
  }

  handleClose = () => {
    const { userVipData } = this.state;
    Taro.navigator({
      url: 'user/member/manage/close',
      key: 'routerParamsChange',
      options: {
        userVipData,
      },
      onArrived: () => {
        console.log('通过postMessage传递大量数据');
      },
    });
  };

  render() {
    const { userVipData = {}, ...rest } = this.state;
    const { open_auto, original_cost, price, next_price, level_name } = userVipData || {};
    return (
      <KbPage {...rest} onUpdate={this.handleUpdate.bind(this)}>
        <View className='kb-vipManage'>
          <View className='kb-vipManage-head'>
            <View className='kb-vipManage-head--bg' />
            <View className='kb-recordCard'>
              <View>
                <View className='kb-recordCard-cardName'>优享寄{level_name}</View>
                <View className='kb-recordCard-body'>
                  <View>次月起¥{next_price}/月续费，可随时取消</View>
                </View>
              </View>
              <View className='kb-recordCard-footer'>
                <View className='price'>
                  <Text className='kb-size__sm'>￥</Text>
                  <Text className='money'>{price}</Text>
                  <Text className='original'>原价{original_cost}元</Text>
                </View>
              </View>
              <View className='kb-recordCard-cardTag'>
                <Text className='kb-recordCard-cardTag_arrow' />
                {open_auto == 1 ? '已开通' : '已关闭'}自动续费
              </View>
            </View>
          </View>
          <View className='kb-vipManage-card'>
            <View className='kb-vipManage-card--desc'>
              会员到期<Text className='kb-color__brand'>前1天</Text>
              ，将在微信账号自动扣款，并延长有效期
            </View>
            <View className='kb-vipManage-card--list'>
              <View className='kb-vipManage-card--item'>
                <Image
                  className='img'
                  mode='widthFix'
                  src='https://cdn-img.kuaidihelp.com/wkd/miniApp/vip2/icon_exyh.png'
                />
                <View className='title'>特享优惠</View>
                <View className='desc'>持续更划算</View>
              </View>
              <View className='kb-vipManage-card--item'>
                <Image
                  className='img'
                  mode='widthFix'
                  src='https://cdn-img.kuaidihelp.com/wkd/miniApp/vip2/icon_ssqx.png'
                />
                <View className='title'>随时取消</View>
                <View className='desc'>便捷管理</View>
              </View>
              <View className='kb-vipManage-card--item'>
                <Image
                  className='img'
                  mode='widthFix'
                  src='https://cdn-img.kuaidihelp.com/wkd/miniApp/vip2/icon_ssgx.png'
                />
                <View className='title'>省时高效</View>
                <View className='desc'>到期自动续费</View>
              </View>
            </View>
            {open_auto == 1 && (
              <AtButton type='primary' circle onClick={this.handleClose}>
                关闭续费
              </AtButton>
            )}
          </View>
        </View>
      </KbPage>
    );
  }
}

export default Index;
