import { Fragment } from '@tarojs/taro';
import KbNoticeBar from '~base/components/notice-bar';
import { useGetOrderNotice } from './_utils';

const OrderNotice = (props) => {
  const { text, extra } = useGetOrderNotice(props);

  return (
    <Fragment>
      {text ? <KbNoticeBar marquee text={text} /> : null}
      {extra ? <KbNoticeBar marquee text={extra} /> : null}
    </Fragment>
  );
};

export default OrderNotice;
