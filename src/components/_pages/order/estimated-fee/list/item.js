import KbCheckbox from '@base/components/checkbox';
import KbServiceType from '@/components/_pages/order/service-type';
import { Image, Text, View } from '@tarojs/components';
import Taro, { Fragment, useMemo, useEffect } from '@tarojs/taro';
import classNames from 'classnames';
import { AtIcon } from 'taro-ui';
import { useSelector } from '@tarojs/redux';
import './index.scss';

const EstimatedListItem = (props) => {
  const {
    item,
    isOpenCredit,
    brand,
    loading,
    isBatch = false,
    product_code,
    loadingBrand,
    activityCouponInfo,
    onChooseBrand,
    onNavigatorDetail,
    handleChange,
    onDpPartnerModal = () => {},
    alipayActivityCoupon,
  } = props;
  const { voucher_id = '' } = alipayActivityCoupon || {};
  const { coupon_id } = item || {};
  const { brands = {} } = useSelector((state) => state.global);
  const { activity } = activityCouponInfo || {};
  if (!item.discount_list) {
    item.discount_list = [];
  }
  const itemCls = classNames('kb-estimatedFeeList-list-item', {
    'kb-estimatedFeeList-list-item--active': brand === item.brand,
    'make-disabled': item.available <= 0,
  });
  const payOnlineActive = !!(item.pay == 2 || (item.pay == 3 && isOpenCredit));
  const brandInfo = brands[item.brand] || {};
  // const needWxCredit = !!(item.pay == 2 || item.pay == 3);

  const showLoading = loading && (!loadingBrand || loadingBrand === item.quotation_brand);

  const handlePayOnlineTips = () => {
    Taro.kbModal({
      topImg: 'https://cdn-img.kuaidihelp.com/wkd/miniApp/youhua/pay_title.png?v=1',
      content: [
        {
          text: '支持先寄后付的快递品牌，用户下单后',
        },
        {
          src: 'https://cdn-img.kuaidihelp.com/wkd/miniApp/youhua/pay_primary.png?v=1',
        },
        {
          text: '快件妥投后，平台会以包裹的结算重量对您的微信账户进行扣费',
        },
      ],
      confirmText: '我知道啦',
    });
  };

  const onNoopEvent = (e) => {
    e.stopPropagation();
  };

  const mode =
    process.env.PLATFORM_ENV === 'alipay' &&
    ((!voucher_id && coupon_id && isOpenCredit) || activity === 'ali_dd_promotion')
      ? '2'
      : '1';

  const discount_fee = useMemo(() => {
    return !isBatch && activityCouponInfo && activityCouponInfo.discount_fee && brand == item.brand
      ? (item.discount_price * 1 - activityCouponInfo.discount_fee * 1).toFixed(2)
      : item.discount_price;
  }, [item, activityCouponInfo, brand, isBatch]);

  useEffect(() => {
    if (brand && item && item.brand === brand && item.is_partner_order) {
      if (onDpPartnerModal) {
        onDpPartnerModal('open', {
          payOnlineActive,
          text: item.is_partner_order,
        });
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [brand, item, payOnlineActive]);
  return (
    <Fragment>
      <View class={itemCls} onClick={onChooseBrand.bind(null, item)} hoverClass='kb-hover-opacity'>
        <View className='kb-estimatedFeeList-list-item__body at-row at-row__justify--between at-row__align--center'>
          <View className='kb-estimatedFeeList-list-item__imgBox'>
            <Image
              className='kb-estimatedFeeList-list-item__img'
              lazyLoad
              mode='widthFix'
              src={brandInfo.logo_link}
            />
          </View>
          <View className='at-col kb-estimatedFeeList-list-item__content'>
            <View className='at-row at-row__justify--between at-row__align--center'>
              <View>
                <View className='at-row at-row__align--center'>
                  <View className='kb-color__black yj-brand-name kb-size__bold'>
                    {brandInfo.name}
                  </View>
                </View>
                <View className='brand-desc'>{item.message}</View>
                {payOnlineActive && (
                  <View
                    class='flex-content-center yj-pay-label'
                    onClick={(e) => {
                      e.stopPropagation();
                      handlePayOnlineTips();
                    }}
                  >
                    <Text className='kb-icon kb-icon-money2 yj-pay-label--icon' />
                    先寄后付
                  </View>
                )}
                {!!item.is_partner_order ? (
                  <View
                    className='kb-partner-tag kb-margin-xs-t'
                    onClick={(e) => {
                      e.stopPropagation();
                      onDpPartnerModal('open', {
                        payOnlineActive,
                        text: item.is_partner_order,
                      });
                    }}
                    hoverClass='kb-hover'
                    hoverStopPropagation
                  >
                    <AtIcon
                      className='kb-icon2 kb-size__sm kb-margin-xs-r'
                      prefixClass='kb-icon'
                      value='partner'
                    />
                    {item.is_partner_order}
                  </View>
                ) : null}
              </View>
              {/* 价格 */}
              <View
                className='at-row at-row__justify--center kb-freight__row'
                onClick={onNavigatorDetail.bind(null, item, (e) => e.stopPropagation())}
                hoverClass='kb-hover-opacity'
                hoverStopPropagation
              >
                <View className='kb-freight'>
                  {showLoading ? (
                    <AtIcon
                      prefixClass='kb-icon'
                      value='loading'
                      className='kb-icon-size__base kb-color__grey'
                    />
                  ) : (
                    <Fragment>
                      {process.env.PLATFORM_ENV === 'alipay' ? (
                        <View>
                          {mode == 2 ? (
                            <Fragment>
                              <Text className='original_price'>
                                {item.original_price > 0 &&
                                Number(item.original_price) > Number(item.discount_price) ? (
                                  <Text>¥{item.original_price}</Text>
                                ) : null}
                              </Text>
                              <Text className='discount_price'>
                                {item.discount_price > 0 ? (
                                  <Fragment>
                                    <Text className='kb-freight__uint'>￥</Text>
                                    <Text className='kb-size__bold'>{item.discount_price}</Text>
                                  </Fragment>
                                ) : (
                                  '--'
                                )}
                              </Text>
                            </Fragment>
                          ) : (
                            <Text className='discount_price'>
                              {item.original_price > 0 ? (
                                <Fragment>
                                  <Text className='kb-freight__uint'>￥</Text>
                                  <Text className='kb-size__bold'>{item.original_price}</Text>
                                </Fragment>
                              ) : (
                                '--'
                              )}
                            </Text>
                          )}
                          <View className='kb-icon kb-icon-arrow kb-icon-size__xs' />
                        </View>
                      ) : (
                        <View>
                          <Text className='original_price'>
                            {item.original_price > 0 &&
                            Number(item.original_price) > Number(item.discount_price) ? (
                              <Text>¥{item.original_price}</Text>
                            ) : null}
                          </Text>
                          <Text className='discount_price'>
                            {item.discount_price > 0 ? (
                              <Fragment>
                                <Text className='kb-freight__uint'>￥</Text>
                                <Text className='kb-size__bold'>{discount_fee}</Text>
                              </Fragment>
                            ) : (
                              '--'
                            )}
                          </Text>
                          <View className='kb-icon kb-icon-arrow kb-icon-size__xs' />
                        </View>
                      )}
                    </Fragment>
                  )}
                  {!isBatch && (
                    <View className='weight'>
                      续重<Text className='kb-color__red'>¥{item.s_fee}</Text>/kg
                    </View>
                  )}
                </View>
              </View>
            </View>
            {/* {process.env.PLATFORM_ENV === 'alipay' && needWxCredit && (
              <View className='kb-size__sm kb-margin-sm-t'>可享“先寄后付，积累信用”</View>
            )} */}
            {item.tips ? <View className='kb-estimatedFeeList-list__tips'>{item.tips}</View> : null}
          </View>
          <View className='kb-estimatedFeeList-list--check'>
            <KbCheckbox checked={brand === item.brand} onChange={onChooseBrand.bind(null, item)} />
          </View>
        </View>
        {item.product_code && brand === item.brand && (
          <View className='kb-estimatedFeeList-list-item__footer' onClick={onNoopEvent}>
            <KbServiceType
              mode='mini'
              current={product_code}
              type={brand}
              payOnline={payOnlineActive}
              onChange={(e) => handleChange('product_code', e, item.quotation_brand)}
            />
          </View>
        )}
        {item.label == 'min' && <View className='kb-tag-badge__sh'>最实惠</View>}
      </View>
    </Fragment>
  );
};

EstimatedListItem.options = {
  addGlobalClass: true,
};

export default EstimatedListItem;
