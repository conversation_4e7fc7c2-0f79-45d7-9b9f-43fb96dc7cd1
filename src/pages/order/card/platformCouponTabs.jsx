/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { useState, Fragment } from '@tarojs/taro';
import { AtTabs, AtTabsPane } from 'taro-ui';
import KbCardListContent from './card-list-content';
import { couponTabs, couponPlatformTabs } from './utils';
import { View } from '@tarojs/components';
import './index.scss';
import classNames from 'classnames';
import KbCardListContentAlipay from './card-list-alipay-content';

const KbPlatformCouponTabs = (props) => {
  const { active, pageSource, isReturnModule } = props;
  const [current, setCurrent] = useState(0);
  const [platformCurrent, setPlatformCurrent] = useState('sjq');

  const handleSwitchTab = (v) => {
    setCurrent(v);
  };

  const handlePlatformSwitchTab = (v) => {
    setPlatformCurrent(v);
  };

  return (
    <Fragment>
      <View className='kb-card-PlatformTabs-topTabs'>
        {couponPlatformTabs.map((item) => {
          const tabs2ItemCls = classNames('kb-card-PlatformTabs-topTabs__item', {
            'kb-card-PlatformTabs-topTabs__item--active': platformCurrent == item.key,
          });
          return (
            <View
              key={item.key}
              className={tabs2ItemCls}
              onClick={handlePlatformSwitchTab.bind(null, item.key)}
            >
              {item.title}
            </View>
          );
        })}
      </View>
      <View className='kb-card-PlatformTabs-tabs'>
        {platformCurrent == 'alipay' ? (
          <KbCardListContentAlipay
            source='tabs'
            action='normal'
            mode='normal'
            pageSource={pageSource}
            isReturnModule={isReturnModule}
            active={active && platformCurrent === 'alipay'}
          />
        ) : (
          <AtTabs
            tabList={couponTabs}
            onClick={handleSwitchTab}
            current={current}
            swipeable={false}
          >
            {couponTabs.map((val, index) => {
              const { key } = val;
              const mode = key == 'normal' ? 'normal' : 'disabled';
              return (
                <AtTabsPane key={key} index={index} current={current}>
                  <KbCardListContent
                    source='tabs'
                    action={key}
                    mode={mode}
                    active={active && index == current && platformCurrent === 'sjq'}
                    pageSource={pageSource}
                    isReturnModule={isReturnModule}
                  />
                </AtTabsPane>
              );
            })}
          </AtTabs>
        )}
      </View>
    </Fragment>
  );
};

export default KbPlatformCouponTabs;
