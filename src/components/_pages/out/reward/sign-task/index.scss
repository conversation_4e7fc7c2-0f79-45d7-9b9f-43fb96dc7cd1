/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb-signTask {
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  width: 702px;
  height: 122px;
  margin: 0 auto 20px;
  padding: 24px;
  background: #fffceb;
  border-radius: 20px;
  &-list {
    display: flex;
    flex: 1;
    align-items: center;
  }
  &-item {
    position: relative;
    display: flex;
    flex: 1;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 115px;
    &::before {
      position: absolute;
      top: 40px;
      right: 0;
      left: 0;
      width: 100%;
      height: 6px;
      background: #feecc1;
      content: '';
    }
    &:first-child {
      &::before {
        left: 50%;
        width: 50%;
      }
    }
    &:last-child {
      &::before {
        right: 50%;
        width: 50%;
      }
    }
    &--active {
      &::before {
        background: #ff5c01;
      }
    }
    &__icon {
      position: relative;
      z-index: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      height: 80px;
      .img_coupon {
        width: 70px;
        height: 80px;
        margin-top: -20px;
      }
      .img {
        width: 44px;
        height: 44px;
      }
    }
    &__text {
      color: #8c4e49;
      font-size: 20px;
    }
  }
  &-sign {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 222px;
    height: 58px;
    margin-left: 24px;
    color: #ffffff;
    font-weight: bold;
    font-size: 28px;
    background: linear-gradient(90deg, #ff941d, #ff5c01);
    border-radius: 58px;
    &--active {
      color: #cc8653;
      background: #feecc1;
    }
  }
}
