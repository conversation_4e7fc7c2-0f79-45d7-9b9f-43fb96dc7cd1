/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { Component } from '@tarojs/taro';
import { View, Canvas, Image } from '@tarojs/components';
import KbPage from '@base/components/page';
import KbImagePicker from '@base/components/image-picker';
import isString from 'lodash/isString';
import request from '@base/utils/request';
import './index.scss';

let cropperW,
  cropperH,
  pageX = 0, // 拖动时候的 pageX
  pageY = 0, // 拖动时候的 pageY
  systemInfo,
  pixelRatio, //设备像素比
  windowWRPX = 750, // 手机的宽度
  windowWRPY = 0,
  cutW,
  cutH,
  cutL,
  cutT,
  imageAspectRadio,
  windowAspectRadio,
  sizeConfPageX = 0, // 调整大小时候的 pageX
  sizeConfPageY = 0, // 调整大小时候的 pageY
  initDragCutW = 0, //
  initDragCutL = 0,
  initDragCutH = 0,
  initDragCutT = 0,
  dragScaleP = 2; // 移动时,手势位移与实际元素位移的比

/**
 * 页面带入参数this.$router.params = {path,action}
 * @path 图片路径
 *  */
class Index extends Component {
  config =
    process.env.PLATFORM_ENV === 'alipay'
      ? {
          navigationBarTitleText: '选择地址部分',
          navigationBarBackgroundColor: '#000000',
          allowsBounceVertical: 'NO',
        }
      : {
          navigationBarTitleText: '选择地址部分',
          navigationBarBackgroundColor: '#000000',
          disableScroll: true,
        };

  constructor() {
    super(...arguments);
    this.state = {
      showSelectBar: false,
      canvasW: 0,
      canvasH: 0,
      path: '', //需要裁剪的图片路径,如微信选择后的图片路径
      cropperInitW: windowWRPX, // 图片整体区域的宽高
      cropperInitH: windowWRPX,
      cropperW: windowWRPX, // 图片框的宽高、left top值
      cropperH: windowWRPX,
      cutW: 0, // 裁剪框 宽高
      cutH: 0,
      cutL: 0,
      cutT: 0,
      scaleP: 0, // 图片缩放值
      imageW: 0, // 图片原始宽度 rpx
      imageH: 0,
    };
  }

  // 页面组件ready
  componentWillMount() {
    const { path } = this.$router.params;
    systemInfo = Taro.systemInfo;
    const { windowWidth, windowHeight, screenHeight } = systemInfo;
    pixelRatio = systemInfo.pixelRatio; //设备像素比
    windowWRPX = 750; // 手机的宽度
    windowWRPY = windowHeight * (750 / windowWidth) - (screenHeight >= 750 ? 200 : 160);
    windowAspectRadio = windowWidth / windowHeight;
    this.setImageInfo(decodeURIComponent(path));
  }

  // 初始化图片
  setImageInfo(path) {
    this.setState(
      {
        path,
        showSelectBar: path ? false : true,
      },
      () => {
        Taro.getImageInfo({
          src: path,
        })
          .then((res) => {
            console.log('res', res);
            const { width, height } = res;
            imageAspectRadio = width / height;
            // 根据图片的宽高显示不同的效果   保证图片可以正常显示
            if (imageAspectRadio >= 1 || imageAspectRadio >= windowAspectRadio) {
              cropperW = windowWRPX;
              cropperH = windowWRPX / imageAspectRadio;
            } else {
              cropperW = windowWRPY * imageAspectRadio;
              cropperH = windowWRPY;
            }
            cutW = cropperW > 50 ? cropperW - 50 : cropperW;
            cutH = Math.min(cropperH, 200);
            cutL = (cropperW - cutW) / 2;
            cutT = (cropperH - cutH) / 2;
            this.setState({
              cropperW,
              cropperH,
              cutW,
              cutH,
              cutL,
              cutT,
              scaleP: (width * pixelRatio) / cropperH,
              imageW: width * pixelRatio,
              imageH: height * pixelRatio,
            });
          })
          .catch(() => {
            this.setState({
              showSelectBar: true,
            });
          });
      },
    );
  }

  // 外部拖动，整体移动
  handleWrapperTouch(e) {
    let { type, touches } = e;
    type = type.toLocaleLowerCase();
    const { pageX: x, pageY: y } = touches[0];
    if (type === 'touchstart') {
      pageX = x;
      pageY = y;
    } else if (type === 'touchmove') {
      const { cutL, cutT, cutW, cutH, cropperW, cropperH, showSelectBar } = this.state;
      let dragLengthX = (pageX - x) * dragScaleP;
      let dragLengthY = (pageY - y) * dragScaleP;
      let minX = Math.max(cutL - dragLengthX, 0);
      let minY = Math.max(cutT - dragLengthY, 0);
      let maxX = cropperW - cutW;
      let maxY = cropperH - cutH;
      this.setState(
        {
          cutL: Math.min(maxX, minX),
          cutT: Math.min(maxY, minY),
        },
        () => {
          // 重新选择位置后，隐藏选择按钮，显示识别按钮
          if (showSelectBar) {
            this.setState({ showSelectBar: false });
          }
        },
      );
      pageX = x;
      pageY = y;
    }
  }

  // 内部横线以及点操作
  handleInnerTouch(direction, e) {
    e.stopPropagation();
    let { type, touches } = e;
    type = type.toLocaleLowerCase();
    const { pageX: x, pageY: y } = touches[0];
    const { cropperW, cropperH, cutW, cutH, cutL, cutT } = this.state;
    if (type === 'touchstart') {
      sizeConfPageX = x;
      sizeConfPageY = y;
      initDragCutW = cutW;
      initDragCutL = cutL;
      initDragCutT = cutT;
      initDragCutH = cutH;
    } else if (type === 'touchmove') {
      let dragLength = 0;
      switch (direction) {
        case 'r':
          dragLength = (sizeConfPageX - e.touches[0].pageX) * dragScaleP;
          if (initDragCutW >= dragLength) {
            // 如果 移动小于0 说明是在往下啦  放大裁剪的高度  这样一来 图片的高度  最大 等于 图片的top值加 当前图片的高度  否则就说明超出界限
            // 如果是移动 大于0  说明在缩小  只需要缩小的距离小于原本裁剪的高度就ok
            if (dragLength > 0 || (dragLength < 0 && cropperW > initDragCutL + cutW)) {
              this.setState({
                cutW: initDragCutW - dragLength,
              });
            }
          }
          break;
        case 'l':
          dragLength = (dragLength = sizeConfPageX - e.touches[0].pageX) * dragScaleP;
          if (initDragCutW >= dragLength && initDragCutL > dragLength) {
            if (dragLength < 0 && Math.abs(dragLength) >= initDragCutW) return;
            this.setState({
              cutL: initDragCutL - dragLength,
              cutW: initDragCutW + dragLength,
            });
          }
          break;
        case 't':
          dragLength = (sizeConfPageY - e.touches[0].pageY) * dragScaleP;
          if (initDragCutH >= dragLength && initDragCutT > dragLength) {
            if (dragLength < 0 && Math.abs(dragLength) >= initDragCutH) return;
            this.setState({
              cutT: initDragCutT - dragLength,
              cutH: initDragCutH + dragLength,
            });
          }
          break;
        case 'b':
          dragLength = (sizeConfPageY - e.touches[0].pageY) * dragScaleP;
          // 必须是 dragLength 向上缩小的时候必须小于原本的高度
          if (initDragCutH >= dragLength) {
            // 如果 移动小于0 说明是在往下啦  放大裁剪的高度  这样一来 图片的高度  最大 等于 图片的top值加 当前图片的高度  否则就说明超出界限
            // 如果是移动 大于0  说明在缩小  只需要缩小的距离小于原本裁剪的高度就ok
            if (dragLength > 0 || (dragLength < 0 && cropperH > initDragCutT + cutH)) {
              this.setState({
                cutH: initDragCutH - dragLength,
              });
            }
          }
          break;
        case 'rb':
          let dragLengthX = (sizeConfPageX - e.touches[0].pageX) * dragScaleP;
          let dragLengthY = (sizeConfPageY - e.touches[0].pageY) * dragScaleP;
          if (initDragCutH >= dragLengthY && initDragCutW >= dragLengthX) {
            // bottom 方向的变化
            if ((dragLengthY < 0 && cropperH > initDragCutT + cutH) || dragLengthY > 0) {
              this.setState({
                cutH: initDragCutH - dragLengthY,
              });
            }

            // right 方向的变化
            if ((dragLengthX < 0 && cropperW > initDragCutL + cutW) || dragLengthX > 0) {
              this.setState({
                cutW: initDragCutW - dragLengthX,
              });
            }
          }
          break;
        default:
          break;
      }
    }
  }

  // 操作边线
  lines = ['t', 'r', 'b', 'l'];
  // 操作点
  points = ['t', 'r', 'b', 'l', 'rt', 'rb', 'lt', 'lb'];

  // 重新选择图片
  handleImageSelect(list) {
    this.setImageInfo(list[0]);
  }

  // 剪切图片
  handleCutImage() {
    // 将图片写入画布
    const ctx = Taro.createCanvasContext('picCanvas');
    let { cutW, cutH, cutL, cutT, cropperW, cropperH, imageW, imageH, path } = this.state;
    const fail = () => {
      this.uploadPic(path);
    };
    // 获取画布要裁剪的位置和宽度   均为百分比 * 画布中图片的宽度 ；解决了在微信小程序中裁剪的图片模糊，位置不对的问题
    let imageW_ = imageW / pixelRatio;
    let imageH_ = imageH / pixelRatio;
    let canvasW = (cutW / cropperW) * imageW_,
      canvasH = (cutH / cropperH) * imageH_,
      canvasL = (cutL / cropperW) * imageW_,
      canvasT = (cutT / cropperH) * imageH_;
    if (process.env.PLATFORM_ENV === 'alipay') {
      ctx.drawImage(path, 0, 0, imageW_, imageH_);
      ctx.draw();
      this.setState(
        {
          canvasW: imageW,
          canvasH: imageH,
        },
        () => {
          clearTimeout(this.timer);
          this.timer = setTimeout(() => {
            ctx.toTempFilePath({
              x: canvasL,
              y: canvasT,
              width: canvasW,
              height: canvasH,
              destWidth: canvasW,
              destHeight: canvasH,
              canvasId: 'picCanvas',
              success: (res) => {
                const { apFilePath } = res;
                if (apFilePath) {
                  //传给后台，解析数据
                  this.uploadPic(apFilePath);
                } else {
                  fail();
                }
              },
              fail,
            });
          });
        },
      );
    } else {
      ctx.drawImage(path, -canvasL, -canvasT);
      ctx.draw();
      this.setState(
        {
          canvasW,
          canvasH,
        },
        () => {
          clearTimeout(this.timer);
          this.timer = setTimeout(() => {
            Taro.canvasToTempFilePath({
              x: 0,
              y: 0,
              width: canvasW,
              height: canvasH,
              destWidth: canvasW,
              destHeight: canvasH,
              canvasId: 'picCanvas',
              success: (res) => {
                const { tempFilePath } = res;
                if (tempFilePath) {
                  //传给后台，解析数据
                  this.uploadPic(tempFilePath);
                } else {
                  fail();
                }
              },
              fail,
            });
          }, 400);
        },
      );
    }
  }

  //图片上传+解析
  uploadPic(filePath) {
    request({
      url: '/v1/WeApp/uploadAttachments',
      data: {
        filePath,
        type: 'minaOcr',
      },
      requestDataType: 'file',
      toastError: true,
      quickTriggerThen: true,
      toastLoading: process.env.PLATFORM_ENV === 'alipay' ? false : '图片上传',
      onThen: (res) => {
        console.log('上传图片', res);
        if (res.code == 0 && res.data && res.data.file_path) {
          request({
            url: '/v1/Ocr/ocrNamePhoneAddress',
            data: {
              img: res.data.file_path,
            },
            toastError: true,
            quickTriggerThen: true,
            toastLoading: '解析中...',
            onThen: ({ data }) => {
              const { original: text } = data || {};
              if (text && isString(text)) {
                // 直接将图片内容返回
                this.onThen('parseImage', {
                  text,
                  img: 'http://upload.kuaidihelp.com/minaOcr/' + res.data.file_path,
                });
              } else {
                this.setState({
                  showSelectBar: true,
                });
              }
            },
          });
        } else {
          this.setState({
            showSelectBar: true,
          });
        }
      },
    });
  }

  // 解析回调、剪切回调
  onThen(type, data) {
    Taro.navigator({
      post: {
        data,
        type,
      },
    });
  }

  render() {
    const {
      showSelectBar,
      canvasW,
      canvasH,
      cutW,
      cutH,
      cutL,
      cutT,
      path,
      cropperW,
      cropperH,
      ...rest
    } = this.state;

    return (
      <KbPage {...rest}>
        <View className='kb-cutting'>
          <View className='kb-cutting__wrapper' id='cuttingWrapper'>
            <View
              className='kb-cutting__content'
              style={{
                width: `${cropperW}rpx`,
                height: `${cropperH}rpx`,
              }}
            >
              <Image className='kb-cutting__content--image' src={path} />
              <View
                className='kb-cutting__content--touch-wrapper'
                style={{
                  width: `${cutW}rpx`,
                  height: `${cutH}rpx`,
                  left: `${cutL}rpx`,
                  top: `${cutT}rpx`,
                }}
                onTouchStart={this.handleWrapperTouch.bind(this)}
                onTouchMove={this.handleWrapperTouch.bind(this)}
              >
                <View className='kb-cutting__content--touch'>
                  {this.lines.map((item) => (
                    <View
                      onTouchStart={this.handleInnerTouch.bind(this, item)}
                      onTouchMove={this.handleInnerTouch.bind(this, item)}
                      key={item}
                      className={`kb-cutting__content--touch-line kb-cutting__content--touch-line--${item}`}
                    />
                  ))}
                  {this.points.map((item) => (
                    <View
                      onTouchStart={this.handleInnerTouch.bind(this, item)}
                      onTouchMove={this.handleInnerTouch.bind(this, item)}
                      key={item}
                      className={`kb-cutting__content--touch-point kb-cutting__content--touch-point--${item}`}
                    />
                  ))}
                </View>
              </View>
            </View>
          </View>
          <View className='kb-cutting__button'>
            {showSelectBar ? (
              <KbImagePicker onChange={this.handleImageSelect.bind(this)} custom>
                <View className='kb-cutting__button--item'>重新选择图片</View>
              </KbImagePicker>
            ) : (
              <View
                className='kb-cutting__button--item'
                hoverClass='kb-hover-opacity'
                onClick={this.handleCutImage.bind(this)}
              >
                识别
              </View>
            )}
          </View>
          <Canvas
            className='kb-cutting__canvas'
            style={{
              width: `${canvasW}px`,
              height: `${canvasH}px`,
            }}
            canvasId='picCanvas'
          />
        </View>
      </KbPage>
    );
  }
}

export default Index;
