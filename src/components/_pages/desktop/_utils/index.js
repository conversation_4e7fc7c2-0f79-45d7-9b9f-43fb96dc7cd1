/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import request from '@base/utils/request';

/**
 * 开始抽奖
 * @returns
 */
export const startPrize = () => {
  return new Promise((resolve) => {
    request({
      url: '/g_order_core/v2/activity/Lottery/wheel',
      data: {
        activity_code: 'DP01',
      },
      toastLoading: false,
      toastError: true,
      onThen: (res) => {
        resolve(res.code == 0 && res.data ? res.data : null);
      },
    });
  });
};

/**
 * 活动描述
 */
export const desktopDescList = [
  '抽奖参与方式：活动期间从桌面进入，首次添加桌面权益可参与活动抽奖',
  '抽奖方式：点击转盘中间的抽奖字样，每次仅可转动一次，指针停留区域便为最终奖项',
  '领取方式：抽奖成功可至我的奖励页面查看，此优惠券适用产品以优惠券上标注的使用规则为准；',
  '有效时间：此优惠券上标有优惠券有效期，可前往我的优惠券查看；过期失效不予补偿，请尽快使用。',
  '如有退件，优惠券金额不予退还。此活动优惠，不可与其他活动优惠同享。',
  '暂不支持港澳台，以及海外区域参与活动优惠。',
];

export const checkIsFromDesktop = () => {
  const options = wx.getEnterOptionsSync();
  console.log('options', options);
  const { scene } = options || {};
  return !!(scene == '1023');
};
