/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { View, Fragment } from '@tarojs/components';
import { AtIcon, AtFloatLayout } from 'taro-ui';
import KbModal from '@base/components/modal';
import KbCheckbox from '@base/components/checkbox';
import classNames from 'classnames';
import { usePayType, JFF_VALUE, SFF_VALUE } from './_utils';
import './index.scss';
import KbCreditComponent from '../../../credit/credit-label';

const KbPayType = (props) => {
  const {
    pay_method,
    payMethod,
    list = [],
    isOpened,
    handleClose,
    handleOpen,
    handleChoose,
    handleConfirm,
    isOpenedInterceptTips,
    handleChangeInterceptTips,
  } = usePayType(props);

  return (
    <Fragment>
      <View className='kb-payType' onClick={handleOpen} hoverClass='kb-hover'>
        <View className='kb-nav-item'>
          <View className='kb-nav-item-label'>付款方式</View>
          <View className='kb-nav-item-value'>
            <View className='content'>
              {pay_method == JFF_VALUE ? (
                '寄方付'
              ) : pay_method == SFF_VALUE ? (
                '收方付'
              ) : (
                <View className='creditTag'>
                  <AtIcon
                    className='kb-icon2 kb-margin-sm-r'
                    prefixClass='kb-icon'
                    value='wxpayflag'
                    color='#07C160'
                    size='14'
                  />
                  微信支付分 | 先寄后付
                </View>
              )}
            </View>
            <AtIcon
              prefixClass='kb-icon'
              value='arrow'
              className='kb-icon2 kb-color__grey kb-icon-size__base'
            />
          </View>
        </View>
      </View>
      <AtFloatLayout isOpened={isOpened} onClose={handleClose} title='选择付款方式'>
        <View className='kb-payType-layout'>
          <View className='kb-payType-layout__list'>
            {list.map((item) => {
              const active = `${payMethod}` === `${item.value}`;
              const itemCls = classNames('kb-payType-layout__item', {
                'kb-payType-layout__item--active': active,
              });
              return (
                <View
                  key={item.value}
                  className={itemCls}
                  onClick={() => handleChoose(item)}
                  hoverClass='kb-hover-opacity'
                >
                  {item.value == 1 ? (
                    <View className='kb-payType-layout__card2'>
                      <View className='title'>
                        <View>{item.label}</View>
                        <View>
                          <KbCheckbox checked={active} onChange={() => handleChoose(item)} />
                        </View>
                      </View>
                      <View>
                        <View className='product'>
                          <AtIcon
                            className='kb-icon2 kb-margin-sm-r'
                            prefixClass='kb-icon'
                            value='wxpayflag'
                            color='#07C160'
                            size='12'
                          />
                          开通微信支付分 | 先寄后付，自动扣款无感快捷支付；
                        </View>
                        <View className='note'>
                          注：微信支付分≥450分可开通（微信官方评估，您当前是否具备开通条件！）
                        </View>
                      </View>
                    </View>
                  ) : (
                    <View className='kb-payType-layout__card'>
                      <View>
                        <View className='title'>{item.label}</View>
                        <View className='desc'>{item.desc}</View>
                      </View>
                      <View>
                        <KbCheckbox checked={active} onChange={() => handleChoose(item)} />
                      </View>
                    </View>
                  )}
                </View>
              );
            })}
          </View>
          <View className='kb-payType-layout__footer'>
            <View className='btn' onClick={handleConfirm}>
              确定
            </View>
          </View>
        </View>
      </AtFloatLayout>
      <KbModal
        isOpened={isOpenedInterceptTips}
        title='温馨提示'
        top={false}
        centered
        onClose={handleChangeInterceptTips.bind(null, 'close')}
        onCancel={handleChangeInterceptTips.bind(null, 'cancel')}
        onConfirm={handleChangeInterceptTips.bind(null, 'confirm')}
        confirmText='我再想想'
        cancelText='确认'
        cancelButtonProps={{
          type: 'secondary',
        }}
        rootClass='kb-changeInterceptTips'
      >
        <View className='kb-changeInterceptTips-inner'>
          <View className='kb-text__center kb-color__red'>当前优惠券不可用</View>
          <View className='kb-changeInterceptTips-credit'>
            <KbCreditComponent type='door' open msg='微信支付分寄件可用' />
          </View>
          <View className='at-row kb-margin-md-t'>
            <View>原因：</View>
            <View className='at-col'>
              限微信支付分 | 先寄后付 下单使用。 到付、现付订单付款类型暂不可用！
            </View>
          </View>
        </View>
      </KbModal>
    </Fragment>
  );
};

KbPayType.options = {
  addGlobalClass: true,
};

KbPayType.defaultProps = {
  pay_method: '1',
  payTypes: '',
  isStudentSend: false,
  isOpenCredit: false,
};

export default KbPayType;
