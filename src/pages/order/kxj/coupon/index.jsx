/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { Component } from '@tarojs/taro';
import { View, Image, Fragment, Button, Text } from '@tarojs/components';
import { AtButton, AtIcon } from 'taro-ui';
import KbPage from '@base/components/page';
import KbLongList from '@base/components/long-list';
import KbModal from '@base/components/modal';
import KbBarrage from '@/components/_pages/order/kxj/barrage';
import KbSubscribe from '@base/components/subscribe';
import { refreshControl, REFRESH_KEY_KXJ_COUPON } from '@/utils/refresh-control';
import { getActivityDetail } from '@/components/_pages/order/kxj/_utils';
import { getShareAppMessage } from '@/utils/share';
import { getShareTimeline } from '@/utils/shareTimeline';
import request from '@base/utils/request';
import isArray from 'lodash/isArray';
import { activityDescList, formatKxjCoupon } from '../../card/utils';
import './index.scss';
import { orderAction } from '@/components/_pages/order/_utils';

class Index extends Component {
  config = {
    navigationBarTitleText: '',
    backgroundColorTop: '#f2f2f2',
    navigationStyle: 'custom',
  };

  constructor() {
    this.state = {
      list: [],
      isOpened: false,
    };
    this.listData = {
      api: {
        url: '/g_order_core/v2/WkdCoupon/getCouponConfig',
        data: {
          activity: 'new_customer',
        },
        formatResponse: (res) => {
          let list = [];
          if (res.code == 0 && res.data && res.data.length > 0) {
            list = formatKxjCoupon(res.data);
          }
          return list.length > 0
            ? {
                code: 0,
                data: {
                  list,
                },
              }
            : { data: void 0 };
        },
        onThen: (list) => {
          this.setState({
            list,
          });
        },
      },
    };
  }

  // 分享
  onShareAppMessage = (ev) => {
    return getShareAppMessage(ev, {
      page: 'activity.coupon',
    });
  };
  // 朋友圈分享
  onShareTimeline = getShareTimeline;

  // 登录状态更新
  onUpdate = (res) => {
    if (res.logined) {
      this.getActivityDetail();
    }
  };

  onReady = (ins) => {
    this.listIns = ins;
  };

  getActivityDetail() {
    getActivityDetail().then((res) => {
      this.setState({
        activityDetail: res,
      });
    });
  }

  addReceiveCoupon = () => {
    request({
      url: '/g_order_core/v2/WkdCoupon/addReceiveCoupon',
      toastError: true,
      data: {
        activity: 'new_customer',
      },
      onThen: (res) => {
        if (res.code == 0) {
          this.getActivityDetail();
          refreshControl(REFRESH_KEY_KXJ_COUPON);
          Taro.kbToast({
            text: '优惠券已领取，可前往“我的优惠券”查看',
          });
        }
      },
    });
  };

  onJump = () => {
    orderAction({
      action: 'edit',
    });
  };

  handleDesc = (key) => {
    switch (key) {
      case 'open':
        this.setState({
          isOpened: true,
        });
        break;
      case 'close':
        this.setState({
          isOpened: false,
        });
        break;
    }
  };

  handleVip = () => {
    Taro.navigator({
      url: 'user/member/right',
    });
  };

  handleCoupon = () => {
    Taro.navigator({
      url: 'order/card',
    });
  };

  handleSubscribe = (key) => {
    switch (key) {
      case 'success':
        Taro.kbToast({
          text: '消息订阅成功',
        });
        break;
      case 'close':
        this.setState({
          subscribe: false,
        });
        break;
    }
  };

  render() {
    const { list, isOpened, activityDetail, subscribe = true, ...rest } = this.state;
    const { is_receive } = activityDetail || {};
    return (
      <KbPage
        className='kb-activity'
        onUpdate={this.onUpdate.bind(this)}
        renderFooter={
          <View className='kb-activity__footer'>
            {is_receive != 1 ? (
              <AtButton
                className='footer-btn'
                type='primary'
                circle
                onClick={this.addReceiveCoupon}
              >
                立即领取
              </AtButton>
            ) : (
              <Fragment>
                <AtButton className='footer-btn' type='primary' circle onClick={this.onJump}>
                  立即寄件
                </AtButton>
                <View className='footer-tag'>完成首单寄件 送VIP会员</View>
              </Fragment>
            )}
          </View>
        }
        {...rest}
      >
        <KbLongList data={this.listData} enableMore={false} onReady={this.onReady.bind(this)}>
          <Image
            className='kb-activity-bg'
            src='https://cdn-img.kuaidihelp.com/wkd/miniApp/activity2/bg.png?v=02'
          />
          {subscribe && (
            <View className='kb-activity-subscribe'>
              <AtIcon className='kb-size__lg kb-margin-md-r' prefixClass='kb-icon' value='bell' />
              <View className='kb-activity-subscribe__text'>立即订阅，抢先领略精彩活动！</View>
              <KbSubscribe
                className='kb-activity-subscribe__btn'
                action='activity'
                type='primary'
                circle
                size='small'
                onSubscribe={() => this.handleSubscribe('success')}
              >
                去订阅
              </KbSubscribe>
              <AtIcon
                className='kb-size__sm kb-margin-md-l'
                onClick={() => this.handleSubscribe('close')}
                prefixClass='kb-icon'
                value='wrong'
              />
            </View>
          )}
          <KbBarrage />
          <View className='kb-activity-coupon'>
            <Image
              className='kb-activity-coupon__title'
              mode='widthFix'
              src='https://cdn-img.kuaidihelp.com/wkd/miniApp/activity2/img01.png?v=01'
            />
            {isArray(list) &&
              list.length > 0 &&
              list.map((item) => {
                return (
                  <View className='kb-activity-coupon__coupon' key={item.id}>
                    <View className='coupon-head'>
                      <View className='coupon-unit'>￥</View>
                      <View className='coupon-fee'>{item.discount_fee}</View>
                    </View>
                    <View className='coupon-body'>
                      <View className='coupon-title'>{item.desc}</View>
                      <View className='coupon-desc'>
                        有效期至领券之日起，{item.invalid_time}天内有效；
                      </View>
                      <View className='coupon-tag'>{item.card_note}</View>
                    </View>
                    {item.coupon_num > 0 && (
                      <View className='coupon-num'>X{item.coupon_num}张</View>
                    )}
                  </View>
                );
              })}
            <View className='kb-activity-coupon__desc'>
              - 可与<Text className='kb-color__red'>优寄</Text>优惠价格叠加使用，享折上折寄件 -
            </View>
          </View>
          <Image
            className='kb-activity-block'
            mode='widthFix'
            src='https://cdn-img.kuaidihelp.com/wkd/miniApp/activity2/img02.png?v=1'
          />
          <View className='kb-activity-block kb-activity-block__vip'>
            <Image
              className='kb-activity-block'
              mode='widthFix'
              src='https://cdn-img.kuaidihelp.com/wkd/miniApp/activity2/img03.png?v=01'
            />
            <View
              className='kb-activity-block__vip--right'
              onClick={this.handleVip.bind(this)}
              hoverClass='kb-hover-opacity'
            >
              优享寄VIP会员专属权益 ，<View className='link'>前往查看特权详情</View>
            </View>
          </View>
          <Image
            className='kb-activity-block'
            mode='widthFix'
            src='https://cdn-img.kuaidihelp.com/wkd/miniApp/activity2/img04.png?v=1'
          />
          <Image
            className='kb-activity-block'
            mode='widthFix'
            src='https://cdn-img.kuaidihelp.com/wkd/miniApp/activity2/img05.png?v=1'
          />
          <View style={{ height: '20px' }} />
          <View className='kb-activity-door kb-activity-door__share' hoverClass='kb-hover-opacity'>
            分享
            <Button className='kb-activity-door__share-btn' openType='share' />
          </View>
          <View
            className='kb-activity-door kb-activity-door__rule'
            onClick={this.handleDesc.bind(this, 'open')}
            hoverClass='kb-hover-opacity'
          >
            规则
          </View>
          <View
            className='kb-activity-door kb-activity-door__coupon'
            onClick={this.handleCoupon.bind(this)}
            hoverClass='kb-hover-opacity'
          >
            优惠券
          </View>
        </KbLongList>
        <KbModal
          title='活动说明'
          top={false}
          closable={false}
          confirmText='我知道了'
          isOpened={isOpened}
          onClose={this.handleDesc.bind(this, 'close')}
          onCancel={this.handleDesc.bind(this, 'close')}
          onConfirm={this.handleDesc.bind(this, 'close')}
        >
          <View className='kb-activity__desc-list'>
            {activityDescList.map((item, index) => {
              return (
                <View className='kb-activity__desc-list-item' key={item}>
                  <View className='kb-activity__desc-list-index'>{index + 1}、</View>
                  <View className='kb-activity__desc-list-content'>{item}</View>
                </View>
              );
            })}
          </View>
        </KbModal>
      </KbPage>
    );
  }
}

export default Index;
