/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { Component } from '@tarojs/taro';
import KbPage from '@base/components/page';
import { View, ScrollView, Swiper, SwiperItem, Image } from '@tarojs/components';
import KbQueryBar from '@/components/_pages/query-bar';
import { AtIcon, AtModal, AtModalContent, AtButton } from 'taro-ui';
import { getAdConfig } from '@/components/_pages/ad-extension/_utils';
import { adNavigator } from '@/components/_pages/ad-extension/sdk';
import { getStorageSync, removeStorageSync } from '@base/utils/utils';
import {
  historyStorageKey,
  jumpToExpressInfo,
  matchWaybillAndBrand,
} from '@/components/_pages/query/_utils';
import './index.scss';

class Index extends Component {
  static config = {
    navigationBarTitleText: '搜索页',
  };
  constructor() {
    this.state = {
      history: [],
      favorite: [],
      ADList: [],
      isOpened: false,
    };
  }
  // 事件
  handleReady = (ins) => {
    this.listIns = ins;
  };

  getAD = (position, cb) => {
    getAdConfig({ position }).then((res) => {
      cb && cb(res);
    });
  };

  handleClick = (item) => {
    adNavigator(item);
  };

  handleDelete = () => {
    removeStorageSync(historyStorageKey);
    this.setState({
      history: [],
    });
    Taro.kbToast({
      text: '删除成功',
    });
    this.handleClose();
  };

  handleClose = () => {
    this.setState({
      isOpened: false,
    });
  };

  handleGoSearch = (word) => {
    matchWaybillAndBrand(word, {
      toastLoading: true,
    })
      .then(({ data: list, errMsg, nomatch }) => {
        jumpToExpressInfo({ word, list, errMsg, nomatch });
      })
      .catch((err) => console.log(err));
  };

  componentWillMount() {
    this.getAD(24, (res) => this.setState({ favorite: res }));
    this.getAD(25, (res) => this.setState({ ADList: res }));
    if (this.$router.params.query) {
      this.handleGoSearch(this.$router.params.query);
    }
  }

  componentDidShow() {
    const { data: storage } = getStorageSync(historyStorageKey) || {};
    if (storage) {
      this.setState({ history: storage });
    }
  }

  render() {
    const { history, favorite, ADList, isOpened, ...rest } = this.state;
    return (
      <KbPage
        {...rest}
        onUpdate={this.handleUpdate}
        renderHeader={
          <View className='kb-query__header kb-spacing-md'>
            <KbQueryBar placeholder='输入单号、单号后四位、手机号查快递' autoClean focus />
          </View>
        }
      >
        <ScrollView scrollY className='kb-scrollView'>
          {history.length && (
            <View className='kb-search__history'>
              <View className='kb-search__history-head'>
                <View className='kb-search__history-head__title'>搜索历史</View>
                <View
                  hoverClass='kb-hover-opacity'
                  onClick={() =>
                    this.setState({
                      isOpened: true,
                    })
                  }
                >
                  <AtIcon prefixClass='kb-icon' value='delete' size={14} />
                </View>
              </View>
              <View className='kb-search__history-content'>
                {history.map((item) => (
                  <View
                    // eslint-disable-next-line react/no-array-index-key
                    key={item}
                    hoverClass='kb-hover'
                    className='kb-search__card'
                    onClick={() => this.handleGoSearch(item)}
                  >
                    {item}
                  </View>
                ))}
              </View>
            </View>
          )}
          {favorite.length && (
            <View className='kb-search__history'>
              <View className='kb-search__history-head'>
                <View className='kb-search__history-head__title'>猜你喜欢</View>
              </View>
              <View className='kb-search__history-content'>
                {favorite.map((item, index) => (
                  <View
                    key={item.id}
                    className='kb-search__cardWrap'
                    onClick={this.handleClick.bind(null, item)}
                  >
                    <View className='kb-search__card'>{item.title}</View>
                    {index == 0 && <View className='kb-hot'>HOT</View>}
                  </View>
                ))}
              </View>
            </View>
          )}
          {ADList.length && (
            <View className='kb-ads'>
              <Swiper
                className='kb-ads__swiper'
                autoplay
                circular
                indicatorDots={ADList.length > 1}
              >
                {ADList.map((item) => (
                  <SwiperItem key={item.id}>
                    <View
                      className='kb-ads__swiper--item'
                      onClick={this.handleClick.bind(null, item)}
                    >
                      <Image className='kb-ads__swiper--image' src={item.imgUrl} />
                    </View>
                  </SwiperItem>
                ))}
              </Swiper>
            </View>
          )}
          <AtModal isOpened={isOpened} className='kb-clearModal' onClose={this.handleClose}>
            <AtModalContent>
              <View className='kb-clearModal__title'>温馨提示</View>
              <View className='kb-clearModal__content'>确认删除最近搜索记录吗？</View>
              <View className='kb-clearModal__footer'>
                <View className='kb-clearModal__footer-btn' onClick={this.handleDelete}>
                  <AtButton circle type='secondary'>
                    删除
                  </AtButton>
                </View>
                <View className='kb-clearModal__footer-btn' onClick={this.handleClose}>
                  <AtButton circle type='primary'>
                    我再想想
                  </AtButton>
                </View>
              </View>
            </AtModalContent>
          </AtModal>
        </ScrollView>
      </KbPage>
    );
  }
}

export default Index;
