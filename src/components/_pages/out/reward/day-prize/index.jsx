/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { useState } from '@tarojs/taro';
import { Text, View } from '@tarojs/components';
import WelfareEveryday from '~/pages/welfare/continuity/target/components/everyday';
import request from '~base/utils/request';
import { useUpdate } from '~base/hooks/page';
import { CreateSubscribeMessage } from '~base/components/subscribe/sdk';
import { frequencyLimitByMinute } from '~base/utils/utils';
import classNames from 'classnames';
import './index.scss';

const KbDayPrize = () => {
  const [activityList, setActivityList] = useState([]);
  const [isSubscribe, setIsSubscribe] = useState(false);

  const subscribeKey = 'out.reward.sub.prize';

  const customList = (data) => {
    return data.map((item) => ({
      ...item,
      img: item.img || '',
      custom: item.img,
    }));
  };

  const getEveryDayList = () => {
    request({
      url:
        process.env.PLATFORM_ENV === 'alipay'
          ? '/g_wkd/v2/marketingActivity/tbk/AliLottery/mailList'
          : '/g_wkd/v2/activity/VxLottery/mailList',
      toastLoading: true,
      data: {
        page: 1,
        page_size: 7,
        show_num: 1,
        order_by: 'lottery_time_asc',
      },
    }).then((res) => {
      const { data = {}, code } = res;
      if (code == 0) {
        const { list = [] } = data;
        setActivityList(customList(list));
      }
    });
  };

  useUpdate(() => {
    getEveryDayList();
    frequencyLimitByMinute('check', subscribeKey, 'max').then((limit) => {
      setIsSubscribe(!!limit);
    });
  }, []);

  const handleClick = () => {
    Taro.navigator({
      url: 'welfare/lotterys',
    });
  };

  const handleDy = () => {
    if (isSubscribe) {
      Taro.kbToast({
        text: '已订阅',
      });
      return;
    }
    const subIns = new CreateSubscribeMessage();
    subIns.trigger('out.reward').then(() => {
      frequencyLimitByMinute('limit', subscribeKey);
      setIsSubscribe(true);
      Taro.kbToast({
        text: '订阅成功',
      });
    });
  };

  const dyBtnCls = classNames('kb-dayPrize-box--dy', {
    'kb-dayPrize-box--dy-active': !isSubscribe,
  });
  return (
    <View className='kb-dayPrize'>
      <View className='kb-dayPrize-box'>
        <View className='kb-dayPrize-box--title'>
          <View className={dyBtnCls} onClick={handleDy}>
            <Text className='kb-icon kb-icon-lingdang1' />
            <Text>{isSubscribe ? '已订阅' : '提醒我'}</Text>
          </View>
        </View>
        <View onClick={handleClick} hoverClass='kb-hover-opacity'>
          <WelfareEveryday list={activityList} getEveryDayLists={getEveryDayList} disableClick />
        </View>
      </View>
    </View>
  );
};

KbDayPrize.options = {
  addGlobalClass: true,
};

export default KbDayPrize;
