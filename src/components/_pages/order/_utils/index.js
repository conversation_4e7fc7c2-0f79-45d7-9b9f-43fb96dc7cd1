/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import {
  addressKeys,
  addressKeysMap,
  checkDataComplete,
  getErrorAddressIndex,
  receiveStorageKey,
  sendStorageKey,
} from '@/components/_pages/address/_utils';
import { addEcode } from '@/components/_pages/ecode/_utils';
import { refreshControl, REFRESH_KEY_ORDER } from '@/utils/refresh-control';
import { scanCode, checkCodeInfo } from '@/utils/scan';
import logger from '@base/utils/logger';
import request from '@base/utils/request';
import rules from '@base/utils/rules';
import {
  createGroup,
  createListener,
  extractData,
  getPage,
  makePhoneCall,
  noop,
  setStorage,
} from '@base/utils/utils';
import Taro from '@tarojs/taro';
import isArray from 'lodash/isArray';
import isPlainObject from 'lodash/isPlainObject';
import merge from 'lodash/merge';
import { cancelOrder, cloneOrder, editOrder, refundCard, refundOrder } from './order.action';

Taro.realnameChecked = {};
export const updateOrderArrivePayPrice = (arrive_pay, order_id) => {
  return new Promise((resolve) => {
    const [url, reqData] =
      process.env.MODE_ENV === 'wkd'
        ? [
            '/v1/WeApp/updateToPayAmount',
            {
              order_id,
              to_pay_amount: arrive_pay,
            },
          ]
        : [
            '/api/weixin/mini/minpost/order/setOrderArrivePrice',
            {
              order_id,
              arrive_pay,
            },
          ];
    request({
      url,
      toastSuccess: false,
      toastLoading: false,
      toastError: false,
      data: reqData,
      onThen(res) {
        resolve(res);
      },
    });
  });
};
// 默认到付运费，有此值表示开启到付金额；
export const defaultToPayAmount = 9999;
export function checkIsDefaultToPayAmount(amount) {
  return Number(amount) === defaultToPayAmount;
}
export const fillOrderArrivePayPrice = ({ action, data, order_id: id }, callback = noop) => {
  return new Promise((resolve, reject) => {
    const { arrive_pay, serviceData } = data || {};
    const { arrive_pay: serviceArrivePay = arrive_pay } = serviceData || {};
    const ids = isArray(id) ? id : [id];
    const openInputModal = checkIsDefaultToPayAmount(serviceArrivePay);

    if (action.includes('print') && openInputModal && ids.length === 1) {
      const [order_id] = ids;
      Taro.kbModal({
        top: '到付运费录入',
        template: [
          {
            tag: 'at-input',
            placeholder: '打印面单需要填写到付金额',
            value: '',
            circle: true,
            border: false,
            name: 'arrive_pay',
            cursorSpacing: 200,
          },
          {
            className: 'kb-color__grey kb-size__base kb-spacing-md-t',
            value: '注：填写的金额将同步打印至电子面单',
          },
        ],
        onConfirm: (e) => {
          const { data: { arrive_pay } = {} } = e;
          if (arrive_pay) {
            updateOrderArrivePayPrice(arrive_pay, order_id).then(({ code, msg }) => {
              if (code != 0) {
                Taro.kbToast({
                  text: msg,
                });
                reject();
              } else {
                callback();
                resolve();
              }
            });
            return false;
          }
          Taro.kbToast({
            text: '请填写到付金额',
          });
          return true;
        },
      });
    } else {
      resolve();
    }
  });
};

export const globalOrderIDs = 'globalOrderIDs';

// 订单编辑地址信息列表
export const addressList = [
  {
    key: 'send',
    placeholder: ['从哪寄件？'],
    color: 'brand',
    tag: '寄',
    selector: true,
  },
  {
    key: 'receive',
    placeholder: ['寄去哪儿？'],
    color: 'orange',
    tag: '收',
  },
];

// 中文状态转化对应
export const getOrderState = (name) => {
  const list = [
    { name: '已下单', key: 'wait', cancel: true },
    { name: '已取消', key: 'cancel' },
    { name: '已完成', key: 'done' },
  ];
  return {
    name,
    key: 'other',
    cancel: false,
    realname: true,
    ...list.find((item) => item.name === name),
  };
};

/**
 * @description 格式化地址
 * @description 默认 name=>send_name; reverse=true时,send_name=>name;
 */
export const formatAddress = (
  data = {},
  key,
  { reverse = false, keys = addressKeys, replace = {} } = {},
) => {
  let has = false;
  let res = {
    data: extractData(
      data,
      keys.map((item) => {
        let from = item;
        let to = `${key}_${item}`;
        if (reverse) {
          [to, from] = [from, to];
        }
        if (data[to] && !has) {
          has = true;
        }
        // 替换key
        const str = replace[item];
        if (str) {
          from = from.replace(item, str);
        }
        return [from, to];
      }),
    ),
    has,
  };
  return res;
};

// 检查联系方式组
export const checkContactGroup = (value, { rule, msg } = rules.contact) => {
  const list = `${value}`.split(/\s+|[,，;]+/g).filter((item) => !!item);
  let errorIndex = -1;
  for (let i = 0, len = list.length; i < len; i++) {
    if (!rule.test(list[i])) {
      errorIndex = i;
      break;
    }
  }
  if (errorIndex >= 0) {
    return msg;
  }
};
// 更新定制表单内容
export const fixDynamicFormsData = (data = {}, TemplateForm = null) => {
  // 设置标准 isShow:是否展示,locked:是否锁住,value:自定义值,placeholder:自定义提示,label:自定义label
  // 设置的数据可以为基本数据类型
  let dynamicForm = isPlainObject(TemplateForm)
    ? { ...TemplateForm }
    : process.env.MODE_ENV === 'wkd'
    ? {
        goods_name: { isShow: true }, //物品类型
        goods_weight: { isShow: true }, //物品重量
        goods_remark: { isShow: true }, //物品备注
        card: { isShow: false }, //权益次卡
        appointmentTime: { isShow: false }, //上门时间
        product_code: { isShow: false }, //服务类型
        volume: { isShow: false }, //体积
        service: { isShow: false }, //增值服务
        extra: { isShow: false }, //展示额外需求
        carType: { isShow: false }, //车型选择
      }
    : {
        goods_name: { isShow: true },
        goods_weight: { isShow: true },
        goods_remark: { isShow: true },
        brand: { isShow: true },
        card: { isShow: true },
      };
  // Object.keys(data).forEach(i => {
  //   dynamicForm[i] =
  //     dynamicForm[i]
  //       ? { ...dynamicForm[i], ...data[i] }
  //       : data[i];
  // });
  dynamicForm = merge({}, dynamicForm, data);
  return dynamicForm;
};

export const freshList = [
  { label: '生鲜', key: 'sx' },
  { label: '水果', key: 'sg' },
  { label: '蔬菜', key: 'sc' },
  { label: '肉类', key: 'rl' },
  { label: '海鲜', key: 'hx' },
  { label: '干货', key: 'gh' },
];
export const packagesList = ['日用品', '数码产品', '衣物', '食物', '文件', '鞋靴'];
export const isFresh = (value) => {
  return value &&
    freshList.find((i) => {
      return i.label === value;
    })
    ? true
    : false;
};
export const serviceKeys = [
  'keep_account',
  'collection',
  'arrive_pay',
  'cost_value',
  'rate_checked',
  'is_arrive_pay',
];
export const serviceConfirmKeys = [
  'keep_account',
  'collection',
  'arrive_pay',
  'rate',
  'is_arrive_pay',
];
export const brandKeys = ['shipper_zipcode_del', 'reserve_time', 'brand', 'product_type'];
export const goodsKeys = ['goods_name', 'goods_weight', 'goods_remark', 'package_images', 'volume'];
// 生成表单
const unRequiredKeys = [
  'company',
  'service',
  ...goodsKeys,
  'save',
  'province_confidence',
  'city_confidence',
  'district_confidence',
];
const noStorageKey = ['reserve_time', 'shipper_zipcode_del'];
const prefixs = {
  send: '寄件人',
  receive: '收件人',
};
const addressKeysRules = {
  name: {
    min: 1,
  },
  address: {
    validator: (value, currentRule = {}) => {
      let tag = currentRule.tag || '详细地址',
        verifyResult = false,
        rule = /^[A-Za-z0-9]+$/;
      value = value + '';
      if (rule && rule.test(value)) {
        verifyResult = `${tag}不能填写为纯字母或纯数字格式`;
      }
      return verifyResult;
    },
  },
};
export function getFormItem({
  keys,
  data,
  form = {},
  prefix = '',
  clean = true,
  merge = {}, // 合入的表单配置项
}) {
  const formData = { ...data };
  keys.map((key) => {
    const formKey = prefix ? `${prefix}_${key}` : key;
    const commonRules = addressKeysRules[key] || {};
    const data = {
      value: formData[formKey] || '',
      clean,
      storage: !noStorageKey.includes(formKey),
      required: !unRequiredKeys.includes(key),
      ...commonRules,
      ...merge[formKey],
    };
    const addressKeyValue = addressKeysMap[key];
    if (addressKeyValue) {
      data.tag = (prefixs[prefix] || '') + addressKeyValue;
    }
    if (key === 'mobile') {
      data.customMsg = data.tag + '格式不正确';
      data.reg = 'contact';
      data.validator = checkContactGroup;
    }
    form[formKey] = data;
  });
  return form;
}
// 获取表单配置 action = order:创建订单；address:地址编辑
export const getForm = ({
  list = addressList,
  keys = addressKeys,
  action = 'order',
  data,
  merge,
} = {}) => {
  const form = {};
  list.map((item) => {
    const prefix = item.key;
    getFormItem({
      keys,
      data,
      form,
      prefix,
      clean: action === 'address' ? true : prefix !== 'send',
      merge,
    });
  });
  return form;
};

// 缓存实名状态
const cacheRealnameStatus = (key, data) => {
  let key_ = `${key}`;
  if (data) {
    const keys = Object.keys(Taro.realnameChecked);
    if (keys.length >= 5) {
      delete Taro.realnameChecked[keys[0]];
    }
    if (key_) {
      Taro.realnameChecked[key_] = data;
    }
  } else {
    return Taro.realnameChecked.hasOwnProperty(key_) ? Taro.realnameChecked[key_] : null;
  }
};

function getApiUrlAndDataRealnameStatus(phone, needInfo, action) {
  let url;
  let data;
  let userkey = 'user_id';
  if (process.env.MODE_ENV === 'wkd') {
    if (phone) {
      url = '/v1/user/getRns';
      data = { mobile: phone };
    } else {
      url = '/v1/user/getRealnameStatus';
    }
  } else {
    url =
      action !== 'self'
        ? `/api/weixin/mini/realname/auth/${needInfo ? 'realNameInfo' : 'getIdCardId'}`
        : '/api/weixin/mini/user/Bind/getUserRealnameStatus';
    data = { phone };
  }
  return {
    url,
    data,
    userkey,
  };
}

// 获取实名认证状态
export const getRealnameStatus = (phone, { needInfo = false, action = '', ...api } = {}) => {
  return new Promise((resolve, reject) => {
    // 去除结尾的连接符号
    phone = `${phone || ''}`.trim().replace(/[,，;\s]$/, '');
    if (process.env.MODE_ENV !== 'wkd') {
      let cacheData = null;
      if (action !== 'self' && (!phone || !!checkContactGroup(phone))) {
        reject(new Error('请输入手机号'));
        return;
      }
      cacheData = cacheRealnameStatus(phone);
      if (cacheData && ((needInfo && cacheData.info) || !needInfo)) {
        resolve(cacheData);
        return;
      }
    } else {
      if (phone && !!checkContactGroup(phone)) {
        // 传入手机号，且手机号不合法跳过
        return;
      }
    }

    request({
      toastLoading: false,
      ...api,
      ...getApiUrlAndDataRealnameStatus(phone, needInfo, action),
      mastLogin: false,
      nonceKey: 'mobile',
      onThen: ({ data }) => {
        let realnamed = data.status === '已认证';
        let info = null;
        if (action === 'self') {
          const { status, realname_status = realnamed ? 1 : 0, ...rest } = data;
          realnamed = realname_status === 1;
          info = { ...rest };
        } else if (isPlainObject(data)) {
          const { status, ...rest } = data;
          if (realnamed) {
            info = rest;
          }
        } else {
          realnamed = data == 1;
        }
        const result = {
          realnamed,
          info,
        };
        resolve(result);
        if (process.env.MODE_ENV !== 'wkd') {
          cacheRealnameStatus(phone, result);
        }
      },
    });
  });
};

// 设置实名认证状态
export const setRealnameStatus = (key, data) => cacheRealnameStatus(key, data);

// 更新当前状态
export const updatePrintStatus = (data, page) => {
  const { activePrintType } = page;
  if (activePrintType) {
    data.status = activePrintType === 'print' ? '已打印' : '已完成';
    data.activePrintType = activePrintType;
    page.activePrintType = '';
  }
};

const orderDetailOptions = (item, params = {}) => {
  // params是页面router参数
  const { channel, order_random, fromShare } = params || {};
  // app分享支付参数处理
  let extraData = {};
  if (channel == 'sharePay') {
    extraData = {
      share_pay_order_random: order_random,
      order_random,
      fromShare,
    };
  }
  const bySources = ['wzg', 'dak'];
  const { source, order_state, order_id, orderTypeAndDate, brand, customer_id } = item;
  const { type: orderType } = orderTypeAndDate || {};
  return {
    ...orderTypeAndDate,
    brand,
    order_id,
    customer_id,
    type:
      order_state === 'temporary' ? order_state : bySources.includes(source) ? source : orderType,
    ...extraData,
  };
};

// 订单操作
const orderActionRef = { current: null };
export const orderAction = (opts = {}, page = getPage(-1)) => {
  return new Promise((resolve, reject) => {
    const {
      action,
      data = page.state.data,
      count = 1,
      api,
      isOpenedKey = 'isOpened',
      formIns = null,
    } = opts;
    const { order_id, proof_image, brand } = data || {};
    logger.info('订单操作', action);
    switch (action) {
      case 'detail':
        console.info('参数', orderDetailOptions(data));
        Taro.navigator({
          url: 'order/detail',
          options: orderDetailOptions(data),
        });
        break;
      case 'list':
        Taro.navigator({
          url: 'order',
          target: 'tab',
        });
        break;
      case 'redit':
        Taro.modifyOrderId = order_id;
      case 'edit': // 继续下单
        editOrder(data);
        break;
      case 'gprint': // 组合打印方式
        Taro.kbActionSheet({
          items: ['预打印', '直接打印'],
          onClick: (index) => {
            orderAction(
              {
                ...opts,
                action: index === 0 ? 'pprint' : 'print',
              },
              page,
            );
          },
        });
        break;
      case 'mprint': // 一单多打
        orderActionRef.current = formIns;
        page.setState({
          [isOpenedKey]: new Date().getTime(),
        });
        break;
      case 'back': // 寄回
      case 'clone': // 再来一单
      case 'brand-switch': // 切换品牌下单
        if (action == 'clone' && data.source == 'ship_code') {
          scanCode({ mode: 'global' }).catch(reject);
        } else {
          cloneOrder(data, { api, count, action }, page).catch(reject);
        }
        break;
      case 'certificate': // 查看底单
        if (!proof_image) {
          Taro.kbToast({
            text: '暂无底单',
          });
          return;
        }

        if (process.env.MODE_ENV === 'wkd') {
          Taro.navigator({
            url: 'order/voucher',
            options: {
              pic: proof_image,
              brand,
              waybillno: data.waybill,
            },
          });
        } else {
          const proof_image_host = 'upload.kuaidihelp.com';
          const urls = [
            proof_image.includes(proof_image_host)
              ? proof_image.replace('http:', 'https:')
              : `https://${proof_image_host}${proof_image}`,
          ];
          Taro.previewImage({
            urls,
            current: urls[0],
          });
        }

        break;
      case 'cancel': // 取消订单
        console.info('是否需要区分订单来源 cancel=====>237400', data);
        if (data.source == 'tcjs') {
          cancelOrder({ data }, page).then(resolve).catch(reject);
        } else {
          cloneOrder(data, { api, count, action: 'cancel' }, page).catch(reject);
        }
        break;
      case 'pprint':
      case 'print':
        const printFn = () => {
          // console.log('触发打印')
          batchPrint(
            {
              ...opts,
              action,
            },
            page,
          )
            .then(resolve)
            .catch(reject);
        };
        if (action === 'print' && data.live_print == 1) {
          // 现场扫码打印
          checkCodeInfo(data).then((res) => {
            console.log('现场扫码打印==>', res);
            if (res) {
              console.log('执行打印==>');
              printFn();
            } else {
              createListener('printScan', () => {
                printFn();
              });
              Taro.navigator({
                url: 'order/print-scan',
                key: 'routerParamsChange',
                options: {
                  orderData: data,
                },
                onArrived: () => {},
              });
            }
          });
        } else {
          printFn();
        }
        break;
      case 'getWaybill':
        batchGetWaybill(
          {
            ...opts,
            action,
          },
          page,
        )
          .then(resolve)
          .catch(reject);
        break;
      case 'pay':
        refreshControl(REFRESH_KEY_ORDER);
        Taro.navigator({
          url: 'order/pay',
          options: orderDetailOptions(data, page.$router.params),
        });
        break;
    }
    if (process.env.MODE_ENV === 'wkd') {
      switch (action) {
        case 'refundCard':
          // 退卡
          refundCard(data).then(resolve).catch(reject);
          break;
        case 'contactCourier':
          // 联系快递员
          makePhoneCall(data.collect_courier_mobile);
          break;
        case 'refundDeal':
          refundOrder(data).then(resolve).catch(reject);
          break;
      }
    }
  });
};
const getWaybillCode = (order_id) => {
  let orderId = isArray(order_id) ? order_id.join(',') : order_id;
  return new Promise((resolve, reject) => {
    request({
      url: '/api/weixin/mini/minpost/order/getWaybillCode',
      data: {
        order_id: orderId,
      },
      onThen({ code, msg, data }) {
        if (code === 0) {
          resolve(data);
        } else {
          reject({ msg, code });
        }
      },
    });
  });
};
// 批量获取运单号
const batchGetWaybill = (e = {}) => {
  return new Promise((resolve, reject) => {
    const { data: { order_id } = {} } = e || {};
    const list = isArray(order_id) ? order_id : order_id.split(',');
    const page_size = 5;
    const total = list.length;
    const fails = []; // 错误信息
    const groups = createGroup(list, page_size); // 订单分组
    const groupsLength = groups.length;
    let success = 0;
    const next = (i) => {
      if (i < groupsLength) {
        triggerGetWaybill(i);
      } else {
        if (success !== total) {
          Taro.kbModal({
            content: [
              `${success > 0 ? '部分' : ''}运单号获取失败！`,
              `失败原因：${fails.slice(0, 2).join(',')}`,
            ],
            cancelText: '我知道了',
            confirmText: '',
          });
        } else {
          Taro.kbToast({
            text: `运单号获取成功`,
          });
          // 打印成功后记录当前的打印类型： 打印、预打印
        }
        if (success > 0) {
          resolve({
            success,
            fails,
            total,
            action: 'getWaybill',
          });
        } else {
          reject(new Error(fails.join(',')));
        }
      }
    };
    const triggerGetWaybill = (i) => {
      const page_num = i + 1;
      const order_ids = groups[i];
      getWaybillCode(order_ids).then(
        (data) => {
          success += order_ids.length;
          order_ids.forEach((i) => {
            let orderVal = data[i];
            if (orderVal) {
              const { code, msg } = orderVal;
              if (code != 0) {
                msg && !fails.includes(msg) && fails.push(msg);
                success--;
              }
            } else {
              // 未读到对应订单信息时也做失败处理
              success--;
            }
          });

          next(page_num);
        },
        ({ msg, code }) => {
          if (!fails.includes(msg) && msg) {
            fails.push(msg);
          }
          // 未设置自主打印机
          if (code !== 9001) {
            next(page_num);
          }
        },
      );
    };
    triggerGetWaybill(0);
  });
};

// 批量打印
const batchPrint = (e = {}, page) => {
  return new Promise((resolve, reject) => {
    const { action, data = page.state.data, count } = e;
    const pprint = action === 'pprint';
    const print_type = pprint ? 1 : 3;
    // 一单多打时存储了formIns，直接调用
    if (orderActionRef.current) {
      orderActionRef.current.submit({
        order_num: count,
        print_type,
        status: action,
      });
      orderActionRef.current = null;
      return;
    }

    if (!data) {
      reject(new Error(`缺少订单信息：${action}`));
      return;
    }

    // 打印、预打印
    const { order_id, customer_id, wkdSupportPrint, brand, collect_courier_id } = data;
    const list = isArray(order_id) ? order_id : order_id.split(',');
    const total = list.length; // 订单总数
    const fails = []; // 错误信息
    const page_size = 5;
    const groups = createGroup(list, page_size); // 订单分组
    const groupsLength = groups.length;
    let index = 0;
    let success = 0; // 打印成功数量
    let extraReqData = {};
    const triggerPrint = (index) => {
      const page_num = 1 + index;
      const order_ids = groups[index];
      const isWkd = process.env.MODE_ENV === 'wkd';
      // 额外请求数据
      if (total > 1) {
        extraReqData = {
          page_num,
          page_size,
          total,
        };
      } else if (count > 1) {
        // 一单多打
        extraReqData = {
          print_num: count,
        };
      }
      const printUrl = isWkd
        ? wkdSupportPrint
          ? '/v1/WeApp/courierOrderprint'
          : '/g_wkd/v2/work/Order/printOrder'
        : '/api/weixin/mini/minpost/order/newOrderPrint';
      if (isWkd) {
        extraReqData.customer_id = customer_id;
        if (wkdSupportPrint) {
          extraReqData.order_id = order_ids.join(',');
          extraReqData.brand = brand;
          extraReqData.courier_id = collect_courier_id;
        }
      }
      request(
        {
          url: printUrl,
          toastLoading: false,
          directTriggerThen: true,
          data: {
            order_ids: order_ids.join(','),
            print_type,
            ...extraReqData,
          },
          onThen: (res) => {
            const { code, data, msg } = res;
            if (wkdSupportPrint) {
              if (code == 0) {
                Taro.kbToast({
                  text: `${pprint ? '预' : ''}打印成功`,
                });
                resolve({
                  success: 1,
                  total: 1,
                  status: action,
                  action: 'print',
                });
              } else {
                Taro.kbToast({
                  text: `失败:${msg}`,
                });
                reject(new Error(msg));
              }
              return;
            }

            if (code != 0) {
              if (msg && !fails.includes(msg)) {
                // 收集错误信息
                fails.push(msg);
              }
            } else {
              success += order_ids.length;
              order_ids.forEach((i) => {
                let orderVal = data[i];
                if (orderVal) {
                  const { code, msg } = orderVal;
                  if (code != 0) {
                    msg && !fails.includes(msg) && fails.push(msg);
                    success--;
                  }
                } else {
                  // 未读到对应订单信息时也做失败处理
                  success--;
                }
              });
            }
            // 9001 未配置打印机，所有打印都会失败，直接中断执行
            if (page_num >= groupsLength || code == 9001) {
              // 所有请求完成
              Taro.kbToast({
                isOpened: false,
              });
              // 包含失败数据
              const errMsg = fails.join('；');
              if (success !== total) {
                Taro.kbModal({
                  content: [`${success > 0 ? '部分' : ''}订单打印失败！`, `失败原因：${errMsg}`],
                  cancelText: '我知道了',
                  confirmText: '',
                });
              } else {
                Taro.kbToast({
                  text: `${pprint ? '预' : ''}打印成功`,
                });
                // 打印成功后记录当前的打印类型： 打印、预打印
                if (page) {
                  page.activePrintType = action;
                }
              }
              // 回调通知
              if (success > 0) {
                resolve({
                  success,
                  total,
                  status: action,
                  action: 'print',
                });
              } else {
                reject(new Error(errMsg));
              }
            } else {
              triggerPrint(page_num);
            }
          },
        },
        page,
      );
    };

    if (total > 0) {
      Taro.kbToast({
        status: 'loading',
      });
      triggerPrint(index);
    } else {
      reject(new Error('缺少可打印订单'));
    }
  });
};

// 检查页面是否挂载地址编辑组件ref
function checkHasAddressRef(page) {
  return page.addressEditRef && page.addressEditRef.current;
}

// 设置地址信息缓存
function setAddressStorage(type, data) {
  switch (type) {
    case 'send':
      setStorage({
        key: sendStorageKey,
        data: data[0],
      });
      break;
    case 'receive':
      setStorage({
        key: receiveStorageKey,
        data,
      });
      break;

    default:
      break;
  }
}

// 设置收件人列表并寄件
export function createByAddressEdit(item, data) {
  const { type: key, action } = item;
  const dataItem = data[0] || {};
  if (process.env.MODE_ENV === 'wkd') {
    if (key === 'ecode-save') {
      //保存快递码
      addEcode(data[0], {
        toastLoading: false,
        toastSuccess: '添加成功！',
      });
      return;
    }
    // 微掌柜邀请下单或优惠寄
    if (action !== 'edit') {
      const page = getPage(-1);
      switch (key) {
        case 'receive':
          if (checkHasAddressRef(page)) {
            page.addressEditRef.current.getReceiveStorageListDebounce('reload', data);
          }
          break;
        case 'fix':
          Taro.navigator({
            url: 'address/edit',
            options: {
              ...dataItem,
              org: 'send',
              action: 'fix',
              fixFor: key,
            },
          });
          break;

        default:
          break;
      }

      return;
    }
  }

  if (item.typeSuffix == 'modify') {
    // 设置全局数据，以便在批量设置页获取
    Taro.kbSetGlobalData(receiveStorageKey, data);
    const current = getPage(-1);
    createListener('addressBatch', ({ list, sendAndReceive }) => {
      if (sendAndReceive) {
        // 同时更新寄收件人
        current.addressEditRef.current.updateSendAndReceive(sendAndReceive);
        return;
      }
      current.addressEditRef.current.updateFormDataByReceiveList(list);
    });
    Taro.navigator({
      url: 'address/batch',
      options: {
        action: 'edit',
      },
    });
    return;
  }

  Taro.navigator({
    url: 'order/edit/send',
    target: 'self',
    onArrived: (page) => {
      if (checkHasAddressRef(page)) {
        // 地址编辑组件ref已挂载
        switch (key) {
          case 'send':
            setAddressStorage(key, data);
            page.addressEditRef.current.getDefaultAddress();
            break;
          case 'receive':
            function setAddressFn(rData) {
              if (rData && rData.length > 1) {
                data = rData.concat(data);
              }
              data = data.map(({ error, save, ...ret }) => ({
                ...ret,
                save,
                error: error || checkDataComplete(ret).error,
              }));

              setAddressStorage(key, data);
              page.addressEditRef.current.getReceiveStorageListDebounce('reload', data);
            }
            page.addressEditRef.current
              .checkReceiveStorageList()
              .then(setAddressFn)
              .catch(setAddressFn);
            if (process.env.MODE_ENV === 'wkd') {
              const { notice } = dataItem;
              if (notice) {
                // 快递码包含寄件提醒
                Taro.kbModal({
                  content: [
                    '如果给我寄快递，请注意：',
                    { text: notice, className: 'kb-color__black' },
                  ],
                  confirmText: '知道了',
                });
              }
            }
            break;
          case 'fix': // 完善地址
            Taro.navigator({
              url: 'address/edit',
              options: {
                ...dataItem,
                org: 'send',
                action: 'fix',
              },
            });
            break;
          default:
            break;
        }
      }
    },
  });
}

// 批量下单
const batchActionRef = {
  current: null,
  orderIds: [],
};

// 清空创建订单
export function cleanBatchSubmitOrderStatus() {
  // 忽略之前的打印数据，重新开始新的打印
  batchActionRef.current = null;
  batchActionRef.orderIds = [];
}

export function batchSubmitOrder(data, page) {
  // 触发请求
  return new Promise((resolve, reject) => {
    const { ignore = false, receiveList, relation_id, extra_info, order_info } = data;
    const { index, errMsg } = getErrorAddressIndex(receiveList);
    if (index >= 0) {
      // 有错误
      reject(new Error(errMsg));
      return;
    }
    if (ignore) {
      cleanBatchSubmitOrderStatus();
    }
    const size = 5; // 分组大小
    const { orderPrice, ...rest } = order_info;
    const sendInfo = formatAddress(formatAddress(rest, 'send').data, 'f', {
      reverse: true,
      replace: {
        mobile: 'phone',
        address: 'detail',
        district: 'county',
      },
    }).data;
    const goodsInfo = extractData(rest, goodsKeys);
    const brandInfo = extractData(rest, brandKeys);
    const serviceInfo = extractData(rest, serviceKeys);
    const groups = createGroup(receiveList, size);
    const triggerSubmit = (opts) => {
      const { index = 0, batch_id = '' } = opts || {};
      const groupItem = groups[index];
      if (!groupItem) {
        // 所有分组处理完毕：关闭loading，并清空提交状态
        Taro.kbToast(
          {
            text: '订单创建完成',
          },
          page,
        );
        const { orderIds } = batchActionRef;
        resolve({
          code: 0,
          data: {
            total: orderIds.length,
            order_id: orderIds,
          },
        });
        cleanBatchSubmitOrderStatus();
        return;
      }
      const { goods_name } = goodsInfo;
      const { shipper_zipcode_del, product_type, reserve_time, brand } = brandInfo;
      // 收件人信息格式化
      const to_msg = groupItem.map((item) => ({
        ...extractData(
          { ...goodsInfo, ...item.extraInfo },
          [
            'goods_name',
            'goods_remark',
            'goods_weight',
            'package_images',
            ...serviceConfirmKeys.map((key) => {
              return [
                key,
                (data) => {
                  const upKey = key === 'rate' ? 'rate_checked' : key;
                  let value = '';
                  if (data['service']) {
                    value = data['service'][upKey] || serviceInfo[upKey];
                  } else {
                    value = serviceInfo[upKey];
                  }
                  return value;
                },
              ];
            }),
          ],
          goodsInfo,
        ),
        ...extractData(
          item,
          addressKeys.map((item) => (item === 'mobile' ? ['phone', 'mobile'] : item)),
        ),
      }));
      // 开始id
      const start_id = 1 + index * size;
      request(
        {
          url: '/order/BatchOrder/batchOrder',
          toastLoading: false,
          directTriggerThen: true,
          data: {
            ...sendInfo,
            relation_id,
            extra_info,
            brand,
            info: goods_name,
            to_msg,
            start_id,
            batch_id,
            shipper_zipcode_del,
            product_type,
            reserve_time,
          },
          onThen: ({ code, msg, data }) => {
            if (code == 0) {
              const { batch_id, order_id } = data || {};
              batchActionRef.current = {
                batch_id,
                index: 1 + index,
              };
              if (isArray(order_id)) {
                batchActionRef.orderIds.push(...order_id);
              }
              // 继续提交
              triggerSubmit(batchActionRef.current);
            } else {
              let toastData = { text: msg };
              if (code == '10086') {
                // 地址错误
                groupItem.map((item) => {
                  item.error = 'whatever';
                });
                // 更新表单状态
                page.addressEditRef.current.updateFormDataByReceiveList();
                Taro.kbModal(
                  {
                    content: msg,
                    confirmText: '去修改',
                    closeOnClickOverlay: false,
                    onConfirm: () => {
                      page.addressEditRef.current.handleBatch({
                        action: 'correct',
                        index: start_id - 1,
                      });
                    },
                  },
                  page,
                );
                toastData = { isOpened: false };
              }
              Taro.kbToast(toastData, page);
            }
          },
        },
        page,
      );
    };
    Taro.kbToast(
      {
        status: 'loading',
      },
      page,
    );
    // 提交订单
    triggerSubmit(batchActionRef.current);
  });
}

export { addressKeys };

//转换标准微快递地址信息
export function transferWkdAddress(data = {}, action = 'all') {
  let arr = [
    ['shipper_province', 'send_province'],
    ['shipper_city', 'send_city'],
    ['shipper_district', 'send_district'],
    ['shipper_address', 'send_address'],
    ['shipping_province', 'receive_province'],
    ['shipping_city', 'receive_city'],
    ['shipping_district', 'receive_district'],
    ['shipping_address', 'receive_address'],
  ];
  if (action == 'all') {
    arr = arr.concat([
      ['shipper_name', 'send_name'],
      ['shipper_mobile', ({ send_mobile, send_tel }) => send_mobile || send_tel],
      ['shipping_name', 'receive_name'],
      ['shipping_mobile', ({ receive_mobile, receive_tel }) => receive_mobile || receive_tel],
    ]);
  }
  return extractData(data, arr);
}
// 创建优惠券
export const createCustomCoupon = (params) => {
  return new Promise((resolve, reject) => {
    request({
      url: '/api/weixin/mini/minpost/Coupon/createCustomCoupon',
      data: { ...params },
      toastLoading: false,
      toastError: false,
      directTriggerThen: true,
      onThen({ data, code, msg }) {
        if (code == 0) {
          resolve(data);
        } else {
          reject(msg);
        }
      },
    });
  });
};

//春节活动日期判断
function isDuringDate(beginDateStr, endDateStr) {
  var curDate = new Date(),
    beginDate = new Date(beginDateStr),
    endDate = new Date(endDateStr);
  if (curDate >= beginDate && curDate <= endDate) {
    return true;
  }
  return false;
}
export const isSpringFestival = isDuringDate('2022/1/25', '2022/2/7');

// 获取快递员配置
export const getCourierConfig = (opt) => {
  const { courier_id } = opt || {};
  return new Promise((resolve) => {
    request({
      url: '/v1/WeApp/getCourierConfig',
      nonceKey: 'courier_id',
      data: {
        courier_id,
      },
      onThen: (res) => {
        if (res.code == 0 && res.data) {
          const { online_pay, update_price, print_times, is_new_address, ...rest } = res.data;
          resolve({
            isOpenOnlinePay: online_pay, // 是否开启线上付 1开启 0 未开启
            isModifyPrice: update_price, // 是否允许修改价格 1允许 0 不允许
            isPrintSecond: print_times, // 是否允许二次打印 1允许 0 不允许
            isPickupAddress: is_new_address, // 是否填写快递员取件地址 1允许 0 不允许
            ...rest,
          });
        } else {
          resolve({});
        }
      },
    });
  });
};
