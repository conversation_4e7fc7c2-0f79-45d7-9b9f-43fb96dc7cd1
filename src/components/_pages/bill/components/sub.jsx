/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { useEffect, useRef, Fragment } from '@tarojs/taro';
import KbSubscribeMsg from '@base/components/subscribe/subscribe-msg';
import KbSubscribe from '@base/components/subscribe';
import { View } from '@tarojs/components';
import { frequencyLimit } from '@base/utils/utils';
import './index.scss';

const AutoSubscribe = (props) => {
  const { subscribeAction } = props;
  const key = 'bill_auto_subscribe';

  const subscribeRef = useRef();

  const trigger = () => {
    if (subscribeRef && subscribeRef.current) {
      const isLimit = frequencyLimit('check', key);
      if (!isLimit) {
        subscribeRef.current.click();
        frequencyLimit('limit', key);
      }
    }
  };

  useEffect(() => {
    trigger();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [subscribeRef.current]);

  // 订阅后跳转
  const handleSubscribe = (e) => {
    console.log('handleSubscribe', e);
  };

  return (
    <Fragment>
      <View className='kb-third-subscribe'>
        <KbSubscribe
          action={subscribeAction}
          type='primary'
          circle
          size='small'
          onSubscribe={handleSubscribe}
          actionRef={subscribeRef}
        >
          订阅
        </KbSubscribe>
      </View>
      <KbSubscribeMsg />
    </Fragment>
  );
};

export default AutoSubscribe;
