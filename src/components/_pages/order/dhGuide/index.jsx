import { View } from '@tarojs/components';
import Taro, { useCallback, useEffect, useState, Fragment, useRef } from '@tarojs/taro';
import KbModal from '~base/components/modal';
import './index.scss';
import { useDHBrandInfo } from '../edit/dh/brand-info/_utils';
import debounce from 'lodash/debounce';
import isNaN from 'lodash/isNaN';
import isArray from 'lodash/isArray';

const MIN_WEIGHT = 30;

// 获取最大体积重量
const getMaxVolumeWeight = (list) => {
  if (!isArray(list)) return 0;
  const filterList = list.map((item) => 1 * item.volumeWeight).filter((item) => !isNaN(item));
  if (filterList.length === 0) return 0;
  return Math.max(...filterList);
};

// 格式化价格
const formatPrice = (list) => {
  return list.map((item) => 1 * item.discount_price).filter((item) => !isNaN(item));
};
const DhGuide = (props) => {
  const ref = useRef({ isRecommend: false });
  const [isOpened, setIsOpened] = useState(false);
  const [volumeWeight, setVolumeWeight] = useState(0);

  const { quotationList, data } = props;

  const { weight, address, extraInfo, volume } = data || {};

  const { quotationList: dhQuotationList, run } = useDHBrandInfo({
    address: null,
    extraInfo,
    weight,
    volume,
  });

  // 关闭
  const handleCancel = () => {
    ref.current.isRecommend = true;
    setIsOpened(false);
  };

  // 跳转经济货运
  const handleConfirm = () => {
    setIsOpened(false);
    Taro.navigator({
      url: 'order/edit/dh',
    });
  };

  // 检查是否需要打开
  const triggerCheckIsOpen = useCallback(
    debounce(
      ({ quotationList, dhQuotationList }) => {
        const maxPrice = Math.max(...formatPrice(quotationList));
        const minDhPrice = Math.min(...formatPrice(dhQuotationList));
        if (minDhPrice < maxPrice) {
          setIsOpened(true);
        }
        console.log('maxPrice', maxPrice);
        console.log('minDhPrice', minDhPrice);
      },
      500,
      { leading: false, trailing: true },
    ),
    [],
  );

  // 计算预估运费中最大重量
  useEffect(() => {
    const maxVolumeWeight = getMaxVolumeWeight(quotationList);
    setVolumeWeight(maxVolumeWeight);
  }, [quotationList]);

  // 判断是否打开弹窗
  useEffect(() => {
    if (ref.current.isRecommend) return; // 已推荐过
    if (!data) return;
    if (!quotationList || quotationList.length === 0) return;
    if (!dhQuotationList || dhQuotationList.length === 0) return;
    triggerCheckIsOpen({ quotationList, dhQuotationList });
  }, [quotationList, dhQuotationList]);

  // 监听体积转换体积
  useEffect(() => {
    if (ref.current.isRecommend) return;
    if (weight >= MIN_WEIGHT || volumeWeight >= MIN_WEIGHT) {
      run({ address });
    }
  }, [volumeWeight, weight]);

  return (
    <Fragment>
      {isOpened ? (
        <KbModal
          isOpened={isOpened}
          cancelText='我再想想'
          confirmText='立即前往'
          top3
          onConfirm={handleConfirm}
          onCancel={handleCancel}
        >
          <View className='kb-dh-guide__content'>
            <View className='kb-color__grey kb-spacing-sm-b'>将为你智能匹配</View>
            <View className='kb-color__grey kb-spacing-sm-b kb-dh-guide__text'>价格更优惠</View>
            <View className='kb-dh-guide__text kb-spacing-sm-b'>30KG+经济货运</View>
            <View className='kb-color__grey'>专属寄件页面</View>
          </View>
        </KbModal>
      ) : null}
    </Fragment>
  );
};

DhGuide.options = {
  addGlobalClass: true,
};

export default DhGuide;
