/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { Component } from '@tarojs/taro';
import KbPage from '@base/components/page';
import { View } from '@tarojs/components';
import KbLongList from '@base/components/long-list';
import classNames from 'classnames';
import './index.scss';

export default class Index extends Component {
  config = {
    navigationBarTitleText: '反馈记录',
  };

  constructor() {
    this.state = {
      list: [],
    };
    this.listData = {
      pageKey: 'page_no',
      api: {
        url: '/g_wkd/v2/FeedBack/list',
        formatResponse: (res) => {
          const { data = {} } = res;
          let list = data.list;
          if (Array.isArray(list) && list.length) {
            return {
              data,
            };
          }
          return {
            data: void 0,
          };
        },
        onThen: (list) => {
          this.setState({
            list,
          });
        },
      },
    };
  }

  onReady = (ins) => {
    this.listIns = ins;
  };

  handleNavigator = (item) => {
    Taro.navigator({
      url: `user/feedback/detail?id=${item.id}`,
    });
  };

  render() {
    const { list, ...rest } = this.state;

    return (
      <KbPage {...rest}>
        <KbLongList enableMore data={this.listData} onReady={this.onReady}>
          <View className='kb-feedback-record'>
            {list.map((item, index) => (
              <View
                key={item.id}
                className={classNames('kb-feedback-record-item at-row at-row__align--center', {
                  'kb-border-t': index != 0,
                })}
                hoverClass='kb-hover-opacity'
                onClick={this.handleNavigator.bind(this, item)}
              >
                <View className='kb-feedback-record-avatar'>
                  {item.type == 1 || item.type == 3 ? '问题' : item.type == 2 ? '建议' : '其他'}
                </View>
                <View className='kb-flex-1 kb-margin-md-lr'>
                  <View className='kb-size__lg'>{item.content}</View>
                  <View className='kb-size__sm kb-color__grey'>{item.create_at}</View>
                </View>
                <View
                  className={item.status == 1 || item.reply ? 'kb-color__brand' : 'kb-color__grey'}
                >
                  {item.status == 1 || item.reply ? '已回复' : '未回复'}
                </View>
              </View>
            ))}
          </View>
        </KbLongList>
      </KbPage>
    );
  }
}
