/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

export const DHFloorMessage = {
  off: [
    {
      title: '有电梯（W为计费重量）',
      desc: [
        'W≤50KG，免费上楼',
        '40<W≤130KG，首重40KG，首重10元;',
        '130<W≤300KG，首重130KG，首重20元;',
        'W>300KG，首重300KG，首重40元，续重0.1元/KG单订单封顶500元',
        '大件加收标准:',
        '50KG<单订单中其中1件实际重量至150KG，加收10元/件',
      ],
    },
    {
      title: '无电梯',
      desc: [
        'W≤40，免费上楼',
        '40<W≤130KG，首重40KG，首重25元;',
        '130<W≤300KG，首重130KG，首重45元;',
        'W>300KG，首重300KG，首重70元，续重0.18元/KG单订单封顶500元',
        '大件加收标准:',
        '50KG<单订单中其中1件实际重量≤100KG，加收20元/件;',
        '100KG<单订单中其中1件实际重量至130KG，加收70元/件。',
        '无电梯最高上楼15楼，超7楼部分每层加收20元/订单',
      ],
    },
    {
      title: '备注',
      desc: [
        '1、所有货物单边长大于 220CM，不提供上楼服务:',
        '2、楼梯可进的货物，电梯不可进，需与商家确认无电梯上楼，则按照无电梯上楼计费;',
        '3、床垫类同一订单货开单上楼，其中含床垫与非床垫货物的，分开上楼计费。',
      ],
    },
  ],
  on: [
    {
      title: '有电梯',
      desc: [
        '宽≤90CM;按普通送货上楼收费标准',
        '90<宽≤150CM，单订单首件30元/件，续件15元/件',
        '150<宽≤200CM，单订单首件40元/件，续件20元/件',
        '200<宽≤220CM，单订单首件60元/件，续件30元/件',
        '无电梯超楼层收费:',
        '无电梯最高上楼15楼，超7楼部分每层加收20元/件; (40KG以内免费上楼)',
      ],
    },
    {
      title: '无电梯',
      desc: [
        '宽≤90CM，按普通送货上楼收费标准',
        '90<宽≤150CM，单订单首件70元/件，续件35元/件150<宽≤200CM，单订单首件90元/件，续件45元/件200<宽≤220CM，单订单首件100元/件，续件50元/件',
      ],
    },
  ],
};

export const teshuquyuMessage = [
  {
    title: '有电梯',
    desc: [
      '宽≤90CM，按普通送货上楼收费标准',
      '90<宽≤150CM，单订单首件30元/件，续件15元/件',
      '150<宽≤200CM，单订单首件40元/件，续件20元/件',
      '200<宽≤220CM，单订单首件60元/件，续件30元/件',
      '无电梯超楼层收费: 无电梯最高上楼15楼，超7楼部分每层加收20元/件; (40KG以内免费上楼)',
    ],
  },
  {
    title: '无电梯',
    desc: [
      '宽≤90CM，按普通送货上楼收费标准',
      '90<宽≤150CM，单订单首件70元/件，续件35元/件',
      '150<宽≤200CM，单订单首件90元/件，续件45元/件',
      '200<宽≤220CM，单订单首件100元/件，续件50元/件',
    ],
  },
];

export const back_sign_bill_optionsMap = {
  sxjd: [
    {
      label: '原件回单',
      value: '原件回单',
    },
    {
      label: '电子回单',
      value: '电子回单',
    },
  ],
  bt: [
    {
      label: '回单原件',
      value: '回单原件',
    },
    {
      label: '电子回单',
      value: '电子回单',
    },
  ],
  ky: [
    {
      label: '回单原件',
      value: '回单原件',
    },
    {
      label: '回单照片',
      value: '回单照片',
    },
  ],
};

// 回单费
export const back_sign_bill_options = (opt = {}) => {
  const { brand } = opt;
  return back_sign_bill_optionsMap[brand] || [];
};

export const package_service_optionsMap = {
  bt: [
    {
      label: '打木架',
      value: '打木架',
      img: 'https://cdn-img.kuaidihelp.com/wkd/miniApp/dh/packaging_service/bt/dmj.png',
    },
    {
      label: '打木箱',
      value: '打木箱',
      img: 'https://cdn-img.kuaidihelp.com/wkd/miniApp/dh/packaging_service/bt/dmx.png',
    },
    {
      label: '打木托',
      value: '打木托',
      img: 'https://cdn-img.kuaidihelp.com/wkd/miniApp/dh/packaging_service/bt/dkb.png',
    },
    {
      label: '纸箱',
      value: '纸箱',
      img: 'https://cdn-img.kuaidihelp.com/wkd/miniApp/dh/packaging_service/bt/zx.png',
    },
    {
      label: '编织袋',
      value: '编织袋',
      img: 'https://cdn-img.kuaidihelp.com/wkd/miniApp/dh/packaging_service/bt/bzd.png',
    },
  ],
  ky: [
    {
      label: '打木架',
      value: '打木架',
      img: 'https://cdn-img.kuaidihelp.com/wkd/miniApp/dh/packaging_service/ky/dmj.png',
    },
    {
      label: '打木箱',
      value: '打木箱',
      img: 'https://cdn-img.kuaidihelp.com/wkd/miniApp/dh/packaging_service/ky/dmx.png',
    },
    {
      label: '打木托',
      value: '打木托',
      img: 'https://cdn-img.kuaidihelp.com/wkd/miniApp/dh/packaging_service/ky/dkb.png',
    },
  ],
};

// 包装服务
export const package_service_options = (opt = {}) => {
  const { brand } = opt;
  return package_service_optionsMap[brand] || [];
};

export const pickup_way_optionsMap = {
  sxjd: [
    {
      label: '送货上楼（无电梯）',
      value: '送货上楼（无电梯）',
    },
    {
      label: '送货上楼（有电梯）',
      value: '送货上楼（有电梯）',
    },
    {
      label: '网点自提',
      value: '网点自提',
    },
    {
      label: '送货（不含上楼）',
      value: '送货（不含上楼）',
    },
  ],
  bt: [
    {
      label: '送货上楼',
      value: '送货上楼',
    },
  ],
  ky: [
    {
      label: '取货爬楼费',
      value: '取货爬楼费',
    },
    {
      label: '派货爬楼费',
      value: '派货爬楼费',
    },
  ],
};

// 上楼费
export const pickup_way_options = (opt = {}) => {
  const { brand } = opt;
  return pickup_way_optionsMap[brand] || [];
};

export const createDhServiceForm = () => {
  return {
    //回单费
    back_sign_bill: {
      value: '',
      clean: true,
      storage: false,
      required: false,
    },
    package_service: {
      value: '',
      clean: true,
      storage: false,
      required: false,
    },
    // 上楼费
    pickup_way: {
      value: '',
      clean: true,
      storage: false,
      required: false,
    },
    // 楼层数
    floor: {
      value: 1,
      clean: true,
      storage: false,
      required: false,
    },
  };
};
