/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */
/* eslint-disable react/no-array-index-key */

import Taro, { Component } from '@tarojs/taro';
import { View, Swiper, SwiperItem, Text, Image } from '@tarojs/components';
import KbPage from '@base/components/page';
import KbModal from '@base/components/modal';
import KbButton from '@base/components/button';
import { getShareAppMessage } from '@/utils/share';
import { getUserVipInfo } from '@/components/_pages/user/_utils';
import { checkCreditService } from '@/components/_pages/order/_utils/order.credit-pay';
import { connect } from '@tarojs/redux';
import { get } from '@/actions/brands';
import './index.scss';

const guides = [
  '通过“微快递"平台指定活动专属入口内参与；',
  '无需领券，折扣直减补贴用户，限在微快递微信小程序寄经济货运线上开通支付分“先享后付”支付时使用，扫码下单或给专属快递员下单时不可用； ',
  '货物有最低限重30KG，下单未达到30KG将按照最小30KG计费； ',
  '活动合作折扣快递公司为顺心捷达、百世快运、壹米滴答、跨越速运等；',
  '单次快递费用超出499元，平台计费将分多次扣费完成支付； ',
  '扣费时间将在快递签收48小时内完成，相关费用扣费结算。 ',
  '如寄件过程发生退改件等场景，会产出其他增值费用，可查看订单详情具体收费明细；',
  '如有其他快递相关服务需求，可在物品信息栏-填写备注； ',
  '活动最终解释权归微快递所有！',
];
const discount = {
  sto: ['5.0', '元'],
  yt: ['5.0', '元'],
  zt: ['5.0', '元'],
  yd: ['5.0', '元'],
  cngg: ['5.0', '元'],
  jt: ['5.2', '元'],
  jd: ['7.5', '折'],
  dp: ['8.3', '折'],
  ems: ['8.5', '折'],
  sf: ['9.3', '折'],
};

@connect(
  ({ global }) => ({
    brands: global.brands || {},
  }),
  { get },
)
class Index extends Component {
  config = {
    navigationBarTitleText: '',
    navigationStyle: 'custom',
  };
  constructor(props) {
    super(props);
    this.state = {
      isOpened: false,
      isVip: false,
      isCredit: false,
    };
    this.navProps = { theme: 'ghost-white', fixed: true };
    this.discountBrands = [
      [{ brand: 'bt' }, { brand: 'htky' }],
      [{ brand: 'sxjd' }, { brand: 'ky' }],
    ];
    this.moreBrands = [
      ['sto', 'yt', 'zt', 'yd', 'cngg'],
      ['jt', 'jd', 'dp', 'ems', 'sf'],
    ];
  }

  setIsOpened = (v) => {
    this.setState({
      isOpened: !!v,
    });
  };

  generatePhoneNumber() {
    let prefix = [
      130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 145, 147, 150, 151, 152, 153, 155, 156, 157,
      158, 159, 184, 185, 186, 187, 188, 189, 190,
    ];
    let prefixIndex = Math.floor(Math.random() * prefix.length);
    let phone = prefix[prefixIndex].toString() + '****';
    for (let i = 0; i < 4; i++) {
      phone += Math.floor(Math.random() * 10).toString();
    }
    return phone;
  }

  onShareAppMessage = (e) => {
    return getShareAppMessage(e, {
      page: 'order.economy',
    });
  };

  getVipStatus = () => {
    getUserVipInfo().then((data) => {
      this.setState({
        isVip: data && data.status == 1,
      });
    });
  };
  getCreditStatus = () => {
    checkCreditService().then((res) => {
      this.setState({
        isCredit: res,
      });
    });
  };

  onUpdate = (data) => {
    const { logined } = data;
    if (logined) {
      this.props.get();
      this.getVipStatus();
      this.getCreditStatus();
    }
  };

  render() {
    const { isOpened, isVip, isCredit, ...rest } = this.state;
    const barrage = Array.from({ length: 30 }).map(() => [
      { phone: this.generatePhoneNumber() },
      { phone: this.generatePhoneNumber() },
    ]);
    const moreBrands = this.moreBrands.map((child) =>
      child.map((item) => ({
        brand: item,
        name:
          (this.props.brands && this.props.brands[item] && this.props.brands[item].name) || item,
        url: `https://cdn-img.kuaidihelp.com/brand_logo/icon_${item}.png`,
        discount: discount[item],
      })),
    );

    return (
      <KbPage onUpdate={this.onUpdate} navProps={this.navProps} {...rest} className='kb-bg'>
        <View className='economy-header'>
          <KbButton className='economy-header_operate' openType='share' page='order.economy'>
            分享
          </KbButton>
          <View
            className='at-row at-row__justify--center at-row__align--center economy-header_operate exp'
            hoverClass='kb-hover-opacity'
            onClick={() => this.setIsOpened(true)}
          >
            说明
          </View>
          <View className='economy-header-swiper'>
            <Swiper className='economy-header-swiper-item' vertical circular autoplay>
              {barrage.map((child, index) => (
                <SwiperItem key={index}>
                  {child.map((item, i) => (
                    <View key={i} className='kb-text__center kb-color__white'>
                      恭喜{item.phone} 成功参与百万补贴
                    </View>
                  ))}
                </SwiperItem>
              ))}
            </Swiper>
          </View>
          <View className='economy-header-title'>
            {isCredit ? '恭喜你！开通微信支付分' : '开通微信支付分可享'}
          </View>
          <View
            className='at-row at-row__justify--center at-row__align--center economy-btn economy-header-btn'
            hoverClass='kb-hover-opacity'
            onClick={() => Taro.navigator({ url: isCredit ? 'order/edit' : 'order/credit-pay' })}
          >
            <View>{isCredit ? '立即寄件' : '立即开通'}</View>
            <View className='economy-btn-arrow' />
          </View>
        </View>
        {this.discountBrands.map((child, index) => (
          <View
            className={`economy-brand ${index === 1 ? 'economy-brand_bottom' : ''}`}
            key={index}
          >
            {child.map((item, i) => (
              <View
                key={i}
                className={`at-row at-row__justify--center at-row__align--center economy-brand-operate ${
                  i === 1 ? 'economy-brand-operate_bottom' : ''
                }`}
              >
                <View
                  className={`at-row at-row__justify--center at-row__align--center economy-btn economy-btn-small ${
                    !isVip ? 'w280' : ''
                  }`}
                  hoverClass='kb-hover-opacity'
                  onClick={() =>
                    Taro.navigator({ url: 'order/edit/dh', options: { brand: item.brand } })
                  }
                >
                  <View>去寄件</View>
                  <View className='economy-btn-arrow economy-btn-arrow__blue' />
                </View>
                <View
                  className={`at-row at-row__justify--center at-row__align--center kb-margin-md-l economy-btn ${
                    !isVip ? 'w280' : ''
                  }`}
                  hoverClass='kb-hover-opacity'
                  onClick={() =>
                    Taro.navigator({ url: isVip ? 'user/member/right' : 'user/member' })
                  }
                >
                  <View>{isVip ? '查看VIP会员特权' : '开通会员'}</View>
                  <View className='economy-btn-arrow' />
                </View>
              </View>
            ))}
          </View>
        ))}
        <View className='economy-vip'>
          <View
            className='at-row at-row__justify--center  at-row__align--center economy-vip-tips'
            hoverClass='kb-hover-opacity'
            onClick={() => Taro.navigator({ url: 'user/member/right' })}
          >
            优享寄VIP会员专属权益，
            <Text className='kb-color__brand'>前往查看特权详情 {'>'}</Text>
          </View>
          <View className='economy-swiperWrap'>
            {moreBrands.map((child, index) => (
              <Swiper
                key={index}
                className={`economy-scrollBrands ${
                  index == 1 ? 'economy-scrollBrands_bottom' : ''
                }`}
                autoplay
                circular
                displayMultipleItems={4}
                interval={1}
                duration={2700}
              >
                {child.map((item) => (
                  <SwiperItem key={item.brand} className='economy-scrollBrands-wrap'>
                    <View
                      key={item.brand}
                      className='at-row at-row__align--center at-row__justify--center economy-brandsItem'
                      hoverClass='kb-hover-opacity'
                      onClick={() =>
                        Taro.navigator({ url: 'order/edit/send', options: { brand: item.brand } })
                      }
                    >
                      <View className='at-row at-row__align--center tips'>
                        <Text className='kb-size__sm'>{item.discount[0]}</Text>
                        <Text>{item.discount[1]}</Text>起
                      </View>
                      <Image className='img' src={item.url} />
                      <View className='kb-color__greyer kb-size__sm kb-weight'>{item.name}</View>
                    </View>
                  </SwiperItem>
                ))}
              </Swiper>
            ))}
          </View>
        </View>
        <View className='economy-footer' />
        <KbModal
          title='活动说明'
          top={false}
          closable={false}
          confirmText='我知道了'
          isOpened={isOpened}
          onClose={() => this.setIsOpened()}
          onCancel={() => this.setIsOpened()}
          onConfirm={() => this.setIsOpened()}
          confirmButtonProps={{ className: 'kb-guide--btn' }}
          className='kb-economy-modal'
        >
          <View className='kb-guide--list'>
            {guides.map((item, index) => {
              return (
                <View className='kb-guide--list-item' key={item}>
                  <View className='kb-guide--list-index'>{index + 1}、</View>
                  <View className='kb-guide--list-content'>{item}</View>
                </View>
              );
            })}
          </View>
        </KbModal>
      </KbPage>
    );
  }
}

export default Index;
