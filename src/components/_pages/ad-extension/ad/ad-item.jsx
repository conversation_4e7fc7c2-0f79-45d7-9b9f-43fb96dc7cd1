/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { Ad, View } from '@tarojs/components';
import { Fragment, useState, useEffect } from '@tarojs/taro';
import classNames from 'classnames';
import { useCheckIsShieldAd } from '../sdk/shield';
import './ad-item.scss';
/* eslint-disable react/no-unknown-property */
/**
 * 微信官方广告组件位;
 */
const AdItem = (props) => {
  const {
    unitId: unitIdAndType = '',
    intervals,
    style,
    onLoad,
    onError,
    onClose,
    wrapper,
    implant,
  } = props;
  const [unitId = '', type = 'normal'] = unitIdAndType.split('.');
  const [isOpened, updateIsOpened] = useState(!!unitId);
  const rootClass = classNames({
    'kb-ad__wrapper': wrapper || type !== 'custom',
    'kb-box': implant,
    'kb-spacing-md': implant && type !== 'custom',
  });

  useEffect(() => {
    updateIsOpened(!!unitId);
  }, [unitId]);

  const handelClose = (e) => {
    updateIsOpened(false);
    onClose(e);
  };

  const handelAdErr = (e) => {
    onError(e);
    handelClose();
  };

  const shield = useCheckIsShieldAd('1');

  return (process.env.PLATFORM_ENV === 'alipay' || process.env.PLATFORM_ENV === 'weapp') &&
    process.env.MODE_ENV !== 'third.post' ? (
    <Fragment>
      {isOpened && !shield ? (
        <View style={{ ...style }} className={rootClass}>
          {process.env.PLATFORM_ENV === 'alipay' ? (
            <Ad
              className='kb-ad'
              unitId={unitId}
              onLoad={onLoad}
              onError={handelAdErr}
              onClose={handelClose}
            />
          ) : type === 'custom' ? (
            <View className='kb-ad' style='height:auto;'>
              <ad-custom-weapp
                adIntervals={intervals}
                unitId={unitId}
                onLoad={onLoad}
                onError={handelAdErr}
              />
            </View>
          ) : (
            <Ad
              className='kb-ad'
              adIntervals={intervals}
              style='height:auto;'
              unitId={unitId}
              onLoad={onLoad}
              onError={handelAdErr}
              onClose={handelClose}
            />
          )}
        </View>
      ) : null}
    </Fragment>
  ) : (
    <Fragment />
  );
};

AdItem.defaultProps = {
  intervals: '2000',
  unitId: '',
  implant: true,
  wrapper: false,
  onLoad: () => {},
  onError: () => {},
  onClose: () => {},
};

AdItem.options = {
  addGlobalClass: true,
};

AdItem.config = {
  usingComponents:
    process.env.MODE_ENV === 'weapp'
      ? {
          'ad-custom-weapp': './custom-weapp/index',
        }
      : {},
};

export default AdItem;
